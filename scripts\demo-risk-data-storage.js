#!/usr/bin/env node

/**
 * Risk Data Storage Demo Script
 * 风险数据存储与历史记录功能演示脚本
 * 
 * 演示功能:
 * - 风险评估结果存储
 * - 风险指标历史数据记录
 * - 风险分析历史记录查询
 * - 风险数据对比分析
 * - 风险指标趋势分析
 */

const { v4: uuidv4 } = require('uuid');

// 模拟风险评估数据
const generateMockRiskAssessment = (ticker, riskLevel = 'medium') => {
    const riskScores = {
        low: Math.floor(Math.random() * 3) + 1,    // 1-3
        medium: Math.floor(Math.random() * 4) + 4, // 4-7
        high: Math.floor(Math.random() * 3) + 8    // 8-10
    };

    const volatilities = {
        low: 0.1 + Math.random() * 0.1,    // 0.1-0.2
        medium: 0.2 + Math.random() * 0.15, // 0.2-0.35
        high: 0.35 + Math.random() * 0.25   // 0.35-0.6
    };

    return {
        workflow_id: uuidv4(),
        overall_risk_level: riskLevel,
        risk_score: riskScores[riskLevel],
        summary: `${ticker} 风险评估 - ${riskLevel === 'low' ? '低风险' : riskLevel === 'medium' ? '中风险' : '高风险'}`,
        market_risk: {
            volatility: volatilities[riskLevel],
            beta: 0.8 + Math.random() * 0.8, // 0.8-1.6
            correlation_with_market: 0.6 + Math.random() * 0.3
        },
        liquidity_risk: {
            bid_ask_spread: 0.001 + Math.random() * 0.01,
            daily_volume: Math.floor(Math.random() * 10000000) + 1000000,
            market_impact: Math.random() * 0.05
        },
        credit_risk: {
            debt_to_equity: Math.random() * 2,
            current_ratio: 1 + Math.random() * 2,
            credit_rating: ['AAA', 'AA', 'A', 'BBB', 'BB'][Math.floor(Math.random() * 5)]
        },
        operational_risk: {
            governance_score: Math.floor(Math.random() * 5) + 6, // 6-10
            management_quality: Math.floor(Math.random() * 5) + 6,
            regulatory_compliance: Math.random() > 0.8 ? 'high' : 'medium'
        },
        scenario_analysis: {
            bear_market_impact: -(Math.random() * 0.4 + 0.1), // -10% to -50%
            stress_test_result: Math.random() > 0.7 ? 'passed' : 'warning',
            monte_carlo_var: Math.random() * 0.1 + 0.02 // 2%-12%
        },
        risk_metrics: {
            var_95: Math.random() * 0.08 + 0.02, // 2%-10%
            var_99: Math.random() * 0.12 + 0.03, // 3%-15%
            max_drawdown: -(Math.random() * 0.3 + 0.05), // -5% to -35%
            sharpe_ratio: Math.random() * 2 - 0.5, // -0.5 to 1.5
            sortino_ratio: Math.random() * 2.5,
            calmar_ratio: Math.random() * 1.5
        },
        recommendations: [
            riskLevel === 'high' ? '建议降低仓位' : '可适当增加仓位',
            `设置止损位于 ${(Math.random() * 0.1 + 0.05).toFixed(2)}`,
            riskLevel === 'low' ? '适合长期持有' : '建议短期交易'
        ],
        risk_controls: {
            max_position_size: riskLevel === 'high' ? 0.02 : riskLevel === 'medium' ? 0.05 : 0.1,
            stop_loss_percentage: Math.random() * 0.1 + 0.05,
            rebalance_frequency: riskLevel === 'high' ? 'daily' : 'weekly'
        },
        risk_warnings: riskLevel === 'high' ? [
            '高波动性警告',
            '流动性风险',
            '市场风险较高'
        ] : [],
        status: 'completed',
        execution_time_ms: Math.floor(Math.random() * 3000) + 500
    };
};

// 生成风险指标历史数据
const generateRiskMetricsHistory = (ticker, days = 30) => {
    const metrics = [];
    const baseVolatility = 0.2 + Math.random() * 0.1;

    for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);

        metrics.push({
            workflow_id: uuidv4(),
            ticker,
            date: date.toISOString().split('T')[0],
            volatility: baseVolatility + (Math.random() - 0.5) * 0.05,
            beta: 1.0 + (Math.random() - 0.5) * 0.3,
            var_95: 0.05 + Math.random() * 0.03,
            var_99: 0.08 + Math.random() * 0.05,
            max_drawdown: -(Math.random() * 0.2 + 0.05),
            sharpe_ratio: Math.random() * 1.5,
            liquidity_ratio: Math.random() * 0.1 + 0.9
        });
    }

    return metrics.reverse(); // 按时间正序
};

// 演示风险数据存储
async function demonstrateRiskDataStorage() {
    console.log('🎯 风险数据存储与历史记录功能演示');
    console.log('='.repeat(50));

    // 1. 生成模拟数据
    console.log('\n📊 1. 生成模拟风险评估数据');
    const tickers = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'AMZN'];
    const riskLevels = ['low', 'medium', 'high'];

    const assessments = [];
    tickers.forEach(ticker => {
        riskLevels.forEach(level => {
            const assessment = generateMockRiskAssessment(ticker, level);
            assessments.push(assessment);
            console.log(`   ✓ ${ticker} - ${level} 风险评估 (评分: ${assessment.risk_score}/10)`);
        });
    });

    // 2. 演示数据存储API调用
    console.log('\n💾 2. 风险评估数据存储演示');
    console.log('API调用示例:');
    console.log('POST /api/risk-data');
    console.log('请求体:', JSON.stringify(assessments[0], null, 2));

    // 3. 演示历史记录查询
    console.log('\n📚 3. 风险分析历史记录查询演示');
    console.log('API调用示例:');
    console.log('GET /api/risk-data?history=true&ticker=AAPL&limit=10');

    const historyExample = {
        success: true,
        data: assessments.filter(a => a.workflow_id.includes('AAPL')).slice(0, 3),
        total: 15,
        pagination: {
            limit: 10,
            offset: 0,
            has_more: true
        }
    };
    console.log('响应示例:', JSON.stringify(historyExample, null, 2));

    // 4. 演示风险指标趋势
    console.log('\n📈 4. 风险指标趋势分析演示');
    const trendData = generateRiskMetricsHistory('AAPL', 7);
    console.log('API调用示例:');
    console.log('GET /api/risk-data/metrics?ticker=AAPL&metric=volatility&days=7');
    console.log('趋势数据示例:');
    trendData.forEach(data => {
        console.log(`   ${data.date}: 波动率 ${data.volatility.toFixed(4)}, Beta ${data.beta.toFixed(2)}`);
    });

    // 5. 演示风险对比分析
    console.log('\n⚖️  5. 风险对比分析演示');
    const comparisonWorkflows = assessments.slice(0, 3).map(a => a.workflow_id);
    console.log('API调用示例:');
    console.log('POST /api/risk-data/compare');
    console.log('请求体:', JSON.stringify({ workflow_ids: comparisonWorkflows }, null, 2));

    const comparisonResult = {
        comparisons: assessments.slice(0, 3).map(a => ({
            workflow_id: a.workflow_id,
            ticker: 'AAPL',
            overall_risk_level: a.overall_risk_level,
            risk_score: a.risk_score,
            key_metrics: a.risk_metrics,
            created_at: new Date().toISOString()
        })),
        summary: {
            risk_level_distribution: { low: 1, medium: 1, high: 1 },
            avg_risk_score: 5.5,
            risk_score_range: { min: 2, max: 9 }
        }
    };
    console.log('对比结果示例:', JSON.stringify(comparisonResult, null, 2));

    // 6. 演示统计信息
    console.log('\n📊 6. 风险评估统计信息演示');
    console.log('API调用示例:');
    console.log('GET /api/risk-data?statistics=true&ticker=AAPL');

    const statisticsExample = {
        total_assessments: assessments.length,
        risk_level_distribution: {
            low: assessments.filter(a => a.overall_risk_level === 'low').length,
            medium: assessments.filter(a => a.overall_risk_level === 'medium').length,
            high: assessments.filter(a => a.overall_risk_level === 'high').length
        },
        avg_risk_score: assessments.reduce((sum, a) => sum + a.risk_score, 0) / assessments.length,
        avg_execution_time_ms: assessments.reduce((sum, a) => sum + a.execution_time_ms, 0) / assessments.length,
        success_rate: 1.0,
        most_common_risks: [
            { risk_type: 'market_volatility', frequency: 12 },
            { risk_type: 'liquidity_risk', frequency: 8 },
            { risk_type: 'credit_risk', frequency: 5 }
        ]
    };
    console.log('统计信息示例:', JSON.stringify(statisticsExample, null, 2));

    // 7. 演示数据导出
    console.log('\n📤 7. 风险数据导出演示');
    console.log('API调用示例:');
    console.log('GET /api/risk-data?export=true&format=json&ticker=AAPL');

    const exportExample = {
        data: assessments.slice(0, 2),
        format: 'json',
        exported_at: new Date().toISOString()
    };
    console.log('导出数据示例:', JSON.stringify(exportExample, null, 2));

    // 8. 前端组件使用示例
    console.log('\n🎨 8. 前端组件使用示例');
    console.log(`
// React Hook 使用示例
import { useRiskAssessmentHistory, useRiskComparison, useRiskMetricsTrend } from '@/hooks/useRiskDataStorage';

// 1. 获取风险历史记录
const { data: history, isLoading } = useRiskAssessmentHistory({
  ticker: 'AAPL',
  risk_level: 'medium',
  limit: 20
});

// 2. 风险对比分析
const { performComparison, comparisonResult } = useRiskDataManager();
await performComparison(['workflow-1', 'workflow-2']);

// 3. 风险指标趋势
const { data: trend } = useRiskMetricsTrend({
  ticker: 'AAPL',
  metric: 'volatility',
  days: 30
});

// 组件使用示例
<RiskHistoryViewer />
<RiskComparisonChart />
<RiskMetricsTrend defaultTicker="AAPL" />
  `);

    console.log('\n✅ 演示完成！');
    console.log('\n📋 功能总结:');
    console.log('   ✓ 风险评估结果自动存储');
    console.log('   ✓ 风险指标历史数据记录');
    console.log('   ✓ 多维度历史记录查询');
    console.log('   ✓ 多工作流风险对比分析');
    console.log('   ✓ 风险指标趋势分析');
    console.log('   ✓ 统计信息和数据导出');
    console.log('   ✓ 完整的前端组件支持');
    console.log('   ✓ 数据完整性和一致性保障');
}

// 运行演示
if (require.main === module) {
    demonstrateRiskDataStorage().catch(console.error);
}

module.exports = {
    generateMockRiskAssessment,
    generateRiskMetricsHistory,
    demonstrateRiskDataStorage
};