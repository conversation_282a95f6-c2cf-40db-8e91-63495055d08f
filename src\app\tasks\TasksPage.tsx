'use client';

import { AnalysisPreview } from '@/components/tasks/AnalysisPreview';
import { TaskCard } from '@/components/tasks/TaskCard';
import { TaskExecutionHistory } from '@/components/tasks/TaskExecutionHistory';
import { WorkflowInfo } from '@/components/tasks/WorkflowInfo';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useAnalysisStatus } from '@/hooks/useAnalysisStatus';
import { usePageTitle } from '@/hooks/usePageTitle';
import { taskApi } from '@/lib/api';
import useUserStore from '@/store/userStore';
import { Task } from '@/types/database';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';

// 消息数据类型定义
interface Message {
  id: number;
  message_id: string;
  conversation_id: string;
  task_id: string;
  message_type: 'human' | 'ai' | 'system' | 'tool';
  content: string;
  metadata: any;
  sequence_number: number;
  parent_message_id: string | null;
  created_at: string;
}

export default function TasksPage() {
  const router = useRouter();
  const { user } = useUserStore();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [executionHistory, setExecutionHistory] = useState<Message[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  // 设置页面标题
  usePageTitle('任务管理');

  // 获取任务状态
  const { status: analysisStatus, loading: statusLoading } = useAnalysisStatus({
    taskId: selectedTask?.task_id || undefined,
  });

  // 获取任务列表
  const fetchTasks = useCallback(async () => {
    if (!user?.id) {
      setError('用户未登录');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await taskApi.getTasks();
      setTasks(response.data || []);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      setError('获取任务列表失败');
      toast.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // 获取执行历史
  const fetchExecutionHistory = useCallback(async (taskId: string) => {
    try {
      setLoadingHistory(true);
      const response = await fetch(`/api/database/messages?task_id=${taskId}`);
      if (!response.ok) {
        throw new Error('获取执行历史失败');
      }
      const data = await response.json();
      setExecutionHistory(data.messages || []);
    } catch (error) {
      console.error('获取执行历史失败:', error);
      toast.error('获取执行历史失败');
    } finally {
      setLoadingHistory(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  // 处理任务选择
  const handleTaskSelect = useCallback(
    (task: Task) => {
      setSelectedTask(task);
      setShowPreview(true);
      if (task.task_id) {
        fetchExecutionHistory(task.task_id);
      }
    },
    [fetchExecutionHistory]
  );

  // 处理任务执行
  const handleTaskExecute = useCallback(
    async (task: Task) => {
      if (!task.task_id) {
        toast.error('任务ID无效');
        return;
      }

      try {
        toast.loading('正在启动任务...', { id: 'task-execute' });

        const response = await fetch('/api/langgraph/start-analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            task_id: task.task_id,
            stock_symbol: task.ticker,
            analysis_type: 'comprehensive',
            time_period: task.analysis_period || '1m',
          }),
        });

        if (!response.ok) {
          throw new Error('启动任务失败');
        }

        const result = await response.json();
        toast.success('任务启动成功', { id: 'task-execute' });

        // 跳转到分析页面
        router.push(`/analysis/${task.task_id}`);
      } catch (error) {
        console.error('执行任务失败:', error);
        toast.error('执行任务失败', { id: 'task-execute' });
      }
    },
    [router]
  );

  // 处理任务删除
  const handleTaskDelete = useCallback(
    async (taskId: string) => {
      if (!confirm('确定要删除这个任务吗？')) {
        return;
      }

      try {
        // TODO: Implement deleteTask API method
        console.log('Delete task:', taskId);
        toast.success('任务删除成功');
        fetchTasks(); // 重新获取任务列表
        if (selectedTask?.task_id === taskId) {
          setSelectedTask(null);
          setShowPreview(false);
        }
      } catch (error) {
        console.error('删除任务失败:', error);
        toast.error('删除任务失败');
      }
    },
    [fetchTasks, selectedTask?.task_id]
  );

  // 处理创建新任务
  const handleCreateTask = useCallback(() => {
    router.push('/create-task');
  }, [router]);

  // 关闭预览
  const handleClosePreview = useCallback(() => {
    setShowPreview(false);
    setSelectedTask(null);
    setExecutionHistory([]);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">{error}</div>
          <button
            onClick={fetchTasks}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和操作 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">任务管理</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              管理和监控您的分析任务，查看执行状态和历史记录
            </p>
          </div>
          <button
            onClick={handleCreateTask}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
          >
            创建新任务
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 任务列表 */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  任务列表 ({tasks.length})
                </h2>
              </div>
              <div className="p-6">
                {tasks.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 text-lg mb-4">暂无任务</div>
                    <button
                      onClick={handleCreateTask}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                    >
                      创建第一个任务
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {tasks.map((task) => (
                      <TaskCard
                        key={task.task_id}
                        task={task}
                        onViewDetails={() => handleTaskSelect(task)}
                        onStartAnalysis={() => handleTaskExecute(task)}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 工作流信息 */}
            {analysisStatus && <WorkflowInfo status={analysisStatus} />}

            {/* 任务预览 */}
            {showPreview && selectedTask && analysisStatus && (
              <AnalysisPreview status={analysisStatus} />
            )}

            {/* 执行历史 */}
            {selectedTask && <TaskExecutionHistory taskId={selectedTask.task_id} />}
          </div>
        </div>
      </div>
    </div>
  );
}
