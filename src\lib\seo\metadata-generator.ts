/**
 * SEO 元数据生成器
 * 负责生成页面元数据、结构化数据和 Open Graph 信息
 */

import { Metadata } from 'next';
import {
  SEOConfig,
  PageSEOProps,
  SupportedLocale,
  StructuredDataConfig,
  OrganizationSchema,
  WebsiteSchema,
  SoftwareApplicationSchema,
} from '@/types/seo';
import {
  SEO_CONSTANTS,
  SEO_CONTENT,
  DEFAULT_SEO_CONFIG,
  STRUCTURED_DATA_TEMPLATES,
} from './config';
import { SEOUtils } from './utils';
import {
  I18N_SEO_CONTENT,
  I18nContentSwitcher,
  HreflangGenerator,
  HREFLANG_CONFIG,
} from './i18n-seo';
import { StructuredDataGenerator, generatePageStructuredData } from './structured-data-generator';

/**
 * SEO 元数据生成器类
 */
export class MetadataGenerator {
  private locale: SupportedLocale;
  private baseConfig: SEOConfig;

  constructor(locale: SupportedLocale = 'zh') {
    this.locale = locale;
    this.baseConfig = this.getBaseConfig();
  }

  /**
   * 生成页面元数据
   */
  generatePageMetadata(props: PageSEOProps): Metadata {
    try {
      const { page, dynamicData, locale = this.locale } = props;
      const pageConfig = this.getPageConfig(page, locale);

      // 生成动态标题和描述
      const title = SEOUtils.generateDynamicTitle(pageConfig.title, dynamicData, locale);

      const description = SEOUtils.generateDynamicDescription(
        pageConfig.description,
        dynamicData,
        locale
      );

      // 生成规范化 URL
      const canonical = this.generateCanonicalUrl(page, dynamicData, locale);

      // 生成 Open Graph 数据
      const openGraph = this.generateOpenGraphData(title, description, canonical, locale);

      // 生成 Twitter Card 数据
      const twitter = this.generateTwitterCardData(title, description, canonical);

      // 生成其他语言链接
      const alternates = this.generateAlternateLinks(page, dynamicData);

      const metadata: Metadata = {
        title,
        description,
        keywords: pageConfig.keywords.join(', '),
        authors: [{ name: SEO_CONSTANTS.SITE_NAME }],
        creator: SEO_CONSTANTS.SITE_NAME,
        publisher: SEO_CONSTANTS.SITE_NAME,
        formatDetection: {
          email: false,
          address: false,
          telephone: false,
        },
        metadataBase: new URL(SEO_CONSTANTS.SITE_URL),
        alternates,
        openGraph,
        twitter,
        robots: {
          index: true,
          follow: true,
          googleBot: {
            index: true,
            follow: true,
            'max-video-preview': -1,
            'max-image-preview': 'large',
            'max-snippet': -1,
          },
        },
        verification: {
          google: SEO_CONSTANTS.GOOGLE_VERIFICATION,
          other: {
            baidu: SEO_CONSTANTS.BAIDU_VERIFICATION,
          },
        },
      };

      return metadata;
    } catch (error) {
      console.error('Error generating page metadata:', error);
      return this.getFallbackMetadata();
    }
  }

  /**
   * 生成结构化数据
   */
  generateStructuredData(page: string, dynamicData?: any): StructuredDataConfig {
    try {
      // 使用增强的结构化数据生成器
      const enhancedData = generatePageStructuredData(
        page as any, // PageType
        this.locale,
        dynamicData
      );

      // 转换为 StructuredDataConfig 格式
      const structuredData: StructuredDataConfig = {
        organization: enhancedData.organization,
        website: enhancedData.website,
        softwareApplication: enhancedData.tradingPlatform || enhancedData.softwareApplication,
        breadcrumb: enhancedData.breadcrumb,
      };

      return structuredData;
    } catch (error) {
      console.error('Error generating structured data:', error);

      // 回退到基础实现
      const structuredData: StructuredDataConfig = {};

      // 组织信息（所有页面）
      structuredData.organization = this.generateOrganizationSchema();

      // 网站信息（首页）
      if (page === 'home') {
        structuredData.website = this.generateWebsiteSchema();
        structuredData.softwareApplication = this.generateSoftwareApplicationSchema();
      }

      // 面包屑导航（非首页）
      if (page !== 'home') {
        structuredData.breadcrumb = this.generateBreadcrumbSchema(page, dynamicData);
      }

      return structuredData;
    }
  }

  /**
   * 生成 JSON-LD 脚本内容
   */
  generateJsonLdScripts(page: string, dynamicData?: any): string[] {
    const structuredData = this.generateStructuredData(page, dynamicData);
    const scripts: string[] = [];

    Object.values(structuredData).forEach((data) => {
      if (data && SEOUtils.validateStructuredData(data)) {
        scripts.push(SEOUtils.generateJsonLdScript(data));
      }
    });

    return scripts;
  }

  /**
   * 生成增强的 JSON-LD 脚本内容（包含金融服务相关数据）
   */
  generateEnhancedJsonLdScripts(page: string, dynamicData?: any): string[] {
    try {
      const enhancedData = generatePageStructuredData(
        page as any, // PageType
        this.locale,
        dynamicData
      );

      const scripts: string[] = [];

      // 生成所有结构化数据的脚本
      Object.entries(enhancedData).forEach(([key, data]) => {
        if (data && SEOUtils.validateStructuredData(data)) {
          scripts.push(SEOUtils.generateJsonLdScript(data));
        }
      });

      return scripts;
    } catch (error) {
      console.error('Error generating enhanced JSON-LD scripts:', error);
      // 回退到基础实现
      return this.generateJsonLdScripts(page, dynamicData);
    }
  }

  /**
   * 获取基础配置
   */
  private getBaseConfig(): SEOConfig {
    return { ...DEFAULT_SEO_CONFIG };
  }

  /**
   * 获取页面配置
   */
  private getPageConfig(page: string, locale: SupportedLocale) {
    // 使用新的 i18n 内容系统
    const pageContent = I18nContentSwitcher.getPageContent(page as any, locale);

    if (!pageContent) {
      console.warn(`No SEO content found for page: ${page}, locale: ${locale}`);
      return I18nContentSwitcher.getPageContent('home', locale);
    }

    return pageContent;
  }

  /**
   * 生成规范化 URL
   */
  private generateCanonicalUrl(
    page: string,
    dynamicData?: any,
    locale: SupportedLocale = 'zh'
  ): string {
    let path = `/${page === 'home' ? '' : page}`;

    // 处理动态路径
    if (dynamicData?.analysisId) {
      path = `/analysis/${dynamicData.analysisId}`;
    }

    return SEOUtils.generateCanonicalUrl(path, locale);
  }

  /**
   * 生成 Open Graph 数据
   */
  private generateOpenGraphData(
    title: string,
    description: string,
    url: string,
    locale: SupportedLocale
  ) {
    return {
      title,
      description,
      url,
      siteName: SEO_CONSTANTS.SITE_NAME,
      images: [
        {
          url: `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`,
          width: SEO_CONSTANTS.OG_IMAGE_WIDTH,
          height: SEO_CONSTANTS.OG_IMAGE_HEIGHT,
          alt: title,
        },
      ],
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    };
  }

  /**
   * 生成 Twitter Card 数据
   */
  private generateTwitterCardData(title: string, description: string, url: string) {
    return {
      card: 'summary_large_image' as const,
      title,
      description,
      site: SEO_CONSTANTS.TWITTER_HANDLE,
      images: [`${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`],
    };
  }

  /**
   * 生成多语言链接
   */
  private generateAlternateLinks(page: string, dynamicData?: any) {
    const basePath = page === 'home' ? '/' : `/${page}`;
    let cleanPath = basePath;

    // 处理动态路径
    if (dynamicData?.analysisId) {
      cleanPath = `/analysis/${dynamicData.analysisId}`;
    }

    // 使用新的 hreflang 生成器
    return HreflangGenerator.generateAlternates(cleanPath, this.locale);
  }

  /**
   * 生成组织信息结构化数据
   */
  private generateOrganizationSchema(): OrganizationSchema {
    return {
      ...STRUCTURED_DATA_TEMPLATES.organization,
      contactPoint: {
        ...STRUCTURED_DATA_TEMPLATES.organization.contactPoint,
        availableLanguage: [
          ...STRUCTURED_DATA_TEMPLATES.organization.contactPoint.availableLanguage,
        ],
      },
      sameAs: [...STRUCTURED_DATA_TEMPLATES.organization.sameAs],
    };
  }

  /**
   * 生成网站信息结构化数据
   */
  private generateWebsiteSchema(): WebsiteSchema {
    return { ...STRUCTURED_DATA_TEMPLATES.website };
  }

  /**
   * 生成软件应用结构化数据
   */
  private generateSoftwareApplicationSchema(): SoftwareApplicationSchema {
    return { ...STRUCTURED_DATA_TEMPLATES.softwareApplication };
  }

  /**
   * 生成面包屑导航结构化数据
   */
  private generateBreadcrumbSchema(page: string, dynamicData?: any) {
    let path = `/${page}`;

    // 处理动态路径
    if (dynamicData?.analysisId) {
      path = `/analysis/${dynamicData.analysisId}`;
    }

    return SEOUtils.generateBreadcrumbSchema(path, this.locale);
  }

  /**
   * 获取后备元数据
   */
  private getFallbackMetadata(): Metadata {
    return {
      title: DEFAULT_SEO_CONFIG.title,
      description: DEFAULT_SEO_CONFIG.description,
      keywords: DEFAULT_SEO_CONFIG.keywords.join(', '),
    };
  }

  /**
   * 验证生成的元数据
   */
  validateMetadata(metadata: Metadata): boolean {
    try {
      // 验证必需字段
      if (!metadata.title || !metadata.description) {
        return false;
      }

      // 验证标题长度
      if (typeof metadata.title === 'string' && !SEOUtils.validateTitle(metadata.title)) {
        return false;
      }

      // 验证描述长度
      if (
        typeof metadata.description === 'string' &&
        !SEOUtils.validateDescription(metadata.description)
      ) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取 SEO 分析报告
   */
  getSEOAnalysis(props: PageSEOProps): {
    score: number;
    issues: string[];
    recommendations: string[];
  } {
    const metadata = this.generatePageMetadata(props);
    const issues: string[] = [];
    const recommendations: string[] = [];

    // 检查标题
    if (typeof metadata.title === 'string') {
      if (metadata.title.length > SEO_CONSTANTS.TITLE_MAX_LENGTH) {
        issues.push('标题过长');
        recommendations.push('缩短标题至60个字符以内');
      }
      if (metadata.title.length < 30) {
        issues.push('标题过短');
        recommendations.push('扩展标题至30-60个字符');
      }
    }

    // 检查描述
    if (typeof metadata.description === 'string') {
      if (metadata.description.length > SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH) {
        issues.push('描述过长');
        recommendations.push('缩短描述至160个字符以内');
      }
      if (metadata.description.length < 120) {
        issues.push('描述过短');
        recommendations.push('扩展描述至120-160个字符');
      }
    }

    // 计算分数
    const config: SEOConfig = {
      title: (metadata.title as string) || '',
      description: (metadata.description as string) || '',
      keywords: typeof metadata.keywords === 'string' ? metadata.keywords.split(', ') : [],
      openGraph: DEFAULT_SEO_CONFIG.openGraph,
      twitter: DEFAULT_SEO_CONFIG.twitter,
    };

    const score = SEOUtils.calculateSEOScore(config);

    return { score, issues, recommendations };
  }
}
