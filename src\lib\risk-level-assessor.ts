/**
 * 风险等级评定算法
 * Risk Level Assessment Algorithm
 */

import { RiskAssessment, RiskLevel, RiskLevelCriteria, RiskType } from '@/types/risk-report';

// 风险等级评定标准配置
export const RISK_LEVEL_CRITERIA: Record<RiskType, RiskLevelCriteria> = {
  [RiskType.MARKET]: {
    type: RiskType.MARKET,
    thresholds: {
      low: 30,
      medium: 60,
      high: 80,
    },
    weights: {
      volatility: 0.3,
      beta: 0.2,
      var: 0.25,
      maxDrawdown: 0.25,
    },
    description: '市场风险基于波动率、Beta系数、VaR和最大回撤评估',
  },

  [RiskType.LIQUIDITY]: {
    type: RiskType.LIQUIDITY,
    thresholds: {
      low: 25,
      medium: 50,
      high: 75,
    },
    weights: {
      volume: 0.4,
      spread: 0.3,
      amihudRatio: 0.3,
    },
    description: '流动性风险基于交易量、价差和Amihud比率评估',
  },

  [RiskType.CREDIT]: {
    type: RiskType.CREDIT,
    thresholds: {
      low: 35,
      medium: 65,
      high: 85,
    },
    weights: {
      debtRatio: 0.3,
      currentRatio: 0.25,
      interestCoverage: 0.25,
      creditRating: 0.2,
    },
    description: '信用风险基于债务比率、流动比率、利息覆盖率和信用评级评估',
  },

  [RiskType.OPERATIONAL]: {
    type: RiskType.OPERATIONAL,
    thresholds: {
      low: 40,
      medium: 70,
      high: 90,
    },
    weights: {
      governance: 0.4,
      management: 0.3,
      compliance: 0.3,
    },
    description: '操作风险基于公司治理、管理层质量和合规性评估',
  },

  [RiskType.SYSTEMATIC]: {
    type: RiskType.SYSTEMATIC,
    thresholds: {
      low: 30,
      medium: 60,
      high: 80,
    },
    weights: {
      marketCorrelation: 0.4,
      sectorExposure: 0.3,
      macroSensitivity: 0.3,
    },
    description: '系统性风险基于市场相关性、行业暴露和宏观敏感性评估',
  },
};

/**
 * 风险等级评定器类
 */
export class RiskLevelAssessor {
  /**
   * 计算单项风险评分
   */
  calculateRiskScore(riskType: RiskType, metrics: Record<string, number>): number {
    const criteria = RISK_LEVEL_CRITERIA[riskType];
    let weightedScore = 0;
    let totalWeight = 0;

    // 根据权重计算加权评分
    for (const [metricName, weight] of Object.entries(criteria.weights)) {
      if (metrics[metricName] !== undefined) {
        const normalizedValue = this.normalizeMetricValue(
          metricName,
          metrics[metricName],
          riskType
        );
        weightedScore += normalizedValue * weight;
        totalWeight += weight;
      }
    }

    // 如果没有足够的指标数据，返回中等风险评分
    if (totalWeight === 0) {
      return 50;
    }

    // 归一化到0-100范围
    return Math.min(100, Math.max(0, (weightedScore / totalWeight) * 100));
  }

  /**
   * 根据评分确定风险等级
   */
  determineRiskLevel(score: number, riskType: RiskType): RiskLevel {
    const criteria = RISK_LEVEL_CRITERIA[riskType];

    if (score <= criteria.thresholds.low) {
      return RiskLevel.LOW;
    } else if (score <= criteria.thresholds.medium) {
      return RiskLevel.MEDIUM;
    } else if (score <= criteria.thresholds.high) {
      return RiskLevel.HIGH;
    } else {
      return RiskLevel.CRITICAL;
    }
  }

  /**
   * 生成风险评估结果
   */
  assessRisk(
    riskType: RiskType,
    metrics: Record<string, number>,
    additionalFactors: string[] = []
  ): RiskAssessment {
    const score = this.calculateRiskScore(riskType, metrics);
    const level = this.determineRiskLevel(score, riskType);

    return {
      type: riskType,
      level,
      score,
      description: this.generateRiskDescription(riskType, level, score),
      metrics,
      factors: this.identifyRiskFactors(riskType, metrics, additionalFactors),
      recommendations: this.generateRecommendations(riskType, level, metrics),
    };
  }

  /**
   * 计算综合风险评分
   */
  calculateOverallRiskScore(assessments: RiskAssessment[]): {
    score: number;
    level: RiskLevel;
    confidence: number;
  } {
    if (assessments.length === 0) {
      return { score: 50, level: RiskLevel.MEDIUM, confidence: 0 };
    }

    // 风险类型权重
    const riskTypeWeights: Record<RiskType, number> = {
      [RiskType.MARKET]: 0.3,
      [RiskType.LIQUIDITY]: 0.2,
      [RiskType.CREDIT]: 0.25,
      [RiskType.OPERATIONAL]: 0.15,
      [RiskType.SYSTEMATIC]: 0.1,
    };

    let weightedScore = 0;
    let totalWeight = 0;
    let dataQualitySum = 0;

    for (const assessment of assessments) {
      const weight = riskTypeWeights[assessment.type] || 0.1;
      weightedScore += assessment.score * weight;
      totalWeight += weight;

      // 基于指标数量评估数据质量
      const dataQuality = Math.min(100, Object.keys(assessment.metrics).length * 20);
      dataQualitySum += dataQuality;
    }

    const overallScore = totalWeight > 0 ? weightedScore / totalWeight : 50;
    const confidence = Math.min(100, dataQualitySum / assessments.length);

    // 确定综合风险等级（使用更严格的标准）
    let overallLevel: RiskLevel;
    if (overallScore <= 25) {
      overallLevel = RiskLevel.LOW;
    } else if (overallScore <= 50) {
      overallLevel = RiskLevel.MEDIUM;
    } else if (overallScore <= 75) {
      overallLevel = RiskLevel.HIGH;
    } else {
      overallLevel = RiskLevel.CRITICAL;
    }

    return {
      score: Math.round(overallScore),
      level: overallLevel,
      confidence: Math.round(confidence),
    };
  }

  /**
   * 标准化指标值到0-100范围
   */
  private normalizeMetricValue(metricName: string, value: number, riskType: RiskType): number {
    // 根据不同指标类型进行标准化
    switch (metricName) {
      case 'volatility':
        // 波动率：0-100%，越高风险越大
        return Math.min(100, value * 100);

      case 'beta':
        // Beta系数：通常0-3，1为市场平均
        return Math.min(100, Math.abs(value - 1) * 50);

      case 'var':
        // VaR：通常为负值，绝对值越大风险越高
        return Math.min(100, Math.abs(value) * 10);

      case 'maxDrawdown':
        // 最大回撤：通常为负值，绝对值越大风险越高
        return Math.min(100, Math.abs(value) * 100);

      case 'volume':
        // 交易量：越低流动性风险越高
        return Math.max(0, 100 - Math.min(100, (value / 1000000) * 10));

      case 'spread':
        // 价差：越大流动性风险越高
        return Math.min(100, value * 1000);

      case 'amihudRatio':
        // Amihud比率：越大流动性风险越高
        return Math.min(100, value * 1000000);

      case 'debtRatio':
        // 债务比率：越高信用风险越大
        return Math.min(100, value * 100);

      case 'currentRatio':
        // 流动比率：越低信用风险越高，理想值为2
        return Math.max(0, 100 - Math.min(100, value * 50));

      case 'interestCoverage':
        // 利息覆盖率：越低信用风险越高
        return Math.max(0, 100 - Math.min(100, value * 10));

      default:
        // 默认情况：假设值越大风险越高
        return Math.min(100, Math.max(0, value));
    }
  }

  /**
   * 生成风险描述
   */
  private generateRiskDescription(riskType: RiskType, level: RiskLevel, score: number): string {
    const riskTypeNames = {
      [RiskType.MARKET]: '市场风险',
      [RiskType.LIQUIDITY]: '流动性风险',
      [RiskType.CREDIT]: '信用风险',
      [RiskType.OPERATIONAL]: '操作风险',
      [RiskType.SYSTEMATIC]: '系统性风险',
    };

    const levelDescriptions = {
      [RiskLevel.LOW]: '较低',
      [RiskLevel.MEDIUM]: '中等',
      [RiskLevel.HIGH]: '较高',
      [RiskLevel.CRITICAL]: '极高',
    };

    return `${riskTypeNames[riskType]}等级为${
      levelDescriptions[level]
    }（评分：${score}），需要${this.getLevelAction(level)}`;
  }

  /**
   * 识别风险因素
   */
  private identifyRiskFactors(
    riskType: RiskType,
    metrics: Record<string, number>,
    additionalFactors: string[]
  ): string[] {
    const factors: string[] = [...additionalFactors];

    // 根据指标值识别主要风险因素
    switch (riskType) {
      case RiskType.MARKET:
        if (metrics.volatility > 0.3) factors.push('高波动率');
        if (metrics.beta > 1.5) factors.push('高Beta系数');
        if (Math.abs(metrics.var) > 0.1) factors.push('高VaR值');
        if (Math.abs(metrics.maxDrawdown) > 0.2) factors.push('大幅回撤');
        break;

      case RiskType.LIQUIDITY:
        if (metrics.volume < 1000000) factors.push('交易量不足');
        if (metrics.spread > 0.01) factors.push('买卖价差过大');
        if (metrics.amihudRatio > 0.001) factors.push('Amihud比率偏高');
        break;

      case RiskType.CREDIT:
        if (metrics.debtRatio > 0.6) factors.push('债务比率过高');
        if (metrics.currentRatio < 1.5) factors.push('流动比率偏低');
        if (metrics.interestCoverage < 2.5) factors.push('利息覆盖率不足');
        break;
    }

    return factors;
  }

  /**
   * 生成风险控制建议
   */
  private generateRecommendations(
    riskType: RiskType,
    level: RiskLevel,
    metrics: Record<string, number>
  ): string[] {
    const recommendations: string[] = [];

    switch (level) {
      case RiskLevel.CRITICAL:
        recommendations.push('建议暂停投资或大幅减仓');
        recommendations.push('立即实施风险控制措施');
        break;

      case RiskLevel.HIGH:
        recommendations.push('建议降低仓位至50%以下');
        recommendations.push('设置严格的止损位');
        break;

      case RiskLevel.MEDIUM:
        recommendations.push('适度控制仓位');
        recommendations.push('密切监控风险指标变化');
        break;

      case RiskLevel.LOW:
        recommendations.push('可适当增加仓位');
        recommendations.push('保持正常风险监控');
        break;
    }

    // 根据风险类型添加特定建议
    switch (riskType) {
      case RiskType.MARKET:
        if (level >= RiskLevel.HIGH) {
          recommendations.push('考虑对冲策略');
          recommendations.push('分散投资降低集中度风险');
        }
        break;

      case RiskType.LIQUIDITY:
        if (level >= RiskLevel.HIGH) {
          recommendations.push('避免大额交易');
          recommendations.push('选择流动性更好的替代标的');
        }
        break;

      case RiskType.CREDIT:
        if (level >= RiskLevel.HIGH) {
          recommendations.push('关注财务状况变化');
          recommendations.push('考虑信用保护措施');
        }
        break;
    }

    return recommendations;
  }

  /**
   * 获取风险等级对应的行动建议
   */
  private getLevelAction(level: RiskLevel): string {
    switch (level) {
      case RiskLevel.LOW:
        return '正常监控';
      case RiskLevel.MEDIUM:
        return '适度关注';
      case RiskLevel.HIGH:
        return '重点关注';
      case RiskLevel.CRITICAL:
        return '立即行动';
      default:
        return '持续监控';
    }
  }
}

// 导出单例实例
export const riskLevelAssessor = new RiskLevelAssessor();
