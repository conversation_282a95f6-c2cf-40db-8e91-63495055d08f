/**
 * 风险场景分析器测试
 */

import { PriceData } from '../risk-metrics-calculator';
import { RiskScenarioAnalyzer, riskScenarioAnalyzer } from '../risk-scenario-analyzer';

// 生成测试用的价格数据
function generateTestPriceData(days: number = 252, initialPrice: number = 100): PriceData[] {
  const data: PriceData[] = [];
  let price = initialPrice;

  for (let i = 0; i < days; i++) {
    const dailyReturn = (Math.random() - 0.5) * 0.04; // ±2% 日波动
    const newPrice = price * (1 + dailyReturn);

    data.push({
      date: new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).toISOString(),
      open: price,
      high: newPrice * 1.01,
      low: newPrice * 0.99,
      close: newPrice,
      volume: Math.floor(Math.random() * 1000000) + 100000,
    });

    price = newPrice;
  }

  return data;
}

describe('RiskScenarioAnalyzer', () => {
  let analyzer: RiskScenarioAnalyzer;
  let testPriceData: PriceData[];
  let testMarketData: PriceData[];

  beforeEach(() => {
    analyzer = RiskScenarioAnalyzer.getInstance();
    testPriceData = generateTestPriceData(252, 100);
    testMarketData = generateTestPriceData(252, 3000);
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = RiskScenarioAnalyzer.getInstance();
      const instance2 = RiskScenarioAnalyzer.getInstance();
      expect(instance1).toBe(instance2);
      expect(instance1).toBe(riskScenarioAnalyzer);
    });
  });

  describe('需求 5.1: 市场下跌场景模拟', () => {
    it('应该模拟-10%、-20%、-30%的市场下跌场景', () => {
      const scenarios = analyzer.simulateMarketDeclineScenarios(testPriceData, testMarketData);

      expect(scenarios).toHaveLength(3);

      // 验证场景参数
      expect(scenarios[0].scenario_name).toBe('市场下跌10%');
      expect(scenarios[1].scenario_name).toBe('市场下跌20%');
      expect(scenarios[2].scenario_name).toBe('市场下跌30%');

      // 验证预期损失
      expect(scenarios[0].expected_loss).toBeCloseTo(0.1, 2);
      expect(scenarios[1].expected_loss).toBeCloseTo(0.2, 2);
      expect(scenarios[2].expected_loss).toBeCloseTo(0.3, 2);

      // 验证场景类型
      scenarios.forEach((scenario) => {
        expect(scenario.scenario_type).toBe('market_decline');
        expect(scenario.probability).toBeGreaterThan(0);
        expect(scenario.probability).toBeLessThan(1);
      });
    });

    it('应该计算压力下的相关性变化', () => {
      const scenarios = analyzer.simulateMarketDeclineScenarios(testPriceData, testMarketData);

      scenarios.forEach((scenario) => {
        expect(scenario.correlation_changes.stressed_correlation).toBeGreaterThan(
          scenario.correlation_changes.original_correlation
        );
        expect(scenario.correlation_changes.correlation_change).toBeGreaterThan(0);
      });
    });

    it('应该计算风险指标', () => {
      const scenarios = analyzer.simulateMarketDeclineScenarios(testPriceData);

      scenarios.forEach((scenario) => {
        expect(scenario.risk_metrics.var_95).toBeGreaterThan(0);
        expect(scenario.risk_metrics.var_99).toBeGreaterThan(scenario.risk_metrics.var_95);
        expect(scenario.risk_metrics.expected_shortfall).toBeGreaterThan(0);
        expect(scenario.risk_metrics.max_drawdown).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('需求 5.2: 利率变动影响分析', () => {
    it('应该分析不同利率变动的影响', () => {
      const rateChanges = [0.005, 0.01, 0.02];
      const scenarios = analyzer.analyzeInterestRateImpact(testPriceData, rateChanges);

      expect(scenarios).toHaveLength(3);

      scenarios.forEach((scenario, index) => {
        expect(scenario.scenario_type).toBe('interest_rate');
        expect(scenario.scenario_name).toContain('利率上升');
        expect(scenario.scenario_parameters.rate_change).toBe(rateChanges[index]);
        expect(scenario.expected_loss).toBeGreaterThan(0);
      });
    });

    it('应该计算利率敏感性', () => {
      const scenarios = analyzer.analyzeInterestRateImpact(testPriceData);

      scenarios.forEach((scenario) => {
        expect(scenario.scenario_parameters.sensitivity).toBe(-5);
        expect(scenario.scenario_parameters.price_impact).toBeLessThan(0);
      });
    });
  });

  describe('需求 5.3: 行业冲击影响评估', () => {
    it('应该评估行业特定风险事件', () => {
      const customShocks = [
        { name: '监管政策变化', impact: -0.15, probability: 0.1 },
        { name: '技术革新冲击', impact: -0.25, probability: 0.05 },
      ];

      const scenarios = analyzer.analyzeSectorShockImpact(testPriceData, customShocks);

      expect(scenarios).toHaveLength(2);

      scenarios.forEach((scenario, index) => {
        expect(scenario.scenario_type).toBe('sector_shock');
        expect(scenario.scenario_name).toBe(customShocks[index].name);
        expect(scenario.expected_loss).toBeCloseTo(Math.abs(customShocks[index].impact), 2);
        expect(scenario.probability).toBe(customShocks[index].probability);
      });
    });

    it('应该使用默认行业冲击场景', () => {
      const scenarios = analyzer.analyzeSectorShockImpact(testPriceData);

      expect(scenarios.length).toBeGreaterThan(0);

      scenarios.forEach((scenario) => {
        expect(scenario.scenario_type).toBe('sector_shock');
        expect(scenario.expected_loss).toBeGreaterThan(0);
        expect(scenario.probability).toBeGreaterThan(0);
      });
    });
  });

  describe('需求 5.4: 蒙特卡洛模拟', () => {
    it('应该运行蒙特卡洛模拟并生成价格分布', () => {
      const numSimulations = 1000;
      const timeHorizon = 252;

      const result = analyzer.runMonteCarloSimulation(testPriceData, numSimulations, timeHorizon);

      expect(result.simulation_id).toContain('mc_');
      expect(result.parameters.num_simulations).toBe(numSimulations);
      expect(result.parameters.time_horizon_days).toBe(timeHorizon);
      expect(result.parameters.initial_price).toBe(testPriceData[testPriceData.length - 1].close);

      // 验证结果
      expect(result.results.final_prices).toHaveLength(numSimulations);
      expect(result.results.returns).toHaveLength(numSimulations);
      expect(result.results.price_paths.length).toBeLessThanOrEqual(100); // 只保存前100条路径

      // 验证百分位数
      const percentiles = result.results.percentiles;
      expect(percentiles.p5).toBeLessThan(percentiles.p25);
      expect(percentiles.p25).toBeLessThan(percentiles.p50);
      expect(percentiles.p50).toBeLessThan(percentiles.p75);
      expect(percentiles.p75).toBeLessThan(percentiles.p95);

      // 验证风险指标
      expect(result.results.risk_metrics.var_95).toBeGreaterThan(0);
      expect(result.results.risk_metrics.var_99).toBeGreaterThan(
        result.results.risk_metrics.var_95
      );
      expect(result.results.risk_metrics.probability_of_loss).toBeGreaterThanOrEqual(0);
      expect(result.results.risk_metrics.probability_of_loss).toBeLessThanOrEqual(1);
    });

    it('应该生成合理的价格路径', () => {
      const result = analyzer.runMonteCarloSimulation(testPriceData, 100, 10);

      result.results.price_paths.forEach((path) => {
        expect(path).toHaveLength(11); // 初始价格 + 10天
        expect(path[0]).toBe(testPriceData[testPriceData.length - 1].close);

        // 验证价格路径的合理性
        path.forEach((price) => {
          expect(price).toBeGreaterThan(0);
        });
      });
    });
  });

  describe('需求 5.5: 情景预期损失计算', () => {
    it('应该计算不同情景下的预期损失', () => {
      const scenarios = analyzer.simulateMarketDeclineScenarios(testPriceData);
      const expectedLoss = analyzer.calculateScenarioExpectedLoss(scenarios);

      expect(expectedLoss.weighted_expected_loss).toBeGreaterThan(0);
      expect(expectedLoss.worst_case_loss).toBeGreaterThanOrEqual(
        expectedLoss.weighted_expected_loss
      );
      expect(expectedLoss.best_case_loss).toBeLessThanOrEqual(expectedLoss.worst_case_loss);

      expect(expectedLoss.scenario_contributions).toHaveLength(scenarios.length);

      expectedLoss.scenario_contributions.forEach((contribution) => {
        expect(contribution.contribution).toBeGreaterThanOrEqual(0);
        expect(contribution.weight).toBeGreaterThan(0);
        expect(contribution.weight).toBeLessThanOrEqual(1);
      });
    });

    it('应该正确计算加权预期损失', () => {
      const mockScenarios = [
        {
          scenario_name: '场景1',
          expected_loss: 0.1,
          probability: 0.3,
        },
        {
          scenario_name: '场景2',
          expected_loss: 0.2,
          probability: 0.2,
        },
      ] as any;

      const result = analyzer.calculateScenarioExpectedLoss(mockScenarios);

      // 加权预期损失 = 0.1 * 0.3 + 0.2 * 0.2 = 0.07
      expect(result.weighted_expected_loss).toBeCloseTo(0.07, 3);
      expect(result.worst_case_loss).toBe(0.2);
      expect(result.best_case_loss).toBe(0.1);
    });
  });

  describe('需求 5.6: 压力下相关性变化分析', () => {
    it('应该分析相关性在压力下的变化', () => {
      const assetReturns = [
        [0.01, -0.02, 0.015, -0.01, 0.005],
        [-0.005, 0.01, -0.008, 0.012, -0.003],
        [0.008, -0.015, 0.02, -0.005, 0.01],
      ];

      const scenarios = analyzer.simulateMarketDeclineScenarios(testPriceData);
      const correlationAnalysis = analyzer.analyzeStressCorrelationChanges(assetReturns, scenarios);

      expect(correlationAnalysis.normal_correlation_matrix).toHaveLength(3);
      expect(correlationAnalysis.stressed_correlation_matrix).toHaveLength(3);
      expect(correlationAnalysis.correlation_changes).toHaveLength(3);

      // 验证矩阵维度
      correlationAnalysis.normal_correlation_matrix.forEach((row) => {
        expect(row).toHaveLength(3);
      });

      // 验证对角线元素为1
      for (let i = 0; i < 3; i++) {
        expect(correlationAnalysis.normal_correlation_matrix[i][i]).toBe(1);
        expect(correlationAnalysis.stressed_correlation_matrix[i][i]).toBe(1);
        expect(correlationAnalysis.correlation_changes[i][i]).toBe(0);
      }

      // 验证相关性崩溃风险评分
      expect(correlationAnalysis.correlation_breakdown_risk).toBeGreaterThanOrEqual(0);
      expect(correlationAnalysis.correlation_breakdown_risk).toBeLessThanOrEqual(100);
    });
  });

  describe('综合压力测试', () => {
    it('应该运行综合压力测试', () => {
      const stressTest = analyzer.runComprehensiveStressTest(testPriceData, testMarketData);

      expect(stressTest.test_name).toContain('综合压力测试');
      expect(stressTest.scenarios.length).toBeGreaterThan(0);

      // 验证包含不同类型的场景
      const scenarioTypes = stressTest.scenarios.map((s) => s.scenario_type);
      expect(scenarioTypes).toContain('market_decline');
      expect(scenarioTypes).toContain('interest_rate');
      expect(scenarioTypes).toContain('sector_shock');
      expect(scenarioTypes).toContain('monte_carlo');

      // 验证汇总指标
      expect(stressTest.summary.worst_case_loss).toBeGreaterThan(0);
      expect(stressTest.summary.average_loss).toBeGreaterThan(0);
      expect(stressTest.summary.probability_of_loss).toBeGreaterThan(0);
      expect(stressTest.summary.stress_test_score).toBeGreaterThanOrEqual(0);
      expect(stressTest.summary.stress_test_score).toBeLessThanOrEqual(100);

      // 验证建议
      expect(stressTest.recommendations.length).toBeGreaterThan(0);
      stressTest.recommendations.forEach((recommendation) => {
        expect(typeof recommendation).toBe('string');
        expect(recommendation.length).toBeGreaterThan(0);
      });
    });

    it('应该根据风险评分生成相应建议', () => {
      // 创建高风险场景的测试数据
      const highRiskData = generateTestPriceData(100, 100);
      // 人为增加波动性
      for (let i = 1; i < highRiskData.length; i++) {
        const volatileReturn = (Math.random() - 0.5) * 0.1; // ±5% 高波动
        highRiskData[i].close = highRiskData[i - 1].close * (1 + volatileReturn);
      }

      const stressTest = analyzer.runComprehensiveStressTest(highRiskData);

      expect(stressTest.recommendations.length).toBeGreaterThan(0);

      // 根据评分验证建议类型
      if (stressTest.summary.stress_test_score < 30) {
        expect(stressTest.recommendations.some((r) => r.includes('暂停投资'))).toBe(true);
      } else if (stressTest.summary.stress_test_score < 60) {
        expect(stressTest.recommendations.some((r) => r.includes('降低仓位'))).toBe(true);
      }
    });
  });

  describe('辅助方法测试', () => {
    it('应该正确计算收益率', () => {
      const simplePriceData: PriceData[] = [
        { date: '2023-01-01', open: 100, high: 102, low: 98, close: 100, volume: 1000 },
        { date: '2023-01-02', open: 100, high: 105, low: 99, close: 102, volume: 1000 },
        { date: '2023-01-03', open: 102, high: 104, low: 100, close: 98, volume: 1000 },
      ];

      // 使用反射访问私有方法进行测试
      const returns = (analyzer as any).calculateReturns(simplePriceData);

      expect(returns).toHaveLength(2);
      expect(returns[0]).toBeCloseTo(0.02, 3); // (102-100)/100 = 0.02
      expect(returns[1]).toBeCloseTo(-0.0392, 3); // (98-102)/102 ≈ -0.0392
    });

    it('应该生成正态分布随机数', () => {
      const samples: number[] = [];
      for (let i = 0; i < 1000; i++) {
        samples.push((analyzer as any).normalRandom());
      }

      const mean = samples.reduce((sum, val) => sum + val, 0) / samples.length;
      const variance =
        samples.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / samples.length;

      // 正态分布的均值应该接近0，方差接近1
      expect(Math.abs(mean)).toBeLessThan(0.1);
      expect(Math.abs(variance - 1)).toBeLessThan(0.2);
    });
  });

  describe('错误处理', () => {
    it('应该处理空数据', () => {
      const result = analyzer.simulateMarketDeclineScenarios([]);
      expect(result).toEqual([]);
    });

    it('应该处理无效参数', () => {
      expect(() => {
        analyzer.runMonteCarloSimulation(testPriceData, 0, 10);
      }).not.toThrow();

      expect(() => {
        analyzer.runMonteCarloSimulation(testPriceData, 100, 0);
      }).not.toThrow();
    });

    it('应该处理空数据的蒙特卡洛模拟', () => {
      expect(() => {
        analyzer.runMonteCarloSimulation([], 100, 10);
      }).toThrow('Price data cannot be empty for Monte Carlo simulation');
    });
  });

  describe('性能测试', () => {
    it('蒙特卡洛模拟应该在合理时间内完成', () => {
      const startTime = Date.now();

      analyzer.runMonteCarloSimulation(testPriceData, 5000, 252);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 5000次模拟应该在10秒内完成
      expect(executionTime).toBeLessThan(10000);
    });

    it('综合压力测试应该在合理时间内完成', () => {
      const startTime = Date.now();

      analyzer.runComprehensiveStressTest(testPriceData.slice(0, 100));

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 综合测试应该在15秒内完成
      expect(executionTime).toBeLessThan(15000);
    });
  });
});
