'use client';

import AnalysisOverview from '@/components/dashboard/AnalysisOverview';
import AnalysisProgressIndicator from '@/components/dashboard/AnalysisProgressIndicator';
import { AnalysisReportSection } from '@/components/dashboard/AnalysisReportSection';
import { FinalDecisionSection } from '@/components/dashboard/FinalDecisionSection';
import { Breadcrumb } from '@/components/navigation/Breadcrumb';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePageTitle } from '@/hooks/usePageTitle';
import { useWebSocket } from '@/hooks/useWebSocket';
import useUserStore from '@/store/userStore';
import { FinalDecision } from '@/types/langgraph-database';
import { ExclamationTriangleIcon, SignalSlashIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { CheckIcon, ClockIcon, WifiIcon } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

// 生成分析页面元数据
// export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
//   const generator = new MetadataGenerator('zh');

//   // 尝试获取任务信息来生成更具体的元数据
//   let stockSymbol: string | undefined;
//   let taskTitle: string | undefined;

//   try {
//     // 这里可以调用API获取任务详情，但为了避免在元数据生成时进行复杂的异步操作
//     // 我们使用通用的分析页面元数据
//     const response = await fetch(
//       `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000'}/api/tasks/${params.id}`,
//       {
//         cache: 'no-store',
//       }
//     );

//     if (response.ok) {
//       const task = await response.json();
//       stockSymbol = task.stock_symbol;
//       taskTitle = `${task.stock_symbol} 分析报告`;
//     }
//   } catch (error) {
//     console.warn('Failed to fetch task details for metadata generation:', error);
//   }

//   return generator.generatePageMetadata({
//     page: 'analysis',
//     dynamicData: {
//       analysisId: params.id,
//       stockSymbol,
//       taskTitle,
//     },
//     locale: 'zh',
//   });
// }

// Enhanced interfaces based on the new database schema and requirements
interface AnalysisStatus {
  workflowId: string;
  taskId?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStage: string;
  ticker: string;
  title: string;
  description?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  finalDecision: FinalDecision | null;
  recentEvents: WorkflowEvent[];
  error?: string;
  config?: any;
}

interface AnalystReport {
  id: number;
  report_id: string;
  analyst_type: 'fundamental' | 'technical' | 'sentiment' | 'news';
  summary?: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: string;
}

interface ResearchReport {
  id: number;
  report_id: string;
  researcher_type: 'bull' | 'bear';
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'completed' | 'failed';
  created_at: string;
}

interface WorkflowEvent {
  id: number;
  event_id: string;
  stage_name?: string;
  event_type: 'message' | 'tool_call' | 'state_change' | 'error' | 'log';
  content: string;
  metadata?: any;
  created_at: string;
}

// WebSocket message types for real-time updates
interface WebSocketMessage {
  type: 'status_update' | 'agent_complete' | 'analysis_complete' | 'error';
  workflowId: string;
  data: any;
  timestamp: string;
}

export default function AnalysisPage() {
  const router = useRouter();
  const params = useParams();
  const workflowId = params?.id as string;
  const { user, initialized, loading: authLoading } = useUserStore();

  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [wsUrl, setWsUrl] = useState<string | null>(null);
  const [showComprehensiveReport, setShowComprehensiveReport] = useState(false);

  // Set page title dynamically based on analysis data
  usePageTitle(
    analysisStatus ? `${analysisStatus.ticker} 分析详情` : '分析详情',
    analysisStatus ? `查看 ${analysisStatus.ticker} 的详细分析结果` : '查看分析的详细结果'
  );

  // Effect to control WebSocket connection based on analysis status
  useEffect(() => {
    if (analysisStatus) {
      const isTaskActive =
        analysisStatus.status === 'running' || analysisStatus.status === 'pending';
      if (isTaskActive) {
        setWsUrl(`${process.env.NEXT_PUBLIC_WS_URL}/ws/analysis/${workflowId}`);
      } else {
        setWsUrl(null); // Disconnect if task is completed, failed, etc.
      }
    }
  }, [analysisStatus, workflowId]);

  const handleWebSocketMessage = useCallback((message: any) => {
    console.log('WebSocket message received:', message);

    switch (message.type) {
      case 'status_update':
        setAnalysisStatus((prev) =>
          prev
            ? {
                ...prev,
                status: message.data.status,
                progress: message.data.progress,
                currentStage: message.data.currentStage,
              }
            : null
        );
        break;

      case 'agent_complete':
        setAnalysisStatus((prev) => {
          if (!prev) return null;

          // Update the corresponding analyst or research report
          if (message.data.type === 'analyst') {
            const updatedAnalystReports = [...prev.analystReports];
            const existingIndex = updatedAnalystReports.findIndex(
              (r) => r.analyst_type === message.data.analyst_type
            );

            if (existingIndex >= 0) {
              updatedAnalystReports[existingIndex] = {
                ...updatedAnalystReports[existingIndex],
                status: 'completed',
                summary: message.data.summary,
                execution_time_ms: message.data.execution_time_ms,
              };
            } else {
              updatedAnalystReports.push({
                id: Date.now(),
                report_id: message.data.report_id || `report_${Date.now()}`,
                analyst_type: message.data.analyst_type,
                summary: message.data.summary,
                status: 'completed',
                execution_time_ms: message.data.execution_time_ms,
                created_at: new Date().toISOString(),
              });
            }

            return { ...prev, analystReports: updatedAnalystReports };
          }

          return prev;
        });
        break;

      case 'analysis_complete':
        setAnalysisStatus((prev) =>
          prev
            ? {
                ...prev,
                status: 'completed',
                progress: 100,
                completedAt: new Date().toISOString(),
                finalDecision: message.data.finalDecision || prev.finalDecision,
              }
            : null
        );
        break;

      case 'error':
        setAnalysisStatus((prev) =>
          prev
            ? {
                ...prev,
                status: 'failed',
                error: message.data.message,
              }
            : null
        );
        break;
    }
  }, []);

  const handleWebSocketError = useCallback((error: Event) => {
    console.error('WebSocket error:', error);
    setError('实时连接出现错误');
  }, []);

  const {
    isConnected,
    isConnecting,
    error: wsError,
    sendMessage,
  } = useWebSocket(wsUrl, {
    onMessage: handleWebSocketMessage,
    onError: handleWebSocketError,
    reconnectAttempts: 5,
    reconnectInterval: 2000,
    shouldReconnect: !!wsUrl,
  });

  // Load initial analysis status
  const loadAnalysisStatus = useCallback(async () => {
    if (!workflowId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/analysis/${workflowId}/status`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('分析不存在');
        }
        throw new Error(`获取分析状态失败: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '获取分析状态失败');
      }

      setAnalysisStatus(result.data);
      setRetryCount(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('Load analysis status error:', err);
    } finally {
      setLoading(false);
    }
  }, [workflowId]);

  // Initial load and authentication check
  useEffect(() => {
    if (initialized && !user) {
      router.push('/login');
      return;
    }

    if (initialized && user && workflowId) {
      loadAnalysisStatus();
    }
  }, [initialized, user, workflowId, router, loadAnalysisStatus]);

  // Join WebSocket room when connected
  useEffect(() => {
    if (isConnected && workflowId) {
      sendMessage({
        type: 'join_analysis',
        workflowId: workflowId,
      });

      // Request current status
      sendMessage({
        type: 'request_status',
        workflowId: workflowId,
      });
    }
  }, [isConnected, workflowId, sendMessage]);

  // Retry mechanism for failed loads
  const handleRetry = useCallback(() => {
    if (retryCount < 3) {
      setRetryCount((prev) => prev + 1);
      loadAnalysisStatus();
    }
  }, [retryCount, loadAnalysisStatus]);

  // Helper functions for UI rendering
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'warning';
      case 'failed':
      case 'cancelled':
        return 'danger';
      case 'pending':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '进行中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      case 'pending':
        return '等待中';
      default:
        return status;
    }
  };

  const getAnalystStatusIcon = (report: AnalystReport) => {
    if (report.status === 'completed') {
      return <CheckIcon className="h-4 w-4 text-green-500" />;
    } else if (report.status === 'failed') {
      return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
    } else {
      return <ClockIcon className="h-4 w-4 text-slate-400" />;
    }
  };

  const getAnalystName = (type: string) => {
    switch (type) {
      case 'fundamental':
        return '基本面分析师';
      case 'technical':
        return '技术分析师';
      case 'sentiment':
        return '情绪分析师';
      case 'news':
        return '新闻分析师';
      default:
        return type;
    }
  };

  // Loading state
  if (!initialized || authLoading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-slate-600 dark:text-slate-400">初始化中...</p>
        </div>
      </div>
    );
  }

  // Loading state with enhanced UI
  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6" />
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
            加载分析状态
          </h2>
          <p className="text-slate-600 dark:text-slate-400 mb-4">
            正在获取工作流 {workflowId} 的详细信息...
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-500">
            {isConnecting ? (
              <>
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                <span>连接实时服务...</span>
              </>
            ) : isConnected ? (
              <>
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span>实时连接已建立</span>
              </>
            ) : (
              <>
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span>实时连接断开</span>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Error state with retry functionality
  if (error || !analysisStatus) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">加载失败</h2>
          <p className="text-slate-600 dark:text-slate-400 mb-6">{error || '未找到分析数据'}</p>

          {/* Connection status */}
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-500 mb-6">
            {isConnected ? (
              <>
                <WifiIcon className="h-4 w-4 text-green-500" />
                <span>实时连接正常</span>
              </>
            ) : (
              <>
                <SignalSlashIcon className="h-4 w-4 text-red-500" />
                <span>实时连接断开</span>
              </>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={handleRetry} disabled={retryCount >= 3} variant="primary">
              {retryCount >= 3
                ? '重试次数已用完'
                : `重试 ${retryCount > 0 ? `(${retryCount}/3)` : ''}`}
            </Button>
            <Button onClick={() => router.push('/tasks')} variant="secondary">
              返回任务列表
            </Button>
          </div>

          {wsError && <p className="text-sm text-red-500 mt-4">WebSocket 错误: {wsError}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 */}
        <div className="mb-4">
          <Breadcrumb />
        </div>

        {/* Analysis Overview Component */}
        <AnalysisOverview
          status={analysisStatus}
          isConnected={isConnected}
          isConnecting={isConnecting}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-2 space-y-8">
            {/* Analysis Progress Indicator */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <AnalysisProgressIndicator
                progress={analysisStatus.progress}
                currentStage={analysisStatus.currentStage}
                status={analysisStatus.status}
              />
            </motion.div>

            {/* 智能体状态 */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <span>👥</span>
                    <span>智能体团队状态</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Analyst Reports */}
                    {analysisStatus.analystReports.map((report, index) => (
                      <motion.div
                        key={report.report_id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 * index }}
                        className="p-4 border border-slate-200 dark:border-slate-600 rounded-lg"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {getAnalystStatusIcon(report)}
                            <span className="font-medium text-slate-900 dark:text-white">
                              {getAnalystName(report.analyst_type)}
                            </span>
                          </div>
                          <span className="text-sm text-slate-500">
                            {report.status === 'completed' ? '100%' : '0%'}
                          </span>
                        </div>
                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full ${
                              report.status === 'completed'
                                ? 'bg-green-500'
                                : report.status === 'failed'
                                ? 'bg-red-500'
                                : 'bg-blue-500'
                            }`}
                            initial={{ width: 0 }}
                            animate={{ width: report.status === 'completed' ? '100%' : '0%' }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                        {report.execution_time_ms && (
                          <p className="text-xs text-slate-500 mt-1">
                            耗时: {Math.round(report.execution_time_ms / 1000)}秒
                          </p>
                        )}
                      </motion.div>
                    ))}

                    {/* Research Reports */}
                    {analysisStatus.researchReports.map((report, index) => (
                      <motion.div
                        key={report.report_id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.1 * (analysisStatus.analystReports.length + index) }}
                        className="p-4 border border-slate-200 dark:border-slate-600 rounded-lg"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {report.status === 'completed' ? (
                              <CheckIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <ClockIcon className="h-4 w-4 text-slate-400" />
                            )}
                            <span className="font-medium text-slate-900 dark:text-white">
                              {report.researcher_type === 'bull' ? '多头研究员' : '空头研究员'}
                            </span>
                          </div>
                          <span className="text-sm text-slate-500">
                            {report.status === 'completed' ? '100%' : '0%'}
                          </span>
                        </div>
                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full ${
                              report.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            initial={{ width: 0 }}
                            animate={{ width: report.status === 'completed' ? '100%' : '0%' }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                        {report.confidence_level && (
                          <p className="text-xs text-slate-500 mt-1">
                            置信度: {Math.round(report.confidence_level * 100)}%
                          </p>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* 分析报告 - 使用增强版报告查看器 */}
            {(analysisStatus.analystReports.length > 0 ||
              analysisStatus.researchReports.length > 0) && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <AnalysisReportSection
                  analystReports={analysisStatus.analystReports}
                  researchReports={analysisStatus.researchReports}
                />
              </motion.div>
            )}

            {/* 最终决策 */}
            {analysisStatus.finalDecision && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <FinalDecisionSection decision={analysisStatus.finalDecision} />
              </motion.div>
            )}

            {/* 事件日志 */}
            {analysisStatus.recentEvents.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <ClockIcon className="h-5 w-5" />
                      <span>事件日志</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {analysisStatus.recentEvents.map((event, index) => (
                        <motion.div
                          key={event.event_id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.05 * index }}
                          className="flex items-start space-x-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg"
                        >
                          <div
                            className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                              event.event_type === 'error'
                                ? 'bg-red-500'
                                : event.event_type === 'state_change'
                                ? 'bg-blue-500'
                                : event.event_type === 'log'
                                ? 'bg-green-500'
                                : 'bg-gray-500'
                            }`}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-slate-900 dark:text-white">
                                {event.stage_name || '系统'}
                              </p>
                              <p className="text-xs text-slate-500">
                                {new Date(event.created_at).toLocaleString()}
                              </p>
                            </div>
                            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                              {event.content}
                            </p>
                            {event.metadata && (
                              <details className="mt-2">
                                <summary className="text-xs text-slate-500 cursor-pointer">
                                  详细信息
                                </summary>
                                <pre className="text-xs text-slate-500 mt-1 overflow-x-auto">
                                  {JSON.stringify(event.metadata, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="space-y-6">
            {/* 分析配置 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">分析配置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">股票代码</span>
                    <code className="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                      {analysisStatus.ticker}
                    </code>
                  </div>
                  {analysisStatus.config && (
                    <>
                      {analysisStatus.config.analysisPeriod && (
                        <div className="flex justify-between">
                          <span className="text-slate-600 dark:text-slate-400">分析周期</span>
                          <span>
                            {analysisStatus.config.analysisPeriod === 'custom'
                              ? `${analysisStatus.config.customStartDate} 至 ${analysisStatus.config.customEndDate}`
                              : analysisStatus.config.analysisPeriod ||
                                analysisStatus.config.analysisDate}
                          </span>
                        </div>
                      )}
                      {analysisStatus.config.researchDepth && (
                        <div className="flex justify-between">
                          <span className="text-slate-600 dark:text-slate-400">研究深度</span>
                          <span className="capitalize">{analysisStatus.config.researchDepth}</span>
                        </div>
                      )}
                      {analysisStatus.config.selectedAnalysts && (
                        <div>
                          <div className="text-slate-600 dark:text-slate-400 mb-1">
                            选择的分析师
                          </div>
                          <div className="space-y-1">
                            {analysisStatus.config.selectedAnalysts.map((analyst: string) => (
                              <div
                                key={analyst}
                                className="text-xs bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded"
                              >
                                {analyst}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* 时间信息 */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">时间信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600 dark:text-slate-400">创建时间</span>
                    <span>{new Date(analysisStatus.createdAt).toLocaleString()}</span>
                  </div>
                  {analysisStatus.startedAt && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">开始时间</span>
                      <span>{new Date(analysisStatus.startedAt).toLocaleString()}</span>
                    </div>
                  )}
                  {analysisStatus.completedAt && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">完成时间</span>
                      <span>{new Date(analysisStatus.completedAt).toLocaleString()}</span>
                    </div>
                  )}
                  {analysisStatus.startedAt && analysisStatus.completedAt && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">总耗时</span>
                      <span>
                        {Math.round(
                          (new Date(analysisStatus.completedAt).getTime() -
                            new Date(analysisStatus.startedAt).getTime()) /
                            1000
                        )}
                        秒
                      </span>
                    </div>
                  )}
                  {analysisStatus.error && (
                    <div className="flex flex-col">
                      <span className="text-slate-600 dark:text-slate-400 mb-1">错误信息</span>
                      <span className="text-red-600 dark:text-red-400 text-xs">
                        {analysisStatus.error}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
