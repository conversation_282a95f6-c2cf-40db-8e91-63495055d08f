'use client';

import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  ConsensusEvaluation,
  DebateUtterance,
  ResearchArgument,
  ResearchReport,
} from '@/types/langgraph-database';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ClockIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ScaleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';

interface ResearchReportCardProps {
  researchReports: ResearchReport[];
  researchArguments?: ResearchArgument[];
  debateUtterances?: DebateUtterance[];
  consensusEvaluation?: ConsensusEvaluation | null;
  className?: string;
}

export function ResearchReportCard({
  researchReports,
  researchArguments = [],
  debateUtterances = [],
  consensusEvaluation,
  className = '',
}: ResearchReportCardProps) {
  const [activeTab, setActiveTab] = useState<'reports' | 'debate' | 'consensus'>('reports');
  const [selectedReport, setSelectedReport] = useState<ResearchReport | null>(null);
  const [expandedArguments, setExpandedArguments] = useState<Set<string>>(new Set());
  const [showDebateDetails, setShowDebateDetails] = useState(false);

  // 分离多头和空头报告
  const bullReports = researchReports.filter((report) => report.researcher_type === 'bull');
  const bearReports = researchReports.filter((report) => report.researcher_type === 'bear');

  // 获取报告的论点
  const getReportArguments = (reportId: string) => {
    return researchArguments.filter((arg) => arg.report_id === reportId);
  };

  // 获取辩论发言
  const getBullUtterances = () => debateUtterances.filter((u) => u.participant_type === 'bull');
  const getBearUtterances = () => debateUtterances.filter((u) => u.participant_type === 'bear');

  // 切换论点展开状态
  const toggleArgumentExpansion = (argumentId: string) => {
    const newExpanded = new Set(expandedArguments);
    if (newExpanded.has(argumentId)) {
      newExpanded.delete(argumentId);
    } else {
      newExpanded.add(argumentId);
    }
    setExpandedArguments(newExpanded);
  };

  // 获取置信度颜色
  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'gray';
    if (confidence >= 80) return 'green';
    if (confidence >= 60) return 'yellow';
    if (confidence >= 40) return 'orange';
    return 'red';
  };

  // 获取强度颜色
  const getStrengthColor = (strength?: number) => {
    if (!strength) return 'gray';
    if (strength >= 0.8) return 'green';
    if (strength >= 0.6) return 'blue';
    if (strength >= 0.4) return 'yellow';
    return 'red';
  };

  // 渲染研究报告
  const renderResearchReport = (report: ResearchReport, type: 'bull' | 'bear') => {
    const reportArguments = getReportArguments(report.report_id);
    const Icon = type === 'bull' ? ArrowTrendingUpIcon : ArrowTrendingDownIcon;
    const colorClass = type === 'bull' ? 'green' : 'red';
    const bgClass =
      type === 'bull' ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20';
    const borderClass =
      type === 'bull'
        ? 'border-green-200 dark:border-green-800'
        : 'border-red-200 dark:border-red-800';

    return (
      <motion.div
        key={report.report_id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`border rounded-lg p-4 ${bgClass} ${borderClass}`}
      >
        {/* 报告头部 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg bg-${colorClass}-100 dark:bg-${colorClass}-900/30`}>
              <Icon className={`h-5 w-5 text-${colorClass}-600`} />
            </div>
            <div>
              <h3 className="font-semibold text-slate-900 dark:text-white">
                {type === 'bull' ? '多头研究报告' : '空头研究报告'}
              </h3>
              <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                <ClockIcon className="h-4 w-4" />
                <span>{new Date(report.created_at).toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* 置信度和目标价格 */}
          <div className="flex items-center space-x-2">
            {report.confidence_level && (
              <Badge
                variant="secondary"
                className={`text-${getConfidenceColor(report.confidence_level)}-600`}
              >
                置信度: {report.confidence_level}%
              </Badge>
            )}
            {report.target_price && (
              <Badge variant="default" className={`text-${colorClass}-600`}>
                目标价: ¥{report.target_price}
              </Badge>
            )}
          </div>
        </div>

        {/* 报告摘要 */}
        {report.summary && (
          <div className="mb-4">
            <h4 className="font-medium text-slate-900 dark:text-white mb-2">核心观点</h4>
            <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{report.summary}</p>
          </div>
        )}

        {/* 时间范围 */}
        {report.time_horizon && (
          <div className="mb-4">
            <span className="text-sm font-medium text-slate-600 dark:text-slate-400">
              投资时间范围: {report.time_horizon}
            </span>
          </div>
        )}

        {/* 论点列表 */}
        {reportArguments.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-slate-900 dark:text-white flex items-center">
              <DocumentTextIcon className="h-4 w-4 mr-2" />
              支撑论点 ({reportArguments.length})
            </h4>
            <div className="space-y-2">
              {reportArguments.map((argument, index) => (
                <div
                  key={argument.argument_id}
                  className="border border-slate-200 dark:border-slate-700 rounded-lg p-3 bg-white dark:bg-slate-800"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium text-slate-900 dark:text-white">
                          论点 {index + 1}
                        </span>
                        {argument.strength_score && (
                          <Badge
                            variant="secondary"
                            className={`text-${getStrengthColor(argument.strength_score)}-600`}
                          >
                            强度: {(argument.strength_score * 100).toFixed(0)}%
                          </Badge>
                        )}
                      </div>

                      <p
                        className={`text-slate-700 dark:text-slate-300 ${
                          expandedArguments.has(argument.argument_id) ? '' : 'line-clamp-2'
                        }`}
                      >
                        {argument.content}
                      </p>

                      {argument.source && (
                        <div className="mt-2 text-xs text-slate-500">来源: {argument.source}</div>
                      )}
                    </div>

                    <button
                      onClick={() => toggleArgumentExpansion(argument.argument_id)}
                      className="ml-2 p-1 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                    >
                      {expandedArguments.has(argument.argument_id) ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 状态指示器 */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {report.status === 'completed' ? (
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
            ) : (
              <XCircleIcon className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm text-slate-600 dark:text-slate-400">
              {report.status === 'completed' ? '分析完成' : '分析失败'}
            </span>
          </div>

          <button
            onClick={() => setSelectedReport(selectedReport === report ? null : report)}
            className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
          >
            {selectedReport === report ? '收起详情' : '查看详情'}
          </button>
        </div>
      </motion.div>
    );
  };

  // 渲染辩论过程
  const renderDebateProcess = () => {
    const bullUtterances = getBullUtterances();
    const bearUtterances = getBearUtterances();
    const allUtterances = [...bullUtterances, ...bearUtterances].sort(
      (a, b) => a.sequence_in_round - b.sequence_in_round
    );

    return (
      <div className="space-y-4">
        {/* 辩论统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <ArrowTrendingUpIcon className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-green-600">{bullUtterances.length}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">多头发言</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <ArrowTrendingDownIcon className="h-5 w-5 text-red-600" />
                <div>
                  <div className="text-2xl font-bold text-red-600">{bearUtterances.length}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">空头发言</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-blue-600">{allUtterances.length}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">总发言数</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 辩论详情切换 */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-white">辩论过程</h3>
          <button
            onClick={() => setShowDebateDetails(!showDebateDetails)}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 dark:text-blue-400"
          >
            <span>{showDebateDetails ? '隐藏详情' : '显示详情'}</span>
            {showDebateDetails ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* 辩论时间线 */}
        <AnimatePresence>
          {showDebateDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              {allUtterances.length > 0 ? (
                <div className="space-y-3">
                  {allUtterances.map((utterance, index) => (
                    <motion.div
                      key={utterance.utterance_id}
                      initial={{ opacity: 0, x: utterance.participant_type === 'bull' ? -20 : 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={`flex ${
                        utterance.participant_type === 'bull' ? 'justify-start' : 'justify-end'
                      }`}
                    >
                      <div
                        className={`max-w-2xl p-4 rounded-lg ${
                          utterance.participant_type === 'bull'
                            ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500'
                            : 'bg-red-50 dark:bg-red-900/20 border-r-4 border-red-500'
                        }`}
                      >
                        <div className="flex items-center space-x-2 mb-2">
                          {utterance.participant_type === 'bull' ? (
                            <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
                          ) : (
                            <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
                          )}
                          <span className="font-medium text-slate-900 dark:text-white">
                            {utterance.participant_type === 'bull' ? '多头研究员' : '空头研究员'}
                          </span>
                          <span className="text-xs text-slate-500">
                            #{utterance.sequence_in_round}
                          </span>
                        </div>

                        <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                          {utterance.content}
                        </p>

                        {utterance.rebuttal_to_utterance_id && (
                          <div className="mt-2 text-xs text-slate-500">
                            回应: #{utterance.rebuttal_to_utterance_id}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-slate-500 py-8">
                  <ChatBubbleLeftRightIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <p>暂无辩论记录</p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  // 渲染共识评估
  const renderConsensusEvaluation = () => {
    if (!consensusEvaluation) {
      return (
        <div className="text-center text-slate-500 py-8">
          <ScaleIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
          <p>暂无共识评估结果</p>
        </div>
      );
    }

    const bullStrength = consensusEvaluation.bull_strength || 0;
    const bearStrength = consensusEvaluation.bear_strength || 0;
    const totalStrength = bullStrength + bearStrength;
    const bullPercentage = totalStrength > 0 ? (bullStrength / totalStrength) * 100 : 50;
    const bearPercentage = totalStrength > 0 ? (bearStrength / totalStrength) * 100 : 50;

    return (
      <div className="space-y-6">
        {/* 共识方向 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ScaleIcon className="h-5 w-5" />
              <span>共识评估结果</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 共识方向 */}
              <div className="text-center">
                <div
                  className={`text-4xl font-bold mb-2 ${
                    consensusEvaluation.consensus_direction === 'bullish'
                      ? 'text-green-600'
                      : consensusEvaluation.consensus_direction === 'bearish'
                      ? 'text-red-600'
                      : 'text-slate-600'
                  }`}
                >
                  {consensusEvaluation.consensus_direction === 'bullish'
                    ? '📈'
                    : consensusEvaluation.consensus_direction === 'bearish'
                    ? '📉'
                    : '➡️'}
                </div>
                <div className="text-lg font-semibold text-slate-900 dark:text-white">
                  {consensusEvaluation.consensus_direction === 'bullish'
                    ? '看涨共识'
                    : consensusEvaluation.consensus_direction === 'bearish'
                    ? '看跌共识'
                    : '中性共识'}
                </div>
              </div>

              {/* 共识置信度 */}
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {consensusEvaluation.consensus_confidence || 0}%
                </div>
                <div className="text-lg font-semibold text-slate-900 dark:text-white">
                  共识置信度
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 观点强度对比 */}
        <Card>
          <CardHeader>
            <CardTitle>观点强度对比</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 强度条 */}
              <div className="relative">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-green-600">多头观点</span>
                  <span className="text-sm font-medium text-red-600">空头观点</span>
                </div>
                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 overflow-hidden">
                  <div className="h-full flex">
                    <div
                      className="bg-green-500 transition-all duration-500"
                      style={{ width: `${bullPercentage}%` }}
                    />
                    <div
                      className="bg-red-500 transition-all duration-500"
                      style={{ width: `${bearPercentage}%` }}
                    />
                  </div>
                </div>
                <div className="flex items-center justify-between mt-2 text-sm text-slate-600 dark:text-slate-400">
                  <span>{bullPercentage.toFixed(1)}%</span>
                  <span>{bearPercentage.toFixed(1)}%</span>
                </div>
              </div>

              {/* 具体数值 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{bullStrength.toFixed(2)}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">多头强度</div>
                </div>
                <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{bearStrength.toFixed(2)}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">空头强度</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 综合总结 */}
        {consensusEvaluation.synthesis_summary && (
          <Card>
            <CardHeader>
              <CardTitle>综合分析总结</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                {consensusEvaluation.synthesis_summary}
              </p>
            </CardContent>
          </Card>
        )}

        {/* 关键一致点和分歧点 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {consensusEvaluation.key_agreement_points && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-green-600">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span>关键一致点</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Array.isArray(consensusEvaluation.key_agreement_points) ? (
                    consensusEvaluation.key_agreement_points.map((point: string, index: number) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-slate-700 dark:text-slate-300">{point}</span>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-slate-700 dark:text-slate-300">
                      {JSON.stringify(consensusEvaluation.key_agreement_points)}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {consensusEvaluation.key_disagreement_points && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-red-600">
                  <ExclamationTriangleIcon className="h-5 w-5" />
                  <span>关键分歧点</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Array.isArray(consensusEvaluation.key_disagreement_points) ? (
                    consensusEvaluation.key_disagreement_points.map(
                      (point: string, index: number) => (
                        <div key={index} className="flex items-start space-x-2">
                          <ExclamationTriangleIcon className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-slate-700 dark:text-slate-300">
                            {point}
                          </span>
                        </div>
                      )
                    )
                  ) : (
                    <p className="text-sm text-slate-700 dark:text-slate-300">
                      {JSON.stringify(consensusEvaluation.key_disagreement_points)}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 标签导航 */}
      <div className="border-b border-slate-200 dark:border-slate-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('reports')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'reports'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            <DocumentTextIcon className="h-4 w-4 inline mr-2" />
            研究报告
          </button>
          <button
            onClick={() => setActiveTab('debate')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'debate'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            <ChatBubbleLeftRightIcon className="h-4 w-4 inline mr-2" />
            辩论过程
          </button>
          <button
            onClick={() => setActiveTab('consensus')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'consensus'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            <ScaleIcon className="h-4 w-4 inline mr-2" />
            共识评估
          </button>
        </nav>
      </div>

      {/* 标签内容 */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'reports' && (
            <div className="space-y-6">
              {/* 报告对比概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-green-600">
                      <ArrowTrendingUpIcon className="h-5 w-5" />
                      <span>多头观点 ({bullReports.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {bullReports.length > 0 ? (
                      <div className="space-y-4">
                        {bullReports.map((report) => renderResearchReport(report, 'bull'))}
                      </div>
                    ) : (
                      <div className="text-center text-slate-500 py-8">
                        <ArrowTrendingUpIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                        <p>暂无多头研究报告</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-red-600">
                      <ArrowTrendingDownIcon className="h-5 w-5" />
                      <span>空头观点 ({bearReports.length})</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {bearReports.length > 0 ? (
                      <div className="space-y-4">
                        {bearReports.map((report) => renderResearchReport(report, 'bear'))}
                      </div>
                    ) : (
                      <div className="text-center text-slate-500 py-8">
                        <ArrowTrendingDownIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                        <p>暂无空头研究报告</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'debate' && renderDebateProcess()}
          {activeTab === 'consensus' && renderConsensusEvaluation()}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
