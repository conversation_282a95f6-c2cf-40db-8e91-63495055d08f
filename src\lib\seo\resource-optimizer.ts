/**
 * 资源优化和缓存策略管理器
 * 优化 JavaScript、CSS 包大小和实现缓存策略
 */

export interface ResourceOptimizationConfig {
  enableBundleAnalysis: boolean;
  enableCodeSplitting: boolean;
  enableTreeShaking: boolean;
  enableMinification: boolean;
  enableGzipCompression: boolean;
  enableBrotliCompression: boolean;
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative';
  preloadCriticalResources: boolean;
  prefetchNextPageResources: boolean;
}

export interface CacheConfig {
  staticAssets: number; // 静态资源缓存时间（秒）
  apiResponses: number; // API 响应缓存时间（秒）
  images: number; // 图片缓存时间（秒）
  fonts: number; // 字体缓存时间（秒）
  scripts: number; // 脚本缓存时间（秒）
  styles: number; // 样式缓存时间（秒）
}

export class ResourceOptimizer {
  private config: ResourceOptimizationConfig;
  private cacheConfig: CacheConfig;

  constructor(config?: Partial<ResourceOptimizationConfig>) {
    this.config = {
      enableBundleAnalysis: true,
      enableCodeSplitting: true,
      enableTreeShaking: true,
      enableMinification: true,
      enableGzipCompression: true,
      enableBrotliCompression: true,
      cacheStrategy: 'moderate',
      preloadCriticalResources: true,
      prefetchNextPageResources: true,
      ...config,
    };

    this.cacheConfig = this.getCacheConfig();
  }

  /**
   * 获取缓存配置
   */
  private getCacheConfig(): CacheConfig {
    const baseConfig = {
      conservative: {
        staticAssets: 3600, // 1 hour
        apiResponses: 300, // 5 minutes
        images: 86400, // 1 day
        fonts: 604800, // 1 week
        scripts: 3600, // 1 hour
        styles: 3600, // 1 hour
      },
      moderate: {
        staticAssets: 86400, // 1 day
        apiResponses: 900, // 15 minutes
        images: 604800, // 1 week
        fonts: 2592000, // 1 month
        scripts: 86400, // 1 day
        styles: 86400, // 1 day
      },
      aggressive: {
        staticAssets: 604800, // 1 week
        apiResponses: 3600, // 1 hour
        images: 2592000, // 1 month
        fonts: 31536000, // 1 year
        scripts: 604800, // 1 week
        styles: 604800, // 1 week
      },
    };

    return baseConfig[this.config.cacheStrategy];
  }

  /**
   * 生成 Next.js 配置优化
   */
  generateNextConfig(): Record<string, any> {
    const config: Record<string, any> = {
      // 启用压缩
      compress: true,

      // 图片优化
      images: {
        formats: ['image/webp', 'image/avif'],
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        minimumCacheTTL: this.cacheConfig.images,
        dangerouslyAllowSVG: false,
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
      },

      // 实验性功能
      experimental: {
        optimizeCss: this.config.enableMinification,
        optimizePackageImports: ['lucide-react', '@heroicons/react', 'framer-motion', 'recharts'],
        turbo: {
          rules: {
            '*.svg': {
              loaders: ['@svgr/webpack'],
              as: '*.js',
            },
          },
        },
      },

      // Webpack 配置
      webpack: (config: any, { dev, isServer }: { dev: boolean; isServer: boolean }) => {
        // 启用 Tree Shaking
        if (this.config.enableTreeShaking && !dev) {
          config.optimization.usedExports = true;
          config.optimization.sideEffects = false;
        }

        // 代码分割优化
        if (this.config.enableCodeSplitting && !dev) {
          config.optimization.splitChunks = {
            chunks: 'all',
            cacheGroups: {
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendors',
                chunks: 'all',
                priority: 10,
              },
              common: {
                name: 'common',
                minChunks: 2,
                chunks: 'all',
                priority: 5,
                reuseExistingChunk: true,
              },
              styles: {
                name: 'styles',
                test: /\\.css$/,
                chunks: 'all',
                enforce: true,
              },
            },
          };
        }

        // Bundle 分析
        if (this.config.enableBundleAnalysis && process.env.ANALYZE === 'true') {
          const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
          config.plugins.push(
            new BundleAnalyzerPlugin({
              analyzerMode: 'static',
              openAnalyzer: false,
              reportFilename: isServer ? '../analyze/server.html' : './analyze/client.html',
            })
          );
        }

        // 压缩优化
        if (this.config.enableMinification && !dev) {
          const TerserPlugin = require('terser-webpack-plugin');
          config.optimization.minimizer.push(
            new TerserPlugin({
              terserOptions: {
                compress: {
                  drop_console: true,
                  drop_debugger: true,
                },
                mangle: true,
              },
            })
          );
        }

        return config;
      },

      // 响应头配置
      async headers() {
        return [
          {
            source: '/(.*)',
            headers: [
              {
                key: 'X-Content-Type-Options',
                value: 'nosniff',
              },
              {
                key: 'X-Frame-Options',
                value: 'DENY',
              },
              {
                key: 'X-XSS-Protection',
                value: '1; mode=block',
              },
              {
                key: 'Referrer-Policy',
                value: 'strict-origin-when-cross-origin',
              },
            ],
          },
          {
            source: '/api/(.*)',
            headers: [
              {
                key: 'Cache-Control',
                value: `public, max-age=${this.cacheConfig.apiResponses}, s-maxage=${this.cacheConfig.apiResponses}`,
              },
            ],
          },
          {
            source: '/_next/static/(.*)',
            headers: [
              {
                key: 'Cache-Control',
                value: `public, max-age=${this.cacheConfig.staticAssets}, immutable`,
              },
            ],
          },
          {
            source: '/_next/image(.*)',
            headers: [
              {
                key: 'Cache-Control',
                value: `public, max-age=${this.cacheConfig.images}`,
              },
            ],
          },
          {
            source: '/fonts/(.*)',
            headers: [
              {
                key: 'Cache-Control',
                value: `public, max-age=${this.cacheConfig.fonts}, immutable`,
              },
            ],
          },
        ];
      },
    };

    return config;
  }

  /**
   * 生成资源预加载策略
   */
  generatePreloadStrategy(): {
    criticalResources: Array<{ href: string; as: string; type?: string; crossorigin?: string }>;
    prefetchResources: Array<{ href: string; as?: string }>;
  } {
    const criticalResources = [];
    const prefetchResources = [];

    if (this.config.preloadCriticalResources) {
      // 关键字体
      criticalResources.push({
        href: '/fonts/inter-var.woff2',
        as: 'font',
        type: 'font/woff2',
        crossorigin: 'anonymous',
      });

      // 关键图片
      criticalResources.push({
        href: '/tradingAgent.png',
        as: 'image',
        type: 'image/png',
      });

      // 关键 CSS（如果有）
      criticalResources.push({
        href: '/_next/static/css/app.css',
        as: 'style',
      });
    }

    if (this.config.prefetchNextPageResources) {
      // 预获取可能访问的页面
      prefetchResources.push(
        { href: '/analysis' },
        { href: '/tasks' },
        { href: '/create-task' },
        { href: '/messages' }
      );
    }

    return {
      criticalResources,
      prefetchResources,
    };
  }

  /**
   * 生成 Service Worker 缓存策略
   */
  generateServiceWorkerConfig(): string {
    return `
// Service Worker 缓存策略
const CACHE_NAME = 'tradingagents-v1';
const STATIC_CACHE_URLS = [
  '/',
  '/analysis',
  '/tasks',
  '/create-task',
  '/messages',
  '/_next/static/css/app.css',
  '/tradingAgent.png',
  '/fonts/inter-var.woff2',
];

const API_CACHE_DURATION = ${this.cacheConfig.apiResponses * 1000}; // ms
const STATIC_CACHE_DURATION = ${this.cacheConfig.staticAssets * 1000}; // ms

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(STATIC_CACHE_URLS))
  );
});

self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // API 请求缓存策略
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(request).then((response) => {
          if (response) {
            const responseTime = new Date(response.headers.get('date')).getTime();
            const now = Date.now();
            
            // 检查缓存是否过期
            if (now - responseTime < API_CACHE_DURATION) {
              return response;
            }
          }
          
          // 获取新数据并缓存
          return fetch(request).then((fetchResponse) => {
            cache.put(request, fetchResponse.clone());
            return fetchResponse;
          });
        });
      })
    );
    return;
  }

  // 静态资源缓存策略
  if (url.pathname.startsWith('/_next/static/') || STATIC_CACHE_URLS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request).then((response) => {
        return response || fetch(request).then((fetchResponse) => {
          return caches.open(CACHE_NAME).then((cache) => {
            cache.put(request, fetchResponse.clone());
            return fetchResponse;
          });
        });
      })
    );
    return;
  }

  // 默认网络优先策略
  event.respondWith(
    fetch(request).catch(() => {
      return caches.match(request);
    })
  );
});
`;
  }

  /**
   * 分析包大小
   */
  async analyzeBundleSize(): Promise<{
    totalSize: number;
    chunks: Array<{ name: string; size: number }>;
    recommendations: string[];
  }> {
    // 这里应该集成实际的包分析工具
    // 目前返回模拟数据
    const mockAnalysis = {
      totalSize: 2.5 * 1024 * 1024, // 2.5MB
      chunks: [
        { name: 'main', size: 800 * 1024 },
        { name: 'vendors', size: 1.2 * 1024 * 1024 },
        { name: 'common', size: 300 * 1024 },
        { name: 'styles', size: 200 * 1024 },
      ],
      recommendations: [] as string[],
    };

    // 生成优化建议
    if (mockAnalysis.totalSize > 3 * 1024 * 1024) {
      mockAnalysis.recommendations.push('总包大小超过 3MB，建议进行代码分割');
    }

    const vendorChunk = mockAnalysis.chunks.find((chunk) => chunk.name === 'vendors');
    if (vendorChunk && vendorChunk.size > 1 * 1024 * 1024) {
      mockAnalysis.recommendations.push('vendor 包过大，建议拆分第三方库');
    }

    return mockAnalysis;
  }

  /**
   * 优化图片资源
   */
  optimizeImages(): {
    formats: string[];
    sizes: number[];
    quality: number;
    placeholder: string;
  } {
    return {
      formats: ['image/webp', 'image/avif', 'image/jpeg'],
      sizes: [640, 750, 828, 1080, 1200, 1920],
      quality: 85,
      placeholder: 'blur',
    };
  }

  /**
   * 生成 CDN 配置
   */
  generateCDNConfig(): {
    domains: string[];
    paths: Record<string, string>;
    headers: Record<string, string>;
  } {
    return {
      domains: ['cdn.tradingagents.com', 'images.tradingagents.com', 'static.tradingagents.com'],
      paths: {
        '/images/*': 'images.tradingagents.com',
        '/_next/static/*': 'static.tradingagents.com',
        '/fonts/*': 'static.tradingagents.com',
      },
      headers: {
        'Cache-Control': `public, max-age=${this.cacheConfig.staticAssets}`,
        'Access-Control-Allow-Origin': '*',
        'Cross-Origin-Resource-Policy': 'cross-origin',
      },
    };
  }

  /**
   * 获取性能预算配置
   */
  getPerformanceBudget(): {
    maxBundleSize: number;
    maxChunkSize: number;
    maxAssetSize: number;
    maxEntrypointSize: number;
  } {
    return {
      maxBundleSize: 3 * 1024 * 1024, // 3MB
      maxChunkSize: 1 * 1024 * 1024, // 1MB
      maxAssetSize: 500 * 1024, // 500KB
      maxEntrypointSize: 2 * 1024 * 1024, // 2MB
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ResourceOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.cacheConfig = this.getCacheConfig();
  }
}
