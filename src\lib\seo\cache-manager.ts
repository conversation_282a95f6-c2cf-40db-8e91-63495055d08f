/**
 * 缓存管理器
 * 处理浏览器缓存、Service Worker 缓存和 CDN 缓存
 */

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  key: string;
}

export interface CacheOptions {
  ttl?: number;
  storage?: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';
  compress?: boolean;
  encrypt?: boolean;
}

export class CacheManager {
  private memoryCache = new Map<string, CacheEntry>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * 设置缓存
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const {
      ttl = this.defaultTTL,
      storage = 'memory',
      compress = false,
      encrypt = false,
    } = options;

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      key,
    };

    let serializedData = JSON.stringify(entry);

    // 压缩数据
    if (compress && typeof window !== 'undefined' && 'CompressionStream' in window) {
      try {
        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();

        writer.write(new TextEncoder().encode(serializedData));
        writer.close();

        const chunks: Uint8Array[] = [];
        let done = false;

        while (!done) {
          const { value, done: readerDone } = await reader.read();
          done = readerDone;
          if (value) chunks.push(value);
        }

        const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
        let offset = 0;
        for (const chunk of chunks) {
          compressed.set(chunk, offset);
          offset += chunk.length;
        }

        serializedData = btoa(String.fromCharCode(...Array.from(compressed)));
      } catch (error) {
        console.warn('Compression failed, using uncompressed data:', error);
      }
    }

    // 加密数据
    if (encrypt) {
      // 简单的 Base64 编码（生产环境应使用更强的加密）
      serializedData = btoa(serializedData);
    }

    switch (storage) {
      case 'memory':
        this.memoryCache.set(key, entry);
        break;

      case 'localStorage':
        if (typeof window !== 'undefined' && window.localStorage) {
          try {
            localStorage.setItem(`cache_${key}`, serializedData);
          } catch (error) {
            console.warn('localStorage write failed:', error);
            this.memoryCache.set(key, entry);
          }
        }
        break;

      case 'sessionStorage':
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            sessionStorage.setItem(`cache_${key}`, serializedData);
          } catch (error) {
            console.warn('sessionStorage write failed:', error);
            this.memoryCache.set(key, entry);
          }
        }
        break;

      case 'indexedDB':
        await this.setIndexedDB(key, entry);
        break;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string, storage: CacheOptions['storage'] = 'memory'): Promise<T | null> {
    let entry: CacheEntry<T> | null = null;

    switch (storage) {
      case 'memory':
        entry = this.memoryCache.get(key) || null;
        break;

      case 'localStorage':
        if (typeof window !== 'undefined' && window.localStorage) {
          try {
            const data = localStorage.getItem(`cache_${key}`);
            if (data) {
              entry = JSON.parse(data);
            }
          } catch (error) {
            console.warn('localStorage read failed:', error);
          }
        }
        break;

      case 'sessionStorage':
        if (typeof window !== 'undefined' && window.sessionStorage) {
          try {
            const data = sessionStorage.getItem(`cache_${key}`);
            if (data) {
              entry = JSON.parse(data);
            }
          } catch (error) {
            console.warn('sessionStorage read failed:', error);
          }
        }
        break;

      case 'indexedDB':
        entry = await this.getIndexedDB<T>(key);
        break;
    }

    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      await this.delete(key, storage);
      return null;
    }

    return entry.data;
  }

  /**
   * 删除缓存
   */
  async delete(key: string, storage: CacheOptions['storage'] = 'memory'): Promise<void> {
    switch (storage) {
      case 'memory':
        this.memoryCache.delete(key);
        break;

      case 'localStorage':
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.removeItem(`cache_${key}`);
        }
        break;

      case 'sessionStorage':
        if (typeof window !== 'undefined' && window.sessionStorage) {
          sessionStorage.removeItem(`cache_${key}`);
        }
        break;

      case 'indexedDB':
        await this.deleteIndexedDB(key);
        break;
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(storage?: CacheOptions['storage']): Promise<void> {
    if (!storage || storage === 'memory') {
      this.memoryCache.clear();
    }

    if (!storage || storage === 'localStorage') {
      if (typeof window !== 'undefined' && window.localStorage) {
        const keys = Object.keys(localStorage).filter((key) => key.startsWith('cache_'));
        keys.forEach((key) => localStorage.removeItem(key));
      }
    }

    if (!storage || storage === 'sessionStorage') {
      if (typeof window !== 'undefined' && window.sessionStorage) {
        const keys = Object.keys(sessionStorage).filter((key) => key.startsWith('cache_'));
        keys.forEach((key) => sessionStorage.removeItem(key));
      }
    }

    if (!storage || storage === 'indexedDB') {
      await this.clearIndexedDB();
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    memoryEntries: number;
    memorySize: number;
    localStorageEntries: number;
    sessionStorageEntries: number;
  } {
    const memoryEntries = this.memoryCache.size;
    const memorySize = JSON.stringify(Array.from(this.memoryCache.values())).length;

    let localStorageEntries = 0;
    let sessionStorageEntries = 0;

    if (typeof window !== 'undefined') {
      if (window.localStorage) {
        localStorageEntries = Object.keys(localStorage).filter((key) =>
          key.startsWith('cache_')
        ).length;
      }

      if (window.sessionStorage) {
        sessionStorageEntries = Object.keys(sessionStorage).filter((key) =>
          key.startsWith('cache_')
        ).length;
      }
    }

    return {
      memoryEntries,
      memorySize,
      localStorageEntries,
      sessionStorageEntries,
    };
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    const now = Date.now();

    // 清理内存缓存
    for (const [key, entry] of Array.from(this.memoryCache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        this.memoryCache.delete(key);
      }
    }

    // 清理 localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      const keys = Object.keys(localStorage).filter((key) => key.startsWith('cache_'));
      for (const key of keys) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const entry: CacheEntry = JSON.parse(data);
            if (now - entry.timestamp > entry.ttl) {
              localStorage.removeItem(key);
            }
          }
        } catch (error) {
          // 删除损坏的缓存项
          localStorage.removeItem(key);
        }
      }
    }

    // 清理 sessionStorage
    if (typeof window !== 'undefined' && window.sessionStorage) {
      const keys = Object.keys(sessionStorage).filter((key) => key.startsWith('cache_'));
      for (const key of keys) {
        try {
          const data = sessionStorage.getItem(key);
          if (data) {
            const entry: CacheEntry = JSON.parse(data);
            if (now - entry.timestamp > entry.ttl) {
              sessionStorage.removeItem(key);
            }
          }
        } catch (error) {
          // 删除损坏的缓存项
          sessionStorage.removeItem(key);
        }
      }
    }
  }

  /**
   * IndexedDB 操作
   */
  private async setIndexedDB<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CacheDB', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');

        const putRequest = store.put(entry, key);
        putRequest.onsuccess = () => resolve();
        putRequest.onerror = () => reject(putRequest.error);
      };

      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache');
        }
      };
    });
  }

  private async getIndexedDB<T>(key: string): Promise<CacheEntry<T> | null> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return null;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CacheDB', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readonly');
        const store = transaction.objectStore('cache');

        const getRequest = store.get(key);
        getRequest.onsuccess = () => resolve(getRequest.result || null);
        getRequest.onerror = () => reject(getRequest.error);
      };

      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache');
        }
      };
    });
  }

  private async deleteIndexedDB(key: string): Promise<void> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CacheDB', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');

        const deleteRequest = store.delete(key);
        deleteRequest.onsuccess = () => resolve();
        deleteRequest.onerror = () => reject(deleteRequest.error);
      };
    });
  }

  private async clearIndexedDB(): Promise<void> {
    if (typeof window === 'undefined' || !window.indexedDB) {
      return;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CacheDB', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');

        const clearRequest = store.clear();
        clearRequest.onsuccess = () => resolve();
        clearRequest.onerror = () => reject(clearRequest.error);
      };
    });
  }
}

// 导出单例实例
export const cacheManager = new CacheManager();

/**
 * 缓存装饰器
 */
export function cached<T extends (...args: any[]) => any>(
  options: CacheOptions & { keyGenerator?: (...args: Parameters<T>) => string } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const { keyGenerator, ...cacheOptions } = options;
      const cacheKey = keyGenerator
        ? keyGenerator(...args)
        : `${target.constructor.name}_${propertyKey}_${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = await cacheManager.get(cacheKey, cacheOptions.storage);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await originalMethod.apply(this, args);

      // 缓存结果
      await cacheManager.set(cacheKey, result, cacheOptions);

      return result;
    };

    return descriptor;
  };
}
