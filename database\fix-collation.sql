-- Fix collation issues in TradingAgents database
-- This script ensures all tables use utf8mb4_unicode_ci collation consistently

USE trading_analysis;

-- Fix database collation
ALTER DATABASE trading_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Fix workflows table collation
ALTER TABLE workflows CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Fix other tables that might exist
ALTER TABLE IF EXISTS analyst_reports CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS technical_analysis_details CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS sentiment_analysis_details CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS research_reports CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS research_arguments CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS debate_sessions CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS debate_rounds CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS debate_utterances CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS final_decisions CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS workflow_events CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS workflow_state_snapshots CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Fix any task flow tables if they exist
ALTER TABLE IF EXISTS tasks CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS messages CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE IF EXISTS system_events CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Recreate the stored procedure to ensure it uses the correct collation
DROP PROCEDURE IF EXISTS UpdateWorkflowStatus;
DELIMITER //
CREATE PROCEDURE UpdateWorkflowStatus(
    IN p_workflow_id VARCHAR(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_current_stage VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_progress INT,
    IN p_status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    IN p_error_message TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
)
BEGIN
    UPDATE workflows SET
        current_stage = p_current_stage,
        progress = p_progress,
        status = p_status,
        error_message = p_error_message,
        updated_at = NOW(),
        started_at = CASE WHEN started_at IS NULL AND p_status = 'running' THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN p_status IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE NULL END
    WHERE workflow_id = p_workflow_id;
END //
DELIMITER ;

SELECT 'Collation fix completed successfully!' as status;