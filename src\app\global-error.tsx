'use client'; // 全局错误组件必须是客户端组件

import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface GlobalErrorProps {
  error: Error & { digest?: string }; // Next.js 提供的错误对象
  reset: () => void; // Next.js 提供的重置函数
}

/**
 * Next.js App Router 全局错误页面
 * 当根布局 (layout.tsx) 发生错误时会显示此页面
 * 这个组件会替换整个根布局，所以需要包含 html 和 body 标签
 * 
 * @param error - 错误对象，包含错误信息和可选的 digest
 * @param reset - 重置函数，用于重新渲染出错的组件
 */
export default function GlobalError({ error, reset }: GlobalErrorProps) {
  React.useEffect(() => {
    // 在客户端记录错误到控制台
    console.error('全局应用程序错误:', error);
    
    // 这里可以添加错误报告逻辑，比如发送到错误监控服务
    // reportError(error);
  }, [error]);

  return (
    <html lang="zh-CN">
      <head>
        <title>应用程序错误 - TradingAgents</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <style>{`
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            line-height: 1.6;
          }
          
          .container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
          }
          
          .error-card {
            background-color: #1e293b;
            border-radius: 0.75rem;
            padding: 2rem;
            max-width: 32rem;
            width: 100%;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid #334155;
          }
          
          .error-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
          }
          
          .error-icon {
            width: 2rem;
            height: 2rem;
            color: #ef4444;
            margin-right: 0.75rem;
            flex-shrink: 0;
          }
          
          .error-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #f1f5f9;
          }
          
          .error-description {
            color: #94a3b8;
            margin-bottom: 1.5rem;
          }
          
          .error-details {
            margin-top: 1rem;
          }
          
          .error-details summary {
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: #cbd5e1;
            margin-bottom: 0.5rem;
          }
          
          .error-details summary:hover {
            color: #f1f5f9;
          }
          
          .error-stack {
            background-color: #0f172a;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 0.75rem;
            border: 1px solid #475569;
          }
          
          .error-name {
            font-weight: 600;
            color: #ef4444;
            margin-bottom: 0.5rem;
          }
          
          .error-digest {
            font-size: 0.75rem;
            color: #64748b;
            margin-bottom: 0.5rem;
          }
          
          .error-stack-trace {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.75rem;
            color: #e2e8f0;
            white-space: pre-wrap;
            overflow: auto;
            max-height: 10rem;
            background-color: #020617;
            padding: 0.5rem;
            border-radius: 0.25rem;
          }
          
          .button-group {
            display: flex;
            gap: 0.75rem;
            margin-top: 1.5rem;
          }
          
          .button {
            flex: 1;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: none;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.2s;
          }
          
          .button-primary {
            background-color: #3b82f6;
            color: white;
          }
          
          .button-primary:hover {
            background-color: #2563eb;
          }
          
          .button-secondary {
            background-color: #475569;
            color: #e2e8f0;
          }
          
          .button-secondary:hover {
            background-color: #64748b;
          }
          
          .button-icon {
            width: 1rem;
            height: 1rem;
          }
          
          .footer {
            text-align: center;
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #334155;
          }
          
          @media (max-width: 640px) {
            .button-group {
              flex-direction: column;
            }
          }
        `}</style>
      </head>
      <body>
        <div className="container">
          <div className="error-card">
            <div className="error-header">
              <svg className="error-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h1 className="error-title">应用程序发生严重错误</h1>
            </div>
            
            <div className="error-description">
              <p>
                很抱歉，应用程序的核心组件遇到了一个严重错误。这可能是由于网络问题、资源加载失败或代码错误导致的。
              </p>
            </div>
            
            {/* 开发环境下显示错误详情 */}
            {process.env.NODE_ENV === 'development' && (
              <details className="error-details">
                <summary>🔍 错误详情 (仅开发模式显示)</summary>
                <div className="error-stack">
                  <div className="error-name">
                    {error.name}: {error.message}
                  </div>
                  {error.digest && (
                    <div className="error-digest">
                      错误 ID: {error.digest}
                    </div>
                  )}
                  <pre className="error-stack-trace">
                    {error.stack}
                  </pre>
                </div>
              </details>
            )}
            
            <div className="button-group">
              <button 
                onClick={reset} 
                className="button button-primary"
              >
                <svg className="button-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                重试
              </button>
              
              <button 
                onClick={() => window.location.reload()}
                className="button button-secondary"
              >
                <svg className="button-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                刷新页面
              </button>
            </div>
            
            <div className="footer">
              如果问题持续存在，请联系技术支持
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
