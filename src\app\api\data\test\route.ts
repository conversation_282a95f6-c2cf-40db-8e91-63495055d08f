import { NextRequest, NextResponse } from 'next/server';
import { dataService } from '@/lib/data-service';

/**
 * 数据 API 测试端点
 * GET /api/data/test?ticker=600519&type=all
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ticker = searchParams.get('ticker') || '600519';
    const type = searchParams.get('type') || 'stock';

    console.log(`[数据API测试] 测试类型: ${type}, 股票代码: ${ticker}`);

    const results: any = {
      ticker,
      timestamp: new Date().toISOString(),
      tests: {},
    };

    // 根据type参数执行不同的测试
    switch (type) {
      case 'stock':
        results.tests.stock = await testStockData(ticker);
        break;

      case 'news':
        results.tests.news = await testNewsData(ticker);
        break;

      case 'technical':
        results.tests.technical = await testTechnicalData(ticker);
        break;

      case 'fundamental':
        results.tests.fundamental = await testFundamentalData(ticker);
        break;

      case 'all':
        // 并行测试所有API
        const [stockTest, newsTest, technicalTest, fundamentalTest] = await Promise.allSettled([
          testStockData(ticker),
          testNewsData(ticker),
          testTechnicalData(ticker),
          testFundamentalData(ticker),
        ]);

        results.tests.stock =
          stockTest.status === 'fulfilled' ? stockTest.value : { error: stockTest.reason.message };
        results.tests.news =
          newsTest.status === 'fulfilled' ? newsTest.value : { error: newsTest.reason.message };
        results.tests.technical =
          technicalTest.status === 'fulfilled'
            ? technicalTest.value
            : { error: technicalTest.reason.message };
        results.tests.fundamental =
          fundamentalTest.status === 'fulfilled'
            ? fundamentalTest.value
            : { error: fundamentalTest.reason.message };
        break;

      case 'comprehensive':
        results.tests.comprehensive = await testComprehensiveData(ticker);
        break;

      case 'batch':
        results.tests.batch = await testBatchData(['600519', '000001', '000002']);
        break;

      default:
        return NextResponse.json(
          {
            error: '不支持的测试类型',
            supportedTypes: [
              'stock',
              'news',
              'technical',
              'fundamental',
              'all',
              'comprehensive',
              'batch',
            ],
          },
          { status: 400 }
        );
    }

    // 计算测试摘要
    const summary = generateTestSummary(results.tests);
    results.summary = summary;

    return NextResponse.json(results);
  } catch (error) {
    console.error('[数据API测试] 错误:', error);

    return NextResponse.json(
      {
        error: '测试执行失败',
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * 测试股票数据API
 */
async function testStockData(ticker: string) {
  const startTime = Date.now();

  try {
    const result = await dataService.getStockData({
      ticker,
      period: '1m',
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: true,
      latency,
      dataPoints: result.data?.length || 0,
      hasStats: !!result.stats,
      sampleData: result.data?.slice(0, 3) || [],
      metadata: {
        ticker: result.ticker,
        name: result.name,
        period: result.period,
        updatedAt: result.updatedAt,
      },
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 测试新闻数据API
 */
async function testNewsData(ticker: string) {
  const startTime = Date.now();

  try {
    const result = await dataService.getNewsData({
      ticker,
      limit: 5,
      days: 7,
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: true,
      latency,
      newsCount: result.news?.length || 0,
      hasStats: !!result.stats,
      sampleNews:
        result.news?.slice(0, 2).map((news) => ({
          title: news.title,
          source: news.source,
          sentiment: news.sentiment,
          category: news.category,
        })) || [],
      metadata: {
        ticker: result.ticker,
        name: result.name,
        period: result.period,
        updatedAt: result.updatedAt,
      },
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 测试技术指标API
 */
async function testTechnicalData(ticker: string) {
  const startTime = Date.now();

  try {
    const result = await dataService.getTechnicalData({
      ticker,
      indicators: ['ma', 'macd', 'rsi'],
      days: 60,
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    const indicators = result.indicators || {};
    const indicatorSummary = Object.keys(indicators).reduce((acc, key) => {
      const indicatorData = (indicators as any)[key];
      acc[key] = {
        dataPoints: indicatorData?.length || 0,
        hasData: (indicatorData?.length || 0) > 0,
      };
      return acc;
    }, {} as any);

    return {
      success: true,
      latency,
      indicators: indicatorSummary,
      hasAnalysis: !!result.analysis,
      dataPoints: (result as any).dataPoints,
      metadata: {
        ticker: result.ticker,
        name: result.name,
        period: result.period,
        updatedAt: result.updatedAt,
      },
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 测试基本面数据API
 */
async function testFundamentalData(ticker: string) {
  const startTime = Date.now();

  try {
    const result = await dataService.getFundamentalData({
      ticker,
      type: 'all',
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    const fundamentals = result.fundamentals || {};
    const categorySummary = Object.keys(fundamentals).reduce((acc, key) => {
      const category = (fundamentals as any)[key];
      if (category && typeof category === 'object') {
        const validFields = Object.values(category).filter(
          (value) => value !== null && value !== undefined
        ).length;
        const totalFields = Object.keys(category).length;
        acc[key] = {
          validFields,
          totalFields,
          completeness:
            totalFields > 0 ? ((validFields / totalFields) * 100).toFixed(1) + '%' : '0%',
        };
      }
      return acc;
    }, {} as any);

    return {
      success: true,
      latency,
      categories: categorySummary,
      hasAnalysis: !!result.analysis,
      metadata: {
        ticker: result.ticker,
        name: result.name,
        period: result.period,
        updatedAt: result.updatedAt,
      },
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 测试综合数据获取
 */
async function testComprehensiveData(ticker: string) {
  const startTime = Date.now();

  try {
    const result = await dataService.getComprehensiveData(ticker, {
      includeStock: true,
      includeNews: true,
      includeTechnical: true,
      includeFundamental: true,
      stockPeriod: '1m',
      newsLimit: 5,
      technicalIndicators: ['ma', 'rsi'],
      fundamentalType: 'valuation',
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    const dataSummary = Object.keys(result).reduce((acc, key) => {
      if (key !== 'ticker' && key !== 'timestamp') {
        const data = result[key];
        acc[key] = {
          success: !data.error,
          hasData: !!data.data || !!data.news || !!data.indicators || !!data.fundamentals,
          error: data.error || null,
        };
      }
      return acc;
    }, {} as any);

    return {
      success: true,
      latency,
      dataSummary,
      ticker: result.ticker,
      timestamp: result.timestamp,
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 测试批量数据获取
 */
async function testBatchData(tickers: string[]) {
  const startTime = Date.now();

  try {
    const result = await dataService.getBatchStockData(tickers, {
      period: '1m',
      dataType: 'stock',
    });

    const endTime = Date.now();
    const latency = endTime - startTime;

    const batchSummary = Object.keys(result).reduce((acc, ticker) => {
      const data = result[ticker];
      acc[ticker] = {
        success: !data.error,
        dataPoints: data.data?.length || 0,
        error: data.error || null,
      };
      return acc;
    }, {} as any);

    return {
      success: true,
      latency,
      tickerCount: tickers.length,
      batchSummary,
    };
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;

    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : '未知错误',
    };
  }
}

/**
 * 生成测试摘要
 */
function generateTestSummary(tests: any) {
  const testResults = Object.values(tests);
  const successCount = testResults.filter((test: any) => test.success).length;
  const totalCount = testResults.length;

  const avgLatency =
    testResults.reduce((sum: number, test: any) => sum + (test.latency || 0), 0) / totalCount;

  return {
    totalTests: totalCount,
    successfulTests: successCount,
    failedTests: totalCount - successCount,
    successRate: totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(1) + '%' : '0%',
    averageLatency: Math.round(avgLatency) + 'ms',
    status:
      successCount === totalCount
        ? 'all_passed'
        : successCount > 0
        ? 'partial_success'
        : 'all_failed',
  };
}
