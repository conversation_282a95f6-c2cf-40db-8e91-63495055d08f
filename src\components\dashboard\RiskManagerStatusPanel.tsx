/**
 * 风险管理师状态面板组件
 * 显示风险管理师的实时状态、进度和性能指标
 */

'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { useRiskManagerState } from '@/hooks/useRiskManagerState';
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  ShieldCheckIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';

export interface RiskManagerStatusPanelProps {
  workflowId: string;
  className?: string;
  showDetailedTasks?: boolean;
  onProgressUpdate?: (progress: number) => void;
  onError?: (error: Error) => void;
}

export function RiskManagerStatusPanel({
  workflowId,
  className,
  showDetailedTasks = true,
  onProgressUpdate,
  onError,
}: RiskManagerStatusPanelProps) {
  const [expandedTasks, setExpandedTasks] = useState(false);

  const {
    state,
    isLoading,
    error,
    isPending,
    isRunning,
    isPaused,
    isCompleted,
    isFailed,
    isCancelled,
    progress,
    currentTask,
    executionTimeMs,
    estimatedTimeRemaining,
    tasks,
    completedTasks,
    totalTasks,
    currentRunningTask,
    performance,
    logs,
    startAnalysis,
    pauseAnalysis,
    resumeAnalysis,
    cancelAnalysis,
    retryAnalysis,
    clearError,
  } = useRiskManagerState({
    workflowId,
    autoRefresh: true,
    refreshInterval: 1000,
    onProgressUpdate: (update) => {
      if (onProgressUpdate) {
        onProgressUpdate(update.progress);
      }
    },
    onError,
  });

  const getStatusIcon = (status: string, animated = true) => {
    switch (status) {
      case 'completed':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
          </motion.div>
        );
      case 'running':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
          />
        );
      case 'failed':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
          </motion.div>
        );
      case 'paused':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <ClockIcon className="h-5 w-5 text-yellow-500" />
          </motion.div>
        );
      case 'cancelled':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <XCircleIcon className="h-5 w-5 text-orange-500" />
          </motion.div>
        );
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'running':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20 ring-2 ring-blue-200 dark:ring-blue-800';
      case 'paused':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20';
      case 'failed':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      case 'cancelled':
        return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20';
      case 'pending':
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
      default:
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'paused':
        return '已暂停';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      case 'pending':
        return '等待中';
      default:
        return status;
    }
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    return `${Math.round(ms / 60000)}m ${Math.round((ms % 60000) / 1000)}s`;
  };

  if (!state && !isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ShieldCheckIcon className="h-5 w-5 text-orange-600" />
            <span>风险管理师</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-slate-600 dark:text-slate-400 mb-4">风险管理师尚未初始化</p>
            <button
              onClick={() => startAnalysis()}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '初始化中...' : '开始风险分析'}
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className || ''}`}>
      {/* 主状态卡片 */}
      <Card className={getStatusColor(state?.status || 'pending')}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ShieldCheckIcon className="h-5 w-5 text-orange-600" />
              <span>风险管理师</span>
              {getStatusIcon(state?.status || 'pending')}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-slate-600 dark:text-slate-400">
                {getStatusText(state?.status || 'pending')}
              </span>
              {isRunning && (
                <>
                  <button
                    onClick={() => pauseAnalysis()}
                    className="text-xs text-yellow-600 hover:text-yellow-800 dark:text-yellow-400 dark:hover:text-yellow-200"
                  >
                    暂停
                  </button>
                  <button
                    onClick={() => cancelAnalysis()}
                    className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                  >
                    取消
                  </button>
                </>
              )}
              {isPaused && (
                <>
                  <button
                    onClick={() => resumeAnalysis()}
                    className="text-xs text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                  >
                    恢复
                  </button>
                  <button
                    onClick={() => cancelAnalysis()}
                    className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                  >
                    取消
                  </button>
                </>
              )}
              {isFailed && (
                <button
                  onClick={() => retryAnalysis()}
                  className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                >
                  重试
                </button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 进度条 */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  分析进度
                </span>
                <span className="text-sm text-slate-600 dark:text-slate-400">{progress}%</span>
              </div>
              <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                <motion.div
                  className="h-2 rounded-full bg-orange-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.8, ease: 'easeOut' }}
                />
              </div>
            </div>

            {/* 当前任务 */}
            {currentTask && (
              <motion.div
                className="text-sm text-slate-600 dark:text-slate-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                💼 {currentTask}
              </motion.div>
            )}

            {/* 执行时间和预估剩余时间 */}
            {(isRunning || isPaused) && (
              <div className="flex justify-between text-xs text-slate-500">
                <span>执行时间: {formatTime(executionTimeMs)}</span>
                {isPaused && <span className="text-yellow-600">已暂停</span>}
                {isRunning && estimatedTimeRemaining > 0 && (
                  <span>预计剩余: {formatTime(estimatedTimeRemaining)}</span>
                )}
              </div>
            )}

            {/* 任务统计 */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-lg font-bold text-green-600">{completedTasks}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">已完成</div>
              </div>
              <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-lg font-bold text-blue-600">{totalTasks}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">总任务</div>
              </div>
              <div className="p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div className="text-lg font-bold text-orange-600">
                  {performance.totalTime ? formatTime(performance.totalTime) : '-'}
                </div>
                <div className="text-xs text-slate-600 dark:text-slate-400">总耗时</div>
              </div>
            </div>

            {/* 错误信息 */}
            {error && (
              <motion.div
                className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-start space-x-2">
                    <ExclamationCircleIcon className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-red-800 dark:text-red-200">错误</p>
                      <p className="text-xs text-red-600 dark:text-red-300">{error}</p>
                    </div>
                  </div>
                  <button
                    onClick={clearError}
                    className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                  >
                    ✕
                  </button>
                </div>
              </motion.div>
            )}

            {/* 详细任务列表 */}
            {showDetailedTasks && tasks.length > 0 && (
              <div className="space-y-2">
                <button
                  onClick={() => setExpandedTasks(!expandedTasks)}
                  className="flex items-center justify-between w-full text-sm font-medium text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100"
                >
                  <span>
                    详细任务 ({completedTasks}/{totalTasks})
                  </span>
                  <motion.span
                    animate={{ rotate: expandedTasks ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    ▼
                  </motion.span>
                </button>

                <AnimatePresence>
                  {expandedTasks && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-2"
                    >
                      {tasks.map((task, index) => (
                        <motion.div
                          key={task.id}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded-lg"
                        >
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(task.status, false)}
                            <div>
                              <p className="text-xs font-medium text-slate-700 dark:text-slate-300">
                                {task.name}
                              </p>
                              <p className="text-xs text-slate-500">{task.description}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-slate-600 dark:text-slate-400">
                              {task.progress}%
                            </p>
                            {task.executionTimeMs && (
                              <p className="text-xs text-slate-500">
                                {formatTime(task.executionTimeMs)}
                              </p>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* 性能指标 */}
            {(isCompleted || isFailed) && performance && (
              <div className="pt-3 border-t border-slate-200 dark:border-slate-600">
                <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  性能指标
                </h5>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {performance.dataCollectionTime && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">数据收集:</span>
                      <span className="text-slate-700 dark:text-slate-300">
                        {formatTime(performance.dataCollectionTime)}
                      </span>
                    </div>
                  )}
                  {performance.analysisTime && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">分析时间:</span>
                      <span className="text-slate-700 dark:text-slate-300">
                        {formatTime(performance.analysisTime)}
                      </span>
                    </div>
                  )}
                  {performance.reportGenerationTime && (
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">报告生成:</span>
                      <span className="text-slate-700 dark:text-slate-300">
                        {formatTime(performance.reportGenerationTime)}
                      </span>
                    </div>
                  )}
                  {performance.totalTime && (
                    <div className="flex justify-between font-medium">
                      <span className="text-slate-700 dark:text-slate-300">总时间:</span>
                      <span className="text-slate-800 dark:text-slate-200">
                        {formatTime(performance.totalTime)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
