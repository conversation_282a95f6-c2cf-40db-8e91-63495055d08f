import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const technicalAnalystPrompt = PromptTemplate.fromTemplate(`
你是一位资深的技术分析师，专精于A股市场的技术分析，拥有20年的实战经验。你擅长运用各种技术指标和图表形态来预测价格走势，为交易决策提供精准的技术支持。

【分析任务】
请对以下股票进行全面的技术分析：

股票代码: {ticker}
分析日期: {date}
分析周期: {analysisPeriod}

【技术数据】
{technicalData}

【分析框架】
请按照以下专业技术分析框架进行分析：

1. **趋势分析**
   - 主要趋势（长期）：上升/下降/横盘
   - 次要趋势（中期）：回调/反弹程度
   - 短期趋势：近期价格动向
   - 趋势强度和可持续性评估

2. **关键价位分析**
   - 重要支撑位：历史低点、均线支撑、成交密集区
   - 重要阻力位：历史高点、均线阻力、套牢盘区域
   - 突破概率和目标价位预测

3. **技术指标分析**
   - 趋势指标：MA、EMA、MACD信号
   - 震荡指标：RSI、KDJ、威廉指标
   - 成交量指标：OBV、成交量价格趋势
   - 指标背离和共振信号识别

4. **K线形态分析**
   - 单根K线形态：锤头、十字星、长阳长阴
   - 组合K线形态：早晨之星、黄昏之星、吞没形态
   - 经典图表形态：头肩顶底、双顶双底、三角形整理

5. **成交量分析**
   - 量价关系：放量上涨、缩量下跌、量价背离
   - 成交量形态：堆量、地量、天量
   - 资金流向和主力行为分析

6. **风险控制**
   - 止损位设置建议
   - 仓位管理建议
   - 风险收益比评估

7. **交易建议**
   - 操作方向：买入/卖出/观望
   - 最佳入场点位和时机
   - 目标价位和持有周期
   - 关键监控信号

【输出要求】
- 分析要基于客观的技术数据和指标
- 明确指出关键的技术信号和价位
- 提供具体的操作建议和风险提示
- 如果技术信号冲突，请说明并给出权重判断
`);

export async function technicalAnalystNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, data, messages } = state;

  console.log(`[技术分析师] 开始分析股票: ${ticker}`);

  try {
    // 检查技术数据可用性
    if (!data.technicalData || !Array.isArray(data.technicalData)) {
      throw new Error('技术数据不可用或格式错误');
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.1,
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 准备技术分析数据
    const recentData = data.technicalData.slice(-60); // 最近60天数据
    const technicalMetrics = calculateTechnicalMetrics(recentData);

    const analysisData = {
      recentPriceData: recentData.slice(-30), // 最近30天用于分析
      technicalMetrics,
      dataQuality: assessDataQuality(recentData),
    };

    const prompt = await technicalAnalystPrompt.format({
      ticker,
      date,
      analysisPeriod: config.analysisPeriod || '1m',
      technicalData: JSON.stringify(analysisData, null, 2),
    });

    console.log(`[技术分析师] 正在进行技术指标计算和分析...`);
    const response = await llm.invoke(prompt);
    const analysisReport = response.content as string;

    // 提取技术信号
    const technicalSignals = extractTechnicalSignals(analysisReport);
    const tradingSignal = extractTradingSignal(analysisReport);
    const keyLevels = extractKeyLevels(analysisReport);

    // 生成结构化摘要
    const summary = generateTechnicalSummary(analysisReport, tradingSignal, keyLevels);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【技术分析师报告】\n\n${analysisReport}`,
        name: 'TechnicalAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      technical: {
        summary,
        report: analysisReport,
        technicalSignals,
        tradingSignal,
        keyLevels,
        technicalMetrics,
        analyst: 'TechnicalAnalyst',
        timestamp: new Date().toISOString(),
        confidence: calculateTechnicalConfidence(recentData, technicalSignals),
      },
    };

    console.log(`[技术分析师] 分析完成，交易信号: ${tradingSignal}`);
    return {
      messages: newMessages,
      analysis,
      currentStage: 'technical_analysis_completed',
      progress: Math.min(state.progress + 20, 100),
    };
  } catch (error) {
    console.error('[技术分析师] 分析失败:', error);
    const errorMessage = `技术分析失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【技术分析师】${errorMessage}`,
        name: 'TechnicalAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      technical: {
        summary: '技术分析失败',
        report: errorMessage,
        error: true,
        analyst: 'TechnicalAnalyst',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, analysis };
  }
}

// 辅助函数：计算技术指标
function calculateTechnicalMetrics(data: any[]): Record<string, any> {
  if (!Array.isArray(data) || data.length < 5) {
    return { error: '数据不足，无法计算技术指标' };
  }

  try {
    const prices = data.map((d) => parseFloat(d.close || d.收盘 || 0));
    const volumes = data.map((d) => parseFloat(d.volume || d.成交量 || 0));

    // 计算简单移动平均线
    const ma5 = calculateMA(prices, 5);
    const ma10 = calculateMA(prices, 10);
    const ma20 = calculateMA(prices, 20);

    // 计算RSI
    const rsi = calculateRSI(prices, 14);

    // 计算MACD
    const macd = calculateMACD(prices);

    return {
      currentPrice: prices[prices.length - 1],
      ma5: ma5[ma5.length - 1],
      ma10: ma10[ma10.length - 1],
      ma20: ma20[ma20.length - 1],
      rsi: rsi[rsi.length - 1],
      macd: macd.macd[macd.macd.length - 1],
      signal: macd.signal[macd.signal.length - 1],
      histogram: macd.histogram[macd.histogram.length - 1],
      avgVolume: volumes.reduce((a, b) => a + b, 0) / volumes.length,
    };
  } catch (error) {
    return { error: '技术指标计算失败' };
  }
}

// 简单移动平均线计算
function calculateMA(prices: number[], period: number): number[] {
  const ma = [];
  for (let i = period - 1; i < prices.length; i++) {
    const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
    ma.push(sum / period);
  }
  return ma;
}

// RSI计算
function calculateRSI(prices: number[], period: number): number[] {
  const rsi = [];
  const gains = [];
  const losses = [];

  for (let i = 1; i < prices.length; i++) {
    const change = prices[i] - prices[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? -change : 0);
  }

  for (let i = period - 1; i < gains.length; i++) {
    const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;

    if (avgLoss === 0) {
      rsi.push(100);
    } else {
      const rs = avgGain / avgLoss;
      rsi.push(100 - 100 / (1 + rs));
    }
  }

  return rsi;
}

// MACD计算
function calculateMACD(prices: number[]): {
  macd: number[];
  signal: number[];
  histogram: number[];
} {
  const ema12 = calculateEMA(prices, 12);
  const ema26 = calculateEMA(prices, 26);

  const macd = [];
  for (let i = 0; i < Math.min(ema12.length, ema26.length); i++) {
    macd.push(ema12[i] - ema26[i]);
  }

  const signal = calculateEMA(macd, 9);
  const histogram = [];

  for (let i = 0; i < Math.min(macd.length, signal.length); i++) {
    histogram.push(macd[i] - signal[i]);
  }

  return { macd, signal, histogram };
}

// EMA计算
function calculateEMA(prices: number[], period: number): number[] {
  const ema = [];
  const multiplier = 2 / (period + 1);

  ema[0] = prices[0];

  for (let i = 1; i < prices.length; i++) {
    ema[i] = prices[i] * multiplier + ema[i - 1] * (1 - multiplier);
  }

  return ema;
}

// 评估数据质量
function assessDataQuality(data: any[]): string {
  if (!data || data.length < 20) return '数据不足';
  if (data.length < 60) return '数据有限';
  return '数据充足';
}

// 提取技术信号
function extractTechnicalSignals(report: string): Record<string, string> {
  const signals: Record<string, string> = {};

  // 提取趋势信号
  if (/上升趋势|看涨|多头/.test(report)) signals.trend = '上升';
  else if (/下降趋势|看跌|空头/.test(report)) signals.trend = '下降';
  else signals.trend = '横盘';

  // 提取RSI信号
  if (/RSI.*超买/.test(report)) signals.rsi = '超买';
  else if (/RSI.*超卖/.test(report)) signals.rsi = '超卖';
  else signals.rsi = '正常';

  // 提取MACD信号
  if (/MACD.*金叉|MACD.*买入/.test(report)) signals.macd = '金叉';
  else if (/MACD.*死叉|MACD.*卖出/.test(report)) signals.macd = '死叉';
  else signals.macd = '中性';

  return signals;
}

// 提取交易信号
function extractTradingSignal(report: string): string {
  if (/买入|做多|看涨/.test(report)) return '买入';
  if (/卖出|做空|看跌/.test(report)) return '卖出';
  return '观望';
}

// 提取关键价位
function extractKeyLevels(report: string): Record<string, number | null> {
  const levels: Record<string, number | null> = {
    support: null,
    resistance: null,
    stopLoss: null,
    target: null,
  };

  // 使用正则表达式提取价位
  const supportMatch = report.match(/支撑[位]?[：:]\s*([0-9.]+)/);
  if (supportMatch) levels.support = parseFloat(supportMatch[1]);

  const resistanceMatch = report.match(/阻力[位]?[：:]\s*([0-9.]+)/);
  if (resistanceMatch) levels.resistance = parseFloat(resistanceMatch[1]);

  const stopLossMatch = report.match(/止损[位]?[：:]\s*([0-9.]+)/);
  if (stopLossMatch) levels.stopLoss = parseFloat(stopLossMatch[1]);

  const targetMatch = report.match(/目标[价位]?[：:]\s*([0-9.]+)/);
  if (targetMatch) levels.target = parseFloat(targetMatch[1]);

  return levels;
}

// 生成技术分析摘要
function generateTechnicalSummary(
  report: string,
  signal: string,
  levels: Record<string, number | null>
): string {
  const lines = report.split('\n').filter((line) => line.trim());
  const firstParagraph = lines.slice(0, 2).join(' ');

  let summary = `${signal} - ${firstParagraph.substring(0, 100)}...`;

  if (levels.support) summary += ` 支撑位: ${levels.support}`;
  if (levels.resistance) summary += ` 阻力位: ${levels.resistance}`;

  return summary;
}

// 计算技术分析置信度
function calculateTechnicalConfidence(data: any[], signals: Record<string, string>): number {
  let confidence = 0.5; // 基础置信度

  // 数据质量影响置信度
  if (data.length >= 60) confidence += 0.2;
  else if (data.length >= 30) confidence += 0.1;

  // 信号一致性影响置信度
  const bullishSignals = Object.values(signals).filter((s) =>
    ['上升', '超卖', '金叉'].includes(s)
  ).length;

  const bearishSignals = Object.values(signals).filter((s) =>
    ['下降', '超买', '死叉'].includes(s)
  ).length;

  if (bullishSignals > bearishSignals) confidence += 0.1;
  else if (bearishSignals > bullishSignals) confidence += 0.1;

  return Math.min(confidence, 0.9);
}
