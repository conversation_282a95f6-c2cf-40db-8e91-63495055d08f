/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-19 16:29:04
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 20:50:07
 * @FilePath: \trading-agents-frontend\src\app\api\auth\login\route.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { error, success } from '@/lib/api-helpers';
import { generateTokens, setAuthCookies, validateEmail, verifyPassword } from '@/lib/auth';
import { createUserSession, getUserByEmail, logUserActivity, updateLastLogin } from '@/lib/user-db';
import { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // 验证输入
    if (!email || !password) {
      return error('请填写邮箱和密码', 400);
    }

    // 验证邮箱格式
    if (!validateEmail(email)) {
      return error('请输入有效的邮箱地址', 400);
    }

    // 获取用户
    const user = await getUserByEmail(email);
    if (!user) {
      return error('邮箱或密码错误', 401);
    }

    // 检查账户是否激活
    if (!user.is_active) {
      return error('账户已被禁用，请联系管理员', 403);
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return error('邮箱或密码错误', 401);
    }

    // 获取客户端信息
    const ipAddress = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // 更新最后登录时间
    await updateLastLogin(user.id);

    // 记录登录活动
    await logUserActivity(user.id, 'login', ipAddress, userAgent, { email: user.email });

    // 生成token并设置cookie
    const { accessToken, refreshToken } = await generateTokens(user.id, user.email);

    // 创建会话
    const sessionId = uuidv4();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15分钟
    const refreshExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天

    await createUserSession(
      user.id,
      sessionId,
      accessToken,
      refreshToken,
      expiresAt,
      refreshExpiresAt,
      ipAddress,
      userAgent
    );

    // 设置认证cookie
    await setAuthCookies(accessToken, refreshToken);

    const response = success(
      {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          email_verified: user.email_verified,
          role: user.role,
        },
        sessionId,
      },
      '登录成功'
    );

    // 添加CORS头
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    response.headers.set('Access-Control-Allow-Origin', '*');

    return response;
  } catch (err) {
    console.error('Login error:', err);
    return error('服务器内部错误', 500);
  }
}
