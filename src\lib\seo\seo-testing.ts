/**
 * SEO 测试工具
 * 用于测试和验证页面的 SEO 实现
 */

import { generatePageStructuredData } from './structured-data-generator';
import {
  validateStructuredData,
  validateMultipleStructuredData,
} from './structured-data-validator';
import { MetadataGenerator } from './metadata-generator';
import { SEOUtils } from './utils';
import { PageType, SupportedLocale } from '@/types/seo';

export interface SEOTestResult {
  page: PageType;
  locale: SupportedLocale;
  passed: boolean;
  score: number;
  results: {
    metadata: {
      passed: boolean;
      score: number;
      errors: string[];
      warnings: string[];
    };
    structuredData: {
      passed: boolean;
      score: number;
      errors: string[];
      warnings: string[];
      validatedSchemas: string[];
    };
    performance: {
      passed: boolean;
      score: number;
      metrics: {
        metadataSize: number;
        structuredDataSize: number;
        totalScripts: number;
      };
    };
  };
}

export interface SEOTestOptions {
  validateStructuredData?: boolean;
  checkPerformance?: boolean;
  strictMode?: boolean;
  locale?: SupportedLocale;
}

/**
 * SEO 测试器类
 */
export class SEOTester {
  private options: SEOTestOptions;

  constructor(options: SEOTestOptions = {}) {
    this.options = {
      validateStructuredData: true,
      checkPerformance: true,
      strictMode: false,
      locale: 'zh',
      ...options,
    };
  }

  /**
   * 测试页面的 SEO 实现
   */
  async testPageSEO(
    page: PageType,
    dynamicData?: any,
    locale: SupportedLocale = 'zh'
  ): Promise<SEOTestResult> {
    const result: SEOTestResult = {
      page,
      locale,
      passed: false,
      score: 0,
      results: {
        metadata: {
          passed: false,
          score: 0,
          errors: [],
          warnings: [],
        },
        structuredData: {
          passed: false,
          score: 0,
          errors: [],
          warnings: [],
          validatedSchemas: [],
        },
        performance: {
          passed: false,
          score: 0,
          metrics: {
            metadataSize: 0,
            structuredDataSize: 0,
            totalScripts: 0,
          },
        },
      },
    };

    try {
      // 测试元数据
      const metadataResult = await this.testMetadata(page, dynamicData, locale);
      result.results.metadata = metadataResult;

      // 测试结构化数据
      if (this.options.validateStructuredData) {
        const structuredDataResult = await this.testStructuredData(page, dynamicData, locale);
        result.results.structuredData = structuredDataResult;
      }

      // 测试性能
      if (this.options.checkPerformance) {
        const performanceResult = await this.testPerformance(page, dynamicData, locale);
        result.results.performance = performanceResult;
      }

      // 计算总分
      const totalScore = Math.round(
        (result.results.metadata.score +
          result.results.structuredData.score +
          result.results.performance.score) /
          3
      );

      result.score = totalScore;
      result.passed = totalScore >= 70; // 70分以上算通过
    } catch (error) {
      result.results.metadata.errors.push(`Test execution error: ${error}`);
    }

    return result;
  }

  /**
   * 测试元数据
   */
  private async testMetadata(page: PageType, dynamicData?: any, locale: SupportedLocale = 'zh') {
    const result = {
      passed: false,
      score: 0,
      errors: [] as string[],
      warnings: [] as string[],
    };

    try {
      const generator = new MetadataGenerator(locale);
      const metadata = generator.generatePageMetadata({ page, dynamicData, locale });

      // 检查标题
      if (!metadata.title) {
        result.errors.push('Missing page title');
      } else {
        const titleLength =
          typeof metadata.title === 'string'
            ? metadata.title.length
            : JSON.stringify(metadata.title).length;

        if (titleLength > 60) {
          result.warnings.push(`Title too long: ${titleLength} characters`);
        } else if (titleLength < 10) {
          result.warnings.push(`Title too short: ${titleLength} characters`);
        } else {
          result.score += 25;
        }
      }

      // 检查描述
      if (!metadata.description) {
        result.errors.push('Missing page description');
      } else {
        const descLength = metadata.description.length;
        if (descLength > 160) {
          result.warnings.push(`Description too long: ${descLength} characters`);
        } else if (descLength < 50) {
          result.warnings.push(`Description too short: ${descLength} characters`);
        } else {
          result.score += 25;
        }
      }

      // 检查 Open Graph
      if (metadata.openGraph) {
        result.score += 25;
        if (!metadata.openGraph.images) {
          result.warnings.push('Missing Open Graph image');
        }
      } else {
        result.errors.push('Missing Open Graph data');
      }

      // 检查 Twitter Card
      if (metadata.twitter) {
        result.score += 25;
      } else {
        result.warnings.push('Missing Twitter Card data');
      }

      result.passed = result.errors.length === 0;
    } catch (error) {
      result.errors.push(`Metadata generation error: ${error}`);
    }

    return result;
  }

  /**
   * 测试结构化数据
   */
  private async testStructuredData(
    page: PageType,
    dynamicData?: any,
    locale: SupportedLocale = 'zh'
  ) {
    const result = {
      passed: false,
      score: 0,
      errors: [] as string[],
      warnings: [] as string[],
      validatedSchemas: [] as string[],
    };

    try {
      const structuredData = generatePageStructuredData(page, locale, dynamicData);
      const dataArray = Object.entries(structuredData).map(([key, data]) => ({ key, data }));

      if (dataArray.length === 0) {
        result.errors.push('No structured data generated');
        return result;
      }

      let totalValidationScore = 0;
      let validSchemas = 0;

      // 验证每个结构化数据对象
      for (const { key, data } of dataArray) {
        if (data) {
          const validation = validateStructuredData(data);
          result.validatedSchemas.push(key);

          if (validation.isValid) {
            validSchemas++;
            totalValidationScore += validation.score;
          } else {
            result.errors.push(`Invalid ${key} schema: ${validation.errors.join(', ')}`);
          }

          if (validation.warnings.length > 0) {
            result.warnings.push(`${key} warnings: ${validation.warnings.join(', ')}`);
          }
        }
      }

      // 计算分数
      if (validSchemas > 0) {
        result.score = Math.round(totalValidationScore / validSchemas);
        result.passed = result.score >= 70;
      }

      // 检查必需的结构化数据
      const requiredSchemas = ['organization'];
      const missingRequired = requiredSchemas.filter(
        (schema) => !result.validatedSchemas.includes(schema)
      );

      if (missingRequired.length > 0) {
        result.errors.push(`Missing required schemas: ${missingRequired.join(', ')}`);
      }
    } catch (error) {
      result.errors.push(`Structured data generation error: ${error}`);
    }

    return result;
  }

  /**
   * 测试性能
   */
  private async testPerformance(page: PageType, dynamicData?: any, locale: SupportedLocale = 'zh') {
    const result = {
      passed: false,
      score: 0,
      metrics: {
        metadataSize: 0,
        structuredDataSize: 0,
        totalScripts: 0,
      },
    };

    try {
      // 测试元数据大小
      const generator = new MetadataGenerator(locale);
      const metadata = generator.generatePageMetadata({ page, dynamicData, locale });
      const metadataJson = JSON.stringify(metadata);
      result.metrics.metadataSize = new Blob([metadataJson]).size;

      // 测试结构化数据大小
      const structuredData = generatePageStructuredData(page, locale, dynamicData);
      const structuredDataJson = JSON.stringify(structuredData);
      result.metrics.structuredDataSize = new Blob([structuredDataJson]).size;
      result.metrics.totalScripts = Object.keys(structuredData).length;

      // 性能评分
      let performanceScore = 100;

      // 元数据大小检查（应该小于 5KB）
      if (result.metrics.metadataSize > 5120) {
        performanceScore -= 20;
      }

      // 结构化数据大小检查（应该小于 50KB）
      if (result.metrics.structuredDataSize > 51200) {
        performanceScore -= 30;
      }

      // 脚本数量检查（应该少于 10 个）
      if (result.metrics.totalScripts > 10) {
        performanceScore -= 20;
      }

      result.score = Math.max(0, performanceScore);
      result.passed = result.score >= 70;
    } catch (error) {
      result.score = 0;
    }

    return result;
  }

  /**
   * 批量测试多个页面
   */
  async testMultiplePages(
    pages: Array<{
      page: PageType;
      dynamicData?: any;
      locale?: SupportedLocale;
    }>
  ): Promise<SEOTestResult[]> {
    const results: SEOTestResult[] = [];

    for (const pageConfig of pages) {
      const result = await this.testPageSEO(
        pageConfig.page,
        pageConfig.dynamicData,
        pageConfig.locale || this.options.locale
      );
      results.push(result);
    }

    return results;
  }

  /**
   * 生成测试报告
   */
  generateTestReport(results: SEOTestResult | SEOTestResult[]): string {
    const resultsArray = Array.isArray(results) ? results : [results];
    const lines: string[] = [];

    lines.push('=== SEO 测试报告 ===');
    lines.push('');

    if (resultsArray.length > 1) {
      // 多页面测试汇总
      const totalScore = Math.round(
        resultsArray.reduce((sum, r) => sum + r.score, 0) / resultsArray.length
      );
      const passedCount = resultsArray.filter((r) => r.passed).length;

      lines.push(`总体评分: ${totalScore}/100`);
      lines.push(`通过页面: ${passedCount}/${resultsArray.length}`);
      lines.push('');
    }

    // 详细结果
    resultsArray.forEach((result, index) => {
      if (resultsArray.length > 1) {
        lines.push(`## 页面 ${index + 1}: ${result.page} (${result.locale})`);
      } else {
        lines.push(`## 页面: ${result.page} (${result.locale})`);
      }

      lines.push(`状态: ${result.passed ? '✅ 通过' : '❌ 失败'}`);
      lines.push(`评分: ${result.score}/100`);
      lines.push('');

      // 元数据测试结果
      lines.push('### 元数据测试');
      lines.push(`评分: ${result.results.metadata.score}/100`);
      if (result.results.metadata.errors.length > 0) {
        lines.push('错误:');
        result.results.metadata.errors.forEach((error) => lines.push(`  - ${error}`));
      }
      if (result.results.metadata.warnings.length > 0) {
        lines.push('警告:');
        result.results.metadata.warnings.forEach((warning) => lines.push(`  - ${warning}`));
      }
      lines.push('');

      // 结构化数据测试结果
      lines.push('### 结构化数据测试');
      lines.push(`评分: ${result.results.structuredData.score}/100`);
      lines.push(`验证的模式: ${result.results.structuredData.validatedSchemas.join(', ')}`);
      if (result.results.structuredData.errors.length > 0) {
        lines.push('错误:');
        result.results.structuredData.errors.forEach((error) => lines.push(`  - ${error}`));
      }
      if (result.results.structuredData.warnings.length > 0) {
        lines.push('警告:');
        result.results.structuredData.warnings.forEach((warning) => lines.push(`  - ${warning}`));
      }
      lines.push('');

      // 性能测试结果
      lines.push('### 性能测试');
      lines.push(`评分: ${result.results.performance.score}/100`);
      lines.push(`元数据大小: ${result.results.performance.metrics.metadataSize} bytes`);
      lines.push(`结构化数据大小: ${result.results.performance.metrics.structuredDataSize} bytes`);
      lines.push(`脚本数量: ${result.results.performance.metrics.totalScripts}`);
      lines.push('');
    });

    return lines.join('\n');
  }
}

/**
 * 便捷函数：测试单个页面
 */
export const testPageSEO = async (
  page: PageType,
  dynamicData?: any,
  locale: SupportedLocale = 'zh',
  options?: SEOTestOptions
): Promise<SEOTestResult> => {
  const tester = new SEOTester(options);
  return await tester.testPageSEO(page, dynamicData, locale);
};

/**
 * 便捷函数：测试所有主要页面
 */
export const testAllPagesSEO = async (
  locale: SupportedLocale = 'zh',
  options?: SEOTestOptions
): Promise<SEOTestResult[]> => {
  const tester = new SEOTester(options);

  const pages: Array<{
    page: PageType;
    dynamicData?: any;
    locale?: SupportedLocale;
  }> = [
    { page: 'home', locale },
    { page: 'analysis', dynamicData: { stockSymbol: 'AAPL', analysisId: 'test-123' }, locale },
    { page: 'tasks', locale },
    { page: 'messages', locale },
    { page: 'create-task', locale },
  ];

  return await tester.testMultiplePages(pages);
};
