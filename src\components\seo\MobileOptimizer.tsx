'use client';

import { MobilePerformanceMetrics, mobileSEOOptimizer } from '@/lib/seo/mobile-seo-optimizer';
import { useEffect, useState } from 'react';

interface MobileFriendlyTestResult {
  passed: boolean;
  issues: string[];
  recommendations: string[];
}

function MobileOptimizerComponent() {
  const [deviceInfo, setDeviceInfo] = useState<{
    isMobile: boolean;
    isTablet: boolean;
    deviceType: string;
  } | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<MobilePerformanceMetrics | null>(
    null
  );
  const [friendlyTest, setFriendlyTest] = useState<MobileFriendlyTestResult | null>(null);

  useEffect(() => {
    // 初始化移动端优化
    mobileSEOOptimizer.initialize();

    // 获取设备信息
    const device = mobileSEOOptimizer.detectMobileDevice();
    setDeviceInfo(device);

    // 运行移动端友好性测试
    const runTests = async () => {
      const testResult = await mobileSEOOptimizer.runMobileFriendlyTest();
      setFriendlyTest(testResult);

      // 获取性能指标
      const metrics = await mobileSEOOptimizer.getMobilePerformanceMetrics();
      setPerformanceMetrics(metrics);
    };

    // 延迟运行测试，确保页面完全加载
    setTimeout(runTests, 2000);
  }, []);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-20 right-4 bg-blue-900 text-white p-4 rounded-lg shadow-lg max-w-sm text-xs z-50">
      <div className="font-semibold mb-2">移动端 SEO 监控</div>

      {deviceInfo && (
        <div className="mb-2">
          <div className="text-blue-200">设备类型: {deviceInfo.deviceType}</div>
          <div className="text-blue-200">
            移动端: {deviceInfo.isMobile ? '是' : '否'} | 平板: {deviceInfo.isTablet ? '是' : '否'}
          </div>
        </div>
      )}

      {friendlyTest && (
        <div className="mb-2">
          <div
            className={`font-semibold ${friendlyTest.passed ? 'text-green-400' : 'text-red-400'}`}
          >
            移动端友好性: {friendlyTest.passed ? '通过' : '未通过'}
          </div>
          {friendlyTest.issues.length > 0 && (
            <div className="text-red-300 text-xs mt-1">
              问题: {friendlyTest.issues.slice(0, 2).join(', ')}
              {friendlyTest.issues.length > 2 && '...'}
            </div>
          )}
        </div>
      )}

      {performanceMetrics && (
        <div className="space-y-1">
          <div className="text-blue-200">性能指标:</div>
          <div className="grid grid-cols-2 gap-1 text-xs">
            <div>FCP: {Math.round(performanceMetrics.firstContentfulPaint)}ms</div>
            <div>LCP: {Math.round(performanceMetrics.largestContentfulPaint)}ms</div>
            <div>FID: {Math.round(performanceMetrics.firstInputDelay)}ms</div>
            <div>CLS: {performanceMetrics.cumulativeLayoutShift.toFixed(3)}</div>
          </div>
        </div>
      )}
    </div>
  );
}

function MobileViewportComponent() {
  useEffect(() => {
    const viewportConfig = mobileSEOOptimizer.generateViewportConfig();

    // 检查是否已存在视口标签
    let viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;

    if (!viewportMeta) {
      viewportMeta = document.createElement('meta');
      viewportMeta.name = 'viewport';
      document.head.appendChild(viewportMeta);
    }

    // 更新视口内容
    const viewportContent = [
      `width=${viewportConfig.width}`,
      `initial-scale=${viewportConfig.initialScale}`,
      viewportConfig.maximumScale && `maximum-scale=${viewportConfig.maximumScale}`,
      viewportConfig.minimumScale && `minimum-scale=${viewportConfig.minimumScale}`,
      viewportConfig.userScalable !== undefined &&
        `user-scalable=${viewportConfig.userScalable ? 'yes' : 'no'}`,
      viewportConfig.viewportFit && `viewport-fit=${viewportConfig.viewportFit}`,
    ]
      .filter(Boolean)
      .join(', ');

    viewportMeta.content = viewportContent;

    // 添加其他移动端元标签
    const mobileMetaTags = mobileSEOOptimizer.generateMobileMetaTags();
    mobileMetaTags.forEach((tag) => {
      let existingMeta = document.querySelector(`meta[name="${tag.name}"]`) as HTMLMetaElement;

      if (!existingMeta) {
        existingMeta = document.createElement('meta');
        existingMeta.name = tag.name;
        document.head.appendChild(existingMeta);
      }

      existingMeta.content = tag.content;
    });
  }, []);

  return null; // 这是一个效果组件，不渲染任何内容
}

function ResponsiveValidatorComponent() {
  const [breakpoints, setBreakpoints] = useState<{
    current: string;
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      let current = 'xl'; // >= 1280px
      if (width < 640) current = 'sm';
      else if (width < 768) current = 'md';
      else if (width < 1024) current = 'lg';
      else if (width < 1280) current = 'xl';
      else current = '2xl';

      setBreakpoints({ current, width, height });
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);

    return () => {
      window.removeEventListener('resize', updateBreakpoint);
    };
  }, []);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development' || !breakpoints) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 bg-purple-900 text-white p-2 rounded text-xs z-50">
      <div className="font-semibold">响应式断点</div>
      <div>
        {breakpoints.current} ({breakpoints.width}x{breakpoints.height})
      </div>
    </div>
  );
}

function TouchTargetValidatorComponent() {
  const [violations, setViolations] = useState<number>(0);

  useEffect(() => {
    const checkTouchTargets = () => {
      const touchTargets = document.querySelectorAll(
        'button, a, input[type="button"], input[type="submit"], [role="button"]'
      );
      let violationCount = 0;

      touchTargets.forEach((target) => {
        const rect = target.getBoundingClientRect();
        const element = target as HTMLElement;

        if (rect.width < 44 || rect.height < 44) {
          violationCount++;

          // 在开发环境中高亮显示问题元素
          if (process.env.NODE_ENV === 'development') {
            element.style.outline = '2px solid red';
            element.title = `触摸目标过小: ${Math.round(rect.width)}x${Math.round(
              rect.height
            )}px (建议: 44x44px)`;
          }
        } else {
          // 移除之前的高亮
          if (element.style.outline === '2px solid red') {
            element.style.outline = '';
            element.title = '';
          }
        }
      });

      setViolations(violationCount);
    };

    // 初始检查
    checkTouchTargets();

    // 监听 DOM 变化
    const observer = new MutationObserver(checkTouchTargets);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // 只在开发环境且有违规时显示
  if (process.env.NODE_ENV !== 'development' || violations === 0) {
    return null;
  }

  return (
    <div className="fixed top-16 left-4 bg-red-900 text-white p-2 rounded text-xs z-50">
      <div className="font-semibold">触摸目标问题</div>
      <div>{violations} 个目标过小 (44px)</div>
    </div>
  );
}

export const MobileOptimizer = MobileOptimizerComponent;
export const MobileViewport = MobileViewportComponent;
export const ResponsiveValidator = ResponsiveValidatorComponent;
export const TouchTargetValidator = TouchTargetValidatorComponent;

export default function MobileOptimizers() {
  return (
    <>
      <MobileOptimizerComponent />
      <MobileViewportComponent />
      <ResponsiveValidatorComponent />
      <TouchTargetValidatorComponent />
    </>
  );
}
