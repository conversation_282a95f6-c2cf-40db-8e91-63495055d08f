'use client';

import { KEYBOARD_SHORTCUTS, KeyboardShortcut } from '@/types/navigation';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef } from 'react';

interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  onShortcutUsed?: (shortcut: string, action: string) => void;
}

interface UseKeyboardShortcutsReturn {
  shortcuts: KeyboardShortcut[];
  isShortcutActive: (key: string) => boolean;
  enableShortcut: (key: string) => void;
  disableShortcut: (key: string) => void;
}

/**
 * Custom hook for managing keyboard shortcuts in the navigation system
 * Supports Alt+H, Alt+C, Alt+T shortcuts with conflict detection
 */
export function useKeyboardShortcuts(
  options: UseKeyboardShortcutsOptions = {}
): UseKeyboardShortcutsReturn {
  const { enabled = true, onShortcutUsed } = options;
  const router = useRouter();
  const activeShortcuts = useRef<Set<string>>(new Set());
  const conflictDetection = useRef<Map<string, string[]>>(new Map());

  // Track navigation events for analytics
  const trackShortcutUsage = useCallback(
    (shortcut: string, action: string) => {
      try {
        const event = {
          eventType: 'keyboard' as const,
          target: action,
          path: window.location.pathname,
          timestamp: new Date(),
          sessionId: getSessionId(),
          shortcut,
        };

        // Store in localStorage for analytics
        const events = JSON.parse(localStorage.getItem('navigationEvents') || '[]');
        events.push(event);

        if (events.length > 100) {
          events.splice(0, events.length - 100);
        }

        localStorage.setItem('navigationEvents', JSON.stringify(events));

        // Call callback if provided
        onShortcutUsed?.(shortcut, action);
      } catch (error) {
        console.error('Failed to track shortcut usage:', error);
      }
    },
    [onShortcutUsed]
  );

  // Define shortcut actions
  const shortcutActions = useMemo(
    () => ({
      [KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY]: () => {
        router.push('/analysis/history');
        trackShortcutUsage(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY, 'analysis-history');
      },
      [KEYBOARD_SHORTCUTS.ANALYSIS_COMPARE]: () => {
        router.push('/analysis/compare');
        trackShortcutUsage(KEYBOARD_SHORTCUTS.ANALYSIS_COMPARE, 'analysis-compare');
      },
      [KEYBOARD_SHORTCUTS.BACK_TO_TASKS]: () => {
        router.push('/tasks');
        trackShortcutUsage(KEYBOARD_SHORTCUTS.BACK_TO_TASKS, 'back-to-tasks');
      },
      [KEYBOARD_SHORTCUTS.TOGGLE_DROPDOWN]: () => {
        // Dispatch custom event for dropdown toggle
        const event = new CustomEvent('toggleAnalysisDropdown');
        window.dispatchEvent(event);
        trackShortcutUsage(KEYBOARD_SHORTCUTS.TOGGLE_DROPDOWN, 'toggle-dropdown');
      },
    }),
    [router, trackShortcutUsage]
  );

  // Create shortcuts configuration
  const shortcuts: KeyboardShortcut[] = [
    {
      key: KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY,
      description: '打开分析历史页面',
      action: shortcutActions[KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY],
      enabled: true,
      category: 'navigation',
    },
    {
      key: KEYBOARD_SHORTCUTS.ANALYSIS_COMPARE,
      description: '打开分析对比页面',
      action: shortcutActions[KEYBOARD_SHORTCUTS.ANALYSIS_COMPARE],
      enabled: true,
      category: 'navigation',
    },
    {
      key: KEYBOARD_SHORTCUTS.BACK_TO_TASKS,
      description: '返回任务列表',
      action: shortcutActions[KEYBOARD_SHORTCUTS.BACK_TO_TASKS],
      enabled: true,
      category: 'navigation',
    },
    {
      key: KEYBOARD_SHORTCUTS.TOGGLE_DROPDOWN,
      description: '切换分析下拉菜单',
      action: shortcutActions[KEYBOARD_SHORTCUTS.TOGGLE_DROPDOWN],
      enabled: true,
      category: 'navigation',
    },
  ];

  // Detect shortcut conflicts
  const detectConflicts = useCallback(() => {
    const conflicts = new Map<string, string[]>();

    shortcuts.forEach((shortcut) => {
      const key = shortcut.key.toLowerCase();
      if (!conflicts.has(key)) {
        conflicts.set(key, []);
      }
      conflicts.get(key)!.push(shortcut.description);
    });

    // Check for browser default shortcuts
    const browserShortcuts = [
      'ctrl+h',
      'ctrl+c',
      'ctrl+t',
      'ctrl+a',
      'cmd+h',
      'cmd+c',
      'cmd+t',
      'cmd+a',
      'f1',
      'f5',
      'f12',
    ];

    shortcuts.forEach((shortcut) => {
      const key = shortcut.key.toLowerCase().replace('alt+', '');
      const altKey = `alt+${key}`;

      if (browserShortcuts.includes(key) || browserShortcuts.includes(altKey)) {
        if (!conflicts.has(shortcut.key)) {
          conflicts.set(shortcut.key, []);
        }
        conflicts.get(shortcut.key)!.push('Browser default');
      }
    });

    conflictDetection.current = conflicts;

    // Log conflicts for debugging
    conflicts.forEach((descriptions, key) => {
      if (descriptions.length > 1) {
        console.warn(`Keyboard shortcut conflict detected for ${key}:`, descriptions);
      }
    });
  }, [shortcuts]);

  // Handle keyboard events
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      // Ignore shortcuts when user is typing in input fields
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return;
      }

      const key = event.key.toLowerCase();
      const shortcutKey = `${event.altKey ? 'Alt+' : ''}${event.ctrlKey ? 'Ctrl+' : ''}${
        event.metaKey ? 'Cmd+' : ''
      }${key.toUpperCase()}`;

      // Find matching shortcut
      const matchingShortcut = shortcuts.find(
        (s) => s.key === shortcutKey && s.enabled && activeShortcuts.current.has(s.key)
      );

      if (matchingShortcut) {
        event.preventDefault();
        event.stopPropagation();

        try {
          matchingShortcut.action();
        } catch (error) {
          console.error('Failed to execute shortcut action:', error);
        }
      }
    },
    [enabled, shortcuts]
  );

  // Shortcut management functions
  const isShortcutActive = useCallback((key: string) => {
    return activeShortcuts.current.has(key);
  }, []);

  const enableShortcut = useCallback((key: string) => {
    activeShortcuts.current.add(key);
  }, []);

  const disableShortcut = useCallback((key: string) => {
    activeShortcuts.current.delete(key);
  }, []);

  // Initialize shortcuts and event listeners
  useEffect(() => {
    if (!enabled) return;

    // Enable all shortcuts by default
    shortcuts.forEach((shortcut) => {
      if (shortcut.enabled) {
        activeShortcuts.current.add(shortcut.key);
      }
    });

    // Detect conflicts
    detectConflicts();

    // Add event listener
    document.addEventListener('keydown', handleKeyDown, { capture: true });

    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
      activeShortcuts.current.clear();
    };
  }, [enabled, handleKeyDown, detectConflicts, shortcuts]);

  return {
    shortcuts,
    isShortcutActive,
    enableShortcut,
    disableShortcut,
  };
}

/**
 * Get or create session ID for analytics
 */
function getSessionId(): string {
  let sessionId = sessionStorage.getItem('navigationSessionId');
  if (!sessionId) {
    sessionId = `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('navigationSessionId', sessionId);
  }
  return sessionId;
}
