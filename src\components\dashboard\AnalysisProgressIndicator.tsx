'use client';

import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  PauseIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

// Types for the component
interface AnalysisProgressIndicatorProps {
  progress: number;
  currentStage: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
}

// Analysis stages configuration
const ANALYSIS_STAGES = [
  {
    key: 'created',
    name: '创建',
    description: '分析任务已创建',
    order: 0,
  },
  {
    key: 'starting',
    name: '启动',
    description: '初始化分析环境',
    order: 1,
  },
  {
    key: 'data_collection',
    name: '数据收集',
    description: '获取股票相关数据',
    order: 2,
  },
  {
    key: 'analyst_analysis',
    name: '分析师分析',
    description: '多个分析师并行分析',
    order: 3,
  },
  {
    key: 'research_debate',
    name: '研究辩论',
    description: '多头空头观点辩论',
    order: 4,
  },
  {
    key: 'risk_assessment',
    name: '风险评估',
    description: '评估投资风险',
    order: 5,
  },
  {
    key: 'final_decision',
    name: '最终决策',
    description: '生成投资建议',
    order: 6,
  },
  {
    key: 'completed',
    name: '完成',
    description: '分析任务完成',
    order: 7,
  },
];

/**
 * AnalysisProgressIndicator Component
 *
 * Displays detailed progress visualization with:
 * - Stage-by-stage progress visualization
 * - Animated progress bar
 * - Stage status indicators with icons
 * - Current stage highlighting
 *
 * Requirements: 需求 2.1, 需求 2.5, 需求 8.3
 */
export function AnalysisProgressIndicator({
  progress,
  currentStage,
  status,
}: AnalysisProgressIndicatorProps) {
  // Find current stage index
  const getCurrentStageIndex = () => {
    const stage = ANALYSIS_STAGES.find(
      (s) => s.key === currentStage || currentStage.includes(s.key) || currentStage.includes(s.name)
    );
    return stage ? stage.order : Math.floor((progress / 100) * (ANALYSIS_STAGES.length - 1));
  };

  const currentStageIndex = getCurrentStageIndex();

  // Get stage status based on current progress
  const getStageStatus = (stageIndex: number) => {
    if (status === 'failed' && stageIndex === currentStageIndex) {
      return 'failed';
    }
    if (status === 'cancelled') {
      return 'cancelled';
    }
    if (stageIndex < currentStageIndex) {
      return 'completed';
    }
    if (stageIndex === currentStageIndex) {
      return status === 'running' ? 'active' : 'current';
    }
    return 'pending';
  };

  // Get stage icon based on status
  const getStageIcon = (stageIndex: number, stageStatus: string) => {
    const iconClass = 'h-5 w-5';

    switch (stageStatus) {
      case 'completed':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} />;
      case 'active':
        return <PlayIcon className={`${iconClass} text-blue-500 animate-pulse`} />;
      case 'failed':
        return <ExclamationCircleIcon className={`${iconClass} text-red-500`} />;
      case 'cancelled':
        return <PauseIcon className={`${iconClass} text-gray-500`} />;
      case 'current':
        return <ClockIcon className={`${iconClass} text-yellow-500`} />;
      default:
        return <ClockIcon className={`${iconClass} text-gray-400`} />;
    }
  };

  // Get stage circle styling
  const getStageCircleClass = (stageStatus: string) => {
    const baseClass =
      'w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300';

    switch (stageStatus) {
      case 'completed':
        return `${baseClass} bg-green-100 border-green-500 dark:bg-green-900/20 dark:border-green-400`;
      case 'active':
        return `${baseClass} bg-blue-100 border-blue-500 dark:bg-blue-900/20 dark:border-blue-400 shadow-lg`;
      case 'failed':
        return `${baseClass} bg-red-100 border-red-500 dark:bg-red-900/20 dark:border-red-400`;
      case 'cancelled':
        return `${baseClass} bg-gray-100 border-gray-500 dark:bg-gray-900/20 dark:border-gray-400`;
      case 'current':
        return `${baseClass} bg-yellow-100 border-yellow-500 dark:bg-yellow-900/20 dark:border-yellow-400`;
      default:
        return `${baseClass} bg-gray-50 border-gray-300 dark:bg-gray-800 dark:border-gray-600`;
    }
  };

  // Get progress bar color based on status
  const getProgressBarColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'cancelled':
        return 'bg-gray-500';
      default:
        return 'bg-gradient-to-r from-blue-500 to-purple-500';
    }
  };

  return (
    <div className="bg-white dark:bg-slate-800 shadow-lg rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-slate-900 dark:text-white">分析进度</h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-slate-600 dark:text-slate-400">{progress}%</span>
          {status === 'running' && (
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          )}
        </div>
      </div>

      {/* Overall Progress Bar */}
      <div className="mb-8">
        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3 mb-2">
          <motion.div
            className={`h-3 rounded-full ${getProgressBarColor()}`}
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          />
        </div>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          当前阶段: <span className="font-medium">{currentStage}</span>
        </p>
      </div>

      {/* Stage Indicators */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="absolute top-5 left-5 right-5 h-0.5 bg-slate-200 dark:bg-slate-700" />

        {/* Progress Line */}
        <motion.div
          className="absolute top-5 left-5 h-0.5 bg-blue-500"
          initial={{ width: 0 }}
          animate={{
            width: `${Math.max(
              0,
              Math.min(100, (currentStageIndex / (ANALYSIS_STAGES.length - 1)) * 100)
            )}%`,
          }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        />

        {/* Stage Circles and Labels */}
        <div className="relative flex justify-between">
          {ANALYSIS_STAGES.map((stage, index) => {
            const stageStatus = getStageStatus(index);

            return (
              <motion.div
                key={stage.key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex flex-col items-center"
              >
                {/* Stage Circle */}
                <div className={getStageCircleClass(stageStatus)}>
                  {getStageIcon(index, stageStatus)}
                </div>

                {/* Stage Info */}
                <div className="mt-3 text-center max-w-20">
                  <div
                    className={`text-xs font-medium ${
                      stageStatus === 'active' || stageStatus === 'current'
                        ? 'text-slate-900 dark:text-white'
                        : stageStatus === 'completed'
                        ? 'text-green-700 dark:text-green-300'
                        : stageStatus === 'failed'
                        ? 'text-red-700 dark:text-red-300'
                        : 'text-slate-500 dark:text-slate-400'
                    }`}
                  >
                    {stage.name}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 leading-tight">
                    {stage.description}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Status Message */}
      <div className="mt-6 p-3 rounded-lg bg-slate-50 dark:bg-slate-700">
        <div className="flex items-center space-x-2">
          {status === 'running' && (
            <>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-sm text-slate-700 dark:text-slate-300">分析正在进行中...</span>
            </>
          )}
          {status === 'completed' && (
            <>
              <CheckCircleIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-300">分析已完成</span>
            </>
          )}
          {status === 'failed' && (
            <>
              <ExclamationCircleIcon className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-700 dark:text-red-300">分析失败</span>
            </>
          )}
          {status === 'cancelled' && (
            <>
              <PauseIcon className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700 dark:text-gray-300">分析已取消</span>
            </>
          )}
          {status === 'pending' && (
            <>
              <ClockIcon className="h-4 w-4 text-yellow-500" />
              <span className="text-sm text-yellow-700 dark:text-yellow-300">等待开始分析</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default AnalysisProgressIndicator;
