/**
 * 风险控制建议生成器演示脚本
 * 展示风险控制建议生成器的各项功能
 */

const { RiskControlRecommender } = require('../src/lib/risk-control-recommender');
const { RiskMetricsCalculator } = require('../src/lib/risk-metrics-calculator');

// 生成模拟价格数据
function generateMockPriceData(days = 252, initialPrice = 100, volatility = 0.02) {
    const priceData = [];
    let price = initialPrice;

    for (let i = 0; i < days; i++) {
        // 使用几何布朗运动模拟价格
        const randomShock = (Math.random() - 0.5) * 2; // -1 到 1
        const dailyReturn = 0.0003 + volatility * randomShock; // 年化8%收益率
        price = price * (1 + dailyReturn);

        priceData.push({
            date: new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).toISOString(),
            open: price * (1 + (Math.random() - 0.5) * 0.01),
            high: price * (1 + Math.random() * 0.015),
            low: price * (1 - Math.random() * 0.015),
            close: price,
            volume: Math.floor(1000000 + Math.random() * 2000000),
        });
    }

    return priceData;
}

// 生成模拟市场数据
function generateMarketData(days = 252) {
    return generateMockPriceData(days, 3000, 0.015); // 市场指数，波动率较低
}

async function demonstrateRiskControlRecommender() {
    console.log('='.repeat(80));
    console.log('风险控制建议生成器演示');
    console.log('='.repeat(80));

    try {
        // 获取实例
        const recommender = RiskControlRecommender.getInstance();
        const riskCalculator = RiskMetricsCalculator.getInstance();

        // 生成测试数据
        console.log('\n📊 生成测试数据...');
        const priceData = generateMockPriceData(252, 100, 0.025); // 年化25%波动率
        const marketData = generateMarketData(252);

        console.log(`✅ 生成了${priceData.length}天的价格数据`);
        console.log(`   当前价格: $${priceData[priceData.length - 1].close.toFixed(2)}`);
        console.log(`   初始价格: $${priceData[0].close.toFixed(2)}`);
        console.log(`   总收益率: ${(((priceData[priceData.length - 1].close / priceData[0].close) - 1) * 100).toFixed(2)}%`);

        // 计算风险指标
        console.log('\n📈 计算风险指标...');
        const riskMetrics = riskCalculator.calculateRiskMetrics(priceData, marketData, 0.03);

        console.log('✅ 风险指标计算完成:');
        console.log(`   年化波动率: ${(riskMetrics.volatility.annualized_volatility * 100).toFixed(2)}%`);
        console.log(`   VaR (95%): ${(riskMetrics.var.var_95_1d * 100).toFixed(2)}%`);
        console.log(`   最大回撤: ${(riskMetrics.drawdown.max_drawdown * 100).toFixed(2)}%`);
        console.log(`   夏普比率: ${riskMetrics.ratios.sharpe_ratio.toFixed(2)}`);
        console.log(`   Beta系数: ${riskMetrics.ratios.beta.toFixed(2)}`);

        // 演示1: 仓位建议
        console.log('\n' + '='.repeat(60));
        console.log('📋 演示1: 仓位建议生成');
        console.log('='.repeat(60));

        const currentPosition = 0.6; // 当前60%仓位
        const riskTolerance = 0.02; // 2%风险容忍度
        const portfolioValue = 1000000; // 100万组合

        const positionRecommendation = recommender.generatePositionRecommendation(
            riskMetrics,
            currentPosition,
            riskTolerance,
            portfolioValue
        );

        console.log('✅ 仓位建议:');
        console.log(`   当前仓位: ${(currentPosition * 100).toFixed(0)}%`);
        console.log(`   建议仓位: ${(positionRecommendation.recommended_position_size * 100).toFixed(0)}%`);
        console.log(`   调整方向: ${positionRecommendation.position_adjustment}`);
        console.log(`   调整原因: ${positionRecommendation.adjustment_reason}`);
        console.log(`   风险预算利用率: ${(positionRecommendation.risk_budget_utilization * 100).toFixed(1)}%`);
        console.log(`   建议置信度: ${(positionRecommendation.confidence_level * 100).toFixed(1)}%`);

        // 演示2: 止损建议
        console.log('\n' + '='.repeat(60));
        console.log('🛑 演示2: 止损位建议');
        console.log('='.repeat(60));

        const currentPrice = priceData[priceData.length - 1].close;
        const stopLossRecommendation = recommender.generateStopLossRecommendation(
            priceData,
            riskMetrics,
            currentPrice,
            positionRecommendation.recommended_position_size,
            0.05 // 5%最大损失
        );

        console.log('✅ 止损建议:');
        console.log(`   当前价格: $${currentPrice.toFixed(2)}`);
        console.log(`   止损价格: $${stopLossRecommendation.stop_loss_price.toFixed(2)}`);
        console.log(`   止损幅度: ${(stopLossRecommendation.stop_loss_percentage * 100).toFixed(2)}%`);
        console.log(`   止损类型: ${stopLossRecommendation.stop_loss_type}`);
        console.log(`   风险收益比: ${stopLossRecommendation.risk_reward_ratio.toFixed(2)}`);
        console.log(`   调整频率: ${stopLossRecommendation.adjustment_frequency}`);

        // 演示3: 时间窗口建议
        console.log('\n' + '='.repeat(60));
        console.log('⏰ 演示3: 投资时间窗口建议');
        console.log('='.repeat(60));

        const marketConditions = {
            trend: 'bullish',
            volatility_regime: riskMetrics.volatility.annualized_volatility > 0.25 ? 'high' : 'medium',
            market_cycle: 'mid',
        };

        const timeHorizonRecommendation = recommender.generateTimeHorizonRecommendation(
            riskMetrics,
            marketConditions
        );

        console.log('✅ 时间窗口建议:');
        console.log(`   建议持有期: ${timeHorizonRecommendation.recommended_holding_period}天`);
        console.log(`   最短持有期: ${timeHorizonRecommendation.min_holding_period}天`);
        console.log(`   最长持有期: ${timeHorizonRecommendation.max_holding_period}天`);
        console.log(`   入场时机: ${timeHorizonRecommendation.optimal_entry_timing}`);
        console.log(`   退出策略: ${timeHorizonRecommendation.exit_strategy}`);
        console.log(`   市场时机评分: ${timeHorizonRecommendation.market_timing_score}/100`);

        // 演示4: 对冲策略建议
        console.log('\n' + '='.repeat(60));
        console.log('🛡️ 演示4: 对冲策略建议');
        console.log('='.repeat(60));

        const hedgingRecommendation = recommender.generateHedgingRecommendation(
            riskMetrics,
            portfolioValue,
            riskMetrics.correlation.market_correlation
        );

        console.log('✅ 对冲策略建议:');
        console.log(`   是否需要对冲: ${hedgingRecommendation.hedging_required ? '是' : '否'}`);
        console.log(`   组合对冲比例: ${(hedgingRecommendation.portfolio_hedge_ratio * 100).toFixed(1)}%`);
        console.log(`   对冲成本效益比: ${hedgingRecommendation.hedging_cost_benefit.toFixed(2)}`);

        if (hedgingRecommendation.hedging_strategies.length > 0) {
            console.log('   推荐对冲策略:');
            hedgingRecommendation.hedging_strategies.forEach((strategy, index) => {
                console.log(`     ${index + 1}. ${strategy.strategy_name}`);
                console.log(`        类型: ${strategy.strategy_type}`);
                console.log(`        对冲比例: ${(strategy.hedge_ratio * 100).toFixed(1)}%`);
                console.log(`        成本估算: $${strategy.cost_estimate.toLocaleString()}`);
                console.log(`        有效性: ${(strategy.effectiveness * 100).toFixed(0)}%`);
                console.log(`        复杂度: ${strategy.implementation_complexity}`);
            });
        }

        // 演示5: 风险调整收益
        console.log('\n' + '='.repeat(60));
        console.log('💰 演示5: 风险调整收益评估');
        console.log('='.repeat(60));

        const expectedReturn = 0.12; // 12%预期收益
        const riskFreeRate = 0.03; // 3%无风险利率

        const riskAdjustedReturn = recommender.calculateRiskAdjustedReturn(
            riskMetrics,
            expectedReturn,
            riskFreeRate
        );

        console.log('✅ 风险调整收益:');
        console.log(`   预期收益率: ${(riskAdjustedReturn.expected_return * 100).toFixed(2)}%`);
        console.log(`   风险调整后收益: ${(riskAdjustedReturn.risk_adjusted_return * 100).toFixed(2)}%`);
        console.log(`   夏普比率: ${riskAdjustedReturn.sharpe_ratio.toFixed(2)}`);
        console.log(`   风险溢价: ${(riskAdjustedReturn.risk_premium * 100).toFixed(2)}%`);
        console.log(`   盈利概率: ${(riskAdjustedReturn.probability_of_profit * 100).toFixed(1)}%`);
        console.log(`   预期最大损失: ${(riskAdjustedReturn.expected_maximum_loss * 100).toFixed(2)}%`);

        // 演示6: 综合风险控制建议
        console.log('\n' + '='.repeat(60));
        console.log('🎯 演示6: 综合风险控制建议');
        console.log('='.repeat(60));

        const comprehensiveRecommendation = recommender.generateComprehensiveRecommendation(
            riskMetrics,
            priceData,
            undefined, // 没有压力测试结果
            currentPosition,
            portfolioValue,
            riskTolerance
        );

        console.log('✅ 综合建议:');
        console.log(`   整体风险等级: ${comprehensiveRecommendation.overall_risk_level}`);
        console.log(`   风险评分: ${comprehensiveRecommendation.risk_score}/100`);
        console.log(`   投资建议: ${comprehensiveRecommendation.investment_recommendation}`);

        console.log('\n   核心建议:');
        comprehensiveRecommendation.summary_recommendations.forEach((rec, index) => {
            console.log(`     ${index + 1}. ${rec}`);
        });

        if (comprehensiveRecommendation.risk_warnings.length > 0) {
            console.log('\n   ⚠️ 风险警告:');
            comprehensiveRecommendation.risk_warnings.forEach((warning, index) => {
                console.log(`     ${index + 1}. ${warning}`);
            });
        }

        console.log('\n   📋 实施优先级:');
        comprehensiveRecommendation.implementation_priority.forEach((item, index) => {
            console.log(`     ${index + 1}. ${item.action} (${item.priority}) - ${item.timeline}`);
        });

        // 演示7: 不同风险情景下的建议对比
        console.log('\n' + '='.repeat(60));
        console.log('📊 演示7: 不同风险情景对比');
        console.log('='.repeat(60));

        // 低风险情景
        const lowRiskData = generateMockPriceData(252, 100, 0.012); // 12%波动率
        const lowRiskMetrics = riskCalculator.calculateRiskMetrics(lowRiskData, marketData, 0.03);
        const lowRiskRecommendation = recommender.generateComprehensiveRecommendation(
            lowRiskMetrics,
            lowRiskData,
            undefined,
            0.3,
            portfolioValue,
            riskTolerance
        );

        // 高风险情景
        const highRiskData = generateMockPriceData(252, 100, 0.045); // 45%波动率
        const highRiskMetrics = riskCalculator.calculateRiskMetrics(highRiskData, marketData, 0.03);
        const highRiskRecommendation = recommender.generateComprehensiveRecommendation(
            highRiskMetrics,
            highRiskData,
            undefined,
            0.8,
            portfolioValue,
            riskTolerance
        );

        console.log('✅ 风险情景对比:');
        console.log('\n   低风险情景:');
        console.log(`     波动率: ${(lowRiskMetrics.volatility.annualized_volatility * 100).toFixed(1)}%`);
        console.log(`     风险等级: ${lowRiskRecommendation.overall_risk_level}`);
        console.log(`     投资建议: ${lowRiskRecommendation.investment_recommendation}`);
        console.log(`     建议仓位: ${(lowRiskRecommendation.position_recommendation.recommended_position_size * 100).toFixed(0)}%`);

        console.log('\n   当前情景:');
        console.log(`     波动率: ${(riskMetrics.volatility.annualized_volatility * 100).toFixed(1)}%`);
        console.log(`     风险等级: ${comprehensiveRecommendation.overall_risk_level}`);
        console.log(`     投资建议: ${comprehensiveRecommendation.investment_recommendation}`);
        console.log(`     建议仓位: ${(comprehensiveRecommendation.position_recommendation.recommended_position_size * 100).toFixed(0)}%`);

        console.log('\n   高风险情景:');
        console.log(`     波动率: ${(highRiskMetrics.volatility.annualized_volatility * 100).toFixed(1)}%`);
        console.log(`     风险等级: ${highRiskRecommendation.overall_risk_level}`);
        console.log(`     投资建议: ${highRiskRecommendation.investment_recommendation}`);
        console.log(`     建议仓位: ${(highRiskRecommendation.position_recommendation.recommended_position_size * 100).toFixed(0)}%`);

        console.log('\n' + '='.repeat(80));
        console.log('✅ 风险控制建议生成器演示完成!');
        console.log('='.repeat(80));

    } catch (error) {
        console.error('❌ 演示过程中发生错误:', error.message);
        console.error('错误详情:', error.stack);
    }
}

// 运行演示
if (require.main === module) {
    demonstrateRiskControlRecommender();
}

module.exports = {
    demonstrateRiskControlRecommender,
    generateMockPriceData,
    generateMarketData,
};