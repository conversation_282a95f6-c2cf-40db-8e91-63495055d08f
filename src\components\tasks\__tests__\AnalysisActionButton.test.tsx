import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { Task } from '@/types/database';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { AnalysisActionButton } from '../AnalysisActionButton';

const mockTask: Task = {
  id: 1,
  task_id: 'task-123456789',
  workflow_id: 'task-123456789',
  thread_id: 'thread-123',
  ticker: 'AAPL',
  title: 'Apple Inc. 股票分析',
  description: '对苹果公司进行全面的投资分析',
  status: 'pending',
  current_stage: 'pending',
  progress: 0,
  config: {},
  created_by: 'user1',
  created_at: new Date('2024-01-15T10:30:00Z'),
  updated_at: new Date('2024-01-15T10:30:00Z'),
  research_depth: 'medium',
  analysis_period: '1d',
  priority: 1,
};

const mockAnalysisStatus: AnalysisStatus = {
  taskId: 'task-123456789',
  ticker: 'AAPL',
  status: 'running',
  progress: 45,
  currentStage: 'technical_analysis',
  stages: {
    fundamental: true,
    technical: false,
    sentiment: false,
    news: false,
    bull: false,
    bear: false,
    consensus: false,
    risk: false,
    decision: false,
  },
  statistics: {
    totalMessages: 25,
    debateRounds: 2,
    duration: 300,
  },
  startedAt: '2024-01-15T10:30:00Z',
  latestReports: {
    analysts: {},
    researchers: {},
  },
};

describe('AnalysisActionButton', () => {
  it('renders start analysis button for pending tasks', () => {
    render(<AnalysisActionButton task={mockTask} />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('开始分析');
    expect(button).toHaveClass('bg-green-600');
  });

  it('renders view analysis button for running tasks', () => {
    const runningTask = { ...mockTask, status: 'running' as const };
    render(<AnalysisActionButton task={runningTask} />);

    const button = screen.getByRole('button', { name: /实时查看任务 AAPL/ });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('实时查看');
    expect(button).toHaveClass('bg-purple-600');
  });

  it('renders view analysis button for completed tasks', () => {
    const completedTask = { ...mockTask, status: 'completed' as const };
    render(<AnalysisActionButton task={completedTask} />);

    const button = screen.getByRole('button', { name: /查看分析任务 AAPL/ });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('查看分析');
    expect(button).toHaveClass('bg-purple-600');
  });

  it('renders view analysis button when analysis status is provided', () => {
    render(<AnalysisActionButton task={mockTask} analysisStatus={mockAnalysisStatus} />);

    const button = screen.getByRole('button', { name: /实时查看任务 AAPL/ });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('实时查看');
  });

  it('does not render button for failed tasks without analysis status', () => {
    const failedTask = { ...mockTask, status: 'failed' as const };
    const { container } = render(<AnalysisActionButton task={failedTask} />);

    expect(container.firstChild).toBeNull();
  });

  it('calls onStartAnalysis when start button is clicked', async () => {
    const onStartAnalysis = jest.fn().mockResolvedValue(undefined);
    render(<AnalysisActionButton task={mockTask} onStartAnalysis={onStartAnalysis} />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    fireEvent.click(button);

    await waitFor(() => {
      expect(onStartAnalysis).toHaveBeenCalledWith(mockTask);
    });
  });

  it('calls onViewAnalysis when view button is clicked', async () => {
    const onViewAnalysis = jest.fn().mockResolvedValue(undefined);
    const runningTask = { ...mockTask, status: 'running' as const };
    render(<AnalysisActionButton task={runningTask} onViewAnalysis={onViewAnalysis} />);

    const button = screen.getByRole('button', { name: /实时查看任务 AAPL/ });
    fireEvent.click(button);

    await waitFor(() => {
      expect(onViewAnalysis).toHaveBeenCalledWith(runningTask);
    });
  });

  it('shows loading state when startingTask matches task ID', () => {
    render(<AnalysisActionButton task={mockTask} startingTask="task-123456789" />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(button).toBeDisabled();
    expect(screen.getByText('启动中...')).toBeInTheDocument();
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument(); // spinner
  });

  it('shows loading state when loading prop is true', () => {
    const runningTask = { ...mockTask, status: 'running' as const };
    render(<AnalysisActionButton task={runningTask} loading={true} />);

    const button = screen.getByRole('button', { name: /实时查看任务 AAPL/ });
    expect(button).toBeDisabled();
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('shows internal loading state during async operation', async () => {
    const onStartAnalysis = jest.fn(() => new Promise((resolve) => setTimeout(resolve, 100)));
    render(<AnalysisActionButton task={mockTask} onStartAnalysis={onStartAnalysis} />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    fireEvent.click(button);

    expect(button).toBeDisabled();
    expect(screen.getByText('启动中...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('开始分析')).toBeInTheDocument();
    });
  });

  it('handles async operation errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const onStartAnalysis = jest.fn().mockRejectedValue(new Error('Test error'));
    render(<AnalysisActionButton task={mockTask} onStartAnalysis={onStartAnalysis} />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    fireEvent.click(button);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Button action failed:', expect.any(Error));
      expect(screen.getByText('开始分析')).toBeInTheDocument();
    });

    consoleSpy.mockRestore();
  });

  it('applies custom className', () => {
    render(<AnalysisActionButton task={mockTask} className="custom-class" />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(button).toHaveClass('custom-class');
  });

  it('has correct accessibility attributes', () => {
    render(<AnalysisActionButton task={mockTask} />);

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(button).toHaveAttribute('type', 'button');
    expect(button).toHaveAttribute('aria-label', '开始分析任务 AAPL');
  });

  it('spinner has aria-hidden attribute', () => {
    render(<AnalysisActionButton task={mockTask} startingTask="task-123456789" />);

    const spinner = screen.getByRole('img', { hidden: true });
    expect(spinner).toHaveAttribute('aria-hidden', 'true');
  });

  it('does not call handlers when button is disabled', () => {
    const onStartAnalysis = jest.fn();
    render(
      <AnalysisActionButton task={mockTask} onStartAnalysis={onStartAnalysis} loading={true} />
    );

    const button = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    fireEvent.click(button);

    expect(onStartAnalysis).not.toHaveBeenCalled();
  });

  it('shows correct button text based on analysis status', () => {
    const completedAnalysisStatus = { ...mockAnalysisStatus, status: 'completed' as const };
    render(<AnalysisActionButton task={mockTask} analysisStatus={completedAnalysisStatus} />);

    const button = screen.getByRole('button', { name: /查看分析任务 AAPL/ });
    expect(button).toHaveTextContent('查看分析');
  });

  it('prioritizes analysis status over task status for button type', () => {
    const completedAnalysisStatus = { ...mockAnalysisStatus, status: 'completed' as const };
    // Task is pending but analysis status is completed
    render(<AnalysisActionButton task={mockTask} analysisStatus={completedAnalysisStatus} />);

    // Should show view button instead of start button
    const button = screen.getByRole('button', { name: /查看分析任务 AAPL/ });
    expect(button).toHaveTextContent('查看分析');
    expect(button).toHaveClass('bg-purple-600');
  });
});
