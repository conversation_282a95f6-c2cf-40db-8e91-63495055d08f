-- 添加共识评估结果表的迁移脚本
-- 执行时间: 2025-01-31

USE trading_analysis;

-- 检查表是否已存在
SET @table_exists = (
    SELECT COUNT(*)
    FROM information_schema.tables 
    WHERE table_schema = 'trading_analysis' 
    AND table_name = 'consensus_evaluations'
);

-- 如果表不存在，则创建
SET @sql = IF(@table_exists = 0,
    'CREATE TABLE consensus_evaluations (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        consensus_id VARCHAR(36) UNIQUE NOT NULL COMMENT ''共识评估唯一标识 (UUID)'',
        workflow_id VARCHAR(36) NOT NULL COMMENT ''关联工作流ID'',
        bull_strength DECIMAL(3, 2) COMMENT ''多头观点强度 (0-1)'',
        bear_strength DECIMAL(3, 2) COMMENT ''空头观点强度 (0-1)'',
        consensus_direction ENUM(''bullish'', ''bearish'', ''neutral'') COMMENT ''共识方向'',
        consensus_confidence DECIMAL(3, 2) COMMENT ''共识置信度 (0-1)'',
        synthesis_summary TEXT COMMENT ''综合总结'',
        key_agreement_points JSON COMMENT ''关键共识点'',
        key_disagreement_points JSON COMMENT ''关键分歧点'',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
        INDEX idx_workflow_id (workflow_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT=''多头空头辩论后的共识评估结果'';',
    'SELECT ''Table consensus_evaluations already exists'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证表创建
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'SUCCESS: consensus_evaluations table is ready'
        ELSE 'ERROR: consensus_evaluations table not found'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'trading_analysis' 
AND table_name = 'consensus_evaluations';