#!/usr/bin/env node

/**
 * 测试共识评估数据存储功能
 * 验证consensus_evaluations表的创建和数据保存
 */

const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'trading_analysis',
    charset: 'utf8mb4',
};

async function testConsensusStorage() {
    let connection;

    try {
        console.log('🔗 连接数据库...');
        connection = await mysql.createConnection(dbConfig);

        // 1. 检查consensus_evaluations表是否存在
        console.log('📋 检查consensus_evaluations表...');
        const [tables] = await connection.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'consensus_evaluations'
    `, [dbConfig.database]);

        if (tables[0].count === 0) {
            console.log('❌ consensus_evaluations表不存在，请先运行迁移脚本');
            console.log('   执行: mysql -u root -p trading_analysis < database/migrations/add_consensus_evaluations.sql');
            return;
        }

        console.log('✅ consensus_evaluations表存在');

        // 2. 创建测试工作流
        console.log('🔧 创建测试工作流...');
        const workflowId = `test_wf_${Date.now()}`;
        await connection.execute(`
      INSERT INTO workflows (workflow_id, ticker, title, description, status, current_stage, progress)
      VALUES (?, 'AAPL', 'Test Consensus Workflow', 'Testing consensus storage', 'running', 'consensus_evaluation', 85)
    `, [workflowId]);

        console.log(`✅ 测试工作流创建成功: ${workflowId}`);

        // 3. 测试保存共识评估数据
        console.log('💾 测试保存共识评估数据...');
        const consensusId = `con_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        const testConsensusData = {
            consensus_id: consensusId,
            workflow_id: workflowId,
            bull_strength: 0.75,
            bear_strength: 0.65,
            consensus_direction: 'bullish',
            consensus_confidence: 0.68,
            synthesis_summary: '基于多空辩论，多头观点略占优势，建议谨慎看多',
            key_agreement_points: JSON.stringify([
                '公司基本面稳健',
                '行业前景良好',
                '管理层执行力强'
            ]),
            key_disagreement_points: JSON.stringify([
                '估值水平争议',
                '短期业绩压力',
                '市场竞争加剧'
            ])
        };

        await connection.execute(`
      INSERT INTO consensus_evaluations (
        consensus_id, workflow_id, bull_strength, bear_strength, 
        consensus_direction, consensus_confidence, synthesis_summary,
        key_agreement_points, key_disagreement_points
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
            testConsensusData.consensus_id,
            testConsensusData.workflow_id,
            testConsensusData.bull_strength,
            testConsensusData.bear_strength,
            testConsensusData.consensus_direction,
            testConsensusData.consensus_confidence,
            testConsensusData.synthesis_summary,
            testConsensusData.key_agreement_points,
            testConsensusData.key_disagreement_points
        ]);

        console.log(`✅ 共识评估数据保存成功: ${consensusId}`);

        // 4. 验证数据读取
        console.log('🔍 验证数据读取...');
        const [consensusRows] = await connection.execute(`
      SELECT * FROM consensus_evaluations WHERE workflow_id = ?
    `, [workflowId]);

        if (consensusRows.length === 0) {
            console.log('❌ 未找到保存的共识评估数据');
            return;
        }

        const savedConsensus = consensusRows[0];
        console.log('✅ 共识评估数据读取成功:');
        console.log(`   - 共识方向: ${savedConsensus.consensus_direction}`);
        console.log(`   - 多头强度: ${savedConsensus.bull_strength}`);
        console.log(`   - 空头强度: ${savedConsensus.bear_strength}`);
        console.log(`   - 置信度: ${savedConsensus.consensus_confidence}`);
        console.log(`   - 综合总结: ${savedConsensus.synthesis_summary}`);

        // 5. 测试JSON字段解析
        console.log('🔧 测试JSON字段解析...');
        try {
            const agreementPoints = JSON.parse(savedConsensus.key_agreement_points);
            const disagreementPoints = JSON.parse(savedConsensus.key_disagreement_points);

            console.log('✅ JSON字段解析成功:');
            console.log(`   - 共识点数量: ${agreementPoints.length}`);
            console.log(`   - 分歧点数量: ${disagreementPoints.length}`);
            console.log(`   - 第一个共识点: ${agreementPoints[0]}`);
            console.log(`   - 第一个分歧点: ${disagreementPoints[0]}`);
        } catch (error) {
            console.log('❌ JSON字段解析失败:', error.message);
        }

        // 6. 测试关联查询
        console.log('🔗 测试关联查询...');
        const [joinRows] = await connection.execute(`
      SELECT 
        w.workflow_id,
        w.ticker,
        w.title,
        w.status,
        c.consensus_direction,
        c.consensus_confidence,
        c.synthesis_summary
      FROM workflows w
      LEFT JOIN consensus_evaluations c ON w.workflow_id = c.workflow_id
      WHERE w.workflow_id = ?
    `, [workflowId]);

        if (joinRows.length > 0) {
            const result = joinRows[0];
            console.log('✅ 关联查询成功:');
            console.log(`   - 工作流: ${result.ticker} (${result.title})`);
            console.log(`   - 状态: ${result.status}`);
            console.log(`   - 共识方向: ${result.consensus_direction}`);
            console.log(`   - 置信度: ${result.consensus_confidence}`);
        }

        // 7. 清理测试数据
        console.log('🧹 清理测试数据...');
        await connection.execute('DELETE FROM consensus_evaluations WHERE workflow_id = ?', [workflowId]);
        await connection.execute('DELETE FROM workflows WHERE workflow_id = ?', [workflowId]);
        console.log('✅ 测试数据清理完成');

        console.log('\n🎉 所有测试通过！共识评估数据存储功能正常工作。');

    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.message);

        if (error.code === 'ER_NO_SUCH_TABLE') {
            console.log('\n💡 解决方案:');
            console.log('1. 确保已创建consensus_evaluations表');
            console.log('2. 运行迁移脚本: mysql -u root -p trading_analysis < database/migrations/add_consensus_evaluations.sql');
        }

    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 运行测试
if (require.main === module) {
    testConsensusStorage().catch(console.error);
}

module.exports = { testConsensusStorage };