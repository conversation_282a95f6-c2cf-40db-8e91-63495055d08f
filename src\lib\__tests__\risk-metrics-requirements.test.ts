/**
 * 风险指标计算引擎需求验证测试
 * 验证任务4的所有子需求是否得到满足
 */

import { PriceData, RiskMetricsCalculator } from '../risk-metrics-calculator';
import {
  calculateStockRiskMetrics,
  formatRiskMetrics,
  generateRiskSummary,
  validateRiskMetrics,
} from '../risk-metrics-utils';

describe('风险指标计算引擎需求验证', () => {
  let calculator: RiskMetricsCalculator;
  let mockPriceData: PriceData[];
  let mockMarketData: PriceData[];

  beforeEach(() => {
    calculator = RiskMetricsCalculator.getInstance();

    // 生成足够的测试数据
    mockPriceData = [];
    mockMarketData = [];
    let price = 100;
    let marketPrice = 1000;

    for (let i = 0; i < 252; i++) {
      const date = new Date(Date.now() - (252 - i) * 24 * 60 * 60 * 1000).toISOString();
      const dailyReturn = (Math.random() - 0.5) * 0.04;
      const marketReturn = (Math.random() - 0.5) * 0.02;

      price *= 1 + dailyReturn;
      marketPrice *= 1 + marketReturn;

      const volume = Math.floor(Math.random() * 1000000) + 100000;

      mockPriceData.push({
        date,
        open: price * 0.999,
        high: price * 1.002,
        low: price * 0.998,
        close: price,
        volume,
      });

      mockMarketData.push({
        date,
        open: marketPrice * 0.999,
        high: marketPrice * 1.002,
        low: marketPrice * 0.998,
        close: marketPrice,
        volume: volume * 10,
      });
    }
  });

  describe('需求 4.1: 计算历史波动率和 Beta 系数', () => {
    test('应该计算历史波动率', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      // 验证波动率计算
      expect(metrics.volatility.daily_volatility).toBeGreaterThan(0);
      expect(metrics.volatility.weekly_volatility).toBeGreaterThan(0);
      expect(metrics.volatility.monthly_volatility).toBeGreaterThan(0);
      expect(metrics.volatility.annualized_volatility).toBeGreaterThan(0);

      // 验证波动率关系
      expect(metrics.volatility.weekly_volatility).toBeGreaterThan(
        metrics.volatility.daily_volatility
      );
      expect(metrics.volatility.monthly_volatility).toBeGreaterThan(
        metrics.volatility.weekly_volatility
      );
      expect(metrics.volatility.annualized_volatility).toBeGreaterThan(
        metrics.volatility.monthly_volatility
      );
    });

    test('应该计算Beta系数', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      // 验证Beta系数计算
      expect(typeof metrics.ratios.beta).toBe('number');
      expect(isFinite(metrics.ratios.beta)).toBe(true);
    });

    test('应该计算增强的Beta指标', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);
      const marketReturns = mockMarketData
        .slice(1)
        .map((data, i) => (data.close - mockMarketData[i].close) / mockMarketData[i].close);

      const betaMetrics = calculator.calculateEnhancedBeta(returns, marketReturns);

      expect(betaMetrics).toHaveProperty('beta');
      expect(betaMetrics).toHaveProperty('alpha');
      expect(betaMetrics).toHaveProperty('r_squared');
      expect(betaMetrics).toHaveProperty('tracking_error');
      expect(betaMetrics).toHaveProperty('up_beta');
      expect(betaMetrics).toHaveProperty('down_beta');
      expect(betaMetrics).toHaveProperty('beta_stability');

      expect(typeof betaMetrics.beta).toBe('number');
      expect(typeof betaMetrics.alpha).toBe('number');
      expect(betaMetrics.r_squared).toBeGreaterThanOrEqual(0);
      expect(betaMetrics.r_squared).toBeLessThanOrEqual(1);
    });
  });

  describe('需求 4.2: 计算 VaR（风险价值）在不同置信水平下的值', () => {
    test('应该计算基础VaR指标', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.var.var_95_1d).toBeGreaterThan(0);
      expect(metrics.var.var_99_1d).toBeGreaterThan(0);
      expect(metrics.var.var_95_10d).toBeGreaterThan(0);
      expect(metrics.var.var_99_10d).toBeGreaterThan(0);
      expect(metrics.var.expected_shortfall_95).toBeGreaterThan(0);
      expect(metrics.var.expected_shortfall_99).toBeGreaterThan(0);

      // 验证VaR关系
      expect(metrics.var.var_99_1d).toBeGreaterThan(metrics.var.var_95_1d);
      expect(metrics.var.expected_shortfall_95).toBeGreaterThan(metrics.var.var_95_1d);
    });

    test('应该计算增强的VaR指标', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);

      const varMetrics = calculator.calculateEnhancedVaR(
        returns,
        [0.95, 0.99],
        ['historical', 'parametric', 'monte_carlo']
      );

      expect(varMetrics).toHaveProperty('historical_var');
      expect(varMetrics).toHaveProperty('parametric_var');
      expect(varMetrics).toHaveProperty('monte_carlo_var');
      expect(varMetrics).toHaveProperty('expected_shortfall');
      expect(varMetrics).toHaveProperty('var_backtesting');

      expect(varMetrics.historical_var.var_95).toBeGreaterThan(0);
      expect(varMetrics.parametric_var.var_95).toBeGreaterThan(0);
      expect(varMetrics.monte_carlo_var?.var_95).toBeGreaterThan(0);
    });
  });

  describe('需求 4.3: 计算最大回撤和回撤持续时间', () => {
    test('应该计算回撤指标', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.drawdown.max_drawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.max_drawdown_duration).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.current_drawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.recovery_time_estimate).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.drawdown_frequency).toBeGreaterThanOrEqual(0);

      // 验证回撤指标合理性
      expect(metrics.drawdown.max_drawdown).toBeLessThanOrEqual(1);
      expect(metrics.drawdown.current_drawdown).toBeLessThanOrEqual(1);
    });
  });

  describe('需求 4.4: 计算夏普比率和风险调整收益', () => {
    test('应该计算风险比率', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      expect(typeof metrics.ratios.sharpe_ratio).toBe('number');
      expect(typeof metrics.ratios.sortino_ratio).toBe('number');
      expect(typeof metrics.ratios.calmar_ratio).toBe('number');
      expect(typeof metrics.ratios.information_ratio).toBe('number');
      expect(typeof metrics.ratios.treynor_ratio).toBe('number');

      expect(isFinite(metrics.ratios.sharpe_ratio)).toBe(true);
      expect(isFinite(metrics.ratios.sortino_ratio)).toBe(true);
    });

    test('应该计算风险调整收益', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);
      const marketReturns = mockMarketData
        .slice(1)
        .map((data, i) => (data.close - mockMarketData[i].close) / mockMarketData[i].close);

      const riskAdjustedReturns = calculator.calculateRiskAdjustedReturns(
        returns,
        marketReturns,
        0.03
      );

      expect(riskAdjustedReturns).toHaveProperty('sharpe_ratio');
      expect(riskAdjustedReturns).toHaveProperty('sortino_ratio');
      expect(riskAdjustedReturns).toHaveProperty('calmar_ratio');
      expect(riskAdjustedReturns).toHaveProperty('max_drawdown');
      expect(riskAdjustedReturns).toHaveProperty('volatility');
      expect(riskAdjustedReturns).toHaveProperty('var_95');
      expect(riskAdjustedReturns).toHaveProperty('expected_shortfall');
    });
  });

  describe('需求 4.5: 评估流动性指标（Amihud 比率、换手率）', () => {
    test('应该计算流动性指标', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.liquidity.amihud_ratio).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.turnover_rate).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.price_impact).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.bid_ask_spread).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.market_depth_score).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.market_depth_score).toBeLessThanOrEqual(100);
    });
  });

  describe('需求 4.6: 计算相关性和集中度风险', () => {
    test('应该计算相关性指标', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      expect(metrics.correlation.market_correlation).toBeGreaterThanOrEqual(-1);
      expect(metrics.correlation.market_correlation).toBeLessThanOrEqual(1);
      expect(metrics.correlation.sector_correlation).toBeGreaterThanOrEqual(-1);
      expect(metrics.correlation.sector_correlation).toBeLessThanOrEqual(1);
      expect(metrics.correlation.correlation_stability).toBeGreaterThanOrEqual(0);
      expect(metrics.correlation.diversification_ratio).toBeGreaterThan(0);
    });

    test('应该计算集中度风险', () => {
      const positions = [
        {
          symbol: 'AAPL',
          weight: 0.4,
          returns: Array.from({ length: 100 }, () => (Math.random() - 0.5) * 0.04),
        },
        {
          symbol: 'GOOGL',
          weight: 0.3,
          returns: Array.from({ length: 100 }, () => (Math.random() - 0.5) * 0.05),
        },
        {
          symbol: 'MSFT',
          weight: 0.2,
          returns: Array.from({ length: 100 }, () => (Math.random() - 0.5) * 0.03),
        },
        {
          symbol: 'TSLA',
          weight: 0.1,
          returns: Array.from({ length: 100 }, () => (Math.random() - 0.5) * 0.08),
        },
      ];

      const concentrationRisk = calculator.calculateConcentrationRisk(positions);

      expect(concentrationRisk).toHaveProperty('herfindahl_index');
      expect(concentrationRisk).toHaveProperty('effective_number_of_positions');
      expect(concentrationRisk).toHaveProperty('max_weight');
      expect(concentrationRisk).toHaveProperty('top_5_concentration');
      expect(concentrationRisk).toHaveProperty('diversification_score');

      expect(concentrationRisk.herfindahl_index).toBeGreaterThan(0);
      expect(concentrationRisk.herfindahl_index).toBeLessThanOrEqual(1);
      expect(concentrationRisk.effective_number_of_positions).toBeGreaterThan(0);
      expect(concentrationRisk.diversification_score).toBeGreaterThanOrEqual(0);
      expect(concentrationRisk.diversification_score).toBeLessThanOrEqual(100);
    });
  });

  describe('需求 4.7: 提供风险指标的历史趋势分析', () => {
    test('应该计算历史趋势分析', () => {
      const trends = calculator.calculateHistoricalTrends(mockPriceData, 30);

      expect(trends).toHaveProperty('volatility_trend');
      expect(trends).toHaveProperty('var_trend');
      expect(trends).toHaveProperty('drawdown_trend');
      expect(trends).toHaveProperty('beta_trend');
      expect(trends).toHaveProperty('sharpe_trend');
      expect(trends).toHaveProperty('dates');

      expect(trends.volatility_trend.length).toBeGreaterThan(0);
      expect(trends.var_trend.length).toBe(trends.volatility_trend.length);
      expect(trends.dates.length).toBe(trends.volatility_trend.length);

      // 验证趋势数据的合理性
      trends.volatility_trend.forEach((vol) => {
        expect(vol).toBeGreaterThan(0);
        expect(isFinite(vol)).toBe(true);
      });

      trends.var_trend.forEach((var_val) => {
        expect(var_val).toBeGreaterThan(0);
        expect(isFinite(var_val)).toBe(true);
      });
    });
  });

  describe('工具函数验证', () => {
    test('应该提供便捷的风险指标计算接口', async () => {
      const metrics = await calculateStockRiskMetrics(mockPriceData, mockMarketData);

      expect(metrics).toHaveProperty('volatility');
      expect(metrics).toHaveProperty('var');
      expect(metrics).toHaveProperty('drawdown');
      expect(metrics).toHaveProperty('ratios');
      expect(metrics).toHaveProperty('liquidity');
      expect(metrics).toHaveProperty('correlation');
    });

    test('应该生成风险报告摘要', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);
      const summary = generateRiskSummary(metrics);

      expect(summary).toHaveProperty('risk_level');
      expect(summary).toHaveProperty('risk_score');
      expect(summary).toHaveProperty('key_risks');
      expect(summary).toHaveProperty('recommendations');

      expect(['LOW', 'MEDIUM', 'HIGH']).toContain(summary.risk_level);
      expect(summary.risk_score).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(summary.key_risks)).toBe(true);
      expect(Array.isArray(summary.recommendations)).toBe(true);
    });

    test('应该格式化风险指标用于显示', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);
      const formatted = formatRiskMetrics(metrics);

      expect(formatted).toHaveProperty('volatility');
      expect(formatted).toHaveProperty('var');
      expect(formatted).toHaveProperty('drawdown');
      expect(formatted).toHaveProperty('ratios');
      expect(formatted).toHaveProperty('liquidity');
      expect(formatted).toHaveProperty('correlation');

      // 验证格式化后的数据包含百分号或数值
      expect(formatted.volatility['年化波动率']).toMatch(/%$/);
      expect(formatted.var['95% VaR (1日)']).toMatch(/%$/);
    });

    test('应该验证风险指标数据质量', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);
      const validation = validateRiskMetrics(metrics);

      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('warnings');
      expect(validation).toHaveProperty('errors');

      expect(typeof validation.isValid).toBe('boolean');
      expect(Array.isArray(validation.warnings)).toBe(true);
      expect(Array.isArray(validation.errors)).toBe(true);
    });
  });
});
