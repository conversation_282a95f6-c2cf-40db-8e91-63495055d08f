'use client';

import { getEnvironmentSummary } from '@/utils/env-logger';
import { useEffect } from 'react';

/**
 * 客户端环境变量日志组件
 * 在浏览器控制台中显示公共环境变量信息
 */
export function EnvLogger() {
  useEffect(() => {
    // 只在开发环境下显示详细信息
    if (process.env.NODE_ENV === 'development') {
      console.log('\n' + '='.repeat(60));
      console.log('🌐 TradingAgents Frontend - 客户端环境变量');
      console.log('='.repeat(60));

      // 客户端可访问的环境变量
      const clientEnvs = [
        {
          name: 'NODE_ENV',
          value: process.env.NODE_ENV,
          description: '运行环境',
        },
        {
          name: 'NEXT_PUBLIC_API_BASE_URL',
          value: process.env.NEXT_PUBLIC_API_BASE_URL,
          description: '前端API基础URL',
        },
        {
          name: 'NEXT_PUBLIC_API_BACKEND_BASE_URL',
          value: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL,
          description: '后端API基础URL',
        },
        {
          name: 'NEXT_PUBLIC_WS_URL',
          value: process.env.NEXT_PUBLIC_WS_URL,
          description: 'WebSocket连接URL',
        },
        {
          name: 'NEXT_PUBLIC_OPENAI_API_KEY',
          value: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
          description: 'OpenAI API密钥',
        },
        {
          name: 'NEXT_PUBLIC_FINNHUB_API_KEY',
          value: process.env.NEXT_PUBLIC_FINNHUB_API_KEY,
          description: 'FinnHub API密钥',
        },
      ];

      clientEnvs.forEach((env) => {
        const status = env.value ? '✅' : '❌';
        let displayValue = env.value || '未设置';

        // 对API密钥进行脱敏处理
        if (env.name.includes('KEY') && env.value && env.value.length > 8) {
          displayValue = `${env.value.substring(0, 6)}****${env.value.substring(
            env.value.length - 4
          )}`;
        }

        console.log(`${status} ${env.name}: ${displayValue}`);
        console.log(`   📝 ${env.description}`);
      });

      // 获取环境摘要
      const summary = getEnvironmentSummary();

      console.log('\n📊 环境摘要');
      console.log('-'.repeat(30));
      console.log(`当前环境: ${summary.environment}`);
      console.log(`配置完整性: ${summary.configuredCount}/${summary.totalConfigs}`);

      if (summary.criticalMissing.length > 0) {
        console.log('🚨 缺少关键配置:', summary.criticalMissing.join(', '));
      } else {
        console.log('✅ 关键配置完整');
      }

      console.log('='.repeat(60) + '\n');
    }
  }, []);

  return null; // 这个组件不渲染任何内容
}
