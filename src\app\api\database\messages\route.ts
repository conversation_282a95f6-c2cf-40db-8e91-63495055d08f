import { error, success } from '@/lib/api-helpers';
import { getCurrentUser } from '@/lib/auth';
import { query } from '@/lib/db';
import { Message } from '@/types/database';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return error('未授权访问，请先登录', 401);
    }

    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');
    const messageType = searchParams.get('message_type');
    const threadId = searchParams.get('thread_id');
    const limit = searchParams.get('limit') || '50';
    const offset = searchParams.get('offset') || '0';
    const includeMetadata = searchParams.get('include_metadata') === 'true';

    if (!taskId) {
      return error('必须提供 task_id', 400);
    }

    // 首先验证工作流是否属于当前用户
    const workflowCheck = await query(
      'SELECT id FROM workflows WHERE workflow_id = ? AND created_by = ?',
      [taskId, currentUser.userId]
    );

    if (!workflowCheck || (workflowCheck as any[]).length === 0) {
      return error('任务不存在或无权访问', 500);
    }

    // 构建查询条件
    let whereConditions: string[] = [];
    let queryParams: any[] = [];

    whereConditions.push('task_id = ?');
    queryParams.push(taskId);

    if (messageType) {
      whereConditions.push('message_type = ?');
      queryParams.push(messageType);
    }

    if (threadId) {
      whereConditions.push('thread_id = ?');
      queryParams.push(threadId);
    }

    // 构建完整的SQL查询 - 从工作流事件表查询消息
    let sql = `
      SELECT
        id,
        event_id as message_id,
        workflow_id as task_id,
        CASE 
          WHEN event_type = 'message' AND JSON_EXTRACT(metadata, '$.sender') = 'user' THEN 'human'
          WHEN event_type = 'message' AND JSON_EXTRACT(metadata, '$.sender') = 'ai' THEN 'ai'
          WHEN event_type = 'message' AND JSON_EXTRACT(metadata, '$.sender') = 'system' THEN 'system'
          WHEN event_type = 'tool_call' THEN 'tool'
          ELSE 'system'
        END as message_type,
        content,
        ${includeMetadata ? 'metadata,' : ''}
        ROW_NUMBER() OVER (ORDER BY created_at) as sequence_number,
        JSON_EXTRACT(metadata, '$.parent_message_id') as parent_message_id,
        JSON_EXTRACT(metadata, '$.thread_id') as thread_id,
        created_at
      FROM workflow_events
      WHERE event_type IN ('message', 'tool_call')
    `;

    // 更新查询条件以使用 workflow_id
    whereConditions = [];
    queryParams = [];

    whereConditions.push('workflow_id = ?');
    queryParams.push(taskId);

    if (messageType) {
      // 根据消息类型过滤事件
      if (messageType === 'tool') {
        whereConditions.push('event_type = ?');
        queryParams.push('tool_call');
      } else {
        whereConditions.push('event_type = ? AND JSON_EXTRACT(metadata, "$.sender") = ?');
        queryParams.push('message');
        queryParams.push(messageType === 'human' ? 'user' : messageType);
      }
    }

    if (threadId) {
      whereConditions.push('JSON_EXTRACT(metadata, "$.thread_id") = ?');
      queryParams.push(threadId);
    }

    if (whereConditions.length > 0) {
      sql += ' AND ' + whereConditions.join(' AND ');
    }

    sql += ' ORDER BY created_at ASC';

    // 使用字符串拼接而不是参数化查询来避免MySQL参数问题
    sql += ` LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    const messages = (await query(sql, queryParams)) as Message[];

    // 获取总数（用于分页）
    let countSql =
      'SELECT COUNT(*) as total FROM workflow_events WHERE event_type IN ("message", "tool_call")';
    let countParams: any[] = [];

    if (whereConditions.length > 0) {
      countSql += ' AND ' + whereConditions.join(' AND ');
      countParams = queryParams; // 使用原始的查询参数（不包含limit和offset）
    }

    const countResult = (await query(countSql, countParams)) as any[];
    const total = countResult[0]?.total || 0;

    return success({
      messages,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: parseInt(offset) + parseInt(limit) < total,
      },
    });
  } catch (err) {
    console.error('查询消息失败:', err);
    return error('查询消息失败');
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return error('未授权访问，请先登录', 401);
    }

    const { task_id, message_type, content, metadata, parent_message_id, thread_id } =
      await request.json();

    // 验证必填字段
    if (!task_id || !message_type || !content) {
      return error('缺少必填字段: task_id, message_type, content', 400);
    }

    // 首先验证工作流是否属于当前用户
    const workflowCheck = await query(
      'SELECT id FROM workflows WHERE workflow_id = ? AND created_by = ?',
      [task_id, currentUser.userId]
    );

    if (!workflowCheck || (workflowCheck as any[]).length === 0) {
      return error('任务不存在或无权访问', 404);
    }

    // 验证消息类型
    const validTypes = ['human', 'ai', 'system', 'tool'];
    if (!validTypes.includes(message_type)) {
      return error(`无效的消息类型: ${message_type}。有效类型: ${validTypes.join(', ')}`, 400);
    }

    // 生成消息ID
    const { v4: uuidv4 } = require('uuid');
    const messageId = uuidv4();

    // 准备元数据
    const eventMetadata = {
      sender: message_type === 'human' ? 'user' : message_type,
      parent_message_id,
      thread_id,
      ...metadata,
    };

    // 插入工作流事件
    await query(
      `INSERT INTO workflow_events (
        event_id, workflow_id, stage_name, event_type, content, metadata
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [messageId, task_id, 'message', 'message', content, JSON.stringify(eventMetadata)]
    );

    // 获取序号（基于创建时间）
    const sequenceResult = (await query(
      'SELECT COUNT(*) + 1 as next_sequence FROM workflow_events WHERE workflow_id = ? AND event_type IN ("message", "tool_call")',
      [task_id]
    )) as any[];
    const sequenceNumber = sequenceResult[0]?.next_sequence || 1;

    return success({
      message_id: messageId,
      sequence_number: sequenceNumber,
    });
  } catch (err) {
    console.error('添加消息失败:', err);
    return error('添加消息失败');
  }
}
