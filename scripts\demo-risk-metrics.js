/**
 * 风险指标计算引擎演示脚本
 * 展示所有风险指标计算功能
 */

const { RiskMetricsCalculator } = require('../src/lib/risk-metrics-calculator.ts');

// 生成模拟股票数据
function generateMockStockData(days = 252, initialPrice = 100) {
    const data = [];
    let price = initialPrice;

    for (let i = 0; i < days; i++) {
        const date = new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).toISOString();
        const dailyReturn = (Math.random() - 0.5) * 0.04; // ±2%随机波动

        price *= (1 + dailyReturn);
        const volume = Math.floor(Math.random() * 2000000) + 500000;

        data.push({
            date,
            open: price * (1 + (Math.random() - 0.5) * 0.005),
            high: price * (1 + Math.random() * 0.01),
            low: price * (1 - Math.random() * 0.01),
            close: price,
            volume,
        });
    }

    return data;
}

// 生成模拟市场数据
function generateMockMarketData(days = 252, initialPrice = 1000) {
    const data = [];
    let price = initialPrice;

    for (let i = 0; i < days; i++) {
        const date = new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000).toISOString();
        const dailyReturn = (Math.random() - 0.5) * 0.02; // ±1%市场波动

        price *= (1 + dailyReturn);
        const volume = Math.floor(Math.random() * 10000000) + 5000000;

        data.push({
            date,
            open: price * (1 + (Math.random() - 0.5) * 0.003),
            high: price * (1 + Math.random() * 0.008),
            low: price * (1 - Math.random() * 0.008),
            close: price,
            volume,
        });
    }

    return data;
}

async function demonstrateRiskMetrics() {
    console.log('='.repeat(80));
    console.log('风险指标计算引擎演示');
    console.log('='.repeat(80));

    const calculator = RiskMetricsCalculator.getInstance();

    // 生成测试数据
    console.log('\n1. 生成测试数据...');
    const stockData = generateMockStockData(252, 100);
    const marketData = generateMockMarketData(252, 1000);

    console.log(`   - 股票数据: ${stockData.length}个交易日`);
    console.log(`   - 市场数据: ${marketData.length}个交易日`);
    console.log(`   - 股票价格范围: ${Math.min(...stockData.map(d => d.close)).toFixed(2)} - ${Math.max(...stockData.map(d => d.close)).toFixed(2)}`);

    // 计算基础风险指标
    console.log('\n2. 计算基础风险指标...');
    const riskMetrics = calculator.calculateRiskMetrics(stockData, marketData, 0.03);

    console.log('\n   波动率指标:');
    console.log(`   - 日波动率: ${(riskMetrics.volatility.daily_volatility * 100).toFixed(2)}%`);
    console.log(`   - 年化波动率: ${(riskMetrics.volatility.annualized_volatility * 100).toFixed(2)}%`);
    console.log(`   - 波动率趋势: ${(riskMetrics.volatility.volatility_trend * 100).toFixed(2)}%`);
    console.log(`   - 波动率百分位: ${riskMetrics.volatility.volatility_percentile.toFixed(1)}%`);

    console.log('\n   VaR指标:');
    console.log(`   - 95% VaR (1日): ${(riskMetrics.var.var_95_1d * 100).toFixed(2)}%`);
    console.log(`   - 99% VaR (1日): ${(riskMetrics.var.var_99_1d * 100).toFixed(2)}%`);
    console.log(`   - 95% VaR (10日): ${(riskMetrics.var.var_95_10d * 100).toFixed(2)}%`);
    console.log(`   - 预期损失 (95%): ${(riskMetrics.var.expected_shortfall_95 * 100).toFixed(2)}%`);

    console.log('\n   回撤指标:');
    console.log(`   - 最大回撤: ${(riskMetrics.drawdown.max_drawdown * 100).toFixed(2)}%`);
    console.log(`   - 回撤持续时间: ${riskMetrics.drawdown.max_drawdown_duration}天`);
    console.log(`   - 当前回撤: ${(riskMetrics.drawdown.current_drawdown * 100).toFixed(2)}%`);
    console.log(`   - 回撤频率: ${riskMetrics.drawdown.drawdown_frequency.toFixed(1)}次/年`);

    console.log('\n   风险比率:');
    console.log(`   - 夏普比率: ${riskMetrics.ratios.sharpe_ratio.toFixed(3)}`);
    console.log(`   - Sortino比率: ${riskMetrics.ratios.sortino_ratio.toFixed(3)}`);
    console.log(`   - Calmar比率: ${riskMetrics.ratios.calmar_ratio.toFixed(3)}`);
    console.log(`   - Beta系数: ${riskMetrics.ratios.beta.toFixed(3)}`);
    console.log(`   - Treynor比率: ${riskMetrics.ratios.treynor_ratio.toFixed(3)}`);

    console.log('\n   流动性指标:');
    console.log(`   - Amihud比率: ${riskMetrics.liquidity.amihud_ratio.toExponential(2)}`);
    console.log(`   - 换手率: ${(riskMetrics.liquidity.turnover_rate * 100).toFixed(2)}%`);
    console.log(`   - 价格冲击: ${(riskMetrics.liquidity.price_impact * 100).toFixed(4)}%`);
    console.log(`   - 市场深度评分: ${riskMetrics.liquidity.market_depth_score.toFixed(1)}/100`);

    // 计算增强的Beta指标
    console.log('\n3. 计算增强的Beta指标...');
    const returns = stockData.slice(1).map((data, i) =>
        (data.close - stockData[i].close) / stockData[i].close
    );
    const marketReturns = marketData.slice(1).map((data, i) =>
        (data.close - marketData[i].close) / marketData[i].close
    );

    const betaMetrics = calculator.calculateEnhancedBeta(returns, marketReturns);

    console.log(`   - Beta系数: ${betaMetrics.beta.toFixed(3)}`);
    console.log(`   - Alpha: ${(betaMetrics.alpha * 100).toFixed(3)}%`);
    console.log(`   - R平方: ${betaMetrics.r_squared.toFixed(3)}`);
    console.log(`   - 跟踪误差: ${(betaMetrics.tracking_error * 100).toFixed(3)}%`);
    console.log(`   - 上行Beta: ${betaMetrics.up_beta.toFixed(3)}`);
    console.log(`   - 下行Beta: ${betaMetrics.down_beta.toFixed(3)}`);
    console.log(`   - Beta稳定性: ${(betaMetrics.beta_stability * 100).toFixed(1)}%`);

    // 计算增强的VaR指标
    console.log('\n4. 计算增强的VaR指标...');
    const varMetrics = calculator.calculateEnhancedVaR(
        returns,
        [0.95, 0.99],
        ['historical', 'parametric', 'monte_carlo']
    );

    console.log('\n   历史模拟法VaR:');
    console.log(`   - 95% VaR: ${(varMetrics.historical_var.var_95 * 100).toFixed(2)}%`);
    console.log(`   - 99% VaR: ${(varMetrics.historical_var.var_99 * 100).toFixed(2)}%`);

    console.log('\n   参数法VaR:');
    console.log(`   - 95% VaR: ${(varMetrics.parametric_var.var_95 * 100).toFixed(2)}%`);
    console.log(`   - 99% VaR: ${(varMetrics.parametric_var.var_99 * 100).toFixed(2)}%`);

    if (varMetrics.monte_carlo_var) {
        console.log('\n   蒙特卡洛VaR:');
        console.log(`   - 95% VaR: ${(varMetrics.monte_carlo_var.var_95 * 100).toFixed(2)}%`);
        console.log(`   - 99% VaR: ${(varMetrics.monte_carlo_var.var_99 * 100).toFixed(2)}%`);
    }

    console.log('\n   VaR回测结果:');
    console.log(`   - 违规次数: ${varMetrics.var_backtesting.violations}`);
    console.log(`   - 违规率: ${(varMetrics.var_backtesting.violation_rate * 100).toFixed(2)}%`);
    console.log(`   - Kupiec检验P值: ${varMetrics.var_backtesting.kupiec_test_pvalue.toFixed(4)}`);

    // 计算历史趋势分析
    console.log('\n5. 计算历史趋势分析...');
    const trends = calculator.calculateHistoricalTrends(stockData, 30);

    console.log(`   - 趋势数据点数: ${trends.volatility_trend.length}`);
    console.log(`   - 最新波动率: ${(trends.volatility_trend[trends.volatility_trend.length - 1] * 100).toFixed(2)}%`);
    console.log(`   - 最新VaR: ${(trends.var_trend[trends.var_trend.length - 1] * 100).toFixed(2)}%`);
    console.log(`   - 最新回撤: ${(trends.drawdown_trend[trends.drawdown_trend.length - 1] * 100).toFixed(2)}%`);
    console.log(`   - 最新Beta: ${trends.beta_trend[trends.beta_trend.length - 1].toFixed(3)}`);
    console.log(`   - 最新夏普比率: ${trends.sharpe_trend[trends.sharpe_trend.length - 1].toFixed(3)}`);

    // 计算集中度风险
    console.log('\n6. 计算集中度风险...');
    const positions = [
        {
            symbol: 'AAPL',
            weight: 0.4,
            returns: returns.slice(0, 100),
        },
        {
            symbol: 'GOOGL',
            weight: 0.3,
            returns: returns.slice(50, 150).map(r => r * 1.2),
        },
        {
            symbol: 'MSFT',
            weight: 0.2,
            returns: returns.slice(100, 200).map(r => r * 0.8),
        },
        {
            symbol: 'TSLA',
            weight: 0.1,
            returns: returns.slice(150, 250).map(r => r * 1.5),
        },
    ];

    const concentrationRisk = calculator.calculateConcentrationRisk(positions);

    console.log(`   - Herfindahl指数: ${concentrationRisk.herfindahl_index.toFixed(3)}`);
    console.log(`   - 有效持仓数量: ${concentrationRisk.effective_number_of_positions.toFixed(1)}`);
    console.log(`   - 最大权重: ${(concentrationRisk.max_weight * 100).toFixed(1)}%`);
    console.log(`   - 前5大集中度: ${(concentrationRisk.top_5_concentration * 100).toFixed(1)}%`);
    console.log(`   - 分散化评分: ${concentrationRisk.diversification_score.toFixed(1)}/100`);

    // 计算风险调整收益
    console.log('\n7. 计算风险调整收益...');
    const riskAdjustedReturns = calculator.calculateRiskAdjustedReturns(
        returns,
        marketReturns,
        0.03
    );

    console.log(`   - 夏普比率: ${riskAdjustedReturns.sharpe_ratio.toFixed(3)}`);
    console.log(`   - Sortino比率: ${riskAdjustedReturns.sortino_ratio.toFixed(3)}`);
    console.log(`   - Calmar比率: ${riskAdjustedReturns.calmar_ratio.toFixed(3)}`);
    console.log(`   - 最大回撤: ${(riskAdjustedReturns.max_drawdown * 100).toFixed(2)}%`);
    console.log(`   - 波动率: ${(riskAdjustedReturns.volatility * 100).toFixed(2)}%`);
    console.log(`   - 95% VaR: ${(riskAdjustedReturns.var_95 * 100).toFixed(2)}%`);
    console.log(`   - 预期损失: ${(riskAdjustedReturns.expected_shortfall * 100).toFixed(2)}%`);

    console.log('\n' + '='.repeat(80));
    console.log('风险指标计算引擎演示完成');
    console.log('='.repeat(80));
}

// 运行演示
if (require.main === module) {
    demonstrateRiskMetrics().catch(console.error);
}

module.exports = {
    demonstrateRiskMetrics,
    generateMockStockData,
    generateMockMarketData,
};