/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-05 23:55:09
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 21:28:01
 * @FilePath: \trading-agents-frontend\src\lib\db.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import mysql from 'mysql2/promise';

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '12345678',
  database: process.env.DB_NAME || 'trading_analysis',
  port: Number(process.env.DB_PORT) || 13306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_PORT:', process.env.DB_PORT);

// 导出执行查询的函数
export async function query(sql: string, params: any[] = []) {
  const [rows] = await pool.execute(sql, params);
  return rows;
}

// 导出获取连接池的函数，以便进行事务处理
export function getConnection() {
  return pool.getConnection();
}

export default pool;
