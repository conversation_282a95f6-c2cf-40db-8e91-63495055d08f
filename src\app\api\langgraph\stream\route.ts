import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { ticker, config, threadId } = await request.json();

    if (!ticker) {
      return NextResponse.json({ error: '股票代码不能为空' }, { status: 400 });
    }

    // 使用 LangGraph 服务进行流式分析
    const { langGraphService } = await import('@/lib/langgraph-server');
    const sessionId =
      threadId ||
      (await langGraphService.createSession({
        ticker,
        title: `Stream Analysis for ${ticker}`,
        config: config || {},
      }));

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of langGraphService.streamAnalysis(sessionId, ticker, config)) {
            const data = `data: ${JSON.stringify(chunk)}\n\n`;
            controller.enqueue(new TextEncoder().encode(data));
          }
          controller.close();
        } catch (error) {
          console.error('流式分析失败:', error);
          const errorData = `data: ${JSON.stringify({ error: '分析失败' })}\n\n`;
          controller.enqueue(new TextEncoder().encode(errorData));
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('流式分析请求失败:', error);
    return NextResponse.json({ error: '流式分析请求失败，请稍后重试' }, { status: 500 });
  }
}
