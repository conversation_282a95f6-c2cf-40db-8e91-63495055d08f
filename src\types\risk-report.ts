/**
 * 风险报告结构化数据类型定义
 * Risk Report Structured Data Types
 */

// 风险等级枚举
export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 风险类型枚举
export enum RiskType {
  MARKET = 'market',
  LIQUIDITY = 'liquidity',
  CREDIT = 'credit',
  OPERATIONAL = 'operational',
  SYSTEMATIC = 'systematic',
}

// 风险预警级别
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  DANGER = 'danger',
  CRITICAL = 'critical',
}

// 单项风险评估结果
export interface RiskAssessment {
  type: RiskType;
  level: RiskLevel;
  score: number; // 0-100 风险评分
  description: string;
  metrics: Record<string, number>;
  factors: string[];
  recommendations: string[];
}

// 风险指标
export interface RiskMetric {
  name: string;
  value: number;
  unit?: string;
  description: string;
  threshold?: {
    low: number;
    medium: number;
    high: number;
  };
  trend?: 'increasing' | 'decreasing' | 'stable';
}

// 风险预警
export interface RiskAlert {
  id: string;
  level: AlertLevel;
  type: RiskType;
  title: string;
  message: string;
  timestamp: Date;
  isActive: boolean;
  threshold?: number;
  currentValue?: number;
  recommendations: string[];
}

// 风险控制建议
export interface RiskControlRecommendation {
  category: 'position' | 'timing' | 'hedging' | 'monitoring';
  priority: 'high' | 'medium' | 'low';
  action: string;
  description: string;
  expectedImpact: string;
  implementation: string[];
}

// 风险场景分析结果
export interface RiskScenarioResult {
  scenario: string;
  probability: number;
  potentialLoss: number;
  potentialLossPercentage: number;
  timeHorizon: string;
  description: string;
  mitigationStrategies: string[];
}

// 完整的风险报告结构
export interface RiskReport {
  // 基本信息
  id: string;
  symbol: string;
  timestamp: Date;
  analysisId: string;

  // 综合风险评估
  overallRisk: {
    level: RiskLevel;
    score: number; // 0-100 综合风险评分
    confidence: number; // 0-100 评估置信度
    summary: string;
  };

  // 各类风险评估
  riskAssessments: RiskAssessment[];

  // 关键风险指标
  keyMetrics: RiskMetric[];

  // 风险预警
  alerts: RiskAlert[];

  // 风险控制建议
  recommendations: RiskControlRecommendation[];

  // 场景分析结果
  scenarioAnalysis: RiskScenarioResult[];

  // 历史对比
  historicalComparison?: {
    previousScore: number;
    trend: 'improving' | 'deteriorating' | 'stable';
    changePercentage: number;
    comparisonPeriod: string;
  };

  // 元数据
  metadata: {
    version: string;
    generatedBy: string;
    processingTime: number;
    dataQuality: number; // 0-100 数据质量评分
    lastUpdated: Date;
  };
}

// 风险报告生成配置
export interface RiskReportConfig {
  includeScenarioAnalysis: boolean;
  includeHistoricalComparison: boolean;
  alertThresholds: Record<RiskType, number>;
  confidenceThreshold: number;
  timeHorizon: '1d' | '1w' | '1m' | '3m' | '1y';
}

// 风险等级评定标准
export interface RiskLevelCriteria {
  type: RiskType;
  thresholds: {
    low: number;
    medium: number;
    high: number;
  };
  weights: Record<string, number>;
  description: string;
}
