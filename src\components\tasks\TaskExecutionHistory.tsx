'use client';

import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useEffect, useState } from 'react';

interface ExecutionRecord {
  id: string;
  workflowId: string;
  status: 'completed' | 'failed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  duration: number;
  progress: number;
  error?: string;
  statistics: {
    totalMessages: number;
    debateRounds: number;
  };
}

interface TaskExecutionHistoryProps {
  taskId: string;
}

export function TaskExecutionHistory({ taskId }: TaskExecutionHistoryProps) {
  const [history, setHistory] = useState<ExecutionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchExecutionHistory();
  }, [taskId]);

  const fetchExecutionHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      // 这里应该调用获取任务执行历史的API
      // 暂时使用模拟数据
      const mockHistory: ExecutionRecord[] = [
        {
          id: '1',
          workflowId: 'wf-001',
          status: 'completed',
          startedAt: '2024-01-15T10:00:00Z',
          completedAt: '2024-01-15T10:15:00Z',
          duration: 900,
          progress: 100,
          statistics: {
            totalMessages: 45,
            debateRounds: 3,
          },
        },
        {
          id: '2',
          workflowId: 'wf-002',
          status: 'failed',
          startedAt: '2024-01-14T14:30:00Z',
          completedAt: '2024-01-14T14:35:00Z',
          duration: 300,
          progress: 25,
          error: '网络连接超时',
          statistics: {
            totalMessages: 12,
            debateRounds: 1,
          },
        },
      ];

      setHistory(mockHistory);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取执行历史失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'cancelled':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`;
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-slate-200 dark:bg-slate-600 rounded w-1/4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6">
        <div className="text-center text-red-600">
          <p>加载执行历史失败: {error}</p>
          <button
            onClick={fetchExecutionHistory}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">执行历史</h3>
        <span className="text-sm text-gray-500">{history.length} 次执行</span>
      </div>

      {history.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>暂无执行历史</p>
        </div>
      ) : (
        <div className="space-y-4">
          {history.map((record) => (
            <div
              key={record.id}
              className="border border-slate-200 dark:border-slate-600 rounded-lg p-4 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(
                      record.status
                    )}`}
                  >
                    {getStatusText(record.status)}
                  </span>
                  <span className="text-sm font-mono text-gray-600">{record.workflowId}</span>
                </div>
                <div className="text-sm text-gray-500">
                  {format(new Date(record.startedAt), 'MM-dd HH:mm', { locale: zhCN })}
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-gray-500">进度</div>
                  <div className="font-medium">{record.progress}%</div>
                </div>
                <div>
                  <div className="text-gray-500">耗时</div>
                  <div className="font-medium">{formatDuration(record.duration)}</div>
                </div>
                <div>
                  <div className="text-gray-500">消息数</div>
                  <div className="font-medium">{record.statistics.totalMessages}</div>
                </div>
                <div>
                  <div className="text-gray-500">辩论轮次</div>
                  <div className="font-medium">{record.statistics.debateRounds}</div>
                </div>
              </div>

              {record.error && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                  错误: {record.error}
                </div>
              )}

              {record.completedAt && (
                <div className="mt-2 text-xs text-gray-500">
                  完成时间:{' '}
                  {format(new Date(record.completedAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
