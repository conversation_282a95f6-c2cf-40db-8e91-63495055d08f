import { KEYBOARD_SHORTCUTS } from '@/types/navigation';
import { act, renderHook } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useKeyboardShortcuts } from '../useKeyboardShortcuts';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

// Mock localStorage and sessionStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

describe('useKeyboardShortcuts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    localStorageMock.getItem.mockReturnValue('[]');
    sessionStorageMock.getItem.mockReturnValue('test-session-id');
  });

  afterEach(() => {
    // Clean up event listeners
    document.removeEventListener('keydown', jest.fn());
  });

  it('should initialize with default shortcuts', () => {
    const { result } = renderHook(() => useKeyboardShortcuts());

    expect(result.current.shortcuts).toHaveLength(4);
    expect(result.current.shortcuts[0].key).toBe(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY);
    expect(result.current.shortcuts[1].key).toBe(KEYBOARD_SHORTCUTS.ANALYSIS_COMPARE);
    expect(result.current.shortcuts[2].key).toBe(KEYBOARD_SHORTCUTS.BACK_TO_TASKS);
    expect(result.current.shortcuts[3].key).toBe(KEYBOARD_SHORTCUTS.TOGGLE_DROPDOWN);
  });

  it('should handle Alt+H shortcut for analysis history', () => {
    renderHook(() => useKeyboardShortcuts());

    // Simulate Alt+H keydown
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/history');
  });

  it('should handle Alt+C shortcut for analysis compare', () => {
    renderHook(() => useKeyboardShortcuts());

    // Simulate Alt+C keydown
    const event = new KeyboardEvent('keydown', {
      key: 'c',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/compare');
  });

  it('should handle Alt+T shortcut for back to tasks', () => {
    renderHook(() => useKeyboardShortcuts());

    // Simulate Alt+T keydown
    const event = new KeyboardEvent('keydown', {
      key: 't',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/tasks');
  });

  it('should handle Alt+A shortcut for toggle dropdown', () => {
    const mockDispatchEvent = jest.spyOn(window, 'dispatchEvent');
    renderHook(() => useKeyboardShortcuts());

    // Simulate Alt+A keydown
    const event = new KeyboardEvent('keydown', {
      key: 'a',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockDispatchEvent).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'toggleAnalysisDropdown',
      })
    );
  });

  it('should ignore shortcuts when disabled', () => {
    renderHook(() => useKeyboardShortcuts({ enabled: false }));

    // Simulate Alt+H keydown
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockRouter.push).not.toHaveBeenCalled();
  });

  it('should ignore shortcuts when typing in input fields', () => {
    renderHook(() => useKeyboardShortcuts());

    // Create a mock input element
    const input = document.createElement('input');
    document.body.appendChild(input);

    // Simulate Alt+H keydown on input
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    Object.defineProperty(event, 'target', {
      value: input,
      enumerable: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(mockRouter.push).not.toHaveBeenCalled();

    // Clean up
    document.body.removeChild(input);
  });

  it('should track shortcut usage', () => {
    const onShortcutUsed = jest.fn();
    renderHook(() => useKeyboardShortcuts({ onShortcutUsed }));

    // Simulate Alt+H keydown
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(onShortcutUsed).toHaveBeenCalledWith(
      KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY,
      'analysis-history'
    );
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'navigationEvents',
      expect.stringContaining('keyboard')
    );
  });

  it('should manage shortcut active state', () => {
    const { result } = renderHook(() => useKeyboardShortcuts());

    // Initially all shortcuts should be active
    expect(result.current.isShortcutActive(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY)).toBe(true);

    // Disable a shortcut
    act(() => {
      result.current.disableShortcut(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY);
    });

    expect(result.current.isShortcutActive(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY)).toBe(false);

    // Re-enable the shortcut
    act(() => {
      result.current.enableShortcut(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY);
    });

    expect(result.current.isShortcutActive(KEYBOARD_SHORTCUTS.ANALYSIS_HISTORY)).toBe(true);
  });

  it('should handle navigation errors gracefully', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    mockRouter.push.mockImplementation(() => {
      throw new Error('Navigation failed');
    });

    renderHook(() => useKeyboardShortcuts());

    // Simulate Alt+H keydown
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to execute shortcut action:',
      expect.any(Error)
    );

    consoleSpy.mockRestore();
  });

  it('should prevent default behavior for handled shortcuts', () => {
    renderHook(() => useKeyboardShortcuts());

    // Create a mock event with preventDefault
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
    const stopPropagationSpy = jest.spyOn(event, 'stopPropagation');

    act(() => {
      document.dispatchEvent(event);
    });

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(stopPropagationSpy).toHaveBeenCalled();
  });

  it('should create session ID if not exists', () => {
    sessionStorageMock.getItem.mockReturnValue(null);

    renderHook(() => useKeyboardShortcuts());

    // Simulate shortcut usage
    const event = new KeyboardEvent('keydown', {
      key: 'h',
      altKey: true,
      bubbles: true,
    });

    act(() => {
      document.dispatchEvent(event);
    });

    expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
      'navigationSessionId',
      expect.stringMatching(/^nav_\d+_[a-z0-9]+$/)
    );
  });
});
