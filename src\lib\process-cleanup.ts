// 全局进程清理处理器
// 确保在进程退出时正确清理所有资源和事件监听器

import { cleanupAnalysisService } from './analysis-service';
import { cleanupLangGraphService } from './langgraph-server';

let isCleanupRegistered = false;

export const registerProcessCleanup = (): void => {
  if (isCleanupRegistered || process.env.NODE_ENV === 'test') {
    return;
  }

  const cleanup = async (signal: string) => {
    console.log(`收到 ${signal} 信号，开始清理资源...`);

    try {
      // 清理分析服务
      cleanupAnalysisService();

      // 清理 LangGraph 服务
      cleanupLangGraphService();

      console.log('资源清理完成');
    } catch (error) {
      console.error('清理资源时出错:', error);
    }

    process.exit(0);
  };

  // 注册进程退出处理器
  process.on('SIGINT', () => cleanup('SIGINT'));
  process.on('SIGTERM', () => cleanup('SIGTERM'));
  process.on('SIGQUIT', () => cleanup('SIGQUIT'));

  // 处理未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    cleanup('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason, 'at:', promise);
    cleanup('unhandledRejection');
  });

  isCleanupRegistered = true;
  console.log('进程清理处理器已注册');
};

// 自动注册清理处理器
registerProcessCleanup();
