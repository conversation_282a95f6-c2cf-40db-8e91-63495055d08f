/**
 * SEO 环境变量和运行时常量
 * 处理环境变量的获取和默认值设置
 */

// 获取环境变量的辅助函数
const getEnvVar = (key: string, defaultValue: string = ''): string => {
  if (typeof window !== 'undefined') {
    // 客户端环境，只能访问 NEXT_PUBLIC_ 前缀的变量
    return (window as any).__ENV__?.[key] || defaultValue;
  }
  // 服务端环境
  return process.env[key] || defaultValue;
};

// SEO 环境变量
export const SEO_ENV = {
  BASE_URL: getEnvVar('NEXT_PUBLIC_BASE_URL', 'https://tradingagents.com'),
  GA_MEASUREMENT_ID: getEnvVar('NEXT_PUBLIC_GA_MEASUREMENT_ID', ''),
  GSC_VERIFICATION: getEnvVar('NEXT_PUBLIC_GSC_VERIFICATION', ''),
  BAIDU_VERIFICATION: getEnvVar('NEXT_PUBLIC_BAIDU_VERIFICATION', ''),
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
} as const;

// 运行时 SEO 配置
export const RUNTIME_SEO_CONFIG = {
  // 是否启用 SEO 功能
  ENABLE_SEO: SEO_ENV.NODE_ENV === 'production',

  // 是否启用分析
  ENABLE_ANALYTICS: Boolean(SEO_ENV.GA_MEASUREMENT_ID),

  // 是否启用搜索引擎验证
  ENABLE_SEARCH_CONSOLE: Boolean(SEO_ENV.GSC_VERIFICATION),

  // 是否启用百度验证
  ENABLE_BAIDU: Boolean(SEO_ENV.BAIDU_VERIFICATION),

  // 是否为生产环境
  IS_PRODUCTION: SEO_ENV.NODE_ENV === 'production',

  // 是否启用调试模式
  DEBUG_MODE: SEO_ENV.NODE_ENV === 'development',
} as const;
