'use client';

import { KeyboardShortcut } from '@/types/navigation';
import { useState } from 'react';

interface ShortcutHelpTooltipProps {
  shortcuts: KeyboardShortcut[];
  className?: string;
}

/**
 * Tooltip component that displays available keyboard shortcuts
 * Provides help information for users about navigation shortcuts
 */
export function ShortcutHelpTooltip({ shortcuts, className = '' }: ShortcutHelpTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    const category = shortcut.category || 'general';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  const categoryNames = {
    navigation: '导航',
    analysis: '分析',
    general: '通用',
  };

  return (
    <div className={`relative inline-block ${className}`}>
      {/* Help trigger button */}
      <button
        type="button"
        className="inline-flex items-center justify-center w-6 h-6 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
        aria-label="显示键盘快捷键帮助"
      >
        <svg
          className="w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>

      {/* Tooltip content */}
      {isVisible && (
        <div
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          role="tooltip"
        >
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-64 max-w-sm">
            {/* Arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200 dark:border-t-gray-700"></div>

            <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3">
              键盘快捷键
            </h3>

            <div className="space-y-3">
              {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
                <div key={category}>
                  <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">
                    {categoryNames[category as keyof typeof categoryNames] || category}
                  </h4>

                  <div className="space-y-1">
                    {categoryShortcuts
                      .filter((shortcut) => shortcut.enabled)
                      .map((shortcut) => (
                        <div
                          key={shortcut.key}
                          className="flex items-center justify-between text-xs"
                        >
                          <span className="text-gray-600 dark:text-gray-300">
                            {shortcut.description}
                          </span>
                          <kbd className="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs font-mono rounded border border-gray-300 dark:border-gray-600">
                            {formatShortcutKey(shortcut.key)}
                          </kbd>
                        </div>
                      ))}
                  </div>
                </div>
              ))}
            </div>

            {shortcuts.length === 0 && (
              <p className="text-xs text-gray-500 dark:text-gray-400">暂无可用的键盘快捷键</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Format shortcut key for display
 */
function formatShortcutKey(key: string): string {
  return key
    .replace('Alt+', '⌥ ')
    .replace('Ctrl+', '⌃ ')
    .replace('Cmd+', '⌘ ')
    .replace('Shift+', '⇧ ');
}
