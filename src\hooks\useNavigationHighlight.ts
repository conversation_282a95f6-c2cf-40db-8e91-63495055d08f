'use client';

import { usePathname } from 'next/navigation';
import { useMemo } from 'react';
import { usePageTitle } from './usePageTitle';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  current: boolean;
}

/**
 * Hook for managing navigation state highlighting and page title synchronization
 * Provides utilities for determining active navigation states and updating page titles
 */
export function useNavigationHighlight() {
  const pathname = usePathname();

  // Determine if current page is analysis-related
  const isAnalysisPage = useMemo(() => {
    if (!pathname) return false;
    return pathname.startsWith('/analysis');
  }, [pathname]);

  // Determine specific analysis page type
  const analysisPageType = useMemo(() => {
    if (!pathname) return null;
    if (pathname === '/analysis/history') return 'history';
    if (pathname === '/analysis/compare') return 'compare';
    if (
      pathname.startsWith('/analysis/') &&
      pathname !== '/analysis/history' &&
      pathname !== '/analysis/compare'
    ) {
      return 'detail';
    }
    return null;
  }, [pathname]);

  // Check if a navigation item should be highlighted
  const isCurrentPage = (href: string) => {
    if (!pathname) return false;

    // Exact match for most pages
    if (href === pathname) return true;

    // Special handling for analysis pages
    if (href.startsWith('/analysis')) {
      return isAnalysisPage;
    }

    // Handle hash links for homepage sections
    if (href.startsWith('/#') && pathname === '/') {
      return false; // Don't highlight hash links as active
    }

    return false;
  };

  // Get navigation item classes with active state
  const getNavigationItemClasses = (href: string, baseClasses: string = '') => {
    const isActive = isCurrentPage(href);
    const activeClasses = isActive
      ? 'text-blue-600 dark:text-blue-400 font-medium'
      : 'text-slate-600 dark:text-slate-300 hover:text-blue-600';

    return `${baseClasses} ${activeClasses} transition-colors`.trim();
  };

  // Generate breadcrumb items for current page
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (!pathname) return [{ label: '首页', href: '/', current: true }];

    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [{ label: '首页', href: '/', current: false }];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      let label = segment;

      // Map path segments to Chinese labels
      switch (segment) {
        case 'tasks':
          label = '任务列表';
          break;
        case 'analysis':
          label = '分析中心';
          break;
        case 'history':
          label = '分析历史';
          break;
        case 'compare':
          label = '分析对比';
          break;
        case 'create-task':
          label = '创建任务';
          break;
        default:
          // For dynamic segments like analysis IDs
          if (
            pathSegments[index - 1] === 'analysis' &&
            segment !== 'history' &&
            segment !== 'compare'
          ) {
            label = `分析详情`;
          }
          break;
      }

      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast,
      });
    });

    return breadcrumbs;
  };

  // Auto-update page title based on current route
  const pageConfig = usePageTitle();

  return {
    currentPath: pathname || '/',
    isAnalysisPage,
    analysisPageType,
    isCurrentPage,
    getNavigationItemClasses,
    generateBreadcrumbs,
    pageConfig,
  };
}
