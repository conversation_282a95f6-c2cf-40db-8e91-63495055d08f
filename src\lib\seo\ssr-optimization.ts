/**
 * 服务端渲染和预渲染优化
 * 确保关键 SEO 内容的 SSR 支持和性能优化
 */

import { Metadata } from 'next';
import { MetadataGenerator } from './metadata-generator';
import { getRuntimeSEOConfig } from './runtime-config';
import type { SupportedLocale, PageSEOProps } from '@/types/seo';

export interface SSROptimizationConfig {
  enablePreloading: boolean;
  enableCriticalCSS: boolean;
  enableResourceHints: boolean;
  enableStaticGeneration: boolean;
  criticalResources: string[];
}

export interface PreloadResource {
  href: string;
  as: 'script' | 'style' | 'font' | 'image' | 'fetch';
  type?: string;
  crossorigin?: 'anonymous' | 'use-credentials';
  media?: string;
}

/**
 * SSR 优化管理器
 */
export class SSROptimizer {
  private config: SSROptimizationConfig;
  private runtimeConfig: ReturnType<typeof getRuntimeSEOConfig>;

  constructor(config?: Partial<SSROptimizationConfig>) {
    this.config = {
      enablePreloading: true,
      enableCriticalCSS: true,
      enableResourceHints: true,
      enableStaticGeneration: true,
      criticalResources: ['/tradingAgent.png', '/fonts/inter-var.woff2'],
      ...config,
    };
    this.runtimeConfig = getRuntimeSEOConfig();
  }

  /**
   * 生成优化的页面元数据
   */
  async generateOptimizedMetadata(props: PageSEOProps): Promise<Metadata> {
    const generator = new MetadataGenerator(props.locale);
    const baseMetadata = generator.generatePageMetadata(props);

    // 添加预加载资源
    const preloadLinks = this.generatePreloadLinks(props.page);

    // 添加资源提示
    const resourceHints = this.generateResourceHints();

    // 合并所有链接 - 确保类型兼容性
    const existingLinks = Array.isArray(baseMetadata.other?.link)
      ? baseMetadata.other.link
      : baseMetadata.other?.link
      ? [baseMetadata.other.link]
      : [];

    const allLinks = [...existingLinks, ...preloadLinks, ...resourceHints];

    // Return metadata without the problematic link field for now
    return {
      ...baseMetadata,
      // Note: Link preloading will be handled by the layout component instead
    };
  }

  /**
   * 生成预加载链接
   */
  private generatePreloadLinks(
    page: string
  ): Array<{ rel: string; href: string; as?: string; type?: string; crossOrigin?: string }> {
    const links: Array<{
      rel: string;
      href: string;
      as?: string;
      type?: string;
      crossOrigin?: string;
    }> = [];

    if (!this.config.enablePreloading) {
      return links;
    }

    // 关键字体预加载
    links.push({
      rel: 'preload',
      href: '/fonts/inter-var.woff2',
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous',
    });

    // 页面特定的预加载资源
    switch (page) {
      case 'home':
        links.push({
          rel: 'preload',
          href: '/tradingAgent.png',
          as: 'image',
        });
        break;
      case 'analysis':
        // 分析页面可能需要的图表库
        links.push({
          rel: 'modulepreload',
          href: '/_next/static/chunks/recharts.js',
        });
        break;
    }

    // 添加配置中的关键资源
    this.config.criticalResources.forEach((resource) => {
      const resourceType = this.getResourceType(resource);
      if (resourceType) {
        links.push({
          rel: 'preload',
          href: resource,
          as: resourceType.as,
          type: resourceType.type,
          crossOrigin: resourceType.crossOrigin,
        });
      }
    });

    return links;
  }

  /**
   * 生成资源提示
   */
  private generateResourceHints(): Array<{ rel: string; href: string }> {
    const hints: Array<{ rel: string; href: string }> = [];

    if (!this.config.enableResourceHints) {
      return hints;
    }

    // DNS 预取
    const externalDomains = [
      'https://api.openai.com',
      'https://www.google-analytics.com',
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
    ];

    externalDomains.forEach((domain) => {
      hints.push({
        rel: 'dns-prefetch',
        href: domain,
      });
    });

    // 预连接关键域名
    hints.push({
      rel: 'preconnect',
      href: 'https://fonts.googleapis.com',
    });

    hints.push({
      rel: 'preconnect',
      href: 'https://fonts.gstatic.com',
    });

    return hints;
  }

  /**
   * 获取资源类型信息
   */
  private getResourceType(
    href: string
  ): { as: string; type?: string; crossOrigin?: string } | null {
    if (href.endsWith('.woff2') || href.endsWith('.woff')) {
      return { as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' };
    }
    if (href.endsWith('.css')) {
      return { as: 'style' };
    }
    if (href.endsWith('.js')) {
      return { as: 'script' };
    }
    if (href.match(/\.(png|jpg|jpeg|gif|webp|avif)$/)) {
      return { as: 'image' };
    }
    return null;
  }

  /**
   * 生成关键 CSS 内联样式
   */
  generateCriticalCSS(page: string): string | null {
    if (!this.config.enableCriticalCSS) {
      return null;
    }

    // 这里应该根据页面类型返回关键 CSS
    // 在实际项目中，可以使用工具如 critical 来提取关键 CSS
    const criticalStyles: Record<string, string> = {
      home: `
        /* 首页关键样式 */
        .hero-section { display: flex; align-items: center; min-height: 100vh; }
        .loading-spinner { animation: spin 1s linear infinite; }
      `,
      analysis: `
        /* 分析页面关键样式 */
        .analysis-dashboard { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
        .chart-container { min-height: 300px; }
      `,
    };

    return criticalStyles[page] || null;
  }

  /**
   * 获取静态生成配置
   */
  getStaticGenerationConfig(page: string): {
    revalidate?: number;
    generateStaticParams?: boolean;
  } {
    if (!this.config.enableStaticGeneration) {
      return {};
    }

    // 根据页面类型配置静态生成策略
    const configs: Record<string, { revalidate?: number; generateStaticParams?: boolean }> = {
      home: {
        revalidate: 3600, // 1小时重新验证
        generateStaticParams: true,
      },
      analysis: {
        revalidate: 300, // 5分钟重新验证（数据更新频繁）
        generateStaticParams: false, // 动态页面
      },
      tasks: {
        revalidate: 1800, // 30分钟重新验证
        generateStaticParams: true,
      },
      'create-task': {
        revalidate: 86400, // 24小时重新验证（相对静态）
        generateStaticParams: true,
      },
      messages: {
        revalidate: 600, // 10分钟重新验证
        generateStaticParams: true,
      },
    };

    return configs[page] || { revalidate: 3600 };
  }

  /**
   * 生成页面的 JSON-LD 脚本标签
   */
  generateStructuredDataScript(
    page: string,
    dynamicData?: any,
    locale?: SupportedLocale
  ): string | null {
    const generator = new MetadataGenerator(locale);
    const structuredData = generator.generateStructuredData(page, dynamicData);

    if (!structuredData || Object.keys(structuredData).length === 0) {
      return null;
    }

    return `<script type="application/ld+json">${JSON.stringify(structuredData, null, 2)}</script>`;
  }

  /**
   * 优化图片加载
   */
  getOptimizedImageProps(
    src: string,
    alt: string,
    priority = false
  ): {
    src: string;
    alt: string;
    loading: 'lazy' | 'eager';
    decoding: 'async' | 'sync';
    fetchPriority?: 'high' | 'low' | 'auto';
  } {
    return {
      src,
      alt,
      loading: priority ? 'eager' : 'lazy',
      decoding: 'async',
      fetchPriority: priority ? 'high' : 'auto',
    };
  }

  /**
   * 检查页面是否应该预渲染
   */
  shouldPrerender(page: string): boolean {
    const prerenderPages = ['home', 'tasks', 'create-task', 'messages'];
    return prerenderPages.includes(page);
  }

  /**
   * 获取页面的缓存策略
   */
  getCacheStrategy(page: string): {
    'Cache-Control': string;
    'CDN-Cache-Control'?: string;
  } {
    const strategies: Record<string, { 'Cache-Control': string; 'CDN-Cache-Control'?: string }> = {
      home: {
        'Cache-Control': 'public, max-age=3600, s-maxage=3600, stale-while-revalidate=86400',
        'CDN-Cache-Control': 'public, max-age=86400',
      },
      analysis: {
        'Cache-Control': 'public, max-age=300, s-maxage=300, stale-while-revalidate=1800',
      },
      tasks: {
        'Cache-Control': 'public, max-age=1800, s-maxage=1800, stale-while-revalidate=3600',
      },
      'create-task': {
        'Cache-Control': 'public, max-age=86400, s-maxage=86400',
        'CDN-Cache-Control': 'public, max-age=604800', // 7天
      },
      messages: {
        'Cache-Control': 'public, max-age=600, s-maxage=600, stale-while-revalidate=1800',
      },
    };

    return (
      strategies[page] || {
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      }
    );
  }
}

/**
 * 创建 SSR 优化器实例
 */
export function createSSROptimizer(config?: Partial<SSROptimizationConfig>): SSROptimizer {
  return new SSROptimizer(config);
}

/**
 * 生成优化的页面元数据（便捷函数）
 */
export async function generateOptimizedMetadata(props: PageSEOProps): Promise<Metadata> {
  const optimizer = createSSROptimizer();
  return await optimizer.generateOptimizedMetadata(props);
}

/**
 * 获取页面的静态生成配置（便捷函数）
 */
export function getStaticGenerationConfig(page: string): {
  revalidate?: number;
  generateStaticParams?: boolean;
} {
  const optimizer = createSSROptimizer();
  return optimizer.getStaticGenerationConfig(page);
}
