/**
 * 多语言 SEO 集成测试
 * 测试整个 i18n SEO 系统的集成功能
 */

import { describe, it, expect } from '@jest/globals';
import { MetadataGenerator } from '../metadata-generator';
import { I18nContentSwitcher, LanguageDetector } from '../i18n-seo';
import { PageSEOProps } from '@/types/seo';

describe('I18n SEO Integration', () => {
  describe('MetadataGenerator with I18n', () => {
    it('should generate Chinese metadata for home page', () => {
      const generator = new MetadataGenerator('zh');
      const props: PageSEOProps = {
        page: 'home',
        locale: 'zh',
      };

      const metadata = generator.generatePageMetadata(props);

      expect(metadata.title).toContain('多智能体');
      expect(metadata.description).toContain('基于多智能体');
      expect(metadata.alternates?.languages?.zh).toBeDefined();
      expect(metadata.alternates?.languages?.en).toBeDefined();
    });

    it('should generate English metadata for home page', () => {
      const generator = new MetadataGenerator('en');
      const props: PageSEOProps = {
        page: 'home',
        locale: 'en',
      };

      const metadata = generator.generatePageMetadata(props);

      expect(metadata.title).toContain('Multi-Agent');
      expect(metadata.description).toContain('multi-agent');
      expect(metadata.alternates?.languages?.zh).toBeDefined();
      expect(metadata.alternates?.languages?.en).toBeDefined();
    });

    it('should generate dynamic metadata with stock symbol', () => {
      const generator = new MetadataGenerator('zh');
      const props: PageSEOProps = {
        page: 'analysis',
        locale: 'zh',
        dynamicData: {
          stockSymbol: 'AAPL',
        },
      };

      const metadata = generator.generatePageMetadata(props);

      expect(metadata.title).toContain('AAPL');
    });
  });

  describe('Language Detection Integration', () => {
    it('should detect language and provide clean path', () => {
      const result = LanguageDetector.detect({
        pathname: '/en/analysis/123',
        acceptLanguage: 'zh-CN,zh;q=0.9',
        cookieValue: 'zh',
      });

      expect(result.locale).toBe('en');
      expect(result.cleanPath).toBe('/analysis/123');
    });

    it('should prioritize path over other detection methods', () => {
      const result = LanguageDetector.detect({
        pathname: '/en/tasks',
        acceptLanguage: 'zh-CN,zh;q=0.9',
        cookieValue: 'zh',
      });

      expect(result.locale).toBe('en');
      expect(result.cleanPath).toBe('/tasks');
    });
  });

  describe('Content Switching Integration', () => {
    it('should provide consistent content across different access methods', () => {
      // Get content directly
      const directContent = I18nContentSwitcher.getPageContent('analysis', 'zh');

      // Get content through dynamic method
      const dynamicContent = I18nContentSwitcher.getDynamicContent('analysis', 'zh');

      expect(directContent.title).toBe(dynamicContent.title);
      expect(directContent.description).toBe(dynamicContent.description);
      expect(directContent.keywords).toEqual(dynamicContent.keywords);
    });

    it('should generate language switch links correctly', () => {
      const links = I18nContentSwitcher.generateLanguageSwitchLinks('/en/analysis', 'en');

      expect(links).toHaveLength(2);

      const activeLink = links.find((link) => link.active);
      const inactiveLink = links.find((link) => !link.active);

      expect(activeLink?.locale).toBe('en');
      expect(activeLink?.href).toBe('/en/analysis');

      expect(inactiveLink?.locale).toBe('zh');
      expect(inactiveLink?.href).toBe('/analysis');
    });
  });

  describe('SEO Score Integration', () => {
    it('should calculate SEO scores for different languages', () => {
      const zhGenerator = new MetadataGenerator('zh');
      const enGenerator = new MetadataGenerator('en');

      const zhProps: PageSEOProps = { page: 'home', locale: 'zh' };
      const enProps: PageSEOProps = { page: 'home', locale: 'en' };

      const zhAnalysis = zhGenerator.getSEOAnalysis(zhProps);
      const enAnalysis = enGenerator.getSEOAnalysis(enProps);

      expect(zhAnalysis.score).toBeGreaterThan(0);
      expect(enAnalysis.score).toBeGreaterThan(0);
      expect(zhAnalysis.issues).toBeInstanceOf(Array);
      expect(enAnalysis.issues).toBeInstanceOf(Array);
    });
  });

  describe('Structured Data Integration', () => {
    it('should generate structured data for different languages', () => {
      const zhGenerator = new MetadataGenerator('zh');
      const enGenerator = new MetadataGenerator('en');

      const zhStructuredData = zhGenerator.generateStructuredData('home');
      const enStructuredData = enGenerator.generateStructuredData('home');

      expect(zhStructuredData.organization).toBeDefined();
      expect(enStructuredData.organization).toBeDefined();
      expect(zhStructuredData.website).toBeDefined();
      expect(enStructuredData.website).toBeDefined();
    });

    it('should generate JSON-LD scripts', () => {
      const generator = new MetadataGenerator('zh');
      const scripts = generator.generateJsonLdScripts('home');

      expect(scripts).toBeInstanceOf(Array);
      expect(scripts.length).toBeGreaterThan(0);

      scripts.forEach((script) => {
        expect(script).toContain('<script type="application/ld+json">');
        expect(script).toContain('</script>');
        expect(() => JSON.parse(script.match(/{[\s\S]*}/)?.[0] || '{}')).not.toThrow();
      });
    });
  });
});
