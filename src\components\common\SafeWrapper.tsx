'use client';

import React from 'react';

interface SafeWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
}

interface SafeWrapperState {
  hasError: boolean;
  error?: Error;
}

/**
 * SafeWrapper 组件
 * 用于捕获和处理组件渲染错误，特别是 webpack 模块加载错误
 * 这是一个临时的错误边界，用于处理特定的模块加载问题
 */
export class SafeWrapper extends React.Component<SafeWrapperProps, SafeWrapperState> {
  constructor(props: SafeWrapperProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): SafeWrapperState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('SafeWrapper caught an error:', error, errorInfo);
    
    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error);
    }

    // 特别处理 webpack 模块加载错误
    if (error.message.includes('Cannot read properties of undefined (reading \'call\')')) {
      console.warn('Detected webpack module loading error, attempting recovery...');
      
      // 延迟重试
      setTimeout(() => {
        this.setState({ hasError: false, error: undefined });
      }, 1000);
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 fallback
      return (
        <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-600 dark:text-yellow-400">⚠️</span>
            <span className="text-sm text-yellow-800 dark:text-yellow-200">
              组件加载出现问题，正在尝试恢复...
            </span>
          </div>
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details className="mt-2">
              <summary className="text-xs text-yellow-700 dark:text-yellow-300 cursor-pointer">
                错误详情
              </summary>
              <pre className="text-xs text-yellow-800 dark:text-yellow-200 mt-1 overflow-auto">
                {this.state.error.message}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 函数式组件版本的安全包装器
 * 使用 React 18 的错误边界 Hook（如果可用）
 */
export function SafeComponent({ 
  children, 
  fallback,
  onError 
}: SafeWrapperProps) {
  return (
    <SafeWrapper fallback={fallback} onError={onError}>
      {children}
    </SafeWrapper>
  );
}

/**
 * 高阶组件版本
 * 用于包装可能出现模块加载问题的组件
 */
export function withSafeWrapper<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  const WrappedComponent = (props: P) => (
    <SafeWrapper fallback={fallback}>
      <Component {...props} />
    </SafeWrapper>
  );

  WrappedComponent.displayName = `withSafeWrapper(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * 用于动态导入的安全包装器
 * 处理动态导入失败的情况
 */
export function SafeDynamicImport({ 
  importFn, 
  fallback = <div>Loading...</div>,
  errorFallback = <div>Failed to load component</div>
}: {
  importFn: () => Promise<{ default: React.ComponentType<any> }>;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
}) {
  const [Component, setComponent] = React.useState<React.ComponentType<any> | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    let mounted = true;

    const loadComponent = async () => {
      try {
        const module = await importFn();
        if (mounted) {
          setComponent(() => module.default);
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          setError(err as Error);
          setLoading(false);
          console.error('Dynamic import failed:', err);
        }
      }
    };

    loadComponent();

    return () => {
      mounted = false;
    };
  }, [importFn]);

  if (loading) {
    return <>{fallback}</>;
  }

  if (error || !Component) {
    return <>{errorFallback}</>;
  }

  return (
    <SafeWrapper>
      <Component />
    </SafeWrapper>
  );
}
