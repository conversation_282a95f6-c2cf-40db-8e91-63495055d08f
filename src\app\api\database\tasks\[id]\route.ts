// 数据库API路由 - 单个任务操作
import { error, success } from '@/lib/api-helpers';
import { getCurrentUser } from '@/lib/auth';
import { query } from '@/lib/db';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return error('未授权访问，请先登录', 401);
    }

    const { id: taskId } = await params;

    // 查询任务，确保只能访问自己创建的任务
    const tasks = await query('SELECT * FROM tasks WHERE task_id = ? AND created_by = ?', [
      taskId,
      currentUser.userId,
    ]);

    if (!tasks || (tasks as any[]).length === 0) {
      return error('任务不存在或无权访问', 404);
    }

    return success((tasks as any[])[0]);
  } catch (err) {
    console.error('获取任务失败:', err);
    return error('获取任务失败', 500);
  }
}
