import React from 'react';

/**
 * Next.js App Router 加载页面
 * 当页面正在加载时会自动显示此组件
 * 这是一个服务端组件，不需要 'use client' 指令
 */
export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
      <div className="text-center space-y-4">
        {/* 加载动画 */}
        <div className="relative">
          {/* 外圈旋转动画 */}
          <div className="w-16 h-16 border-4 border-slate-200 dark:border-slate-700 rounded-full animate-spin border-t-blue-600 dark:border-t-blue-400"></div>
          
          {/* 内圈脉冲动画 */}
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent rounded-full animate-pulse">
            <div className="w-full h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-20 animate-ping"></div>
          </div>
        </div>
        
        {/* 加载文本 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
            正在加载...
          </h2>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            请稍候，系统正在为您准备内容
          </p>
        </div>
        
        {/* 进度指示器 */}
        <div className="w-48 bg-slate-200 dark:bg-slate-700 rounded-full h-1 overflow-hidden">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
        </div>
        
        {/* 加载提示 */}
        <div className="text-xs text-slate-500 dark:text-slate-400 max-w-xs">
          首次加载可能需要几秒钟时间
        </div>
      </div>
    </div>
  );
}
