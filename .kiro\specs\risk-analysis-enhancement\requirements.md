# 风险分析增强需求文档

## 简介

本需求文档专注于完善 TradingAgents Web 应用的风险分析功能，特别是风险管理师（Risk Manager）智能体的实现。当前系统在 AgentStatusPanel 中已配置了 risk_manager，但缺乏与 TradingAgents 后端仓库对应的完整风险分析逻辑、提示词和状态管理机制。

需要基于 TradingAgents 后端的分析流程，实现风险管理师智能体的完整工作流，包括风险识别、量化评估、场景分析和控制策略建议。系统应与现有的分析师团队（基本面、技术面、情绪面、新闻分析师）和研究团队（多头、空头研究员）协同工作，在最终决策前提供关键的风险评估。

## 需求

### 需求 1：风险管理师智能体集成

**用户故事:** 作为系统用户，我希望风险管理师智能体能够与现有的 LangGraph 分析流程无缝集成，在适当的阶段介入风险评估。

#### 验收标准

1. 当分析师团队完成初步分析时，系统应触发风险管理师智能体开始工作
2. 风险管理师应能够访问所有分析师的报告和数据
3. 系统应在研究团队辩论后，让风险管理师评估辩论结果的风险含义
4. 风险管理师应在最终决策前提供风险评估报告
5. 系统应正确更新风险管理师的状态（pending → running → completed/failed）
6. 风险管理师应能够与其他智能体进行信息交换
7. 系统应记录风险管理师的执行时间和处理过程

### 需求 2：风险分析提示词和逻辑实现

**用户故事:** 作为开发者，我希望风险管理师智能体具有完整的分析逻辑和专业的提示词，能够进行准确的风险评估。

#### 验收标准

1. 当风险管理师启动时，系统应使用专业的风险分析提示词
2. 系统应实现市场风险评估逻辑（波动率、Beta、VaR 计算）
3. 系统应实现流动性风险分析（交易量、价差、市场深度）
4. 系统应实现信用风险评估（财务健康度、债务比率）
5. 系统应实现操作风险识别（公司治理、管理层风险）
6. 系统应能够综合各类风险，给出整体风险评级
7. 系统应生成结构化的风险分析报告

### 需求 3：风险数据收集与处理

**用户故事:** 作为风险管理师智能体，我需要获取准确的风险相关数据，以便进行科学的风险评估。

#### 验收标准

1. 当进行风险分析时，系统应获取历史价格数据用于波动率计算
2. 系统应获取财务数据用于信用风险评估
3. 系统应获取交易量数据用于流动性风险分析
4. 系统应获取行业和市场数据用于系统性风险评估
5. 系统应处理数据缺失和异常值情况
6. 系统应验证数据质量和时效性
7. 系统应缓存风险计算结果，提高效率

### 需求 4：风险指标计算与量化

**用户故事:** 作为投资者，我希望系统能够提供量化的风险指标，帮助我理解投资风险的大小。

#### 验收标准

1. 当计算市场风险时，系统应计算历史波动率和 Beta 系数
2. 系统应计算 VaR（风险价值）在不同置信水平下的值
3. 系统应计算最大回撤和回撤持续时间
4. 系统应计算夏普比率和风险调整收益
5. 系统应评估流动性指标（Amihud 比率、换手率）
6. 系统应计算相关性和集中度风险
7. 系统应提供风险指标的历史趋势分析

### 需求 5：风险场景分析与压力测试

**用户故事:** 作为风险管理师，我希望系统能够进行场景分析，评估极端情况下的潜在损失。

#### 验收标准

1. 当进行压力测试时，系统应模拟市场下跌场景（-10%、-20%、-30%）
2. 系统应分析利率变动对股价的影响
3. 系统应评估行业特定风险事件的冲击
4. 系统应进行蒙特卡洛模拟，生成价格分布
5. 系统应计算不同情景下的预期损失
6. 系统应分析相关性在压力下的变化
7. 系统应提供压力测试结果的可视化

### 需求 6：风险控制建议生成

**用户故事:** 作为投资者，我希望风险管理师能够基于风险分析，提供具体的风险控制建议。

#### 验收标准

1. 当风险过高时，系统应建议降低仓位或延迟投资
2. 系统应推荐合适的止损位设置
3. 系统应建议投资时间窗口和持有期限
4. 系统应评估风险调整后的预期收益
5. 系统应提供对冲策略建议（如果适用）
6. 系统应建议分散投资或资产配置调整
7. 系统应提供风险监控的关键指标

### 需求 7：风险报告结构化输出

**用户故事:** 作为用户，我希望能够查看结构化的风险分析报告，清晰了解各项风险评估结果。

#### 验收标准

1. 当风险分析完成时，系统应生成标准化的风险报告
2. 报告应包含风险等级（低、中、高）和综合评分
3. 报告应详细列出各类风险的评估结果
4. 报告应包含关键风险指标的数值和解释
5. 报告应提供风险控制建议和操作指导
6. 报告应包含风险预警和注意事项
7. 报告应支持前端的结构化展示和可视化

### 需求 8：风险状态管理与监控

**用户故事:** 作为系统管理员，我希望能够监控风险管理师智能体的工作状态和执行过程。

#### 验收标准

1. 当风险分析开始时，系统应更新智能体状态为"running"
2. 系统应实时显示风险分析的当前进度和阶段
3. 系统应记录风险分析的执行时间和性能指标
4. 当出现错误时，系统应记录详细的错误信息
5. 系统应支持风险分析的暂停和恢复操作
6. 系统应提供风险分析的日志和调试信息
7. 系统应在分析完成后更新状态为"completed"

### 需求 9：风险数据存储与历史记录

**用户故事:** 作为用户，我希望系统能够保存风险分析的历史记录，便于回顾和对比分析。

#### 验收标准

1. 当风险分析完成时，系统应将结果存储到数据库
2. 系统应保存风险指标的历史数据和趋势
3. 系统应记录风险评估的参数和配置
4. 系统应支持风险报告的查询和检索
5. 系统应提供风险分析的对比功能
6. 系统应维护风险数据的完整性和一致性
7. 系统应支持风险数据的导出和备份

### 需求 10：前端风险展示集成

**用户故事:** 作为用户，我希望在前端界面中能够清晰地看到风险管理师的工作状态和分析结果。

#### 验收标准

1. 当访问分析页面时，系统应显示风险管理师的状态和进度
2. 系统应在智能体状态面板中正确显示风险管理师信息
3. 系统应提供专门的风险分析报告展示组件
4. 系统应在最终决策中集成风险评估结果
5. 系统应提供风险指标的图表化展示
6. 系统应支持风险报告的详细查看和交互
7. 系统应在历史分析中包含风险评估信息
