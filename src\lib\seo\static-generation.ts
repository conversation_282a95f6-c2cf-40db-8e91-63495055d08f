/**
 * 静态生成辅助工具
 * 支持关键页面的静态生成和增量静态再生成 (ISR)
 */

import { Metadata } from 'next';
import { generateOptimizedMetadata } from './ssr-optimization';
import type { SupportedLocale, PageSEOProps } from '@/types/seo';

export interface StaticPageConfig {
  path: string;
  revalidate?: number | false;
  generateStaticParams?: boolean;
  priority: 'high' | 'medium' | 'low';
  locales: SupportedLocale[];
}

export interface StaticGenerationResult {
  paths: Array<{
    params: Record<string, string>;
    locale?: string;
  }>;
  fallback: boolean | 'blocking';
}

/**
 * 静态生成管理器
 */
export class StaticGenerationManager {
  private staticPages: StaticPageConfig[];

  constructor() {
    this.staticPages = [
      {
        path: '/',
        revalidate: 3600, // 1小时
        generateStaticParams: true,
        priority: 'high',
        locales: ['zh', 'en'],
      },
      {
        path: '/tasks',
        revalidate: 1800, // 30分钟
        generateStaticParams: true,
        priority: 'high',
        locales: ['zh', 'en'],
      },
      {
        path: '/create-task',
        revalidate: 86400, // 24小时
        generateStaticParams: true,
        priority: 'medium',
        locales: ['zh', 'en'],
      },
      {
        path: '/messages',
        revalidate: 600, // 10分钟
        generateStaticParams: true,
        priority: 'medium',
        locales: ['zh', 'en'],
      },
      {
        path: '/analysis',
        revalidate: false, // 动态页面，不预生成
        generateStaticParams: false,
        priority: 'low',
        locales: ['zh', 'en'],
      },
    ];
  }

  /**
   * 获取静态页面配置
   */
  getStaticPageConfig(path: string): StaticPageConfig | undefined {
    return this.staticPages.find((page) => page.path === path);
  }

  /**
   * 生成静态参数
   */
  async generateStaticParams(path: string): Promise<Array<{ locale: string }>> {
    const config = this.getStaticPageConfig(path);

    if (!config || !config.generateStaticParams) {
      return [];
    }

    return config.locales.map((locale) => ({ locale }));
  }

  /**
   * 获取页面的重新验证时间
   */
  getRevalidateTime(path: string): number | false {
    const config = this.getStaticPageConfig(path);
    return config?.revalidate ?? 3600; // 默认1小时
  }

  /**
   * 检查页面是否应该静态生成
   */
  shouldGenerateStatically(path: string): boolean {
    const config = this.getStaticPageConfig(path);
    return config?.generateStaticParams ?? false;
  }

  /**
   * 生成页面的静态元数据
   */
  async generateStaticMetadata(
    path: string,
    locale: SupportedLocale = 'zh',
    dynamicData?: any
  ): Promise<Metadata> {
    const pageType = this.getPageTypeFromPath(path);

    const seoProps: PageSEOProps = {
      page: pageType,
      locale,
      dynamicData,
    };

    return await generateOptimizedMetadata(seoProps);
  }

  /**
   * 从路径获取页面类型
   */
  private getPageTypeFromPath(
    path: string
  ): 'home' | 'tasks' | 'create-task' | 'messages' | 'analysis' {
    if (path === '/') return 'home';
    if (path === '/tasks') return 'tasks';
    if (path === '/create-task') return 'create-task';
    if (path === '/messages') return 'messages';
    if (path.startsWith('/analysis')) return 'analysis';
    return 'home';
  }

  /**
   * 获取所有需要预生成的路径
   */
  getAllStaticPaths(): Array<{
    path: string;
    locale: SupportedLocale;
    priority: 'high' | 'medium' | 'low';
  }> {
    const paths: Array<{
      path: string;
      locale: SupportedLocale;
      priority: 'high' | 'medium' | 'low';
    }> = [];

    this.staticPages
      .filter((page) => page.generateStaticParams)
      .forEach((page) => {
        page.locales.forEach((locale) => {
          paths.push({
            path: page.path,
            locale,
            priority: page.priority,
          });
        });
      });

    return paths.sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }

  /**
   * 生成 Next.js 静态路径配置
   */
  generateNextStaticPaths(path: string): StaticGenerationResult {
    const config = this.getStaticPageConfig(path);

    if (!config || !config.generateStaticParams) {
      return {
        paths: [],
        fallback: 'blocking',
      };
    }

    const paths = config.locales.map((locale) => ({
      params: {},
      locale,
    }));

    return {
      paths,
      fallback: config.priority === 'high' ? false : 'blocking',
    };
  }

  /**
   * 获取页面的缓存标签
   */
  getCacheTags(path: string, locale: SupportedLocale): string[] {
    const tags = ['seo', `page:${path}`, `locale:${locale}`];

    // 添加页面特定的标签
    if (path === '/') {
      tags.push('homepage', 'critical');
    } else if (path === '/tasks') {
      tags.push('tasks', 'dynamic');
    } else if (path === '/analysis') {
      tags.push('analysis', 'realtime');
    }

    return tags;
  }

  /**
   * 检查页面是否需要重新生成
   */
  shouldRegenerate(path: string, lastGenerated: Date, currentTime: Date = new Date()): boolean {
    const config = this.getStaticPageConfig(path);

    if (!config || config.revalidate === false) {
      return false;
    }

    const revalidateMs = (config.revalidate || 3600) * 1000;
    const timeSinceGeneration = currentTime.getTime() - lastGenerated.getTime();

    return timeSinceGeneration > revalidateMs;
  }

  /**
   * 获取页面的构建优先级
   */
  getBuildPriority(path: string): number {
    const config = this.getStaticPageConfig(path);

    if (!config) return 0;

    const priorityScores = {
      high: 100,
      medium: 50,
      low: 10,
    };

    return priorityScores[config.priority];
  }

  /**
   * 生成预渲染提示
   */
  generatePrerenderHints(): Array<{
    path: string;
    priority: number;
    headers: Record<string, string>;
  }> {
    return this.staticPages
      .filter((page) => page.generateStaticParams)
      .flatMap((page) =>
        page.locales.map((locale) => ({
          path: locale === 'zh' ? page.path : `/en${page.path}`,
          priority: this.getBuildPriority(page.path),
          headers: {
            'X-Prerender': 'true',
            'X-Priority': page.priority,
            'X-Revalidate': String(page.revalidate || 3600),
          },
        }))
      )
      .sort((a, b) => b.priority - a.priority);
  }
}

/**
 * 创建静态生成管理器实例
 */
export function createStaticGenerationManager(): StaticGenerationManager {
  return new StaticGenerationManager();
}

/**
 * 生成页面的静态参数（便捷函数）
 */
export async function generateStaticParams(path: string): Promise<Array<{ locale: string }>> {
  const manager = createStaticGenerationManager();
  return await manager.generateStaticParams(path);
}

/**
 * 获取页面的重新验证时间（便捷函数）
 */
export function getRevalidateTime(path: string): number | false {
  const manager = createStaticGenerationManager();
  return manager.getRevalidateTime(path);
}

/**
 * 生成页面的静态元数据（便捷函数）
 */
export async function generateStaticMetadata(
  path: string,
  locale: SupportedLocale = 'zh',
  dynamicData?: any
): Promise<Metadata> {
  const manager = createStaticGenerationManager();
  return await manager.generateStaticMetadata(path, locale, dynamicData);
}
