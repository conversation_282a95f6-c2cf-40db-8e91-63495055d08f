import { getCurrentUser } from '@/lib/auth';
import { query } from '@/lib/db';
import { NextRequest, NextResponse } from 'next/server';

// 更新任务状态
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return NextResponse.json({ error: '未授权访问，请先登录' }, { status: 401 });
    }

    const { id: taskId } = await params;
    const { status, error_message } = await request.json();

    // 验证状态值
    const validStatuses = ['pending', 'running', 'completed', 'failed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `无效的状态值: ${status}。有效状态: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    // 首先检查工作流是否存在且属于当前用户
    const existingWorkflows = await query(
      'SELECT id FROM workflows WHERE workflow_id = ? AND created_by = ?',
      [taskId, currentUser.userId]
    );

    if (!existingWorkflows || (existingWorkflows as any[]).length === 0) {
      return NextResponse.json({ error: '任务不存在或无权访问' }, { status: 404 });
    }

    // 构建更新SQL
    let updateSql = 'UPDATE workflows SET status = ?, updated_at = NOW()';
    let updateParams: (string | number)[] = [status, taskId, currentUser.userId];

    // 根据状态添加时间戳
    if (status === 'running') {
      updateSql += ', started_at = NOW()';
    } else if (status === 'completed' || status === 'failed' || status === 'cancelled') {
      updateSql += ', completed_at = NOW()';
    }

    // 如果有错误消息，添加到更新中
    if (error_message) {
      updateSql += ', error_message = ?';
      updateParams.splice(-1, 0, error_message); // 在taskId之前插入error_message
    }

    updateSql += ' WHERE workflow_id = ? AND created_by = ?';

    // 执行更新
    const result = await query(updateSql, updateParams);

    // 检查是否更新了任何行
    if ((result as any).affectedRows === 0) {
      return NextResponse.json({ error: '任务不存在' }, { status: 404 });
    }

    // 如果任务开始运行，添加系统事件
    if (status === 'running') {
      const { v4: uuidv4 } = require('uuid');
      await query(
        `INSERT INTO workflow_events (
          event_id, workflow_id, stage_name, event_type, content, metadata
        ) VALUES (?, ?, ?, ?, ?, ?)`,
        [
          uuidv4(),
          taskId,
          'start',
          'message',
          '任务开始执行',
          JSON.stringify({ type: 'system_start' }),
        ]
      );
    }

    return NextResponse.json({
      success: true,
      message: '任务状态更新成功',
      task_id: taskId,
      status: status,
    });
  } catch (error) {
    console.error('更新任务状态失败:', error);
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return NextResponse.json(
      {
        error: '更新任务状态失败',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
