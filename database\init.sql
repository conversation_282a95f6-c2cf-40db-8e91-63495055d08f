-- TradingAgents Frontend 数据库初始化脚本
-- 版本: 4.0 (Workflow-centric Redesign)
-- 创建时间: 2025-07-29
-- 说明: 这是一个基于工作流为中心的全新数据库设计。
--       它旨在提供更清晰、规范化和可扩展的数据模型。

-- ============================================================================
-- 基础设置
-- ============================================================================
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
CREATE DATABASE IF NOT EXISTS trading_analysis;
USE trading_analysis;

-- ============================================================================
-- 核心模型：工作流 (Workflows)
-- ============================================================================

-- 1. 工作流表 (原 tasks 和 langgraph_workflows 的统一)
--    所有分析任务的唯一入口点。
CREATE TABLE IF NOT EXISTS workflows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id VARCHAR(36) UNIQUE NOT NULL COMMENT '工作流唯一标识 (UUID)',
    thread_id VARCHAR(100) COMMENT 'LangGraph线程ID',
    ticker VARCHAR(20) NOT NULL COMMENT '股票代码',
    title VARCHAR(255) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '工作流状态',
    current_stage VARCHAR(50) DEFAULT 'start' COMMENT '当前执行阶段',
    progress INT DEFAULT 0 COMMENT '整体进度百分比(0-100)',
    config JSON COMMENT '工作流配置参数',
    created_by VARCHAR(100) DEFAULT 'system' COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_ticker (ticker),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='核心工作流表，所有分析任务的起点';


-- ============================================================================
-- 分析产出模型 (Analysis Outputs)
-- ============================================================================

-- 2. 分析报告目录表 (原 analysis_results 的替代品)
--    存储所有类型分析报告的通用元数据。
CREATE TABLE IF NOT EXISTS analyst_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_id VARCHAR(36) UNIQUE NOT NULL COMMENT '报告唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    analyst_type ENUM('fundamental', 'technical', 'sentiment', 'news') NOT NULL COMMENT '分析师类型',
    summary TEXT COMMENT '报告摘要',
    status ENUM('completed', 'failed') DEFAULT 'completed' COMMENT '生成状态',
    execution_time_ms INT COMMENT '执行耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_report_id (report_id),
    INDEX idx_workflow_analyst (workflow_id, analyst_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析报告的目录索引';

-- 3. 技术分析详情表
CREATE TABLE IF NOT EXISTS technical_analysis_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_id VARCHAR(36) UNIQUE NOT NULL COMMENT '关联报告ID',
    trading_signal ENUM('buy', 'sell', 'hold', 'avoid') COMMENT '交易信号',
    trend_signal ENUM('up', 'down', 'sideways') COMMENT '趋势信号',
    support_level DECIMAL(10, 2) COMMENT '支撑位',
    resistance_level DECIMAL(10, 2) COMMENT '阻力位',
    stop_loss_level DECIMAL(10, 2) COMMENT '止损位',
    target_price DECIMAL(10, 2) COMMENT '目标价',
    rsi_value DECIMAL(5, 2) COMMENT 'RSI指标值',
    macd_signal ENUM('golden_cross', 'death_cross', 'neutral') COMMENT 'MACD信号',
    key_levels JSON COMMENT '其他关键价位',
    FOREIGN KEY (report_id) REFERENCES analyst_reports(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技术分析报告的独有结构化数据';

-- 4. 情绪分析详情表
CREATE TABLE IF NOT EXISTS sentiment_analysis_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_id VARCHAR(36) UNIQUE NOT NULL COMMENT '关联报告ID',
    overall_sentiment ENUM('very_positive', 'positive', 'neutral', 'negative', 'very_negative') COMMENT '整体情绪',
    sentiment_score DECIMAL(5, 4) COMMENT '情绪分数 (-1 to 1)',
    positive_news_count INT DEFAULT 0,
    negative_news_count INT DEFAULT 0,
    neutral_news_count INT DEFAULT 0,
    key_drivers TEXT COMMENT '关键情绪驱动因素',
    FOREIGN KEY (report_id) REFERENCES analyst_reports(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='情绪分析报告的独有结构化数据';

-- 5. 新闻分析详情表
CREATE TABLE IF NOT EXISTS news_analysis_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_id VARCHAR(36) UNIQUE NOT NULL COMMENT '关联报告ID',
    impact_score DECIMAL(5, 4) COMMENT '新闻影响分',
    key_news_summary TEXT COMMENT '关键新闻摘要',
    short_term_outlook TEXT COMMENT '短期展望',
    long_term_outlook TEXT COMMENT '长期展望',
    FOREIGN KEY (report_id) REFERENCES analyst_reports(report_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分析报告的独有结构化数据';

-- 6. 研究报告主表 (原 researcher_results 的替代品)
CREATE TABLE IF NOT EXISTS research_reports (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    report_id VARCHAR(36) UNIQUE NOT NULL COMMENT '研究报告唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    researcher_type ENUM('bull', 'bear') NOT NULL COMMENT '研究员类型 (多头/空头)',
    summary TEXT COMMENT '研究报告整体摘要',
    confidence_level DECIMAL(3, 2) COMMENT '信心水平 (0-1)',
    target_price DECIMAL(10, 2) COMMENT '目标价格',
    time_horizon VARCHAR(50) COMMENT '时间范围',
    status ENUM('completed', 'failed') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_report_id (report_id),
    INDEX idx_workflow_researcher (workflow_id, researcher_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多头/空头研究报告主表';

-- 7. 研究论点表 (结构化存储论点、证据、反驳)
CREATE TABLE IF NOT EXISTS research_arguments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    argument_id VARCHAR(36) UNIQUE NOT NULL COMMENT '论点唯一标识 (UUID)',
    report_id VARCHAR(36) NOT NULL COMMENT '关联研究报告ID',
    parent_argument_id VARCHAR(36) COMMENT '父论点ID (用于构建论证树)',
    argument_type ENUM('main_argument', 'evidence', 'counter_argument', 'rebuttal') NOT NULL COMMENT '论点类型',
    content TEXT NOT NULL COMMENT '论点/证据的具体内容',
    source VARCHAR(255) COMMENT '证据来源',
    strength_score DECIMAL(3, 2) COMMENT '论点强度评分 (0-1)',
    sequence_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES research_reports(report_id) ON DELETE CASCADE,
    FOREIGN KEY (parent_argument_id) REFERENCES research_arguments(argument_id) ON DELETE SET NULL,
    INDEX idx_report_id (report_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结构化存储研究报告的论证过程';

-- 8. 辩论会话主表
CREATE TABLE IF NOT EXISTS debate_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(36) UNIQUE NOT NULL COMMENT '辩论会话唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    status ENUM('in_progress', 'completed', 'cancelled') DEFAULT 'in_progress',
    total_rounds INT DEFAULT 0,
    summary TEXT COMMENT '辩论会话最终总结',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='一场完整辩论的元信息';

-- 9. 辩论轮次表
CREATE TABLE IF NOT EXISTS debate_rounds (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    round_id VARCHAR(36) UNIQUE NOT NULL COMMENT '辩论轮次唯一标识 (UUID)',
    session_id VARCHAR(36) NOT NULL COMMENT '关联辩论会话ID',
    round_number INT NOT NULL,
    focus_question TEXT COMMENT '本轮辩论的核心问题',
    round_summary TEXT COMMENT '本轮辩论小结',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES debate_sessions(session_id) ON DELETE CASCADE,
    INDEX idx_session_round (session_id, round_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论中每一轮的元信息';

-- 10. 辩论发言表
CREATE TABLE IF NOT EXISTS debate_utterances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    utterance_id VARCHAR(36) UNIQUE NOT NULL COMMENT '发言唯一标识 (UUID)',
    round_id VARCHAR(36) NOT NULL COMMENT '关联辩论轮次ID',
    participant_type ENUM('bull', 'bear', 'moderator') NOT NULL COMMENT '发言者身份',
    utterance_type ENUM('statement', 'question', 'rebuttal', 'summary') NOT NULL COMMENT '发言类型',
    content TEXT NOT NULL COMMENT '发言的具体内容',
    rebuttal_to_utterance_id VARCHAR(36) COMMENT '反驳的发言ID (构建交锋图)',
    related_argument_id VARCHAR(36) COMMENT '关联的研究论点ID',
    sequence_in_round INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (round_id) REFERENCES debate_rounds(round_id) ON DELETE CASCADE,
    FOREIGN KEY (rebuttal_to_utterance_id) REFERENCES debate_utterances(utterance_id) ON DELETE SET NULL,
    FOREIGN KEY (related_argument_id) REFERENCES research_arguments(argument_id) ON DELETE SET NULL,
    INDEX idx_round_id (round_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记录每一次具体的辩论发言';

-- 11. 共识评估结果表
CREATE TABLE IF NOT EXISTS consensus_evaluations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    consensus_id VARCHAR(36) UNIQUE NOT NULL COMMENT '共识评估唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    bull_strength DECIMAL(3, 2) COMMENT '多头观点强度 (0-1)',
    bear_strength DECIMAL(3, 2) COMMENT '空头观点强度 (0-1)',
    consensus_direction ENUM('bullish', 'bearish', 'neutral') COMMENT '共识方向',
    consensus_confidence DECIMAL(3, 2) COMMENT '共识置信度 (0-1)',
    synthesis_summary TEXT COMMENT '综合总结',
    key_agreement_points JSON COMMENT '关键共识点',
    key_disagreement_points JSON COMMENT '关键分歧点',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='多头空头辩论后的共识评估结果';

-- 12. 最终决策表 (原 trading_decisions)
CREATE TABLE IF NOT EXISTS final_decisions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    decision_id VARCHAR(36) UNIQUE NOT NULL COMMENT '决策唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    decision_type ENUM('buy', 'sell', 'hold', 'avoid') NOT NULL COMMENT '决策类型',
    confidence_level DECIMAL(3, 2) COMMENT '决策信心水平(0-1)',
    decision_rationale TEXT COMMENT '决策理由',
    entry_price_range JSON COMMENT '入场价格区间',
    stop_loss_price DECIMAL(10, 2) COMMENT '止损价格',
    take_profit_price DECIMAL(10, 2) COMMENT '止盈价格',
    position_size_percentage DECIMAL(5, 2) COMMENT '建议仓位百分比',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流的最终交易决策';


-- ============================================================================
-- 辅助模型 (Supporting Models)
-- ============================================================================

-- 13. 工作流事件日志 (原 messages, workflow_messages, tool_calls 的统一)
CREATE TABLE IF NOT EXISTS workflow_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    event_id VARCHAR(36) UNIQUE NOT NULL COMMENT '事件唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    stage_name VARCHAR(50) COMMENT '所属阶段',
    event_type ENUM('message', 'tool_call', 'state_change', 'error', 'log') NOT NULL COMMENT '事件类型',
    content TEXT NOT NULL COMMENT '事件内容或消息',
    metadata JSON COMMENT '事件元数据 (如工具参数、消息发送者等)',
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') COMMENT '事件状态(主要用于tool_call)',
    duration_ms INT COMMENT '执行时长(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一记录工作流所有事件和消息';

-- 14. 状态快照表 (原 workflow_state_snapshots)
CREATE TABLE IF NOT EXISTS workflow_state_snapshots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    snapshot_id VARCHAR(36) UNIQUE NOT NULL COMMENT '快照唯一标识 (UUID)',
    workflow_id VARCHAR(36) NOT NULL COMMENT '关联工作流ID',
    stage_name VARCHAR(50) NOT NULL COMMENT '阶段名称',
    state_data JSON NOT NULL COMMENT '完整状态数据',
    checkpoint_id VARCHAR(100) COMMENT 'LangGraph检查点ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    INDEX idx_workflow_stage (workflow_id, stage_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工作流在各阶段的完整状态快照';

-- 15. 用户表 (完整的认证系统支持)
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(36) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否已验证',
    avatar_url VARCHAR(500) COMMENT '用户头像URL',
    role ENUM('user', 'admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    verification_token VARCHAR(255) COMMENT '邮箱验证令牌',
    reset_token VARCHAR(255) COMMENT '密码重置令牌',
    reset_token_expires TIMESTAMP NULL COMMENT '密码重置令牌过期时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_user_id (user_id),
    INDEX idx_verification_token (verification_token),
    INDEX idx_reset_token (reset_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表 - 完整的认证系统支持';

-- 16. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(36) UNIQUE NOT NULL COMMENT '会话唯一标识',
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    token TEXT NOT NULL COMMENT '访问令牌',
    refresh_token TEXT NOT NULL COMMENT '刷新令牌',
    expires_at TIMESTAMP NOT NULL COMMENT '访问令牌过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新令牌过期时间',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '客户端User-Agent',
    is_active BOOLEAN DEFAULT TRUE COMMENT '会话是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话管理表';

-- 17. 用户活动日志表
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    activity_type VARCHAR(50) NOT NULL COMMENT '活动类型 (login, logout, register, etc.)',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '客户端User-Agent',
    details JSON COMMENT '活动详细信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- 18. 系统日志表 (保留)
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    log_id VARCHAR(36) UNIQUE NOT NULL,
    workflow_id VARCHAR(36) COMMENT '关联工作流ID',
    log_level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL') NOT NULL,
    component VARCHAR(100) NOT NULL,
    operation VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_log_level (log_level),
    INDEX idx_component (component),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统级别的操作日志';


-- ============================================================================
-- 视图定义 (Views)
-- ============================================================================

-- 1. 工作流概览视图
CREATE OR REPLACE VIEW workflow_overview AS
SELECT
    w.workflow_id,
    w.ticker,
    w.title,
    w.status,
    w.current_stage,
    w.progress,
    w.created_at,
    w.completed_at,
    TIMESTAMPDIFF(SECOND, w.started_at, w.completed_at) as duration_seconds,
    (SELECT COUNT(*) FROM analyst_reports ar WHERE ar.workflow_id = w.workflow_id) as report_count,
    (SELECT COUNT(*) FROM workflow_events we WHERE we.workflow_id = w.workflow_id AND we.event_type = 'error') as error_count
FROM workflows w;

-- 2. 完整报告视图
CREATE OR REPLACE VIEW full_final_report AS
SELECT
    w.workflow_id,
    w.ticker,
    w.title,
    w.status,
    fd.decision_type,
    fd.confidence_level,
    fd.decision_rationale,
    (SELECT GROUP_CONCAT(ar.summary SEPARATOR '\n---\n') FROM analyst_reports ar WHERE ar.workflow_id = w.workflow_id) as analyst_summaries,
    (SELECT GROUP_CONCAT(rr.summary SEPARATOR '\n---\n') FROM research_reports rr WHERE rr.workflow_id = w.workflow_id) as research_summaries,
    ds.summary as debate_summary
FROM workflows w
LEFT JOIN final_decisions fd ON w.workflow_id = fd.workflow_id
LEFT JOIN debate_sessions ds ON w.workflow_id = ds.workflow_id
WHERE w.status = 'completed';


-- ============================================================================
-- 存储过程 (Stored Procedures)
-- ============================================================================

-- 创建新的工作流
DROP PROCEDURE IF EXISTS CreateWorkflow;
DELIMITER //
CREATE PROCEDURE CreateWorkflow(
    IN p_workflow_id VARCHAR(36),
    IN p_ticker VARCHAR(20),
    IN p_title VARCHAR(255),
    IN p_description TEXT,
    IN p_config JSON,
    IN p_created_by VARCHAR(100)
)
BEGIN
    INSERT INTO workflows (workflow_id, ticker, title, description, config, created_by, status)
    VALUES (p_workflow_id, p_ticker, p_title, p_description, p_config, p_created_by, 'pending');
END //
DELIMITER ;

-- 更新工作流状态
DROP PROCEDURE IF EXISTS UpdateWorkflowStatus;
DELIMITER //
CREATE PROCEDURE UpdateWorkflowStatus(
    IN p_workflow_id VARCHAR(36),
    IN p_current_stage VARCHAR(50),
    IN p_progress INT,
    IN p_status ENUM('pending', 'running', 'completed', 'failed', 'cancelled'),
    IN p_error_message TEXT
)
BEGIN
    UPDATE workflows SET
        current_stage = p_current_stage,
        progress = p_progress,
        status = p_status,
        error_message = p_error_message,
        updated_at = NOW(),
        started_at = CASE WHEN started_at IS NULL AND p_status = 'running' THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN p_status IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE NULL END
    WHERE workflow_id = p_workflow_id;
END //
DELIMITER ;

-- 记录工作流事件
DROP PROCEDURE IF EXISTS LogWorkflowEvent;
DELIMITER //
CREATE PROCEDURE LogWorkflowEvent(
    IN p_event_id VARCHAR(36),
    IN p_workflow_id VARCHAR(36),
    IN p_stage_name VARCHAR(50),
    IN p_event_type VARCHAR(50),
    IN p_content TEXT,
    IN p_metadata JSON
)
BEGIN
    INSERT INTO workflow_events(event_id, workflow_id, stage_name, event_type, content, metadata)
    VALUES(p_event_id, p_workflow_id, p_stage_name, p_event_type, p_content, p_metadata);
END //
DELIMITER ;


-- ============================================================================
-- 完成
-- ============================================================================
SET FOREIGN_KEY_CHECKS = 1;

SELECT 'TradingAgents Database V4.0 (Workflow-centric) initialized successfully!' as status;
