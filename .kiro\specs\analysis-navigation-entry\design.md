# 分析路由入口设计文档

## 概述

本设计文档描述了 TradingAgents Web 应用中分析功能导航入口的完整设计方案。该功能旨在为现有的分析功能（分析详情、分析对比、分析历史）提供便捷的导航入口，提升用户体验和功能可发现性。

设计基于现有的 Next.js 13+ App Router 架构，采用 React 18 和 TypeScript，遵循现有的设计系统和交互模式。重点关注响应式设计、无障碍访问、性能优化和用户体验的一致性。

## 架构

### 整体架构设计

```mermaid
graph TB
    A[Header Component] --> B[Navigation Menu]
    B --> C[Analysis Dropdown]
    C --> D[Analysis History]
    C --> E[Analysis Compare]

    F[Tasks Page] --> G[Task Cards]
    G --> H[View Analysis Button]
    H --> I[Analysis Detail Page]

    J[Analysis Pages] --> K[Breadcrumb Navigation]
    K --> L[Page Context]

    M[Mobile Navigation] --> N[Hamburger Menu]
    N --> O[Analysis Section]

    P[Keyboard Navigation] --> Q[Shortcut Keys]
    Q --> R[Focus Management]
```

### 组件层次结构

```
Header (Enhanced)
├── Desktop Navigation
│   ├── Logo
│   ├── Public Navigation Items
│   ├── Analysis Dropdown (New)
│   │   ├── Analysis History Link
│   │   ├── Analysis Compare Link
│   │   └── Quick Actions
│   ├── Protected Navigation Items
│   └── User Menu
└── Mobile Navigation
    ├── Hamburger Button
    └── Mobile Menu (Enhanced)
        ├── Public Items
        ├── Analysis Section (New)
        │   ├── Analysis History
        │   └── Analysis Compare
        └── User Section
```

### 路由架构

现有路由结构保持不变，新增导航入口：

```
/                    # 首页
/tasks              # 任务列表 (Enhanced)
/analysis/history   # 分析历史 (Enhanced Navigation)
/analysis/compare   # 分析对比 (Enhanced Navigation)
/analysis/[id]      # 分析详情 (Enhanced Entry Points)
```

## 组件和接口

### 1. Enhanced Header Component

**位置**: `src/components/layout/Header.tsx`

**主要增强**:

- 添加分析功能下拉菜单
- 改进移动端导航体验
- 增加键盘导航支持
- 优化无障碍访问

**接口定义**:

```typescript
interface AnalysisDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
}

interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  description?: string;
}

interface EnhancedHeaderProps {
  currentPath?: string;
  analysisStats?: {
    totalAnalyses: number;
    recentAnalyses: number;
  };
}
```

**核心功能**:

- 响应式分析功能下拉菜单
- 智能高亮当前页面状态
- 键盘快捷键支持
- 移动端优化的触摸交互

### 2. Analysis Dropdown Component

**位置**: `src/components/navigation/AnalysisDropdown.tsx`

**功能特性**:

- 分析历史快速访问
- 分析对比功能入口
- 最近分析快捷链接
- 统计信息展示

**接口定义**:

```typescript
interface AnalysisDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
  recentAnalyses?: AnalysisItem[];
  stats?: AnalysisStats;
}

interface AnalysisItem {
  id: string;
  ticker: string;
  title: string;
  status: 'completed' | 'running' | 'failed';
  createdAt: string;
}

interface AnalysisStats {
  total: number;
  completed: number;
  running: number;
  todayCount: number;
}
```

### 3. Enhanced Task Cards

**位置**: `src/components/tasks/TaskCard.tsx` (New Component)

**功能增强**:

- 智能显示"查看分析"按钮
- 分析状态实时更新
- 一键跳转到分析详情
- 加载状态和错误处理

**接口定义**:

```typescript
interface TaskCardProps {
  task: Task;
  analysisStatus?: AnalysisStatus;
  onViewAnalysis: (taskId: string) => void;
  onStartAnalysis: (taskId: string) => void;
  loading?: boolean;
}

interface AnalysisActionButtonProps {
  task: Task;
  status?: AnalysisStatus;
  onClick: () => void;
  loading?: boolean;
}
```

### 4. Breadcrumb Navigation Component

**位置**: `src/components/navigation/Breadcrumb.tsx`

**功能特性**:

- 动态生成面包屑路径
- 支持自定义页面标题
- 响应式设计
- 无障碍访问支持

**接口定义**:

```typescript
interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}
```

### 5. Navigation State Hook

**位置**: `src/hooks/useNavigationState.ts`

**功能特性**:

- 当前路径状态管理
- 导航历史跟踪
- 快捷键状态管理
- 分析统计数据获取

**接口定义**:

```typescript
interface NavigationState {
  currentPath: string;
  isAnalysisPage: boolean;
  analysisStats: AnalysisStats | null;
  recentAnalyses: AnalysisItem[];
  loading: boolean;
}

interface UseNavigationStateReturn extends NavigationState {
  refreshStats: () => Promise<void>;
  navigateToAnalysis: (id: string) => void;
  navigateToHistory: () => void;
  navigateToCompare: (ids?: string[]) => void;
}
```

## 数据模型

### 1. Navigation Context

```typescript
interface NavigationContext {
  currentPath: string;
  breadcrumbs: BreadcrumbItem[];
  analysisStats: AnalysisStats;
  recentAnalyses: AnalysisItem[];
  shortcuts: KeyboardShortcut[];
}

interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  enabled: boolean;
}
```

### 2. Analysis Statistics

```typescript
interface AnalysisStats {
  total: number;
  completed: number;
  running: number;
  failed: number;
  todayCount: number;
  weekCount: number;
  successRate: number;
  avgDuration: number;
}
```

### 3. Navigation Analytics

```typescript
interface NavigationEvent {
  eventType: 'click' | 'keyboard' | 'touch';
  target: string;
  path: string;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}

interface NavigationMetrics {
  clickCounts: Record<string, number>;
  pathTransitions: Record<string, string[]>;
  shortcutUsage: Record<string, number>;
  errorRates: Record<string, number>;
}
```

## 错误处理

### 1. 导航错误处理策略

**路由错误**:

- 无效路径自动重定向到首页
- 权限不足显示友好提示
- 网络错误提供重试机制

**组件错误**:

- 下拉菜单加载失败的降级处理
- 分析统计获取失败的默认显示
- 快捷键冲突的智能处理

**实现方案**:

```typescript
// 错误边界组件
class NavigationErrorBoundary extends React.Component {
  // 捕获导航相关错误
  // 提供降级 UI
  // 记录错误日志
}

// 错误处理 Hook
function useNavigationErrorHandler() {
  const handleRouteError = (error: Error, path: string) => {
    // 记录错误
    // 显示用户友好提示
    // 提供恢复选项
  };

  return { handleRouteError };
}
```

### 2. 性能错误处理

**加载超时**:

- 设置合理的超时时间
- 提供加载状态指示
- 支持手动重试

**内存泄漏防护**:

- 组件卸载时清理事件监听
- 取消未完成的网络请求
- 清理定时器和订阅

## 测试策略

### 1. 单元测试

**组件测试**:

```typescript
// Header 组件测试
describe('Enhanced Header', () => {
  test('renders analysis dropdown correctly', () => {});
  test('handles keyboard navigation', () => {});
  test('displays correct active state', () => {});
});

// Analysis Dropdown 测试
describe('Analysis Dropdown', () => {
  test('shows recent analyses', () => {});
  test('handles click events', () => {});
  test('closes on outside click', () => {});
});
```

**Hook 测试**:

```typescript
// Navigation State Hook 测试
describe('useNavigationState', () => {
  test('tracks current path correctly', () => {});
  test('fetches analysis stats', () => {});
  test('handles navigation actions', () => {});
});
```

### 2. 集成测试

**导航流程测试**:

- 从任务列表到分析详情的完整流程
- 分析历史页面的筛选和导航
- 分析对比功能的多步骤操作

**响应式测试**:

- 不同屏幕尺寸下的导航体验
- 移动端触摸交互测试
- 键盘导航的完整性测试

### 3. 端到端测试

**用户场景测试**:

```typescript
// E2E 测试示例
describe('Analysis Navigation E2E', () => {
  test('user can navigate from tasks to analysis', async () => {
    // 登录用户
    // 访问任务列表
    // 点击查看分析
    // 验证跳转到正确页面
  });

  test('analysis dropdown works correctly', async () => {
    // 点击分析下拉菜单
    // 验证菜单项显示
    // 点击分析历史
    // 验证页面跳转
  });
});
```

### 4. 无障碍测试

**ARIA 标签测试**:

- 验证所有交互元素有正确的 ARIA 标签
- 测试屏幕阅读器兼容性
- 验证键盘导航的逻辑顺序

**对比度测试**:

- 验证所有文本和背景的对比度符合 WCAG 标准
- 测试高对比度模式的兼容性
- 验证焦点指示器的可见性

### 5. 性能测试

**加载性能**:

- 导航组件的首次加载时间
- 下拉菜单的展开响应时间
- 页面跳转的性能指标

**内存使用**:

- 长时间使用后的内存占用
- 组件卸载后的内存清理
- 事件监听器的正确清理

## 实现细节

### 1. 键盘快捷键实现

```typescript
// 快捷键配置
const SHORTCUTS = {
  ANALYSIS_HISTORY: 'Alt+H',
  ANALYSIS_COMPARE: 'Alt+C',
  BACK_TO_TASKS: 'Alt+T',
  TOGGLE_DROPDOWN: 'Alt+A',
};

// 快捷键处理 Hook
function useKeyboardShortcuts() {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey) {
        switch (event.key.toLowerCase()) {
          case 'h':
            router.push('/analysis/history');
            break;
          case 'c':
            router.push('/analysis/compare');
            break;
          // 其他快捷键处理
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
}
```

### 2. 响应式设计实现

```typescript
// 响应式断点
const BREAKPOINTS = {
  mobile: '768px',
  tablet: '1024px',
  desktop: '1280px',
};

// 响应式 Hook
function useResponsive() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  return { isMobile };
}
```

### 3. 性能优化实现

```typescript
// 组件懒加载
const AnalysisDropdown = lazy(() => import('./AnalysisDropdown'));

// 防抖处理
const debouncedSearch = useMemo(
  () =>
    debounce((query: string) => {
      // 搜索逻辑
    }, 300),
  []
);

// 缓存策略
const analysisStatsCache = new Map();

function useAnalysisStats() {
  return useQuery({
    queryKey: ['analysisStats'],
    queryFn: fetchAnalysisStats,
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    cacheTime: 10 * 60 * 1000, // 10分钟保留
  });
}
```

### 4. 无障碍访问实现

```typescript
// ARIA 标签和角色
const AnalysisDropdown = () => {
  return (
    <div role="menu" aria-label="分析功能菜单" aria-expanded={isOpen}>
      <button
        role="menuitem"
        aria-describedby="analysis-history-desc"
        onClick={() => router.push('/analysis/history')}
      >
        分析历史
      </button>
      <div id="analysis-history-desc" className="sr-only">
        查看所有历史分析记录
      </div>
    </div>
  );
};

// 焦点管理
function useFocusManagement() {
  const focusRef = useRef<HTMLElement>(null);

  const trapFocus = useCallback((element: HTMLElement) => {
    // 焦点陷阱实现
  }, []);

  return { focusRef, trapFocus };
}
```

## 设计决策和理由

### 1. 架构决策

**选择增强现有 Header 组件而非创建新组件**:

- 理由：保持设计一致性，减少代码重复
- 影响：需要仔细处理向后兼容性

**采用下拉菜单而非顶级导航项**:

- 理由：避免导航栏过于拥挤，提供更好的组织结构
- 影响：需要额外的交互设计和无障碍考虑

### 2. 用户体验决策

**在任务卡片中添加直接跳转按钮**:

- 理由：减少用户操作步骤，提供更直观的工作流程
- 影响：需要处理不同任务状态的显示逻辑

**支持键盘快捷键**:

- 理由：提升高频用户的操作效率
- 影响：需要考虑快捷键冲突和学习成本

### 3. 技术决策

**使用 React Query 进行状态管理**:

- 理由：提供更好的缓存和同步机制
- 影响：需要处理加载状态和错误状态

**采用 CSS-in-JS 而非传统 CSS**:

- 理由：更好的动态样式支持和主题切换
- 影响：可能影响首次加载性能

这个设计文档为分析路由入口功能提供了全面的技术指导，确保实现过程中的一致性和质量。所有设计决策都基于用户需求和技术最佳实践，同时考虑了可维护性和可扩展性。
