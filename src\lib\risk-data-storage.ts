/**
 * Risk Data Storage and Historical Records
 * 风险数据存储与历史记录功能实现
 *
 * 功能包括:
 * - 风险分析结果存储
 * - 风险数据检索接口
 * - 风险分析历史记录
 * - 风险数据对比和趋势分析
 */

import { RiskAssessment, SaveRiskAssessmentRequest } from '@/types/langgraph-database';
import { v4 as uuidv4 } from 'uuid';
import { getConnection, query } from './db';

// ============================================================================
// 风险数据存储接口
// ============================================================================

/**
 * 保存风险评估结果到数据库
 * 需求 9.1: 当风险分析完成时，系统应将结果存储到数据库
 */
export async function saveRiskAssessment(request: SaveRiskAssessmentRequest): Promise<string> {
  const riskId = uuidv4();

  const sql = `
    INSERT INTO risk_assessments (
      risk_id, workflow_id, overall_risk_level, risk_score, summary,
      market_risk, liquidity_risk, credit_risk, operational_risk,
      scenario_analysis, risk_metrics, recommendations, risk_controls,
      risk_warnings, status, execution_time_ms
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const params = [
    riskId,
    request.workflow_id,
    request.overall_risk_level,
    request.risk_score,
    request.summary || null,
    JSON.stringify(request.market_risk || {}),
    JSON.stringify(request.liquidity_risk || {}),
    JSON.stringify(request.credit_risk || {}),
    JSON.stringify(request.operational_risk || {}),
    JSON.stringify(request.scenario_analysis || {}),
    JSON.stringify(request.risk_metrics || {}),
    JSON.stringify(request.recommendations || {}),
    JSON.stringify(request.risk_controls || {}),
    JSON.stringify(request.risk_warnings || {}),
    request.status || 'completed',
    request.execution_time_ms || null,
  ];

  await query(sql, params);
  return riskId;
}

/**
 * 批量保存风险指标历史数据
 * 需求 9.2: 系统应保存风险指标的历史数据和趋势
 */
export async function saveRiskMetricsHistory(
  workflowId: string,
  ticker: string,
  metricsData: {
    volatility?: number;
    beta?: number;
    var_95?: number;
    var_99?: number;
    max_drawdown?: number;
    sharpe_ratio?: number;
    liquidity_ratio?: number;
    [key: string]: any;
  }
): Promise<void> {
  // 创建风险指标历史表（如果不存在）
  const createTableSql = `
    CREATE TABLE IF NOT EXISTS risk_metrics_history (
      id BIGINT PRIMARY KEY AUTO_INCREMENT,
      workflow_id VARCHAR(36) NOT NULL,
      ticker VARCHAR(10) NOT NULL,
      metric_name VARCHAR(50) NOT NULL,
      metric_value DECIMAL(15,6),
      calculation_date DATE NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_workflow_ticker (workflow_id, ticker),
      INDEX idx_ticker_date (ticker, calculation_date),
      INDEX idx_metric_name (metric_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
  `;

  await query(createTableSql);

  // 批量插入风险指标数据
  const insertSql = `
    INSERT INTO risk_metrics_history (workflow_id, ticker, metric_name, metric_value, calculation_date)
    VALUES (?, ?, ?, ?, CURDATE())
  `;

  const connection = await getConnection();
  try {
    await connection.beginTransaction();

    for (const [metricName, metricValue] of Object.entries(metricsData)) {
      if (metricValue !== null && metricValue !== undefined) {
        await connection.execute(insertSql, [workflowId, ticker, metricName, metricValue]);
      }
    }

    await connection.commit();
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

// ============================================================================
// 风险数据检索接口
// ============================================================================

/**
 * 根据工作流ID获取风险评估结果
 * 需求 9.4: 系统应支持风险报告的查询和检索
 */
export async function getRiskAssessmentByWorkflowId(
  workflowId: string
): Promise<RiskAssessment | null> {
  const sql = `
    SELECT 
      id, risk_id, workflow_id, overall_risk_level, risk_score, summary,
      market_risk, liquidity_risk, credit_risk, operational_risk,
      scenario_analysis, risk_metrics, recommendations, risk_controls,
      risk_warnings, status, execution_time_ms, created_at
    FROM risk_assessments 
    WHERE workflow_id = ? 
    ORDER BY created_at DESC 
    LIMIT 1
  `;

  const results = (await query(sql, [workflowId])) as any[];

  if (results.length === 0) {
    return null;
  }

  const row = results[0];
  return {
    id: row.id,
    risk_id: row.risk_id,
    workflow_id: row.workflow_id,
    overall_risk_level: row.overall_risk_level,
    risk_score: row.risk_score,
    summary: row.summary,
    market_risk: row.market_risk ? JSON.parse(row.market_risk) : null,
    liquidity_risk: row.liquidity_risk ? JSON.parse(row.liquidity_risk) : null,
    credit_risk: row.credit_risk ? JSON.parse(row.credit_risk) : null,
    operational_risk: row.operational_risk ? JSON.parse(row.operational_risk) : null,
    scenario_analysis: row.scenario_analysis ? JSON.parse(row.scenario_analysis) : null,
    risk_metrics: row.risk_metrics ? JSON.parse(row.risk_metrics) : null,
    recommendations: row.recommendations ? JSON.parse(row.recommendations) : null,
    risk_controls: row.risk_controls ? JSON.parse(row.risk_controls) : null,
    risk_warnings: row.risk_warnings ? JSON.parse(row.risk_warnings) : null,
    status: row.status,
    execution_time_ms: row.execution_time_ms,
    created_at: row.created_at,
  };
}

/**
 * 获取多个工作流的风险评估结果
 */
export async function getRiskAssessmentsByWorkflowIds(
  workflowIds: string[]
): Promise<RiskAssessment[]> {
  if (workflowIds.length === 0) {
    return [];
  }

  const placeholders = workflowIds.map(() => '?').join(',');
  const sql = `
    SELECT 
      id, risk_id, workflow_id, overall_risk_level, risk_score, summary,
      market_risk, liquidity_risk, credit_risk, operational_risk,
      scenario_analysis, risk_metrics, recommendations, risk_controls,
      risk_warnings, status, execution_time_ms, created_at
    FROM risk_assessments 
    WHERE workflow_id IN (${placeholders})
    ORDER BY created_at DESC
  `;

  const results = (await query(sql, workflowIds)) as any[];

  return results.map((row) => ({
    id: row.id,
    risk_id: row.risk_id,
    workflow_id: row.workflow_id,
    overall_risk_level: row.overall_risk_level,
    risk_score: row.risk_score,
    summary: row.summary,
    market_risk: row.market_risk ? JSON.parse(row.market_risk) : null,
    liquidity_risk: row.liquidity_risk ? JSON.parse(row.liquidity_risk) : null,
    credit_risk: row.credit_risk ? JSON.parse(row.credit_risk) : null,
    operational_risk: row.operational_risk ? JSON.parse(row.operational_risk) : null,
    scenario_analysis: row.scenario_analysis ? JSON.parse(row.scenario_analysis) : null,
    risk_metrics: row.risk_metrics ? JSON.parse(row.risk_metrics) : null,
    recommendations: row.recommendations ? JSON.parse(row.recommendations) : null,
    risk_controls: row.risk_controls ? JSON.parse(row.risk_controls) : null,
    risk_warnings: row.risk_warnings ? JSON.parse(row.risk_warnings) : null,
    status: row.status,
    execution_time_ms: row.execution_time_ms,
    created_at: row.created_at,
  }));
}

// ============================================================================
// 风险分析历史记录功能
// ============================================================================

/**
 * 获取风险分析历史记录
 * 需求 9.3: 系统应记录风险评估的参数和配置
 */
export async function getRiskAssessmentHistory(
  options: {
    ticker?: string;
    riskLevel?: 'low' | 'medium' | 'high';
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  } = {}
): Promise<{
  assessments: RiskAssessment[];
  total: number;
}> {
  const { ticker, riskLevel, dateFrom, dateTo, limit = 50, offset = 0 } = options;

  // 构建查询条件
  const conditions: string[] = [];
  const params: any[] = [];

  if (ticker) {
    conditions.push('w.ticker = ?');
    params.push(ticker);
  }

  if (riskLevel) {
    conditions.push('ra.overall_risk_level = ?');
    params.push(riskLevel);
  }

  if (dateFrom) {
    conditions.push('ra.created_at >= ?');
    params.push(dateFrom);
  }

  if (dateTo) {
    conditions.push('ra.created_at <= ?');
    params.push(dateTo);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // 获取总数
  const countSql = `
    SELECT COUNT(*) as total
    FROM risk_assessments ra
    JOIN workflows w ON ra.workflow_id = w.workflow_id
    ${whereClause}
  `;

  const countResult = (await query(countSql, params)) as any[];
  const total = countResult[0].total;

  // 获取分页数据
  const dataSql = `
    SELECT 
      ra.id, ra.risk_id, ra.workflow_id, ra.overall_risk_level, ra.risk_score, ra.summary,
      ra.market_risk, ra.liquidity_risk, ra.credit_risk, ra.operational_risk,
      ra.scenario_analysis, ra.risk_metrics, ra.recommendations, ra.risk_controls,
      ra.risk_warnings, ra.status, ra.execution_time_ms, ra.created_at,
      w.ticker, w.title as workflow_title
    FROM risk_assessments ra
    JOIN workflows w ON ra.workflow_id = w.workflow_id
    ${whereClause}
    ORDER BY ra.created_at DESC
    LIMIT ? OFFSET ?
  `;

  const dataParams = [...params, limit, offset];
  const results = (await query(dataSql, dataParams)) as any[];

  const assessments = results.map((row) => ({
    id: row.id,
    risk_id: row.risk_id,
    workflow_id: row.workflow_id,
    overall_risk_level: row.overall_risk_level,
    risk_score: row.risk_score,
    summary: row.summary,
    market_risk: row.market_risk ? JSON.parse(row.market_risk) : null,
    liquidity_risk: row.liquidity_risk ? JSON.parse(row.liquidity_risk) : null,
    credit_risk: row.credit_risk ? JSON.parse(row.credit_risk) : null,
    operational_risk: row.operational_risk ? JSON.parse(row.operational_risk) : null,
    scenario_analysis: row.scenario_analysis ? JSON.parse(row.scenario_analysis) : null,
    risk_metrics: row.risk_metrics ? JSON.parse(row.risk_metrics) : null,
    recommendations: row.recommendations ? JSON.parse(row.recommendations) : null,
    risk_controls: row.risk_controls ? JSON.parse(row.risk_controls) : null,
    risk_warnings: row.risk_warnings ? JSON.parse(row.risk_warnings) : null,
    status: row.status,
    execution_time_ms: row.execution_time_ms,
    created_at: row.created_at,
  }));

  return { assessments, total };
}

// ============================================================================
// 风险数据对比和趋势分析
// ============================================================================

/**
 * 获取风险指标趋势数据
 * 需求 9.5: 系统应提供风险分析的对比功能
 */
export async function getRiskMetricsTrend(
  ticker: string,
  metricName: string,
  days: number = 30
): Promise<
  Array<{
    date: string;
    value: number;
    workflow_id: string;
  }>
> {
  const sql = `
    SELECT 
      DATE(calculation_date) as date,
      metric_value as value,
      workflow_id
    FROM risk_metrics_history
    WHERE ticker = ? AND metric_name = ?
      AND calculation_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
    ORDER BY calculation_date ASC
  `;

  const results = (await query(sql, [ticker, metricName, days])) as any[];

  return results.map((row) => ({
    date: row.date,
    value: parseFloat(row.value),
    workflow_id: row.workflow_id,
  }));
}

/**
 * 比较多个工作流的风险评估结果
 * 需求 9.5: 系统应提供风险分析的对比功能
 */
export async function compareRiskAssessments(workflowIds: string[]): Promise<{
  comparisons: Array<{
    workflow_id: string;
    ticker: string;
    overall_risk_level: string;
    risk_score: number;
    key_metrics: any;
    created_at: Date;
  }>;
  summary: {
    risk_level_distribution: Record<string, number>;
    avg_risk_score: number;
    risk_score_range: { min: number; max: number };
  };
}> {
  if (workflowIds.length === 0) {
    return {
      comparisons: [],
      summary: {
        risk_level_distribution: {},
        avg_risk_score: 0,
        risk_score_range: { min: 0, max: 0 },
      },
    };
  }

  const placeholders = workflowIds.map(() => '?').join(',');
  const sql = `
    SELECT 
      ra.workflow_id, w.ticker, ra.overall_risk_level, ra.risk_score,
      ra.risk_metrics, ra.created_at
    FROM risk_assessments ra
    JOIN workflows w ON ra.workflow_id = w.workflow_id
    WHERE ra.workflow_id IN (${placeholders})
    ORDER BY ra.created_at DESC
  `;

  const results = (await query(sql, workflowIds)) as any[];

  const comparisons = results.map((row) => ({
    workflow_id: row.workflow_id,
    ticker: row.ticker,
    overall_risk_level: row.overall_risk_level,
    risk_score: row.risk_score,
    key_metrics: row.risk_metrics ? JSON.parse(row.risk_metrics) : {},
    created_at: row.created_at,
  }));

  // 计算汇总统计
  const riskLevelDistribution: Record<string, number> = {};
  let totalRiskScore = 0;
  let minRiskScore = Infinity;
  let maxRiskScore = -Infinity;

  comparisons.forEach((comp) => {
    // 风险等级分布
    riskLevelDistribution[comp.overall_risk_level] =
      (riskLevelDistribution[comp.overall_risk_level] || 0) + 1;

    // 风险评分统计
    totalRiskScore += comp.risk_score;
    minRiskScore = Math.min(minRiskScore, comp.risk_score);
    maxRiskScore = Math.max(maxRiskScore, comp.risk_score);
  });

  const avgRiskScore = comparisons.length > 0 ? totalRiskScore / comparisons.length : 0;

  return {
    comparisons,
    summary: {
      risk_level_distribution: riskLevelDistribution,
      avg_risk_score: avgRiskScore,
      risk_score_range: {
        min: minRiskScore === Infinity ? 0 : minRiskScore,
        max: maxRiskScore === -Infinity ? 0 : maxRiskScore,
      },
    },
  };
}

/**
 * 获取风险评估统计信息
 * 需求 9.6: 系统应维护风险数据的完整性和一致性
 */
export async function getRiskAssessmentStatistics(
  options: {
    ticker?: string;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
): Promise<{
  total_assessments: number;
  risk_level_distribution: Record<string, number>;
  avg_risk_score: number;
  avg_execution_time_ms: number;
  success_rate: number;
  most_common_risks: Array<{
    risk_type: string;
    frequency: number;
  }>;
}> {
  const { ticker, dateFrom, dateTo } = options;

  // 构建查询条件
  const conditions: string[] = [];
  const params: any[] = [];

  if (ticker) {
    conditions.push('w.ticker = ?');
    params.push(ticker);
  }

  if (dateFrom) {
    conditions.push('ra.created_at >= ?');
    params.push(dateFrom);
  }

  if (dateTo) {
    conditions.push('ra.created_at <= ?');
    params.push(dateTo);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  const sql = `
    SELECT 
      COUNT(*) as total_assessments,
      AVG(ra.risk_score) as avg_risk_score,
      AVG(ra.execution_time_ms) as avg_execution_time_ms,
      SUM(CASE WHEN ra.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
      ra.overall_risk_level,
      COUNT(ra.overall_risk_level) as level_count
    FROM risk_assessments ra
    JOIN workflows w ON ra.workflow_id = w.workflow_id
    ${whereClause}
    GROUP BY ra.overall_risk_level
  `;

  const results = (await query(sql, params)) as any[];

  let totalAssessments = 0;
  let totalRiskScore = 0;
  let totalExecutionTime = 0;
  let completedCount = 0;
  const riskLevelDistribution: Record<string, number> = {};

  results.forEach((row) => {
    totalAssessments += row.level_count;
    totalRiskScore += row.avg_risk_score * row.level_count;
    totalExecutionTime += (row.avg_execution_time_ms || 0) * row.level_count;
    completedCount += row.completed_count;
    riskLevelDistribution[row.overall_risk_level] = row.level_count;
  });

  const avgRiskScore = totalAssessments > 0 ? totalRiskScore / totalAssessments : 0;
  const avgExecutionTimeMs = totalAssessments > 0 ? totalExecutionTime / totalAssessments : 0;
  const successRate = totalAssessments > 0 ? completedCount / totalAssessments : 0;

  return {
    total_assessments: totalAssessments,
    risk_level_distribution: riskLevelDistribution,
    avg_risk_score: avgRiskScore,
    avg_execution_time_ms: avgExecutionTimeMs,
    success_rate: successRate,
    most_common_risks: [], // 可以根据需要扩展实现
  };
}

// ============================================================================
// 数据导出和备份功能
// ============================================================================

/**
 * 导出风险数据
 * 需求 9.7: 系统应支持风险数据的导出和备份
 */
export async function exportRiskData(
  options: {
    ticker?: string;
    dateFrom?: Date;
    dateTo?: Date;
    format?: 'json' | 'csv';
  } = {}
): Promise<{
  data: any[];
  format: string;
  exported_at: Date;
}> {
  const { ticker, dateFrom, dateTo, format = 'json' } = options;

  const conditions: string[] = [];
  const params: any[] = [];

  if (ticker) {
    conditions.push('w.ticker = ?');
    params.push(ticker);
  }

  if (dateFrom) {
    conditions.push('ra.created_at >= ?');
    params.push(dateFrom);
  }

  if (dateTo) {
    conditions.push('ra.created_at <= ?');
    params.push(dateTo);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  const sql = `
    SELECT 
      ra.risk_id, ra.workflow_id, w.ticker, w.title,
      ra.overall_risk_level, ra.risk_score, ra.summary,
      ra.market_risk, ra.liquidity_risk, ra.credit_risk, ra.operational_risk,
      ra.scenario_analysis, ra.risk_metrics, ra.recommendations,
      ra.status, ra.execution_time_ms, ra.created_at
    FROM risk_assessments ra
    JOIN workflows w ON ra.workflow_id = w.workflow_id
    ${whereClause}
    ORDER BY ra.created_at DESC
  `;

  const results = (await query(sql, params)) as any[];

  const data = results.map((row) => ({
    risk_id: row.risk_id,
    workflow_id: row.workflow_id,
    ticker: row.ticker,
    title: row.title,
    overall_risk_level: row.overall_risk_level,
    risk_score: row.risk_score,
    summary: row.summary,
    market_risk: row.market_risk ? JSON.parse(row.market_risk) : null,
    liquidity_risk: row.liquidity_risk ? JSON.parse(row.liquidity_risk) : null,
    credit_risk: row.credit_risk ? JSON.parse(row.credit_risk) : null,
    operational_risk: row.operational_risk ? JSON.parse(row.operational_risk) : null,
    scenario_analysis: row.scenario_analysis ? JSON.parse(row.scenario_analysis) : null,
    risk_metrics: row.risk_metrics ? JSON.parse(row.risk_metrics) : null,
    recommendations: row.recommendations ? JSON.parse(row.recommendations) : null,
    status: row.status,
    execution_time_ms: row.execution_time_ms,
    created_at: row.created_at,
  }));

  return {
    data,
    format,
    exported_at: new Date(),
  };
}
