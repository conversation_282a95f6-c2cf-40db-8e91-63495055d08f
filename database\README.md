# TradingAgents Frontend 数据库

## 概述

这是 TradingAgents Frontend 项目的统一数据库，包含完整的任务管理系统和 LangGraph 工作流系统。采用现代化的数据库设计，支持多智能体协作的金融分析工作流。

## 数据库结构

### 基础任务管理系统 (6 张表)

1. **tasks** - 任务表

   - 记录分析任务的基本信息
   - 包含任务状态、配置参数、时间戳等

2. **messages** - 消息表

   - 记录任务执行过程中的消息
   - 直接关联到 task_id，支持不同类型的消息

3. **tool_calls** - 工具调用表

   - 记录分析过程中的工具调用
   - 包含输入参数、输出结果、执行时间等

4. **analysis_results** - 分析结果表（统一表）

   - 同时支持原始系统和 LangGraph 系统
   - 记录各种类型的分析结果

5. **analysis_steps** - 分析步骤表

   - 记录分析过程中的详细步骤
   - 包含步骤状态、进度、性能指标等

6. **system_logs** - 系统日志表
   - 记录系统级别的操作日志

### LangGraph 工作流系统 (10 张表)

7. **langgraph_workflows** - LangGraph 工作流实例表
8. **workflow_configs** - 工作流配置表
9. **data_collection_results** - 数据收集结果表
10. **researcher_results** - 研究员结果表
11. **debate_records** - 辩论记录表
12. **consensus_evaluations** - 共识评估结果表
13. **risk_assessments** - 风险评估结果表
14. **trading_decisions** - 最终决策表
15. **workflow_state_snapshots** - 工作流状态快照表
16. **workflow_messages** - 工作流消息表

### 视图

- **task_overview** - 任务概览视图
- **workflow_complete_status** - 工作流完整状态视图

### 存储过程

- **CreateAnalysisTask** - 创建分析任务
- **StartTaskExecution** - 开始任务执行
- **CreateWorkflowInstance** - 创建工作流实例
- **UpdateWorkflowStage** - 更新工作流状态
- **SaveWorkflowSnapshot** - 保存状态快照

## 安装和使用

### 1. 创建数据库

```sql
-- 使用统一初始化脚本（推荐）
mysql -u root -p < database/init.sql
```

### 2. 验证安装

```sql
USE trading_analysis;
SHOW TABLES;
SELECT COUNT(*) FROM tasks;
SELECT COUNT(*) FROM langgraph_workflows;
```

### 3. 基本使用

#### 创建任务

```sql
CALL CreateAnalysisTask(
    'task_001',               -- task_id
    'AAPL',                   -- ticker
    'Apple股票分析',           -- title
    '分析Apple公司股票',       -- description
    '{"type": "comprehensive"}', -- config
    'user1',                  -- created_by
    'medium',                 -- research_depth
    '1m'                      -- analysis_period
);
```

#### 创建工作流实例

```sql
CALL CreateWorkflowInstance(
    'wf_001',                 -- workflow_id
    'task_001',               -- task_id
    'thread_001',             -- thread_id
    'AAPL',                   -- ticker
    CURDATE(),                -- analysis_date
    '{"deep_think_llm": "gpt-4o"}' -- config
);
```

#### 查询数据

```sql
-- 查看所有任务
SELECT * FROM task_overview;

-- 查看工作流状态
SELECT * FROM workflow_complete_status;

-- 查看特定任务的消息
SELECT * FROM messages WHERE task_id = 'task_001' ORDER BY sequence_number;
```

## 系统架构说明

### 双系统设计

```
基础系统: Task -> Message -> Analysis Results
LangGraph系统: Task -> Workflow -> Multi-Agent Analysis -> Trading Decision
```

### 主要特点

1. **统一的数据存储**

   - `analysis_results` 表同时支持两个系统
   - 通过 `task_id` 和 `workflow_id` 字段区分
   - 保持数据一致性和完整性

2. **多智能体协作**

   - 支持基本面、技术面、情绪面、新闻分析师
   - 多头和空头研究员辩论机制
   - 风险评估和最终决策制定

3. **完整的状态管理**
   - 工作流状态快照
   - 实时进度跟踪
   - 详细的执行日志

### 工作流程

1. **任务创建** - 用户创建分析任务
2. **工作流启动** - 创建 LangGraph 工作流实例
3. **多智能体分析** - 并行执行各类分析
4. **研究员辩论** - 多头空头观点碰撞
5. **共识评估** - 综合各方观点
6. **风险评估** - 全面风险分析
7. **决策制定** - 生成最终交易建议

## 配置说明

### 研究深度 (research_depth)

- `shallow` - 浅层分析，快速概览
- `medium` - 中等深度，平衡速度和质量
- `deep` - 深度分析，详细全面

### 分析周期 (analysis_period)

- `1d` - 1 天
- `1w` - 1 周
- `1m` - 1 个月
- `3m` - 3 个月
- `6m` - 6 个月
- `1y` - 1 年
- `custom` - 自定义

### 消息类型 (message_type)

- `human` - 用户消息
- `ai` - AI 回复
- `system` - 系统消息
- `tool` - 工具调用消息

### 任务状态 (status)

- `pending` - 等待执行
- `running` - 正在执行
- `completed` - 已完成
- `failed` - 执行失败
- `cancelled` - 已取消

## 维护和监控

### 清理旧数据

```sql
-- 删除30天前的已完成任务
DELETE FROM tasks
WHERE status = 'completed'
AND completed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 性能监控

```sql
-- 查看任务执行统计
SELECT
    status,
    COUNT(*) as count,
    AVG(TIMESTAMPDIFF(SECOND, created_at, completed_at)) as avg_duration
FROM tasks
WHERE completed_at IS NOT NULL
GROUP BY status;
```

### 备份建议

```bash
# 备份整个数据库
mysqldump -u root -p trading_analysis > backup_$(date +%Y%m%d).sql

# 只备份数据（不包含结构）
mysqldump -u root -p --no-create-info trading_analysis > data_backup_$(date +%Y%m%d).sql
```

## 故障排除

### 常见问题

1. **外键约束错误**

   - 确保引用的 task_id 存在
   - 检查字符编码是否一致

2. **JSON 字段错误**

   - 确保 JSON 格式正确
   - 使用 JSON_VALID() 函数验证

3. **时区问题**
   - 确保数据库时区设置正确
   - 使用 UTC 时间存储

### 调试技巧

```sql
-- 检查表结构
DESCRIBE tasks;

-- 检查索引
SHOW INDEX FROM messages;

-- 检查外键约束
SELECT * FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'trading_analysis';
```

## 版本历史

- **v3.0** - 统一初始化脚本，整合基础系统和 LangGraph 工作流系统
- **v2.0** - 移除 conversation 概念，简化架构
- **v1.0** - 初始版本，包含 conversation 概念

## 部署说明

### 生产环境部署

```bash
# 1. 创建数据库用户
mysql -u root -p -e "CREATE USER 'trading_user'@'%' IDENTIFIED BY 'your_password';"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON trading_analysis.* TO 'trading_user'@'%';"
mysql -u root -p -e "FLUSH PRIVILEGES;"

# 2. 执行初始化脚本
mysql -u trading_user -p < database/init.sql

# 3. 验证部署
mysql -u trading_user -p trading_analysis -e "SHOW TABLES; SELECT COUNT(*) FROM tasks;"
```

### 环境变量配置

```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=trading_user
DB_PASSWORD=your_password
DB_NAME=trading_analysis
```

### 性能优化建议

1. **索引优化**

   - 所有主要查询字段都已建立索引
   - 定期分析查询性能并优化

2. **数据清理**

   - 定期清理过期的日志和快照数据
   - 设置数据保留策略

3. **监控指标**
   - 监控数据库连接数
   - 跟踪查询执行时间
   - 监控存储空间使用
