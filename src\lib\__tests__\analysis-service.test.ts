// Analysis Service Tests
// Tests for the core analysis service functionality

import { AnalysisError, AnalysisService } from '../analysis-service';
import { EnhancedLangGraphDatabase } from '../enhanced-langgraph-database';
import { langGraphService } from '../langgraph-server';
import { taskFlowDb } from '../task-flow-database';

// Mock dependencies
jest.mock('../enhanced-langgraph-database');
jest.mock('../task-flow-database');
jest.mock('../langgraph-server');

const mockEnhancedLangGraphDatabase = EnhancedLangGraphDatabase as jest.Mocked<
  typeof EnhancedLangGraphDatabase
>;
const mockTaskFlowDb = taskFlowDb as jest.Mocked<typeof taskFlowDb>;
const mockLangGraphService = langGraphService as jest.Mocked<typeof langGraphService>;

describe('AnalysisService', () => {
  let analysisService: AnalysisService;

  beforeEach(() => {
    jest.useFakeTimers();
    analysisService = new AnalysisService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up the analysis service properly
    analysisService.destroy();
    jest.clearAllTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('executeAnalysis', () => {
    it('should successfully start analysis for valid task', async () => {
      // Mock task data
      const mockTask = {
        task_id: 'task_123',
        ticker: 'AAPL',
        title: 'Apple Analysis',
        description: 'Test analysis',
        status: 'pending',
        config: { analysisType: 'comprehensive' },
        created_by: 'user_123',
        created_at: '2024-01-01T00:00:00Z',
      };

      // Mock database responses
      mockTaskFlowDb.getTask = jest.fn().mockResolvedValue(mockTask);
      mockEnhancedLangGraphDatabase.createCompleteWorkflow = jest
        .fn()
        .mockResolvedValue('workflow_123');
      mockTaskFlowDb.updateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockTaskFlowDb.logSystemEvent = jest.fn().mockResolvedValue(undefined);
      mockLangGraphService.analyzeStock = jest.fn().mockResolvedValue({
        content: 'Analysis completed',
        analysisResults: {},
        tradingDecision: {},
      });

      const result = await analysisService.executeAnalysis({
        taskId: 'task_123',
        config: { analysisType: 'comprehensive' },
      });

      expect(result).toEqual({
        workflowId: 'workflow_123',
        taskId: 'task_123',
        status: 'started',
        message: '分析已启动',
      });

      expect(mockEnhancedLangGraphDatabase.createCompleteWorkflow).toHaveBeenCalledWith({
        ticker: 'AAPL',
        title: 'Apple Analysis',
        description: 'Test analysis',
        config: { analysisType: 'comprehensive' },
        created_by: 'user_123',
      });

      expect(mockTaskFlowDb.updateTaskStatus).toHaveBeenCalledWith('task_123', 'running');
    });

    it('should throw error for non-existent task', async () => {
      mockTaskFlowDb.getTask = jest.fn().mockResolvedValue(null);

      await expect(analysisService.executeAnalysis({ taskId: 'non_existent' })).rejects.toThrow(
        new AnalysisError('任务不存在', 'TASK_NOT_FOUND', 404)
      );
    });

    it('should throw error for task with invalid status', async () => {
      const mockTask = {
        task_id: 'task_123',
        ticker: 'AAPL',
        title: 'Apple Analysis',
        status: 'running', // Invalid status for starting analysis
        created_by: 'user_123',
        created_at: '2024-01-01T00:00:00Z',
      };

      mockTaskFlowDb.getTask = jest.fn().mockResolvedValue(mockTask);

      await expect(analysisService.executeAnalysis({ taskId: 'task_123' })).rejects.toThrow(
        new AnalysisError('任务状态不允许执行分析', 'INVALID_TASK_STATUS', 400)
      );
    });

    it('should restore task status on analysis startup failure', async () => {
      const mockTask = {
        task_id: 'task_123',
        ticker: 'AAPL',
        title: 'Apple Analysis',
        status: 'pending',
        created_by: 'user_123',
        created_at: '2024-01-01T00:00:00Z',
      };

      mockTaskFlowDb.getTask = jest.fn().mockResolvedValue(mockTask);
      mockEnhancedLangGraphDatabase.createCompleteWorkflow = jest
        .fn()
        .mockRejectedValue(new Error('Database error'));
      mockTaskFlowDb.updateTaskStatus = jest.fn().mockResolvedValue(undefined);

      await expect(analysisService.executeAnalysis({ taskId: 'task_123' })).rejects.toThrow(
        '执行分析失败: Database error'
      );

      // Should attempt to restore task status
      expect(mockTaskFlowDb.updateTaskStatus).toHaveBeenCalledWith('task_123', 'pending');
    });
  });

  describe('getAnalysisStatus', () => {
    it('should return complete analysis status', async () => {
      const mockCompleteStatus = {
        workflow: {
          workflow_id: 'workflow_123',
          status: 'running',
          progress: 50,
          current_stage: 'analysis',
          ticker: 'AAPL',
          title: 'Apple Analysis',
          created_at: new Date('2024-01-01T00:00:00Z'),
          started_at: new Date('2024-01-01T00:01:00Z'),
          completed_at: null,
          error_message: null,
        },
        analystReports: [],
        researchReports: [],
        finalDecision: null,
        recentEvents: [],
      };

      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus = jest
        .fn()
        .mockResolvedValue(mockCompleteStatus);

      // Set up active analysis
      (analysisService as any).activeAnalyses.set('workflow_123', {
        workflowId: 'workflow_123',
        taskId: 'task_123',
      });

      const result = await analysisService.getAnalysisStatus('workflow_123');

      expect(result.workflowId).toBe('workflow_123');
      expect(result.taskId).toBe('task_123');
      expect(result.status).toBe('running');
      expect(result.progress).toBe(50);
      expect(result.currentStage).toBe('analysis');
      expect(result.ticker).toBe('AAPL');
      expect(result.title).toBe('Apple Analysis');
      expect(result.analystReports).toEqual([]);
      expect(result.researchReports).toEqual([]);
      expect(result.finalDecision).toBeNull();
      expect(result.recentEvents).toEqual([]);
      expect(result.error).toBeUndefined();
    });

    it('should throw error when workflow status cannot be retrieved', async () => {
      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus = jest
        .fn()
        .mockRejectedValue(new Error('Database error'));

      await expect(analysisService.getAnalysisStatus('workflow_123')).rejects.toThrow(
        new AnalysisError('获取状态失败: Database error')
      );
    });
  });

  describe('stopAnalysis', () => {
    it('should successfully stop active analysis', async () => {
      // Set up active analysis
      (analysisService as any).activeAnalyses.set('workflow_123', {
        workflowId: 'workflow_123',
        taskId: 'task_123',
      });

      mockEnhancedLangGraphDatabase.updateWorkflowStatus = jest.fn().mockResolvedValue(undefined);
      mockTaskFlowDb.updateTaskStatus = jest.fn().mockResolvedValue(undefined);
      mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue('event_123');

      await analysisService.stopAnalysis('workflow_123');

      expect(mockEnhancedLangGraphDatabase.updateWorkflowStatus).toHaveBeenCalledWith({
        workflow_id: 'workflow_123',
        current_stage: 'cancelled',
        progress: 0,
        status: 'cancelled',
        error_message: '用户取消分析',
      });

      expect(mockTaskFlowDb.updateTaskStatus).toHaveBeenCalledWith('task_123', 'pending');

      // Should remove from active analyses
      expect((analysisService as any).activeAnalyses.has('workflow_123')).toBe(false);
    });

    it('should throw error for non-existent analysis', async () => {
      await expect(analysisService.stopAnalysis('non_existent')).rejects.toThrow(
        '停止分析失败: 分析不存在或未运行'
      );
    });
  });

  describe('getActiveAnalyses', () => {
    it('should return list of active analyses', () => {
      // Set up some active analyses
      (analysisService as any).activeAnalyses.set('workflow_1', {
        workflowId: 'workflow_1',
        taskId: 'task_1',
      });
      (analysisService as any).activeAnalyses.set('workflow_2', {
        workflowId: 'workflow_2',
        taskId: 'task_2',
      });

      const activeAnalyses = analysisService.getActiveAnalyses();

      expect(activeAnalyses).toHaveLength(2);
      expect(activeAnalyses).toContainEqual({
        workflowId: 'workflow_1',
        taskId: 'task_1',
      });
      expect(activeAnalyses).toContainEqual({
        workflowId: 'workflow_2',
        taskId: 'task_2',
      });
    });

    it('should return empty array when no active analyses', () => {
      const activeAnalyses = analysisService.getActiveAnalyses();
      expect(activeAnalyses).toEqual([]);
    });
  });
});
