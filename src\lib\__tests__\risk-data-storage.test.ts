/**
 * Risk Data Storage Tests
 * 风险数据存储功能测试
 */

import { SaveRiskAssessmentRequest } from '@/types/langgraph-database';
import {
  compareRiskAssessments,
  exportRiskData,
  getRiskAssessmentByWorkflowId,
  getRiskAssessmentStatistics,
  getRiskMetricsTrend,
  saveRiskAssessment,
  saveRiskMetricsHistory,
} from '../risk-data-storage';

// Mock database module
jest.mock('../db', () => ({
  query: jest.fn(),
  getConnection: jest.fn(() => ({
    beginTransaction: jest.fn(),
    execute: jest.fn(),
    commit: jest.fn(),
    rollback: jest.fn(),
    release: jest.fn(),
  })),
}));

// Mock uuid
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-risk-id-123'),
}));

import { getConnection, query } from '../db';

describe('Risk Data Storage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('saveRiskAssessment', () => {
    it('should save risk assessment successfully', async () => {
      const mockRequest: SaveRiskAssessmentRequest = {
        workflow_id: 'test-workflow-123',
        overall_risk_level: 'medium',
        risk_score: 6,
        summary: 'Test risk assessment',
        market_risk: { volatility: 0.25 },
        liquidity_risk: { bid_ask_spread: 0.01 },
        credit_risk: { debt_ratio: 0.3 },
        operational_risk: { governance_score: 8 },
        scenario_analysis: { stress_test: 'passed' },
        risk_metrics: { var_95: 0.05 },
        recommendations: ['Reduce position size'],
        risk_controls: { stop_loss: 0.95 },
        risk_warnings: ['High volatility'],
        status: 'completed',
        execution_time_ms: 1500,
      };

      (query as jest.Mock).mockResolvedValue([]);

      const result = await saveRiskAssessment(mockRequest);

      expect(result).toBe('test-risk-id-123');
      expect(query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO risk_assessments'),
        expect.arrayContaining([
          'test-risk-id-123',
          'test-workflow-123',
          'medium',
          6,
          'Test risk assessment',
        ])
      );
    });

    it('should handle missing optional fields', async () => {
      const mockRequest: SaveRiskAssessmentRequest = {
        workflow_id: 'test-workflow-123',
        overall_risk_level: 'low',
        risk_score: 3,
      };

      (query as jest.Mock).mockResolvedValue([]);

      const result = await saveRiskAssessment(mockRequest);

      expect(result).toBe('test-risk-id-123');
      expect(query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO risk_assessments'),
        expect.arrayContaining([
          'test-risk-id-123',
          'test-workflow-123',
          'low',
          3,
          null, // summary should be null
        ])
      );
    });
  });

  describe('getRiskAssessmentByWorkflowId', () => {
    it('should retrieve risk assessment by workflow ID', async () => {
      const mockDbResult = [
        {
          id: 1,
          risk_id: 'test-risk-id-123',
          workflow_id: 'test-workflow-123',
          overall_risk_level: 'medium',
          risk_score: 6,
          summary: 'Test risk assessment',
          market_risk: '{"volatility": 0.25}',
          liquidity_risk: '{"bid_ask_spread": 0.01}',
          credit_risk: '{"debt_ratio": 0.3}',
          operational_risk: '{"governance_score": 8}',
          scenario_analysis: '{"stress_test": "passed"}',
          risk_metrics: '{"var_95": 0.05}',
          recommendations: '["Reduce position size"]',
          risk_controls: '{"stop_loss": 0.95}',
          risk_warnings: '["High volatility"]',
          status: 'completed',
          execution_time_ms: 1500,
          created_at: new Date('2024-01-01T10:00:00Z'),
        },
      ];

      (query as jest.Mock).mockResolvedValue(mockDbResult);

      const result = await getRiskAssessmentByWorkflowId('test-workflow-123');

      expect(result).toEqual({
        id: 1,
        risk_id: 'test-risk-id-123',
        workflow_id: 'test-workflow-123',
        overall_risk_level: 'medium',
        risk_score: 6,
        summary: 'Test risk assessment',
        market_risk: { volatility: 0.25 },
        liquidity_risk: { bid_ask_spread: 0.01 },
        credit_risk: { debt_ratio: 0.3 },
        operational_risk: { governance_score: 8 },
        scenario_analysis: { stress_test: 'passed' },
        risk_metrics: { var_95: 0.05 },
        recommendations: ['Reduce position size'],
        risk_controls: { stop_loss: 0.95 },
        risk_warnings: ['High volatility'],
        status: 'completed',
        execution_time_ms: 1500,
        created_at: mockDbResult[0].created_at,
      });
    });

    it('should return null when no assessment found', async () => {
      (query as jest.Mock).mockResolvedValue([]);

      const result = await getRiskAssessmentByWorkflowId('non-existent-workflow');

      expect(result).toBeNull();
    });
  });

  describe('compareRiskAssessments', () => {
    it('should compare multiple risk assessments', async () => {
      const mockDbResult = [
        {
          workflow_id: 'workflow-1',
          ticker: 'AAPL',
          overall_risk_level: 'low',
          risk_score: 3,
          risk_metrics: '{"volatility": 0.15}',
          created_at: new Date('2024-01-01T10:00:00Z'),
        },
        {
          workflow_id: 'workflow-2',
          ticker: 'TSLA',
          overall_risk_level: 'high',
          risk_score: 8,
          risk_metrics: '{"volatility": 0.35}',
          created_at: new Date('2024-01-02T10:00:00Z'),
        },
      ];

      (query as jest.Mock).mockResolvedValue(mockDbResult);

      const result = await compareRiskAssessments(['workflow-1', 'workflow-2']);

      expect(result.comparisons).toHaveLength(2);
      expect(result.summary.avg_risk_score).toBe(5.5);
      expect(result.summary.risk_level_distribution).toEqual({
        low: 1,
        high: 1,
      });
      expect(result.summary.risk_score_range).toEqual({
        min: 3,
        max: 8,
      });
    });

    it('should handle empty workflow list', async () => {
      const result = await compareRiskAssessments([]);

      expect(result.comparisons).toEqual([]);
      expect(result.summary.avg_risk_score).toBe(0);
    });
  });

  describe('saveRiskMetricsHistory', () => {
    it('should save risk metrics history', async () => {
      const mockConnection = {
        beginTransaction: jest.fn(),
        execute: jest.fn(),
        commit: jest.fn(),
        rollback: jest.fn(),
        release: jest.fn(),
      };

      (getConnection as jest.Mock).mockResolvedValue(mockConnection);
      (query as jest.Mock).mockResolvedValue([]);

      const metricsData = {
        volatility: 0.25,
        beta: 1.2,
        var_95: 0.05,
      };

      await saveRiskMetricsHistory('workflow-123', 'AAPL', metricsData);

      expect(mockConnection.beginTransaction).toHaveBeenCalled();
      expect(mockConnection.execute).toHaveBeenCalledTimes(3);
      expect(mockConnection.commit).toHaveBeenCalled();
      expect(mockConnection.release).toHaveBeenCalled();
    });

    it('should rollback on error', async () => {
      const mockConnection = {
        beginTransaction: jest.fn(),
        execute: jest.fn().mockRejectedValue(new Error('Database error')),
        commit: jest.fn(),
        rollback: jest.fn(),
        release: jest.fn(),
      };

      (getConnection as jest.Mock).mockResolvedValue(mockConnection);
      (query as jest.Mock).mockResolvedValue([]);

      const metricsData = { volatility: 0.25 };

      await expect(saveRiskMetricsHistory('workflow-123', 'AAPL', metricsData)).rejects.toThrow(
        'Database error'
      );

      expect(mockConnection.rollback).toHaveBeenCalled();
      expect(mockConnection.release).toHaveBeenCalled();
    });
  });

  describe('getRiskMetricsTrend', () => {
    it('should retrieve risk metrics trend', async () => {
      const mockDbResult = [
        {
          date: '2024-01-01',
          value: 0.25,
          workflow_id: 'workflow-1',
        },
        {
          date: '2024-01-02',
          value: 0.27,
          workflow_id: 'workflow-2',
        },
      ];

      (query as jest.Mock).mockResolvedValue(mockDbResult);

      const result = await getRiskMetricsTrend('AAPL', 'volatility', 30);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        date: '2024-01-01',
        value: 0.25,
        workflow_id: 'workflow-1',
      });
    });
  });

  describe('getRiskAssessmentStatistics', () => {
    it('should calculate risk assessment statistics', async () => {
      const mockDbResult = [
        {
          total_assessments: 10,
          avg_risk_score: 5.5,
          avg_execution_time_ms: 1500,
          completed_count: 3,
          overall_risk_level: 'low',
          level_count: 3,
        },
        {
          total_assessments: 10,
          avg_risk_score: 5.5,
          avg_execution_time_ms: 1500,
          completed_count: 4,
          overall_risk_level: 'medium',
          level_count: 4,
        },
        {
          total_assessments: 10,
          avg_risk_score: 5.5,
          avg_execution_time_ms: 1500,
          completed_count: 2,
          overall_risk_level: 'high',
          level_count: 3,
        },
      ];

      (query as jest.Mock).mockResolvedValue(mockDbResult);

      const result = await getRiskAssessmentStatistics({
        ticker: 'AAPL',
      });

      expect(result.total_assessments).toBe(10);
      expect(result.avg_risk_score).toBe(5.5);
      expect(result.success_rate).toBe(0.9);
      expect(result.risk_level_distribution).toEqual({
        low: 3,
        medium: 4,
        high: 3,
      });
    });
  });

  describe('exportRiskData', () => {
    it('should export risk data in JSON format', async () => {
      const mockDbResult = [
        {
          risk_id: 'risk-1',
          workflow_id: 'workflow-1',
          ticker: 'AAPL',
          title: 'AAPL Analysis',
          overall_risk_level: 'low',
          risk_score: 3,
          summary: 'Low risk',
          market_risk: '{"volatility": 0.15}',
          liquidity_risk: '{}',
          credit_risk: '{}',
          operational_risk: '{}',
          scenario_analysis: '{}',
          risk_metrics: '{}',
          recommendations: '[]',
          status: 'completed',
          execution_time_ms: 1000,
          created_at: new Date('2024-01-01T10:00:00Z'),
        },
      ];

      (query as jest.Mock).mockResolvedValue(mockDbResult);

      const result = await exportRiskData({
        ticker: 'AAPL',
        format: 'json',
      });

      expect(result.format).toBe('json');
      expect(result.data).toHaveLength(1);
      expect(result.data[0].risk_id).toBe('risk-1');
      expect(result.data[0].market_risk).toEqual({ volatility: 0.15 });
      expect(result.exported_at).toBeInstanceOf(Date);
    });
  });
});

describe('Risk Data Storage Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle database connection errors', async () => {
    (query as jest.Mock).mockRejectedValue(new Error('Connection failed'));

    await expect(
      saveRiskAssessment({
        workflow_id: 'test',
        overall_risk_level: 'low',
        risk_score: 3,
      })
    ).rejects.toThrow('Connection failed');
  });
});
