// Enhanced LangGraph Database Tests
// Comprehensive unit tests for the enhanced database operations

import {
  EnhancedWorkflowQueryOptions,
  PaginatedWorkflowResponse,
  SaveAnalystReportRequest,
  WorkflowPerformanceMetrics,
} from '@/types/langgraph-database';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import type {
  AgentStateUpdate,
  AnalysisStatistics,
  CompleteWorkflowStatus,
  CreateCompleteWorkflowRequest,
  StatisticsOptions,
  WorkflowStatusSummary,
} from '../enhanced-langgraph-database';

// Mock the enhanced database class methods with proper typing
const mockEnhancedLangGraphDatabase = {
  createCompleteWorkflow: jest.fn<(request: CreateCompleteWorkflowRequest) => Promise<string>>(),
  getCompleteWorkflowStatus: jest.fn<(workflowId: string) => Promise<CompleteWorkflowStatus>>(),
  batchUpdateAgentStates: jest.fn<(updates: AgentStateUpdate[]) => Promise<void>>(),
  batchSaveAnalystResults: jest.fn<(results: SaveAnalystReportRequest[]) => Promise<string[]>>(),
  getAnalysisStatistics: jest.fn<(options?: StatisticsOptions) => Promise<AnalysisStatistics>>(),
  getWorkflowStatusSummaries:
    jest.fn<(workflowIds: string[]) => Promise<WorkflowStatusSummary[]>>(),
  getAnalysisReports:
    jest.fn<(workflowId: string, type?: 'analyst' | 'research' | 'all') => Promise<any>>(),
  queryWorkflowsEnhanced:
    jest.fn<(options?: EnhancedWorkflowQueryOptions) => Promise<PaginatedWorkflowResponse>>(),
  validateWorkflowAccess: jest.fn<(workflowId: string, userId?: string) => Promise<boolean>>(),
  getWorkflowPerformanceMetrics:
    jest.fn<(workflowId: string) => Promise<WorkflowPerformanceMetrics>>(),
  validateWorkflowIntegrity:
    jest.fn<(workflowId: string) => Promise<{ isValid: boolean; issues: string[] }>>(),
  cleanupFailedWorkflows: jest.fn<(olderThanDays?: number) => Promise<number>>(),
  // Parent class methods
  createWorkflow: jest.fn(),
  getWorkflow: jest.fn(),
  logWorkflowEvent: jest.fn(),
  saveAnalystReport: jest.fn(),
};

// Mock the module
jest.mock('../enhanced-langgraph-database', () => ({
  __esModule: true,
  default: mockEnhancedLangGraphDatabase,
  EnhancedLangGraphDatabase: mockEnhancedLangGraphDatabase,
}));

describe('EnhancedLangGraphDatabase', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // ============================================================================
  // Enhanced Workflow Management Tests
  // ============================================================================

  describe('createCompleteWorkflow', () => {
    it('should create a complete workflow with initialization', async () => {
      const request: CreateCompleteWorkflowRequest = {
        ticker: 'TEST001',
        title: '测试分析任务',
        description: '这是一个测试任务',
        config: { test: true },
        created_by: 'test_user',
      };

      const expectedWorkflowId = 'wf_test_123';
      mockEnhancedLangGraphDatabase.createCompleteWorkflow.mockResolvedValue(expectedWorkflowId);

      const workflowId = await mockEnhancedLangGraphDatabase.createCompleteWorkflow(request);

      expect(workflowId).toBe(expectedWorkflowId);
      expect(mockEnhancedLangGraphDatabase.createCompleteWorkflow).toHaveBeenCalledWith(request);
    });

    it('should handle errors during workflow creation', async () => {
      const request: CreateCompleteWorkflowRequest = {
        ticker: 'TEST001',
        title: '测试分析任务',
      };

      const error = new Error('Database error');
      mockEnhancedLangGraphDatabase.createCompleteWorkflow.mockRejectedValue(error);

      await expect(mockEnhancedLangGraphDatabase.createCompleteWorkflow(request)).rejects.toThrow(
        'Database error'
      );
    });
  });

  describe('getCompleteWorkflowStatus', () => {
    it('should return complete workflow status', async () => {
      const workflowId = 'wf_test_123';

      const mockStatus: CompleteWorkflowStatus = {
        workflow: {
          id: 1,
          workflow_id: workflowId,
          ticker: 'TEST001',
          title: '测试任务',
          status: 'completed',
          progress: 100,
          current_stage: 'completed',
          created_by: 'test_user',
          created_at: new Date(),
          updated_at: new Date(),
        },
        analystReports: [
          {
            id: 1,
            report_id: 'rep_1',
            workflow_id: workflowId,
            analyst_type: 'technical',
            summary: '技术分析报告',
            status: 'completed',
            created_at: new Date(),
          },
        ],
        researchReports: [
          {
            id: 1,
            report_id: 'res_1',
            workflow_id: workflowId,
            researcher_type: 'bull',
            summary: '多头研究报告',
            status: 'completed',
            created_at: new Date(),
          },
        ],
        finalDecision: {
          id: 1,
          decision_id: 'dec_1',
          workflow_id: workflowId,
          decision_type: 'buy',
          confidence_level: 0.85,
          created_at: new Date(),
        },
        recentEvents: [
          {
            id: 1,
            event_id: 'evt_1',
            workflow_id: workflowId,
            event_type: 'log',
            content: '分析完成',
            created_at: new Date(),
          },
        ],
      };

      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus.mockResolvedValue(mockStatus);

      const result = await mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId);

      expect(result).toBeDefined();
      expect(result.workflow.workflow_id).toBe(workflowId);
      expect(result.analystReports).toHaveLength(1);
      expect(result.researchReports).toHaveLength(1);
      expect(result.finalDecision).toBeDefined();
      expect(result.recentEvents).toHaveLength(1);
    });

    it('should handle workflow not found', async () => {
      const workflowId = 'nonexistent';

      const error = new Error(`工作流不存在: ${workflowId}`);
      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus.mockRejectedValue(error);

      await expect(
        mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId)
      ).rejects.toThrow(`工作流不存在: ${workflowId}`);
    });
  });

  // ============================================================================
  // Batch Operations Tests
  // ============================================================================

  describe('batchUpdateAgentStates', () => {
    it('should update multiple agent states', async () => {
      const updates: AgentStateUpdate[] = [
        {
          workflow_id: 'wf_test_123',
          analyst_type: 'technical',
          status: 'completed',
          execution_time_ms: 5000,
        },
        {
          workflow_id: 'wf_test_123',
          analyst_type: 'fundamental',
          status: 'completed',
          execution_time_ms: 7000,
        },
      ];

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates.mockResolvedValue(undefined);

      await mockEnhancedLangGraphDatabase.batchUpdateAgentStates(updates);

      expect(mockEnhancedLangGraphDatabase.batchUpdateAgentStates).toHaveBeenCalledWith(updates);
    });

    it('should handle batch update errors', async () => {
      const updates: AgentStateUpdate[] = [
        {
          workflow_id: 'wf_test_123',
          analyst_type: 'technical',
          status: 'completed',
        },
      ];

      const error = new Error('Update failed');
      mockEnhancedLangGraphDatabase.batchUpdateAgentStates.mockRejectedValue(error);

      await expect(mockEnhancedLangGraphDatabase.batchUpdateAgentStates(updates)).rejects.toThrow(
        'Update failed'
      );
    });
  });

  describe('batchSaveAnalystResults', () => {
    it('should save multiple analyst results', async () => {
      const results: SaveAnalystReportRequest[] = [
        {
          workflow_id: 'wf_test_123',
          analyst_type: 'technical',
          summary: '技术分析报告',
          status: 'completed',
          details: { trading_signal: 'buy' },
        },
        {
          workflow_id: 'wf_test_123',
          analyst_type: 'sentiment',
          summary: '情绪分析报告',
          status: 'completed',
          details: { overall_sentiment: 'positive' },
        },
      ];

      const expectedReportIds = ['rep_1', 'rep_2'];
      mockEnhancedLangGraphDatabase.batchSaveAnalystResults.mockResolvedValue(expectedReportIds);

      const reportIds = await mockEnhancedLangGraphDatabase.batchSaveAnalystResults(results);

      expect(reportIds).toEqual(expectedReportIds);
      expect(mockEnhancedLangGraphDatabase.batchSaveAnalystResults).toHaveBeenCalledWith(results);
    });
  });

  // ============================================================================
  // Analysis Statistics Tests
  // ============================================================================

  describe('getAnalysisStatistics', () => {
    it('should return comprehensive statistics', async () => {
      const options: StatisticsOptions = {
        date_from: '2024-01-01',
        date_to: '2024-12-31',
        ticker: 'TEST001',
      };

      const mockStatistics: AnalysisStatistics = {
        basic: {
          total_workflows: 100,
          completed_workflows: 85,
          failed_workflows: 10,
          avg_duration_seconds: 300,
        },
        successRates: [
          {
            ticker: 'TEST001',
            total_count: 50,
            success_count: 42,
          },
          {
            ticker: 'TEST002',
            total_count: 30,
            success_count: 25,
          },
        ],
      };

      mockEnhancedLangGraphDatabase.getAnalysisStatistics.mockResolvedValue(mockStatistics);

      const statistics = await mockEnhancedLangGraphDatabase.getAnalysisStatistics(options);

      expect(statistics).toBeDefined();
      expect(statistics.basic.total_workflows).toBe(100);
      expect(statistics.basic.completed_workflows).toBe(85);
      expect(statistics.basic.failed_workflows).toBe(10);
      expect(statistics.basic.avg_duration_seconds).toBe(300);
      expect(statistics.successRates).toHaveLength(2);
      expect(statistics.successRates[0].ticker).toBe('TEST001');
    });

    it('should work with no options', async () => {
      const mockStatistics: AnalysisStatistics = {
        basic: {
          total_workflows: 50,
          completed_workflows: 40,
          failed_workflows: 5,
          avg_duration_seconds: 250,
        },
        successRates: [],
      };

      mockEnhancedLangGraphDatabase.getAnalysisStatistics.mockResolvedValue(mockStatistics);

      const statistics = await mockEnhancedLangGraphDatabase.getAnalysisStatistics();

      expect(statistics).toBeDefined();
      expect(statistics.basic.total_workflows).toBe(50);
      expect(statistics.successRates).toHaveLength(0);
    });
  });

  describe('getWorkflowStatusSummaries', () => {
    it('should return status summaries for multiple workflows', async () => {
      const workflowIds = ['wf_1', 'wf_2'];

      const mockSummaries: WorkflowStatusSummary[] = [
        {
          workflow_id: 'wf_1',
          status: 'completed',
          progress: 100,
          current_stage: 'completed',
          analyst_completion: {
            fundamental: true,
            technical: true,
            sentiment: true,
            news: true,
          },
          research_completion: {
            bull: true,
            bear: true,
          },
          final_decision_completed: true,
        },
        {
          workflow_id: 'wf_2',
          status: 'running',
          progress: 50,
          current_stage: 'analyst_analysis',
          analyst_completion: {
            fundamental: true,
            technical: false,
            sentiment: false,
            news: false,
          },
          research_completion: {
            bull: false,
            bear: false,
          },
          final_decision_completed: false,
        },
      ];

      mockEnhancedLangGraphDatabase.getWorkflowStatusSummaries.mockResolvedValue(mockSummaries);

      const summaries = await mockEnhancedLangGraphDatabase.getWorkflowStatusSummaries(workflowIds);

      expect(summaries).toHaveLength(2);
      expect(summaries[0].workflow_id).toBe('wf_1');
      expect(summaries[0].status).toBe('completed');
      expect(summaries[0].analyst_completion.fundamental).toBe(true);
      expect(summaries[0].analyst_completion.technical).toBe(true);
      expect(summaries[0].final_decision_completed).toBe(true);

      expect(summaries[1].workflow_id).toBe('wf_2');
      expect(summaries[1].status).toBe('running');
      expect(summaries[1].analyst_completion.fundamental).toBe(true);
      expect(summaries[1].analyst_completion.technical).toBe(false);
      expect(summaries[1].final_decision_completed).toBe(false);
    });

    it('should return empty array for empty input', async () => {
      mockEnhancedLangGraphDatabase.getWorkflowStatusSummaries.mockResolvedValue([]);

      const summaries = await mockEnhancedLangGraphDatabase.getWorkflowStatusSummaries([]);
      expect(summaries).toEqual([]);
    });
  });

  // ============================================================================
  // Enhanced Query Methods Tests
  // ============================================================================

  describe('getAnalysisReports', () => {
    it('should return analyst reports only', async () => {
      const workflowId = 'wf_test_123';

      const mockResult = {
        analystReports: [
          {
            report_id: 'rep_1',
            workflow_id: workflowId,
            analyst_type: 'technical',
            summary: '技术分析报告',
            status: 'completed',
          },
        ],
        researchReports: [],
      };

      mockEnhancedLangGraphDatabase.getAnalysisReports.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.getAnalysisReports(workflowId, 'analyst');

      expect(result.analystReports).toHaveLength(1);
      expect(result.researchReports).toHaveLength(0);
      expect(result.analystReports[0].analyst_type).toBe('technical');
    });

    it('should return research reports only', async () => {
      const workflowId = 'wf_test_123';

      const mockResult = {
        analystReports: [],
        researchReports: [
          {
            report_id: 'res_1',
            workflow_id: workflowId,
            researcher_type: 'bull',
            summary: '多头研究报告',
          },
        ],
      };

      mockEnhancedLangGraphDatabase.getAnalysisReports.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.getAnalysisReports(workflowId, 'research');

      expect(result.analystReports).toHaveLength(0);
      expect(result.researchReports).toHaveLength(1);
      expect(result.researchReports[0].researcher_type).toBe('bull');
    });

    it('should return all reports', async () => {
      const workflowId = 'wf_test_123';

      const mockResult = {
        analystReports: [
          {
            report_id: 'rep_1',
            analyst_type: 'technical',
            summary: '技术分析报告',
          },
        ],
        researchReports: [
          {
            report_id: 'res_1',
            researcher_type: 'bull',
            summary: '多头研究报告',
          },
        ],
      };

      mockEnhancedLangGraphDatabase.getAnalysisReports.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.getAnalysisReports(workflowId, 'all');

      expect(result.analystReports).toHaveLength(1);
      expect(result.researchReports).toHaveLength(1);
    });
  });

  describe('queryWorkflowsEnhanced', () => {
    it('should return paginated workflows with stats', async () => {
      const options: EnhancedWorkflowQueryOptions = {
        ticker: 'TEST001',
        status: ['completed'],
        page: 1,
        limit: 10,
        include_stats: true,
        sort_by: 'created_at',
        sort_order: 'desc',
      };

      const mockResult: PaginatedWorkflowResponse = {
        workflows: [
          {
            id: 1,
            workflow_id: 'wf_1',
            ticker: 'TEST001',
            title: 'Test 1',
            status: 'completed',
            current_stage: 'completed',
            progress: 100,
            created_by: 'test_user',
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            id: 2,
            workflow_id: 'wf_2',
            ticker: 'TEST001',
            title: 'Test 2',
            status: 'completed',
            current_stage: 'completed',
            progress: 100,
            created_by: 'test_user',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ],
        total: 25,
        page: 1,
        limit: 10,
      };

      mockEnhancedLangGraphDatabase.queryWorkflowsEnhanced.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.queryWorkflowsEnhanced(options);

      expect(result.workflows).toHaveLength(2);
      expect(result.total).toBe(25);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.workflows[0].workflow_id).toBe('wf_1');
    });

    it('should work with minimal options', async () => {
      const mockResult: PaginatedWorkflowResponse = {
        workflows: [
          {
            id: 1,
            workflow_id: 'wf_1',
            ticker: 'TEST001',
            title: 'Test 1',
            status: 'completed',
            current_stage: 'completed',
            progress: 100,
            created_by: 'test_user',
            created_at: new Date(),
            updated_at: new Date(),
          },
        ],
        total: 5,
        page: 1,
        limit: 10,
      };

      mockEnhancedLangGraphDatabase.queryWorkflowsEnhanced.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.queryWorkflowsEnhanced();

      expect(result.workflows).toHaveLength(1);
      expect(result.total).toBe(5);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });
  });

  // ============================================================================
  // Utility Methods Tests
  // ============================================================================

  describe('validateWorkflowAccess', () => {
    it('should return true for existing workflow', async () => {
      mockEnhancedLangGraphDatabase.validateWorkflowAccess.mockResolvedValue(true);

      const result = await mockEnhancedLangGraphDatabase.validateWorkflowAccess('wf_test_123');

      expect(result).toBe(true);
      expect(mockEnhancedLangGraphDatabase.validateWorkflowAccess).toHaveBeenCalledWith(
        'wf_test_123'
      );
    });

    it('should return false for non-existing workflow', async () => {
      mockEnhancedLangGraphDatabase.validateWorkflowAccess.mockResolvedValue(false);

      const result = await mockEnhancedLangGraphDatabase.validateWorkflowAccess('nonexistent');

      expect(result).toBe(false);
    });
  });

  describe('getWorkflowPerformanceMetrics', () => {
    it('should return performance metrics', async () => {
      const workflowId = 'wf_test_123';

      const mockMetrics: WorkflowPerformanceMetrics = {
        total_duration_seconds: 300,
        analyst_durations: {
          technical: 5000,
          fundamental: 7000,
        },
        event_counts: {
          log: 10,
          error: 2,
        },
      };

      mockEnhancedLangGraphDatabase.getWorkflowPerformanceMetrics.mockResolvedValue(mockMetrics);

      const metrics = await mockEnhancedLangGraphDatabase.getWorkflowPerformanceMetrics(workflowId);

      expect(metrics.total_duration_seconds).toBe(300);
      expect(metrics.analyst_durations.technical).toBe(5000);
      expect(metrics.analyst_durations.fundamental).toBe(7000);
      expect(metrics.event_counts.log).toBe(10);
      expect(metrics.event_counts.error).toBe(2);
    });

    it('should handle missing data gracefully', async () => {
      const workflowId = 'wf_test_123';

      const mockMetrics: WorkflowPerformanceMetrics = {
        total_duration_seconds: 0,
        analyst_durations: {},
        event_counts: {},
      };

      mockEnhancedLangGraphDatabase.getWorkflowPerformanceMetrics.mockResolvedValue(mockMetrics);

      const metrics = await mockEnhancedLangGraphDatabase.getWorkflowPerformanceMetrics(workflowId);

      expect(metrics.total_duration_seconds).toBe(0);
      expect(Object.keys(metrics.analyst_durations)).toHaveLength(0);
      expect(Object.keys(metrics.event_counts)).toHaveLength(0);
    });
  });

  // ============================================================================
  // Data Integrity and Cleanup Tests
  // ============================================================================

  describe('validateWorkflowIntegrity', () => {
    it('should return valid for complete workflow', async () => {
      const workflowId = 'wf_test_123';

      const mockResult = {
        isValid: true,
        issues: [],
      };

      mockEnhancedLangGraphDatabase.validateWorkflowIntegrity.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.validateWorkflowIntegrity(workflowId);

      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should return invalid with issues for incomplete workflow', async () => {
      const workflowId = 'wf_incomplete';

      const mockResult = {
        isValid: false,
        issues: ['缺少technical分析师报告', '缺少fundamental分析师报告'],
      };

      mockEnhancedLangGraphDatabase.validateWorkflowIntegrity.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.validateWorkflowIntegrity(workflowId);

      expect(result.isValid).toBe(false);
      expect(result.issues).toHaveLength(2);
      expect(result.issues).toContain('缺少technical分析师报告');
      expect(result.issues).toContain('缺少fundamental分析师报告');
    });

    it('should handle non-existent workflow', async () => {
      const workflowId = 'nonexistent';

      const mockResult = {
        isValid: false,
        issues: ['工作流不存在'],
      };

      mockEnhancedLangGraphDatabase.validateWorkflowIntegrity.mockResolvedValue(mockResult);

      const result = await mockEnhancedLangGraphDatabase.validateWorkflowIntegrity(workflowId);

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('工作流不存在');
    });
  });

  describe('cleanupFailedWorkflows', () => {
    it('should clean up failed workflows older than specified days', async () => {
      const olderThanDays = 7;
      const expectedCleanedCount = 5;

      mockEnhancedLangGraphDatabase.cleanupFailedWorkflows.mockResolvedValue(expectedCleanedCount);

      const cleanedCount = await mockEnhancedLangGraphDatabase.cleanupFailedWorkflows(
        olderThanDays
      );

      expect(cleanedCount).toBe(expectedCleanedCount);
      expect(mockEnhancedLangGraphDatabase.cleanupFailedWorkflows).toHaveBeenCalledWith(
        olderThanDays
      );
    });

    it('should use default days when not specified', async () => {
      const expectedCleanedCount = 3;

      mockEnhancedLangGraphDatabase.cleanupFailedWorkflows.mockResolvedValue(expectedCleanedCount);

      const cleanedCount = await mockEnhancedLangGraphDatabase.cleanupFailedWorkflows();

      expect(cleanedCount).toBe(expectedCleanedCount);
      expect(mockEnhancedLangGraphDatabase.cleanupFailedWorkflows).toHaveBeenCalledWith();
    });

    it('should return 0 when no workflows to clean', async () => {
      mockEnhancedLangGraphDatabase.cleanupFailedWorkflows.mockResolvedValue(0);

      const cleanedCount = await mockEnhancedLangGraphDatabase.cleanupFailedWorkflows(30);

      expect(cleanedCount).toBe(0);
    });
  });

  // ============================================================================
  // Integration Tests
  // ============================================================================

  describe('Integration scenarios', () => {
    it('should handle complete workflow lifecycle', async () => {
      const workflowId = 'wf_lifecycle_test';

      // Create workflow
      const createRequest: CreateCompleteWorkflowRequest = {
        ticker: 'LIFECYCLE',
        title: '生命周期测试',
        created_by: 'test_user',
      };

      mockEnhancedLangGraphDatabase.createCompleteWorkflow.mockResolvedValue(workflowId);

      const createdId = await mockEnhancedLangGraphDatabase.createCompleteWorkflow(createRequest);
      expect(createdId).toBe(workflowId);

      // Update agent states
      const updates: AgentStateUpdate[] = [
        {
          workflow_id: workflowId,
          analyst_type: 'technical',
          status: 'completed',
          execution_time_ms: 5000,
        },
      ];

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates.mockResolvedValue(undefined);
      await mockEnhancedLangGraphDatabase.batchUpdateAgentStates(updates);

      // Get complete status
      const mockStatus: CompleteWorkflowStatus = {
        workflow: {
          id: 1,
          workflow_id: workflowId,
          ticker: 'LIFECYCLE',
          title: '生命周期测试',
          status: 'running',
          progress: 50,
          current_stage: 'analyst_analysis',
          created_by: 'test_user',
          created_at: new Date(),
          updated_at: new Date(),
        },
        analystReports: [],
        researchReports: [],
        finalDecision: null,
        recentEvents: [],
      };

      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus.mockResolvedValue(mockStatus);

      const status = await mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId);
      expect(status.workflow.workflow_id).toBe(workflowId);
      expect(status.workflow.status).toBe('running');
    });

    it('should handle error scenarios gracefully', async () => {
      const workflowId = 'wf_error_test';

      // Test database connection error
      const dbError = new Error('Database connection failed');
      mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus.mockRejectedValue(dbError);

      await expect(
        mockEnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId)
      ).rejects.toThrow('Database connection failed');

      // Test validation error
      const validationError = new Error('Invalid workflow ID format');
      mockEnhancedLangGraphDatabase.validateWorkflowAccess.mockRejectedValue(validationError);

      await expect(
        mockEnhancedLangGraphDatabase.validateWorkflowAccess('invalid-id')
      ).rejects.toThrow('Invalid workflow ID format');
    });
  });
});
