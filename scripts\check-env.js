#!/usr/bin/env node

/**
 * 环境变量检查脚本
 * 在应用启动前检查和显示环境变量配置
 */

const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

// 环境变量配置定义
const envConfigs = [
    // 基础配置
    {
        name: 'NODE_ENV',
        required: true,
        category: '基础配置',
        description: '运行环境'
    },

    // API 配置
    {
        name: 'NEXT_PUBLIC_API_BASE_URL',
        required: true,
        category: 'API配置',
        description: '前端API基础URL'
    },
    {
        name: 'NEXT_PUBLIC_API_BACKEND_BASE_URL',
        required: false,
        category: 'API配置',
        description: '后端API基础URL'
    },
    {
        name: 'NEXT_PUBLIC_WS_URL',
        required: false,
        category: 'API配置',
        description: 'WebSocket连接URL'
    },
    {
        name: 'BACK_END_URL',
        required: false,
        category: 'API配置',
        description: '后端服务URL'
    },
    {
        name: 'FRONTEND_URL',
        required: false,
        category: 'API配置',
        description: '前端服务URL'
    },

    // AI 配置
    {
        name: 'NEXT_PUBLIC_OPENAI_API_KEY',
        required: true,
        category: 'AI配置',
        description: 'OpenAI API密钥',
        sensitive: true
    },
    {
        name: 'OPENAI_BASE_URL',
        required: false,
        category: 'AI配置',
        description: 'OpenAI API基础URL'
    },

    // 第三方API
    {
        name: 'NEXT_PUBLIC_FINNHUB_API_KEY',
        required: false,
        category: '第三方API',
        description: 'FinnHub API密钥',
        sensitive: true
    },

    // 数据库配置
    {
        name: 'DB_HOST',
        required: false,
        category: '数据库配置',
        description: '数据库主机地址'
    },
    {
        name: 'DB_PORT',
        required: false,
        category: '数据库配置',
        description: '数据库端口'
    },
    {
        name: 'MYSQL_ROOT_PASSWORD',
        required: false,
        category: '数据库配置',
        description: 'MySQL root密码',
        sensitive: true
    }
];

// 加载环境变量文件
function loadEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        return {};
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};

    content.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const [key, ...valueParts] = line.split('=');
            if (key && valueParts.length > 0) {
                env[key.trim()] = valueParts.join('=').trim();
            }
        }
    });

    return env;
}

// 格式化敏感信息
function formatSensitiveValue(value) {
    if (!value || value.length <= 8) {
        return value || '未设置';
    }
    return `${value.substring(0, 4)}****${value.substring(value.length - 4)}`;
}

// 主函数
function checkEnvironment() {
    console.log(colorize('\n' + '='.repeat(80), 'cyan'));
    console.log(colorize('🚀 TradingAgents Frontend - 环境变量检查', 'bright'));
    console.log(colorize('='.repeat(80), 'cyan'));

    // 检测当前环境
    const nodeEnv = process.env.NODE_ENV || 'development';
    console.log(colorize(`\n📍 当前环境: ${nodeEnv}`, 'blue'));

    // 尝试加载环境变量文件
    const envFiles = [
        `.env.${nodeEnv}`,
        '.env.local',
        '.env'
    ];

    const loadedEnvs = {};
    envFiles.forEach(file => {
        const filePath = path.join(process.cwd(), file);
        if (fs.existsSync(filePath)) {
            console.log(colorize(`📄 加载环境文件: ${file}`, 'green'));
            Object.assign(loadedEnvs, loadEnvFile(filePath));
        }
    });

    // 合并 process.env 和加载的环境变量
    const allEnvs = { ...loadedEnvs, ...process.env };

    // 按类别分组检查
    const categories = {};
    envConfigs.forEach(config => {
        if (!categories[config.category]) {
            categories[config.category] = [];
        }
        categories[config.category].push(config);
    });

    let totalConfigs = 0;
    let configuredCount = 0;
    let missingRequired = [];

    Object.entries(categories).forEach(([category, configs]) => {
        console.log(colorize(`\n📂 ${category}`, 'magenta'));
        console.log(colorize('-'.repeat(50), 'white'));

        configs.forEach(config => {
            totalConfigs++;
            const value = allEnvs[config.name];
            const hasValue = Boolean(value);

            if (hasValue) {
                configuredCount++;
            } else if (config.required) {
                missingRequired.push(config.name);
            }

            const status = hasValue ? '✅' : (config.required ? '❌' : '⚠️');
            const statusColor = hasValue ? 'green' : (config.required ? 'red' : 'yellow');

            console.log(`${colorize(status, statusColor)} ${config.name}`);

            if (hasValue) {
                const displayValue = config.sensitive ? formatSensitiveValue(value) : value;
                console.log(`   ${colorize('值:', 'white')} ${displayValue}`);
            } else {
                console.log(`   ${colorize('状态:', statusColor)} 未设置`);
            }

            console.log(`   ${colorize('描述:', 'white')} ${config.description}`);
            console.log('');
        });
    });

    // 统计信息
    console.log(colorize('📊 配置统计', 'cyan'));
    console.log(colorize('-'.repeat(50), 'white'));
    console.log(`总配置项: ${totalConfigs}`);
    console.log(colorize(`已配置: ${configuredCount}`, 'green'));
    console.log(colorize(`未配置: ${totalConfigs - configuredCount}`, 'yellow'));

    // 检查必需配置
    if (missingRequired.length > 0) {
        console.log(colorize('\n🚨 缺少必需的环境变量:', 'red'));
        missingRequired.forEach(name => {
            const config = envConfigs.find(c => c.name === name);
            console.log(colorize(`   • ${name} - ${config.description}`, 'red'));
        });

        console.log(colorize('\n💡 请在相应的 .env 文件中设置这些变量', 'yellow'));
        console.log(colorize('   例如: .env.development 或 .env.production', 'yellow'));
    } else {
        console.log(colorize('\n✅ 所有必需的环境变量都已配置', 'green'));
    }

    // 环境文件建议
    console.log(colorize('\n📋 环境文件建议:', 'blue'));
    console.log('   • 开发环境: .env.development');
    console.log('   • 生产环境: .env.production');
    console.log('   • 本地覆盖: .env.local');

    console.log(colorize('\n' + '='.repeat(80), 'cyan'));
    console.log(colorize('🎯 环境检查完成', 'bright'));
    console.log(colorize('='.repeat(80) + '\n', 'cyan'));

    // 如果有必需的配置缺失，退出并返回错误码
    if (missingRequired.length > 0) {
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    checkEnvironment();
}

module.exports = { checkEnvironment };