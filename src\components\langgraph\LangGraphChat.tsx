'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';
import { formatDateTime } from '@/utils/helpers';
import {
  ArrowPathIcon,
  ChatBubbleLeftRightIcon,
  CpuChipIcon,
  PaperAirplaneIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

interface LangGraphChatProps {
  ticker?: string;
  onAnalysisComplete?: (result: any) => void;
  className?: string;
}

export function LangGraphChat({ ticker, onAnalysisComplete, className }: LangGraphChatProps) {
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    isProcessing,
    isStreaming,
    currentStep,
    progress,
    analysisResults,
    tradingDecision,
    error,
    connectionStatus,
    sessionInfo,
    threadId,
    analyzeStock,
    sendMessage,
    streamAnalysis,
    clearConversation,
    retry,
  } = useLangGraphAgent();

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 分析完成回调
  useEffect(() => {
    if (tradingDecision && onAnalysisComplete) {
      onAnalysisComplete({
        analysisResults,
        tradingDecision,
        messages,
      });
    }
  }, [tradingDecision, analysisResults, messages, onAnalysisComplete]);

  // 流式分析状态
  const [streamingResults, setStreamingResults] = useState<any[]>([]);
  const [showStreamingMode, setShowStreamingMode] = useState(false);

  // 处理流式分析
  const handleStreamAnalysis = async () => {
    if (!ticker) return;

    setShowStreamingMode(true);
    setStreamingResults([]);

    try {
      for await (const chunk of streamAnalysis(ticker)) {
        setStreamingResults((prev) => [...prev, chunk]);
      }
    } catch (error) {
      console.error('流式分析失败:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    const message = inputMessage.trim();
    setInputMessage('');

    try {
      await sendMessage(message);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  const handleAnalyzeStock = async () => {
    if (!ticker || isProcessing) return;

    try {
      await analyzeStock(ticker, {
        analysisType: 'comprehensive',
        includeRisk: true,
        includeSentiment: true,
      });
    } catch (error) {
      console.error('分析失败:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'human':
        return '👤';
      case 'ai':
        return '🤖';
      case 'tool':
        return '🔧';
      case 'system':
        return '⚙️';
      default:
        return '💬';
    }
  };

  const getMessageBgColor = (type: string) => {
    switch (type) {
      case 'human':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'ai':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'tool':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      case 'system':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <CpuChipIcon className="h-5 w-5 text-blue-600" />
            <span>LangGraph 智能分析</span>
            {threadId && <Badge variant="secondary">会话: {threadId.slice(-8)}</Badge>}
            {/* 连接状态指示器 */}
            <div className="flex items-center gap-1">
              <div
                className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected'
                    ? 'bg-green-500'
                    : connectionStatus === 'connecting'
                    ? 'bg-yellow-500'
                    : connectionStatus === 'error'
                    ? 'bg-red-500'
                    : 'bg-gray-400'
                }`}
              />
              <span className="text-xs text-slate-500">
                {connectionStatus === 'connected'
                  ? '已连接'
                  : connectionStatus === 'connecting'
                  ? '连接中'
                  : connectionStatus === 'error'
                  ? '连接错误'
                  : '未连接'}
              </span>
            </div>
          </CardTitle>

          <div className="flex items-center space-x-2">
            {ticker && (
              <>
                <Button
                  size="sm"
                  onClick={handleAnalyzeStock}
                  disabled={isProcessing}
                  className="flex items-center space-x-1"
                >
                  <ChatBubbleLeftRightIcon className="h-4 w-4" />
                  <span>分析 {ticker}</span>
                </Button>

                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleStreamAnalysis}
                  disabled={isProcessing || isStreaming}
                  className="flex items-center space-x-1"
                >
                  <div className={`h-4 w-4 ${isStreaming ? 'animate-pulse' : ''}`}>⚡</div>
                  <span>流式分析</span>
                </Button>
              </>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={retry}
              disabled={isProcessing || messages.length === 0}
            >
              <ArrowPathIcon className="h-4 w-4" />
            </Button>

            <Button variant="ghost" size="sm" onClick={clearConversation} disabled={isProcessing}>
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 状态和进度显示 */}
        {(isProcessing || isStreaming) && (
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400">
              <LoadingSpinner size="sm" />
              <span>{currentStep || (isStreaming ? '流式分析中...' : '处理中...')}</span>
              {isStreaming && (
                <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                  STREAMING
                </span>
              )}
            </div>

            {/* 进度条 */}
            {progress > 0 && (
              <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
                <div className="text-xs text-slate-500 mt-1">{progress}%</div>
              </div>
            )}
          </div>
        )}

        {/* 会话信息 */}
        {sessionInfo.messageCount > 0 && (
          <div className="text-xs text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 p-2 rounded">
            消息数: {sessionInfo.messageCount}
            {sessionInfo.updatedAt && ` | 最后更新: ${sessionInfo.updatedAt.toLocaleTimeString()}`}
            {sessionInfo.createdAt && ` | 创建时间: ${sessionInfo.createdAt.toLocaleTimeString()}`}
          </div>
        )}

        {error && (
          <div className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
            错误: {error}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 消息列表 */}
        <div className="h-96 overflow-y-auto space-y-3 p-2">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`p-3 rounded-lg border ${getMessageBgColor(message.type)}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 text-lg">{getMessageIcon(message.type)}</div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        {message.type === 'human'
                          ? '用户'
                          : message.type === 'ai'
                          ? 'AI助手'
                          : message.type === 'tool'
                          ? '工具'
                          : '系统'}
                      </span>
                      <span className="text-xs text-slate-500">
                        {formatDateTime(message.timestamp.toISOString(), 'HH:mm:ss')}
                      </span>
                    </div>

                    <div className="text-sm text-slate-800 dark:text-slate-200 whitespace-pre-wrap">
                      {message.content}
                    </div>

                    {message.metadata && (
                      <details className="mt-2">
                        <summary className="text-xs text-slate-500 cursor-pointer">元数据</summary>
                        <pre className="text-xs text-slate-600 dark:text-slate-400 mt-1 p-2 bg-slate-100 dark:bg-slate-800 rounded overflow-auto">
                          {JSON.stringify(message.metadata, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {/* 流式分析结果显示 */}
          {showStreamingMode && streamingResults.length > 0 && (
            <div className="border-t pt-4 mt-4">
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                流式分析结果
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {streamingResults.map((result, index) => (
                  <div
                    key={index}
                    className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded text-sm"
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-purple-700 dark:text-purple-300">
                        步骤 {index + 1}: {result.currentStep || '处理中...'}
                      </span>
                      {result.progress && (
                        <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded">
                          {result.progress}%
                        </span>
                      )}
                    </div>
                    {result.content && (
                      <div className="mt-1 text-slate-600 dark:text-slate-400 text-xs">
                        {result.content.substring(0, 100)}...
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {messages.length === 0 && !showStreamingMode && (
            <div className="text-center text-slate-500 py-8">
              <CpuChipIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>开始与 LangGraph 智能代理对话</p>
              <p className="text-sm mt-1">
                {ticker ? `输入消息或点击"分析 ${ticker}"开始` : '输入消息开始对话'}
              </p>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className="flex space-x-2">
          <div className="flex-1">
            <textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入消息... (Shift+Enter 换行)"
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
              disabled={isProcessing}
            />
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isProcessing}
            className="px-3"
          >
            {isProcessing ? (
              <LoadingSpinner size="sm" />
            ) : (
              <PaperAirplaneIcon className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* 快捷操作 */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('请解释一下当前的市场趋势')}
            disabled={isProcessing}
          >
            市场趋势
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('这只股票的风险如何？')}
            disabled={isProcessing}
          >
            风险评估
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setInputMessage('给我一些投资建议')}
            disabled={isProcessing}
          >
            投资建议
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
