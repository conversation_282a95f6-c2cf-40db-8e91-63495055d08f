/**
 * 多语言 SEO 系统验证脚本
 * 用于验证 i18n SEO 功能是否正常工作
 */

import {
  LanguageDetector,
  HreflangGenerator,
  I18nContentSwitcher,
  I18N_SEO_CONTENT,
} from './i18n-seo';
import { MetadataGenerator } from './metadata-generator';
import { PageSEOProps } from '@/types/seo';

/**
 * 验证语言检测功能
 */
export function verifyLanguageDetection() {
  console.log('🔍 验证语言检测功能...');

  // 测试从路径检测语言
  const pathTests = [
    { path: '/', expected: 'zh' },
    { path: '/en', expected: 'en' },
    { path: '/analysis', expected: 'zh' },
    { path: '/en/analysis', expected: 'en' },
  ];

  pathTests.forEach(({ path, expected }) => {
    const result = LanguageDetector.detectFromPath(path);
    console.log(`  路径 "${path}" -> 语言: ${result.locale}, 清理路径: ${result.cleanPath}`);
    if (result.locale !== expected) {
      console.error(`    ❌ 期望: ${expected}, 实际: ${result.locale}`);
    } else {
      console.log(`    ✅ 正确`);
    }
  });

  // 测试从请求头检测语言
  const headerTests = [
    { header: 'zh-CN,zh;q=0.9,en;q=0.8', expected: 'zh' },
    { header: 'en-US,en;q=0.9', expected: 'en' },
    { header: 'fr-FR,fr;q=0.9', expected: 'zh' }, // 不支持的语言应返回默认值
  ];

  headerTests.forEach(({ header, expected }) => {
    const result = LanguageDetector.detectFromHeaders(header);
    console.log(`  请求头 "${header}" -> 语言: ${result}`);
    if (result !== expected) {
      console.error(`    ❌ 期望: ${expected}, 实际: ${result}`);
    } else {
      console.log(`    ✅ 正确`);
    }
  });
}

/**
 * 验证 hreflang 生成功能
 */
export function verifyHreflangGeneration() {
  console.log('\n🔗 验证 hreflang 生成功能...');

  const testPaths = ['/', '/analysis', '/tasks'];

  testPaths.forEach((path) => {
    const links = HreflangGenerator.generateHreflangLinks(path, 'zh');
    console.log(`  路径 "${path}" 的 hreflang 链接:`);

    links.forEach((link) => {
      console.log(`    ${link.hreflang}: ${link.href}`);
    });

    // 验证必需的链接存在
    const hasZhCN = links.some((link) => link.hreflang === 'zh-CN');
    const hasEnUS = links.some((link) => link.hreflang === 'en-US');
    const hasDefault = links.some((link) => link.hreflang === 'x-default');

    if (hasZhCN && hasEnUS && hasDefault) {
      console.log(`    ✅ 所有必需的 hreflang 链接都存在`);
    } else {
      console.error(`    ❌ 缺少必需的 hreflang 链接`);
    }
  });
}

/**
 * 验证内容切换功能
 */
export function verifyContentSwitching() {
  console.log('\n🌐 验证内容切换功能...');

  const pages = ['home', 'analysis', 'tasks', 'messages', 'create-task'] as const;
  const locales = ['zh', 'en'] as const;

  pages.forEach((page) => {
    console.log(`  页面 "${page}":`);

    locales.forEach((locale) => {
      const content = I18nContentSwitcher.getPageContent(page, locale);
      console.log(`    ${locale}: ${content.title}`);

      // 验证内容完整性
      if (content.title && content.description && content.keywords.length > 0) {
        console.log(`      ✅ 内容完整`);
      } else {
        console.error(`      ❌ 内容不完整`);
      }
    });
  });
}

/**
 * 验证动态内容生成
 */
export function verifyDynamicContent() {
  console.log('\n🎯 验证动态内容生成...');

  const dynamicTests = [
    {
      page: 'analysis' as const,
      locale: 'zh' as const,
      variables: { stockSymbol: 'AAPL' },
      shouldContain: 'AAPL',
    },
    {
      page: 'tasks' as const,
      locale: 'en' as const,
      variables: { taskTitle: 'Apple Stock Analysis' },
      shouldContain: 'Apple Stock Analysis',
    },
  ];

  dynamicTests.forEach(({ page, locale, variables, shouldContain }) => {
    const content = I18nContentSwitcher.getDynamicContent(page, locale, variables as any);
    console.log(`  ${page} (${locale}) 动态内容: ${content.title}`);

    if (content.title.includes(shouldContain)) {
      console.log(`    ✅ 包含期望内容: ${shouldContain}`);
    } else {
      console.error(`    ❌ 不包含期望内容: ${shouldContain}`);
    }
  });
}

/**
 * 验证语言切换链接生成
 */
export function verifyLanguageSwitchLinks() {
  console.log('\n🔄 验证语言切换链接生成...');

  const testCases = [
    { path: '/analysis', currentLocale: 'zh' as const },
    { path: '/en/tasks', currentLocale: 'en' as const },
  ];

  testCases.forEach(({ path, currentLocale }) => {
    const links = I18nContentSwitcher.generateLanguageSwitchLinks(path, currentLocale);
    console.log(`  路径 "${path}" (当前语言: ${currentLocale}):`);

    links.forEach((link) => {
      const status = link.active ? '(当前)' : '';
      console.log(`    ${link.label}: ${link.href} ${status}`);
    });

    // 验证链接数量和活动状态
    const activeLinks = links.filter((link) => link.active);
    if (activeLinks.length === 1 && activeLinks[0].locale === currentLocale) {
      console.log(`    ✅ 语言切换链接正确`);
    } else {
      console.error(`    ❌ 语言切换链接有误`);
    }
  });
}

/**
 * 验证元数据生成集成
 */
export function verifyMetadataGeneration() {
  console.log('\n📄 验证元数据生成集成...');

  const testCases = [
    { page: 'home' as const, locale: 'zh' as const },
    { page: 'home' as const, locale: 'en' as const },
    { page: 'analysis' as const, locale: 'zh' as const, dynamicData: { stockSymbol: 'TSLA' } },
  ];

  testCases.forEach(({ page, locale, dynamicData }) => {
    const generator = new MetadataGenerator(locale);
    const props: PageSEOProps = { page, locale, dynamicData };

    try {
      const metadata = generator.generatePageMetadata(props);
      console.log(`  ${page} (${locale}): ${metadata.title}`);

      // 验证基本字段
      if (metadata.title && metadata.description) {
        console.log(`    ✅ 基本元数据正确`);
      } else {
        console.error(`    ❌ 基本元数据缺失`);
      }

      // 验证多语言链接
      if (metadata.alternates?.languages) {
        const hasMultipleLanguages = Object.keys(metadata.alternates.languages).length > 1;
        if (hasMultipleLanguages) {
          console.log(`    ✅ 多语言链接正确`);
        } else {
          console.error(`    ❌ 多语言链接缺失`);
        }
      }
    } catch (error) {
      console.error(`    ❌ 元数据生成失败: ${error}`);
    }
  });
}

/**
 * 运行所有验证测试
 */
export function runAllVerifications() {
  console.log('🚀 开始验证多语言 SEO 系统...\n');

  try {
    verifyLanguageDetection();
    verifyHreflangGeneration();
    verifyContentSwitching();
    verifyDynamicContent();
    verifyLanguageSwitchLinks();
    verifyMetadataGeneration();

    console.log('\n✅ 所有验证测试完成！');
    console.log('🎉 多语言 SEO 系统已成功实现并验证！');
  } catch (error) {
    console.error('\n❌ 验证过程中出现错误:', error);
  }
}

// 如果直接运行此文件，执行所有验证
if (require.main === module) {
  runAllVerifications();
}
