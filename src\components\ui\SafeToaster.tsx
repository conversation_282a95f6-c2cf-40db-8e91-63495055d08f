'use client';

import { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';

/**
 * 安全的 Toaster 组件
 * 解决 SSR 水合不匹配问题
 * 
 * react-hot-toast 的 Toaster 组件在服务端渲染时可能会导致水合不匹配，
 * 因为它依赖于客户端的 DOM 操作和事件监听。
 * 这个组件确保 Toaster 只在客户端渲染。
 */
export function SafeToaster() {
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件只在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 在服务端渲染时返回 null，避免水合不匹配
  if (!isMounted) {
    return null;
  }

  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 4000,
        style: {
          background: '#363636',
          color: '#fff',
        },
        success: {
          duration: 3000,
          iconTheme: {
            primary: '#22c55e',
            secondary: '#fff',
          },
        },
        error: {
          duration: 5000,
          iconTheme: {
            primary: '#ef4444',
            secondary: '#fff',
          },
        },
      }}
    />
  );
}
