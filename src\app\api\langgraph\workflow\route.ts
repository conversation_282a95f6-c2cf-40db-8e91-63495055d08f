// LangGraph 工作流管理 API
import LangGraphDatabase from '@/lib/langgraph-database';
import { CreateWorkflowRequest, UpdateWorkflowStatusRequest } from '@/types/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

// POST - 创建新的工作流实例
export async function POST(request: NextRequest) {
  try {
    const body: CreateWorkflowRequest = await request.json();

    // 验证必需字段
    if (!body.ticker || !body.title) {
      return NextResponse.json(
        { success: false, message: '缺少必需字段: ticker, title' },
        { status: 400 }
      );
    }

    // 创建工作流实例
    const workflowId = await LangGraphDatabase.createWorkflow(body);

    return NextResponse.json(
      {
        success: true,
        data: { workflow_id: workflowId },
        message: '工作流创建成功',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建工作流实例失败:', error);
    return NextResponse.json({ success: false, message: '服务器内部错误' }, { status: 500 });
  }
}

// GET - 查询工作流实例
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflow_id = searchParams.get('workflow_id');
    const task_id = searchParams.get('task_id');
    const ticker = searchParams.get('ticker');
    const status = searchParams.get('status');
    const current_stage = searchParams.get('current_stage');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    if (workflow_id) {
      // 获取单个工作流实例
      const workflow = await LangGraphDatabase.getWorkflow(workflow_id);
      if (workflow) {
        return NextResponse.json({ success: true, data: workflow });
      } else {
        return NextResponse.json({ success: false, message: '工作流实例不存在' }, { status: 404 });
      }
    } else {
      // 查询工作流列表
      const queryOptions = {
        task_id: task_id || undefined,
        ticker: ticker || undefined,
        status: status ? (status.split(',') as any) : undefined,
        current_stage: current_stage || undefined,
        limit: limit ? parseInt(limit) : undefined,
        offset: offset ? parseInt(offset) : undefined,
      };

      const workflows = await LangGraphDatabase.queryWorkflows(queryOptions);
      return NextResponse.json({ success: true, data: workflows });
    }
  } catch (error) {
    console.error('查询工作流实例失败:', error);
    return NextResponse.json({ success: false, message: '服务器内部错误' }, { status: 500 });
  }
}

// PUT - 更新工作流状态
export async function PUT(request: NextRequest) {
  try {
    const body: UpdateWorkflowStatusRequest = await request.json();

    // 验证必需字段
    if (!body.workflow_id || !body.current_stage || body.progress === undefined || !body.status) {
      return NextResponse.json(
        { success: false, message: '缺少必需字段: workflow_id, current_stage, progress, status' },
        { status: 400 }
      );
    }

    // 更新工作流状态
    await LangGraphDatabase.updateWorkflowStatus(body);
    const result = { success: true, message: '工作流状态更新成功' };

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error('更新工作流状态失败:', error);
    return NextResponse.json({ success: false, message: '服务器内部错误' }, { status: 500 });
  }
}
