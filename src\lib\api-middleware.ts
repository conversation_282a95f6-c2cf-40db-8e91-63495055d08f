/**
 * API 错误处理中间件
 * 提供统一的API错误处理和响应格式
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError, LogContext, logger, PerformanceMonitor } from './logger';
import { monitoring } from './monitoring';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
  timestamp: string;
  requestId?: string;
}

export interface ApiHandlerOptions {
  requireAuth?: boolean;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
  timeout?: number;
}

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 提取请求上下文
function extractRequestContext(request: NextRequest): LogContext {
  const url = new URL(request.url);
  return {
    method: request.method,
    path: url.pathname,
    userAgent: request.headers.get('user-agent') || undefined,
    ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
    referer: request.headers.get('referer') || undefined,
  };
}

// API处理器包装函数
export function withApiHandler<T = any>(
  handler: (request: NextRequest, context: LogContext) => Promise<T>,
  options: ApiHandlerOptions = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const requestId = generateRequestId();
    const requestContext = extractRequestContext(request);
    const context: LogContext = {
      ...requestContext,
      requestId,
      component: 'API',
    };

    const monitor = new PerformanceMonitor(`${request.method} ${requestContext.path}`, context);

    try {
      logger.info('API请求开始', context, {
        method: request.method,
        path: requestContext.path,
        hasBody: request.body !== null,
      });

      // 超时处理
      const timeoutMs = options.timeout || 30000; // 默认30秒
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
      });

      // 执行处理器
      const result = (await Promise.race([handler(request, context), timeoutPromise])) as T;

      const response: ApiResponse<T> = {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        requestId,
      };

      const responseTime = Date.now() - (monitor as any).startTime;

      monitor.end(true, {
        hasResult: !!result,
        resultType: typeof result,
      });

      // 记录到监控系统
      monitoring.recordRequest(requestContext.path || 'unknown', responseTime, true);

      logger.info('API请求成功', context, {
        hasResult: !!result,
        resultType: typeof result,
        responseTime,
      });

      return NextResponse.json(response);
    } catch (error) {
      const responseTime = Date.now() - (monitor as any).startTime;

      monitor.error(error as Error);

      // 记录错误到监控系统
      monitoring.recordError(error as Error, context);
      monitoring.recordRequest(requestContext.path || 'unknown', responseTime, false);

      const errorInfo = handleApiError(error, context);

      const response: ApiResponse = {
        success: false,
        error: errorInfo.error,
        code: errorInfo.code,
        timestamp: new Date().toISOString(),
        requestId,
      };

      // 在开发环境中添加详细错误信息
      if (process.env.NODE_ENV === 'development' && errorInfo.details) {
        (response as any).details = errorInfo.details;
      }

      return NextResponse.json(response, {
        status: errorInfo.statusCode,
      });
    }
  };
}

// 流式响应处理器
export function withStreamHandler(
  handler: (request: NextRequest, context: LogContext) => AsyncIterable<any>,
  options: ApiHandlerOptions = {}
) {
  return async (request: NextRequest): Promise<Response> => {
    const requestId = generateRequestId();
    const requestContext = extractRequestContext(request);
    const context: LogContext = {
      ...requestContext,
      requestId,
      component: 'StreamAPI',
    };

    const monitor = new PerformanceMonitor(
      `STREAM ${request.method} ${requestContext.path}`,
      context
    );

    try {
      logger.info('流式API请求开始', context);

      const stream = new ReadableStream({
        async start(controller) {
          try {
            let chunkCount = 0;

            for await (const chunk of handler(request, context)) {
              const data = `data: ${JSON.stringify(chunk)}\n\n`;
              controller.enqueue(new TextEncoder().encode(data));
              chunkCount++;
            }

            // 发送结束标记
            controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'));
            controller.close();

            monitor.end(true, { chunkCount });
            logger.info('流式API请求完成', context, { chunkCount });
          } catch (error) {
            monitor.error(error as Error);

            const errorInfo = handleApiError(error, context);
            const errorData = `data: ${JSON.stringify({
              error: errorInfo.error,
              code: errorInfo.code,
            })}\n\n`;

            controller.enqueue(new TextEncoder().encode(errorData));
            controller.close();
          }
        },
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive',
          'X-Request-ID': requestId,
        },
      });
    } catch (error) {
      monitor.error(error as Error);

      const errorInfo = handleApiError(error, context);

      return NextResponse.json(
        {
          success: false,
          error: errorInfo.error,
          code: errorInfo.code,
          timestamp: new Date().toISOString(),
          requestId,
        },
        {
          status: errorInfo.statusCode,
        }
      );
    }
  };
}

// 验证请求体
export async function validateRequestBody<T>(
  request: NextRequest,
  validator: (body: any) => T,
  context: LogContext
): Promise<T> {
  try {
    const body = await request.json();

    logger.debug('请求体解析成功', context, {
      bodyKeys: Object.keys(body || {}),
      bodySize: JSON.stringify(body).length,
    });

    return validator(body);
  } catch (error) {
    logger.error('请求体验证失败', context, error as Error);
    throw new Error('Invalid request body');
  }
}

// 健康检查处理器
export const healthCheckHandler = withApiHandler(async (request, context) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  };

  logger.debug('健康检查', context, health);

  return health;
});

// 错误响应辅助函数
export function createErrorResponse(
  error: string,
  code: string = 'UNKNOWN_ERROR',
  statusCode: number = 500,
  requestId?: string
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error,
    code,
    timestamp: new Date().toISOString(),
    requestId,
  };

  return NextResponse.json(response, { status: statusCode });
}

// 成功响应辅助函数
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  requestId?: string
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
    requestId,
  };

  return NextResponse.json(response);
}
