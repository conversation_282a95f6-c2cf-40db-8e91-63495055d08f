// LangGraph Workflow Database Type Definitions - V4.0 (Workflow-centric)
// Corresponds to the new schema in database/init.sql

// ============================================================================
// ENUM Types
// ============================================================================

export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type AnalystType = 'fundamental' | 'technical' | 'sentiment' | 'news';
export type ResearcherType = 'bull' | 'bear';
export type ParticipantType = 'bull' | 'bear' | 'moderator';
export type DecisionType = 'buy' | 'sell' | 'hold' | 'avoid';
export type EventType = 'message' | 'tool_call' | 'state_change' | 'error' | 'log';
export type ArgumentType = 'main_argument' | 'evidence' | 'counter_argument' | 'rebuttal';
export type UtteranceType = 'statement' | 'question' | 'rebuttal' | 'summary';

// ============================================================================
// Core Table Interfaces
// ============================================================================

// 1. Workflow (replaces Task and LangGraphWorkflow)
export interface Workflow {
  id: number;
  workflow_id: string;
  thread_id?: string;
  ticker: string;
  title: string;
  description?: string;
  status: WorkflowStatus;
  current_stage: string;
  progress: number;
  config?: any;
  created_by: string;
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
  error_message?: string;
}

// 2. Analyst Report (Directory Table)
export interface AnalystReport {
  id: number;
  report_id: string;
  workflow_id: string;
  analyst_type: AnalystType;
  summary?: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: Date;
}

// 3. Technical Analysis Details
export interface TechnicalAnalysisDetails {
  report_id: string; // Foreign Key to AnalystReport
  trading_signal?: 'buy' | 'sell' | 'hold' | 'avoid';
  trend_signal?: 'up' | 'down' | 'sideways';
  support_level?: number;
  resistance_level?: number;
  stop_loss_level?: number;
  target_price?: number;
  rsi_value?: number;
  macd_signal?: 'golden_cross' | 'death_cross' | 'neutral';
  key_levels?: any;
}

// 4. Sentiment Analysis Details
export interface SentimentAnalysisDetails {
  report_id: string; // Foreign Key to AnalystReport
  overall_sentiment: 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative';
  sentiment_score: number;
  positive_news_count: number;
  negative_news_count: number;
  neutral_news_count: number;
  key_drivers?: string;
}

// 5. Research Report
export interface ResearchReport {
  id: number;
  report_id: string;
  workflow_id: string;
  researcher_type: ResearcherType;
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'completed' | 'failed';
  created_at: Date;
}

// 6. Research Argument
export interface ResearchArgument {
  id: number;
  argument_id: string;
  report_id: string;
  parent_argument_id?: string;
  argument_type: ArgumentType;
  content: string;
  source?: string;
  strength_score?: number;
  sequence_order: number;
  created_at: Date;
}

// 7. Debate Session
export interface DebateSession {
  id: number;
  session_id: string;
  workflow_id: string;
  status: 'in_progress' | 'completed' | 'cancelled';
  total_rounds: number;
  summary?: string;
  created_at: Date;
}

// 8. Debate Round
export interface DebateRound {
  id: number;
  round_id: string;
  session_id: string;
  round_number: number;
  focus_question?: string;
  round_summary?: string;
  created_at: Date;
}

// 9. Debate Utterance
export interface DebateUtterance {
  id: number;
  utterance_id: string;
  round_id: string;
  participant_type: ParticipantType;
  utterance_type: UtteranceType;
  content: string;
  rebuttal_to_utterance_id?: string;
  related_argument_id?: string;
  sequence_in_round: number;
  created_at: Date;
}

// 10. Consensus Evaluation
export interface ConsensusEvaluation {
  id: number;
  consensus_id: string;
  workflow_id: string;
  bull_strength?: number;
  bear_strength?: number;
  consensus_direction?: 'bullish' | 'bearish' | 'neutral';
  consensus_confidence?: number;
  synthesis_summary?: string;
  key_agreement_points?: any;
  key_disagreement_points?: any;
  created_at: Date;
}

// 11. Risk Assessment
export interface RiskAssessment {
  id: number;
  risk_id: string;
  workflow_id: string;
  overall_risk_level: 'low' | 'medium' | 'high';
  risk_score: number;
  summary?: string;
  market_risk?: any;
  liquidity_risk?: any;
  credit_risk?: any;
  operational_risk?: any;
  scenario_analysis?: any;
  risk_metrics?: any;
  recommendations?: any;
  risk_controls?: any;
  risk_warnings?: any;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: Date;
}

// 12. Final Decision
export interface FinalDecision {
  id: number;
  decision_id: string;
  workflow_id: string;
  decision_type: DecisionType;
  confidence_level?: number;
  decision_rationale?: string;
  entry_price_range?: any;
  stop_loss_price?: number;
  take_profit_price?: number;
  position_size_percentage?: number;
  created_at: Date;
}

// 13. Workflow Event
export interface WorkflowEvent {
  id: number;
  event_id: string;
  workflow_id: string;
  stage_name?: string;
  event_type: EventType;
  content: string;
  metadata?: any;
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  duration_ms?: number;
  created_at: Date;
}

// 14. Workflow State Snapshot
export interface WorkflowStateSnapshot {
  id: number;
  snapshot_id: string;
  workflow_id: string;
  stage_name: string;
  state_data: any;
  checkpoint_id?: string;
  created_at: Date;
}

// ============================================================================
// API Request/Response Interfaces
// ============================================================================

// Create Workflow
export interface CreateWorkflowRequest {
  ticker: string;
  title: string;
  description?: string;
  config?: any;
  created_by?: string;
}

// Update Workflow Status
export interface UpdateWorkflowStatusRequest {
  workflow_id: string;
  current_stage: string;
  progress: number;
  status: WorkflowStatus;
  error_message?: string;
}

// Save Analyst Report
export interface SaveAnalystReportRequest {
  workflow_id: string;
  analyst_type: AnalystType;
  summary?: string;
  status?: 'completed' | 'failed';
  execution_time_ms?: number;
  details: TechnicalAnalysisDetails | SentimentAnalysisDetails | any; // Can be extended
}

// Save Research Report
export interface SaveResearchReportRequest {
  workflow_id: string;
  researcher_type: ResearcherType;
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status?: 'completed' | 'failed';
  arguments: Omit<ResearchArgument, 'id' | 'argument_id' | 'report_id' | 'created_at'>[];
}

// Save Consensus Evaluation
export interface SaveConsensusEvaluationRequest {
  workflow_id: string;
  bull_strength?: number;
  bear_strength?: number;
  consensus_direction?: 'bullish' | 'bearish' | 'neutral';
  consensus_confidence?: number;
  synthesis_summary?: string;
  key_agreement_points?: any;
  key_disagreement_points?: any;
}

// Save Risk Assessment
export interface SaveRiskAssessmentRequest {
  workflow_id: string;
  overall_risk_level: 'low' | 'medium' | 'high';
  risk_score: number;
  summary?: string;
  market_risk?: any;
  liquidity_risk?: any;
  credit_risk?: any;
  operational_risk?: any;
  scenario_analysis?: any;
  risk_metrics?: any;
  recommendations?: any;
  risk_controls?: any;
  risk_warnings?: any;
  status?: 'completed' | 'failed';
  execution_time_ms?: number;
}

// Save Final Decision
export interface SaveFinalDecisionRequest {
  workflow_id: string;
  decision_type: DecisionType;
  confidence_level?: number;
  decision_rationale?: string;
  entry_price_range?: any;
  stop_loss_price?: number;
  take_profit_price?: number;
  position_size_percentage?: number;
}

// Log Workflow Event
export interface LogWorkflowEventRequest {
  workflow_id: string;
  stage_name?: string;
  event_type: EventType;
  content: string;
  metadata?: any;
}

// Save State Snapshot
export interface SaveStateSnapshotRequest {
  workflow_id: string;
  stage_name: string;
  state_data: any;
  checkpoint_id?: string;
}

// Query Options
export interface WorkflowQueryOptions {
  ticker?: string;
  status?: WorkflowStatus[];
  date_from?: Date;
  date_to?: Date;
  limit?: number;
  offset?: number;
}

// ============================================================================
// Enhanced Types for Analysis Data Storage
// ============================================================================

// Complete workflow status with all related data
export interface CompleteWorkflowStatus {
  workflow: Workflow;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  consensusEvaluation: ConsensusEvaluation | null;
  riskAssessment: RiskAssessment | null;
  finalDecision: FinalDecision | null;
  recentEvents: WorkflowEvent[];
}

// Enhanced workflow creation request
export interface CreateCompleteWorkflowRequest extends CreateWorkflowRequest {
  config?: any;
}

// Agent state update for batch operations
export interface AgentStateUpdate {
  workflow_id: string;
  analyst_type: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
}

// Statistics query options
export interface StatisticsOptions {
  date_from?: string;
  date_to?: string;
  ticker?: string;
}

// Analysis statistics response
export interface AnalysisStatistics {
  basic: {
    total_workflows: number;
    completed_workflows: number;
    failed_workflows: number;
    avg_duration_seconds: number;
  };
  successRates: Array<{
    ticker: string;
    total_count: number;
    success_count: number;
  }>;
}

// Workflow status summary for multiple workflows
export interface WorkflowStatusSummary {
  workflow_id: string;
  status: string;
  progress: number;
  current_stage: string;
  analyst_completion: {
    fundamental: boolean;
    technical: boolean;
    sentiment: boolean;
    news: boolean;
  };
  research_completion: {
    bull: boolean;
    bear: boolean;
  };
  final_decision_completed: boolean;
}

// Enhanced workflow query options
export interface EnhancedWorkflowQueryOptions extends WorkflowQueryOptions {
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  include_stats?: boolean;
}

// Paginated workflow response
export interface PaginatedWorkflowResponse {
  workflows: Workflow[];
  total: number;
  page: number;
  limit: number;
}

// Performance metrics
export interface WorkflowPerformanceMetrics {
  total_duration_seconds: number;
  analyst_durations: Record<string, number>;
  event_counts: Record<string, number>;
}
