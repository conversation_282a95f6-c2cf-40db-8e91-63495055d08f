# SEO 基础架构实现文档

## 概述

本模块实现了 TradingAgents 项目的 SEO 基础架构，包括类型定义、配置管理、工具函数和元数据生成器。

## 文件结构

```
src/lib/seo/
├── index.ts                    # 模块入口文件
├── types/                      # TypeScript 类型定义
├── config.ts                   # SEO 配置和常量
├── utils.ts                    # SEO 工具函数
├── metadata-generator.ts       # 元数据生成器
├── error-handler.ts           # 错误处理器
├── runtime-config.ts          # 运行时配置管理
├── constants.ts               # 环境变量常量
├── validate-seo.ts            # SEO 验证脚本
├── __tests__/                 # 测试文件
└── README.md                  # 本文档
```

## 核心功能

### 1. 类型定义 (`src/types/seo.ts`)

- `SEOConfig`: SEO 配置接口
- `PageSEOProps`: 页面 SEO 属性
- `SupportedLocale`: 支持的语言类型
- `StructuredDataConfig`: 结构化数据配置
- `CoreWebVitals`: Core Web Vitals 指标

### 2. 配置管理 (`config.ts`)

- `SEO_CONSTANTS`: SEO 相关常量
- `DEFAULT_SEO_CONFIG`: 默认 SEO 配置
- `SEO_CONTENT`: 多语言 SEO 内容
- `STRUCTURED_DATA_TEMPLATES`: 结构化数据模板

### 3. 工具函数 (`utils.ts`)

- `SEOUtils`: SEO 工具类

  - 标题、描述、关键词验证
  - 标题和描述优化
  - URL 生成和语言检测
  - 结构化数据验证
  - SEO 分数计算

- `WebVitalsUtils`: Core Web Vitals 工具类
  - LCP、FID、CLS 性能评估
  - 整体性能分数计算

### 4. 元数据生成器 (`metadata-generator.ts`)

- `MetadataGenerator`: 元数据生成器类
  - 页面元数据生成
  - 结构化数据生成
  - JSON-LD 脚本生成
  - 多语言支持
  - 动态数据处理

## 使用方法

### 基本使用

```typescript
import { generatePageMetadata, MetadataGenerator } from '@/lib/seo';

// 生成页面元数据
const metadata = generatePageMetadata({
  page: 'home',
  locale: 'zh',
});

// 使用元数据生成器
const generator = new MetadataGenerator('zh');
const structuredData = generator.generateStructuredData('home');
```

### 在 Next.js 页面中使用

```typescript
// app/page.tsx
import { generatePageMetadata } from '@/lib/seo';

export const metadata = generatePageMetadata({
  page: 'home',
  locale: 'zh',
});

export default function HomePage() {
  return <div>首页内容</div>;
}
```

### 动态页面元数据

```typescript
// app/analysis/[id]/page.tsx
import { generatePageMetadata } from '@/lib/seo';

export async function generateMetadata({ params }: { params: { id: string } }) {
  return generatePageMetadata({
    page: 'analysis',
    dynamicData: {
      analysisId: params.id,
    },
    locale: 'zh',
  });
}
```

## 配置说明

### 环境变量

```bash
# 网站基础 URL
NEXT_PUBLIC_BASE_URL=https://tradingagents.com

# Google Analytics 测量 ID
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Search Console 验证码
NEXT_PUBLIC_GSC_VERIFICATION=your-verification-code

# 百度站长验证码
NEXT_PUBLIC_BAIDU_VERIFICATION=your-baidu-verification-code
```

### SEO 常量配置

```typescript
export const SEO_CONSTANTS = {
  TITLE_MAX_LENGTH: 60, // 标题最大长度
  DESCRIPTION_MAX_LENGTH: 160, // 描述最大长度
  KEYWORDS_MAX_COUNT: 10, // 关键词最大数量
  SITE_NAME: 'TradingAgents', // 网站名称
  DEFAULT_OG_IMAGE: '/tradingAgent.png', // 默认 OG 图片
};
```

## 验证和测试

### 运行验证脚本

```bash
npx tsx src/lib/seo/validate-seo.ts
```

### 测试结果示例

```
🔍 开始验证 SEO 基础架构...
✅ SEO 基础架构验证通过！

📊 验证统计:
  - 错误: 0
  - 警告: 0
  - 状态: 通过
```

## 最佳实践

### 1. 标题优化

- 长度控制在 50-60 个字符
- 包含主要关键词
- 包含品牌名称
- 避免关键词堆砌

### 2. 描述优化

- 长度控制在 150-160 个字符
- 包含相关关键词
- 提供清晰的页面描述
- 包含行动号召

### 3. 关键词策略

- 每页 5-10 个相关关键词
- 包含长尾关键词
- 考虑用户搜索意图
- 定期更新和优化

### 4. 结构化数据

- 使用 Schema.org 标准
- 提供准确的业务信息
- 定期验证数据格式
- 监控搜索结果展示

## 扩展功能

### 添加新页面类型

1. 在 `PageType` 类型中添加新页面
2. 在 `SEO_CONTENT` 中添加对应内容
3. 更新元数据生成器逻辑

### 添加新语言支持

1. 在 `SupportedLocale` 中添加语言代码
2. 在 `SEO_CONTENT` 中添加对应翻译
3. 更新语言检测逻辑

### 自定义结构化数据

1. 定义新的 Schema 接口
2. 在 `STRUCTURED_DATA_TEMPLATES` 中添加模板
3. 更新生成器逻辑

## 故障排除

### 常见问题

1. **元数据未生成**: 检查页面类型和语言配置
2. **结构化数据无效**: 使用 Google Rich Results Test 验证
3. **环境变量未加载**: 确保变量名以 `NEXT_PUBLIC_` 开头
4. **类型错误**: 检查导入路径和类型定义

### 调试方法

1. 使用验证脚本检查基础功能
2. 在浏览器开发者工具中检查元数据
3. 使用 Google Search Console 监控索引状态
4. 定期运行 SEO 审计工具

## 更新日志

### v1.0.0 (当前版本)

- ✅ 实现基础 SEO 类型定义
- ✅ 实现 SEO 配置和常量管理
- ✅ 实现 SEO 工具函数
- ✅ 实现元数据生成器
- ✅ 实现错误处理机制
- ✅ 实现验证和测试功能
- ✅ 支持多语言 SEO
- ✅ 支持动态元数据生成
- ✅ 支持结构化数据生成

## 贡献指南

1. 遵循现有的代码风格和命名约定
2. 添加适当的类型定义和注释
3. 编写相应的测试用例
4. 更新文档和示例
5. 运行验证脚本确保功能正常
