/**
 * 结构化数据验证器
 * 验证 Schema.org 结构化数据的格式和完整性
 */

import {
  OrganizationSchema,
  WebsiteSchema,
  SoftwareApplicationSchema,
  BreadcrumbSchema,
  FinancialServiceSchema,
  InvestmentServiceSchema,
  TradingPlatformSchema,
  AnalysisReportSchema,
  StockAnalysisSchema,
  AIAgentSchema,
} from '@/types/seo';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 0-100
}

export interface ValidationOptions {
  strict?: boolean; // 严格模式，要求所有可选字段
  checkUrls?: boolean; // 检查URL的有效性
  validateDates?: boolean; // 验证日期格式
}

/**
 * 结构化数据验证器类
 */
export class StructuredDataValidator {
  private options: ValidationOptions;

  constructor(options: ValidationOptions = {}) {
    this.options = {
      strict: false,
      checkUrls: true,
      validateDates: true,
      ...options,
    };
  }

  /**
   * 验证基础结构化数据
   */
  validateBasicStructure(data: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      score: 0,
    };

    // 检查必需的 @context 和 @type
    if (!data['@context']) {
      result.errors.push('Missing required @context field');
      result.isValid = false;
    } else if (data['@context'] !== 'https://schema.org') {
      result.warnings.push('@context should be "https://schema.org"');
    } else {
      result.score += 20;
    }

    if (!data['@type']) {
      result.errors.push('Missing required @type field');
      result.isValid = false;
    } else {
      result.score += 20;
    }

    return result;
  }

  /**
   * 验证组织信息结构化数据
   */
  validateOrganizationSchema(data: OrganizationSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'url'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof OrganizationSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 10;
      }
    });

    // 检查URL格式
    if (this.options.checkUrls && data.url && !this.isValidUrl(data.url)) {
      result.errors.push('Invalid URL format in url field');
      result.isValid = false;
    }

    // 检查logo URL
    if (data.logo && !this.isValidUrl(data.logo)) {
      result.warnings.push('Invalid logo URL format');
    } else if (data.logo) {
      result.score += 5;
    }

    // 检查联系信息
    if (data.contactPoint) {
      if (!data.contactPoint['@type'] || data.contactPoint['@type'] !== 'ContactPoint') {
        result.warnings.push('ContactPoint should have @type: "ContactPoint"');
      } else {
        result.score += 5;
      }
    }

    // 检查社交媒体链接
    if (data.sameAs && Array.isArray(data.sameAs)) {
      const invalidUrls = data.sameAs.filter((url) => !this.isValidUrl(url));
      if (invalidUrls.length > 0) {
        result.warnings.push(`Invalid URLs in sameAs: ${invalidUrls.join(', ')}`);
      } else {
        result.score += 10;
      }
    }

    return result;
  }

  /**
   * 验证网站信息结构化数据
   */
  validateWebsiteSchema(data: WebsiteSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'url', 'description'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof WebsiteSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 检查搜索功能
    if (data.potentialAction) {
      if (data.potentialAction['@type'] !== 'SearchAction') {
        result.warnings.push('potentialAction should have @type: "SearchAction"');
      } else {
        result.score += 15;
      }

      if (!data.potentialAction.target) {
        result.warnings.push('SearchAction should have target URL');
      }
    }

    return result;
  }

  /**
   * 验证软件应用结构化数据
   */
  validateSoftwareApplicationSchema(data: SoftwareApplicationSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'applicationCategory'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof SoftwareApplicationSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 检查应用类别
    if (data.applicationCategory && data.applicationCategory !== 'FinanceApplication') {
      result.warnings.push('applicationCategory should be "FinanceApplication" for financial apps');
    }

    // 检查评分信息
    if (data.aggregateRating) {
      if (!data.aggregateRating.ratingValue || !data.aggregateRating.ratingCount) {
        result.warnings.push('aggregateRating should include ratingValue and ratingCount');
      } else {
        result.score += 10;
      }
    }

    // 检查价格信息
    if (data.offers) {
      if (!data.offers.price || !data.offers.priceCurrency) {
        result.warnings.push('offers should include price and priceCurrency');
      } else {
        result.score += 5;
      }
    }

    return result;
  }

  /**
   * 验证金融服务结构化数据
   */
  validateFinancialServiceSchema(data: FinancialServiceSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'provider', 'category'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof FinancialServiceSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 检查提供商信息
    if (data.provider && typeof data.provider === 'object') {
      const providerResult = this.validateOrganizationSchema(data.provider);
      if (!providerResult.isValid) {
        result.errors.push('Invalid provider organization data');
        result.isValid = false;
      } else {
        result.score += 10;
      }
    }

    return result;
  }

  /**
   * 验证投资服务结构化数据
   */
  validateInvestmentServiceSchema(data: InvestmentServiceSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'provider', 'serviceType'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof InvestmentServiceSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 检查服务类型
    const validServiceTypes = ['Investment Advisory', 'Portfolio Management', 'Trading Platform'];
    if (data.serviceType && !validServiceTypes.includes(data.serviceType)) {
      result.warnings.push(`serviceType should be one of: ${validServiceTypes.join(', ')}`);
    }

    // 检查服务区域
    if (!data.areaServed || !Array.isArray(data.areaServed) || data.areaServed.length === 0) {
      result.warnings.push('areaServed should be a non-empty array');
    } else {
      result.score += 5;
    }

    return result;
  }

  /**
   * 验证分析报告结构化数据
   */
  validateAnalysisReportSchema(data: AnalysisReportSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'author', 'datePublished'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof AnalysisReportSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 验证日期格式
    if (this.options.validateDates && data.datePublished) {
      if (!this.isValidISODate(data.datePublished)) {
        result.errors.push('datePublished should be in ISO 8601 format');
        result.isValid = false;
      }
    }

    if (this.options.validateDates && data.dateModified) {
      if (!this.isValidISODate(data.dateModified)) {
        result.warnings.push('dateModified should be in ISO 8601 format');
      }
    }

    // 检查关键词
    if (!data.keywords || !Array.isArray(data.keywords) || data.keywords.length === 0) {
      result.warnings.push('keywords should be a non-empty array');
    } else {
      result.score += 5;
    }

    return result;
  }

  /**
   * 验证股票分析结构化数据
   */
  validateStockAnalysisSchema(data: StockAnalysisSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['headline', 'description', 'author', 'datePublished', 'about'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof StockAnalysisSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 12;
      }
    });

    // 检查分析类型
    const validAnalysisTypes = ['Technical', 'Fundamental', 'Sentiment', 'Risk'];
    if (data.analysisType && !validAnalysisTypes.includes(data.analysisType)) {
      result.warnings.push(`analysisType should be one of: ${validAnalysisTypes.join(', ')}`);
    } else if (data.analysisType) {
      result.score += 5;
    }

    // 检查推荐类型
    if (data.recommendation) {
      const validRecommendations = ['Buy', 'Sell', 'Hold'];
      if (!validRecommendations.includes(data.recommendation)) {
        result.warnings.push(`recommendation should be one of: ${validRecommendations.join(', ')}`);
      } else {
        result.score += 5;
      }
    }

    // 检查股票信息
    if (data.about && typeof data.about === 'object') {
      if (!data.about.tickerSymbol) {
        result.warnings.push('about.tickerSymbol is recommended for stock analysis');
      } else {
        result.score += 5;
      }
    }

    return result;
  }

  /**
   * 验证AI智能体结构化数据
   */
  validateAIAgentSchema(data: AIAgentSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    // 检查必需字段
    const requiredFields = ['name', 'description', 'creator', 'applicationCategory'];
    requiredFields.forEach((field) => {
      if (!data[field as keyof AIAgentSchema]) {
        result.errors.push(`Missing required field: ${field}`);
        result.isValid = false;
      } else {
        result.score += 15;
      }
    });

    // 检查应用类别
    const validCategories = ['AI Assistant', 'Financial Analyst', 'Trading Bot'];
    if (data.applicationCategory && !validCategories.includes(data.applicationCategory)) {
      result.warnings.push(`applicationCategory should be one of: ${validCategories.join(', ')}`);
    }

    // 检查能力列表
    if (!data.capabilities || !Array.isArray(data.capabilities) || data.capabilities.length === 0) {
      result.warnings.push('capabilities should be a non-empty array');
    } else {
      result.score += 10;
    }

    return result;
  }

  /**
   * 验证面包屑导航结构化数据
   */
  validateBreadcrumbSchema(data: BreadcrumbSchema): ValidationResult {
    const result = this.validateBasicStructure(data);

    if (!data.itemListElement || !Array.isArray(data.itemListElement)) {
      result.errors.push('itemListElement should be an array');
      result.isValid = false;
      return result;
    }

    if (data.itemListElement.length === 0) {
      result.warnings.push('itemListElement should not be empty');
    }

    // 检查每个面包屑项目
    data.itemListElement.forEach((item, index) => {
      if (!item['@type'] || item['@type'] !== 'ListItem') {
        result.errors.push(`Item ${index + 1}: @type should be "ListItem"`);
        result.isValid = false;
      }

      if (!item.position || typeof item.position !== 'number') {
        result.errors.push(`Item ${index + 1}: position should be a number`);
        result.isValid = false;
      }

      if (!item.name) {
        result.errors.push(`Item ${index + 1}: name is required`);
        result.isValid = false;
      }

      if (!item.item) {
        result.errors.push(`Item ${index + 1}: item URL is required`);
        result.isValid = false;
      } else if (this.options.checkUrls && !this.isValidUrl(item.item)) {
        result.warnings.push(`Item ${index + 1}: invalid URL format`);
      }
    });

    if (result.isValid) {
      result.score += 40;
    }

    return result;
  }

  /**
   * 验证完整的结构化数据对象
   */
  validateStructuredDataObject(data: any, type?: string): ValidationResult {
    if (!data || typeof data !== 'object') {
      return {
        isValid: false,
        errors: ['Data should be a valid object'],
        warnings: [],
        score: 0,
      };
    }

    const dataType = type || data['@type'];

    switch (dataType) {
      case 'Organization':
        return this.validateOrganizationSchema(data);
      case 'WebSite':
        return this.validateWebsiteSchema(data);
      case 'SoftwareApplication':
        return this.validateSoftwareApplicationSchema(data);
      case 'FinancialProduct':
        return this.validateFinancialServiceSchema(data);
      case 'Service':
        return this.validateInvestmentServiceSchema(data);
      case 'Report':
        return this.validateAnalysisReportSchema(data);
      case 'AnalysisNewsArticle':
        return this.validateStockAnalysisSchema(data);
      case 'SoftwareAgent':
        return this.validateAIAgentSchema(data);
      case 'BreadcrumbList':
        return this.validateBreadcrumbSchema(data);
      default:
        return this.validateBasicStructure(data);
    }
  }

  /**
   * 批量验证多个结构化数据对象
   */
  validateMultipleStructuredData(dataArray: any[]): ValidationResult {
    const results = dataArray.map((data) => this.validateStructuredDataObject(data));

    const combinedResult: ValidationResult = {
      isValid: results.every((r) => r.isValid),
      errors: results.flatMap((r) => r.errors),
      warnings: results.flatMap((r) => r.warnings),
      score: Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length),
    };

    return combinedResult;
  }

  /**
   * 检查URL是否有效
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检查日期是否为有效的ISO 8601格式
   */
  private isValidISODate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) && dateString.includes('T');
  }
}

/**
 * 便捷函数：验证结构化数据
 */
export const validateStructuredData = (
  data: any,
  options?: ValidationOptions
): ValidationResult => {
  const validator = new StructuredDataValidator(options);
  return validator.validateStructuredDataObject(data);
};

/**
 * 便捷函数：批量验证结构化数据
 */
export const validateMultipleStructuredData = (
  dataArray: any[],
  options?: ValidationOptions
): ValidationResult => {
  const validator = new StructuredDataValidator(options);
  return validator.validateMultipleStructuredData(dataArray);
};

/**
 * 便捷函数：生成验证报告
 */
export const generateValidationReport = (result: ValidationResult): string => {
  const lines: string[] = [];

  lines.push(`=== 结构化数据验证报告 ===`);
  lines.push(`验证状态: ${result.isValid ? '✅ 通过' : '❌ 失败'}`);
  lines.push(`评分: ${result.score}/100`);
  lines.push('');

  if (result.errors.length > 0) {
    lines.push('🚨 错误:');
    result.errors.forEach((error) => lines.push(`  - ${error}`));
    lines.push('');
  }

  if (result.warnings.length > 0) {
    lines.push('⚠️ 警告:');
    result.warnings.forEach((warning) => lines.push(`  - ${warning}`));
    lines.push('');
  }

  if (result.isValid && result.errors.length === 0 && result.warnings.length === 0) {
    lines.push('🎉 所有检查都通过了！');
  }

  return lines.join('\n');
};
