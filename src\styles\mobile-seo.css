/**
 * 移动端 SEO 优化样式
 * 确保移动端的最佳用户体验和 SEO 表现
 */

/* 基础移动端优化 */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

/* 触摸优化 */
button,
a,
input[type="button"],
input[type="submit"],
[role="button"] {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
  cursor: pointer;
}

/* 防止 iOS Safari 缩放 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
  font-size: 16px;
  -webkit-appearance: none;
  border-radius: 0;
}

/* 滚动优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 响应式视频 */
video {
  max-width: 100%;
  height: auto;
}

/* 移动端导航优化 */
@media (max-width: 768px) {
  /* 确保导航元素足够大 */
  nav a,
  nav button {
    padding: 12px 16px;
    min-height: 48px;
  }

  /* 优化表单元素 */
  input,
  textarea,
  select {
    padding: 12px;
    margin: 8px 0;
  }

  /* 优化按钮 */
  button {
    padding: 12px 24px;
    font-size: 16px;
  }

  /* 优化文本可读性 */
  body {
    font-size: 16px;
    line-height: 1.5;
  }

  h1 { font-size: 28px; }
  h2 { font-size: 24px; }
  h3 { font-size: 20px; }
  h4 { font-size: 18px; }
  h5 { font-size: 16px; }
  h6 { font-size: 14px; }

  /* 优化间距 */
  .container {
    padding: 16px;
  }

  /* 优化卡片布局 */
  .card {
    margin: 8px 0;
    padding: 16px;
  }

  /* 优化列表 */
  ul, ol {
    padding-left: 20px;
  }

  li {
    margin: 8px 0;
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  /* 更紧凑的布局 */
  .container {
    padding: 12px;
  }

  /* 更小的字体 */
  h1 { font-size: 24px; }
  h2 { font-size: 20px; }
  h3 { font-size: 18px; }

  /* 优化表格 */
  table {
    font-size: 14px;
  }

  /* 隐藏非关键内容 */
  .hide-on-mobile {
    display: none;
  }
}

/* 横屏优化 */
@media (max-height: 500px) and (orientation: landscape) {
  /* 减少垂直间距 */
  .container {
    padding: 8px 16px;
  }

  h1, h2, h3 {
    margin: 8px 0;
  }
}

/* 高 DPI 屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 优化图标和小图片 */
  .icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 深色模式移动端优化 */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  /* 确保深色模式下的对比度 */
  body {
    background-color: #000;
    color: #fff;
  }

  input,
  textarea,
  select {
    background-color: #1a1a1a;
    color: #fff;
    border: 1px solid #333;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  button,
  a {
    border: 2px solid;
  }

  input,
  textarea,
  select {
    border: 2px solid;
  }
}

/* 打印样式优化 */
@media print {
  /* 隐藏导航和交互元素 */
  nav,
  button,
  .no-print {
    display: none;
  }

  /* 优化文本 */
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
  }

  /* 优化链接 */
  a::after {
    content: " (" attr(href) ")";
    font-size: 10pt;
    color: #666;
  }
}

/* 可访问性优化 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 焦点指示器 */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* 移动端特定的性能优化 */
@media (max-width: 768px) {
  /* 减少阴影和渐变以提高性能 */
  .performance-optimized {
    box-shadow: none;
    background-image: none;
  }

  /* 简化动画 */
  .animate {
    animation-duration: 0.2s;
  }

  /* 优化字体渲染 */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeSpeed;
  }
}

/* 触摸设备特定样式 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果 */
  button:hover,
  a:hover {
    background-color: initial;
    color: initial;
  }

  /* 优化触摸反馈 */
  button:active,
  a:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}