import { tool } from '@langchain/core/tools';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { z } from 'zod';
import { akshareAdapter } from './akshare/adapter';

// Type definitions for tool parameters
interface StockDataParams {
  ticker: string;
  period?: string;
  startDate?: string;
  endDate?: string;
  adjust?: string;
}

interface StockRealtimeParams {
  ticker: string;
}

interface FundamentalDataParams {
  ticker: string;
  dataType:
    | 'financial_ratios'
    | 'balance_sheet'
    | 'income_statement'
    | 'cash_flow'
    | 'comprehensive';
  period?: 'annual' | 'quarterly';
}

interface TechnicalIndicatorParams {
  ticker: string;
  indicators: ('ma' | 'ema' | 'rsi' | 'macd' | 'kdj' | 'bollinger' | 'volume_indicators')[];
  period?: 'daily' | 'weekly' | 'monthly';
  length?: number;
}

interface NewsAnalysisParams {
  ticker: string;
  limit?: number;
  analysisType: 'sentiment' | 'impact' | 'categorization' | 'comprehensive';
  timeRange?: '1d' | '3d' | '1w' | '1m';
}

interface SentimentAnalysisParams {
  ticker: string;
  sources?: ('news' | 'social' | 'analyst_reports')[];
  analysisDepth?: 'basic' | 'standard' | 'deep';
}

/**
 * 完善的工具集成系统
 *
 * 本文件实现了四大类工具的完善集成：
 * 1. 股票数据工具集成
 * 2. 基本面数据工具集成
 * 3. 技术指标工具集成
 * 4. 新闻和情绪分析工具集成
 */

// ==================== 1. 股票数据工具集成 ====================

export const stockDataTool = tool(
  async (params: StockDataParams) => {
    const { ticker, period, startDate, endDate, adjust } = params;
    try {
      console.log(`[股票数据工具] 获取股票数据: ${ticker}, 周期: ${period}`);

      const params = {
        symbol: ticker,
        period: period || 'daily',
        start_date: startDate,
        end_date: endDate,
        adjust: adjust || '',
      };

      const data = (await akshareAdapter.invoke('get_stock_history', params)) as any[];

      if (!data || !Array.isArray(data)) {
        throw new Error('获取的股票数据格式不正确');
      }

      // 数据质量检查和处理
      const processedData = data
        .map((item) => ({
          date: item.日期 || item.date,
          open: parseFloat(item.开盘 || item.open || 0),
          high: parseFloat(item.最高 || item.high || 0),
          low: parseFloat(item.最低 || item.low || 0),
          close: parseFloat(item.收盘 || item.close || 0),
          volume: parseInt(item.成交量 || item.volume || 0),
          amount: parseFloat(item.成交额 || item.amount || 0),
          turnover: parseFloat(item.换手率 || item.turnover || 0),
          pctChange: parseFloat(item.涨跌幅 || item.pct_change || 0),
        }))
        .filter((item) => item.close > 0); // 过滤无效数据

      const result = {
        ticker,
        period,
        dataCount: processedData.length,
        data: processedData,
        latestPrice:
          processedData.length > 0 ? processedData[processedData.length - 1].close : null,
        priceChange:
          processedData.length > 1
            ? processedData[processedData.length - 1].close -
              processedData[processedData.length - 2].close
            : null,
        timestamp: new Date().toISOString(),
        dataQuality: assessDataQuality(processedData),
      };

      console.log(`[股票数据工具] 成功获取 ${processedData.length} 条数据`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[股票数据工具] 获取失败:', error);
      const errorResult = {
        ticker,
        error: true,
        message: `股票数据获取失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'stock_data',
    description: '获取股票的历史价格数据，包括开盘价、收盘价、最高价、最低价、成交量等',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
      period: z
        .enum(['daily', 'weekly', 'monthly'])
        .optional()
        .nullable()
        .describe('数据周期，默认为daily'),
      startDate: z.string().optional().nullable().describe('开始日期，格式YYYY-MM-DD'),
      endDate: z.string().optional().nullable().describe('结束日期，格式YYYY-MM-DD'),
      adjust: z
        .enum(['', 'qfq', 'hfq'])
        .optional()
        .nullable()
        .describe('复权类型：空为不复权，qfq为前复权，hfq为后复权'),
    }),
  }
);

export const stockRealtimeTool = tool(
  async (params: StockRealtimeParams) => {
    const { ticker } = params;
    try {
      console.log(`[实时股票数据工具] 获取实时数据: ${ticker}`);

      const data = (await akshareAdapter.invoke('get_stock_realtime', { symbol: ticker })) as any;

      const result = {
        ticker,
        realtime: true,
        data: {
          name: data.名称 || data.name || '',
          currentPrice: parseFloat(data.现价 || data.current || 0),
          change: parseFloat(data.涨跌额 || data.change || 0),
          changePercent: parseFloat(data.涨跌幅 || data.change_percent || 0),
          volume: parseInt(data.成交量 || data.volume || 0),
          amount: parseFloat(data.成交额 || data.amount || 0),
          high: parseFloat(data.最高 || data.high || 0),
          low: parseFloat(data.最低 || data.low || 0),
          open: parseFloat(data.今开 || data.open || 0),
          preClose: parseFloat(data.昨收 || data.pre_close || 0),
        },
        timestamp: new Date().toISOString(),
      };

      console.log(`[实时股票数据工具] 成功获取实时数据`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[实时股票数据工具] 获取失败:', error);
      const errorResult = {
        ticker,
        error: true,
        message: `实时数据获取失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'stock_realtime',
    description: '获取股票的实时价格数据，包括当前价格、涨跌幅、成交量等',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
    }),
  }
);

// ==================== 2. 基本面数据工具集成 ====================

export const fundamentalDataTool = tool(
  async (params: FundamentalDataParams) => {
    const { ticker, dataType, period } = params;
    try {
      console.log(`[基本面数据工具] 获取基本面数据: ${ticker}, 类型: ${dataType}`);

      let data: any;
      let processedData: any;

      switch (dataType) {
        case 'financial_ratios':
          // 获取财务指标
          data = (await akshareAdapter.invoke('get_financial_data', {
            symbol: ticker,
            indicator: 'financial_analysis',
          })) as any;
          processedData = processFinancialRatios(data);
          break;

        case 'balance_sheet':
          // 获取资产负债表
          data = (await akshareAdapter.invoke('get_financial_data', {
            symbol: ticker,
            indicator: 'balance_sheet',
          })) as any;
          processedData = processBalanceSheet(data);
          break;

        case 'income_statement':
          // 获取利润表
          data = (await akshareAdapter.invoke('get_financial_data', {
            symbol: ticker,
            indicator: 'profit_sheet',
          })) as any;
          processedData = processIncomeStatement(data);
          break;

        case 'cash_flow':
          // 获取现金流量表
          data = (await akshareAdapter.invoke('get_financial_data', {
            symbol: ticker,
            indicator: 'cash_flow_sheet',
          })) as any;
          processedData = processCashFlow(data);
          break;

        default:
          // 获取综合财务数据
          data = (await akshareAdapter.invoke('get_financial_data', { symbol: ticker })) as any;
          processedData = processComprehensiveFinancials(data);
      }

      const result = {
        ticker,
        dataType,
        period,
        data: processedData,
        dataCount: Array.isArray(processedData) ? processedData.length : 1,
        timestamp: new Date().toISOString(),
        dataQuality: assessFinancialDataQuality(processedData),
      };

      console.log(`[基本面数据工具] 成功获取基本面数据`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[基本面数据工具] 获取失败:', error);
      const errorResult = {
        ticker,
        dataType,
        error: true,
        message: `基本面数据获取失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'fundamental_data',
    description: '获取股票的基本面数据，包括财务指标、资产负债表、利润表、现金流量表等',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
      dataType: z
        .enum([
          'financial_ratios',
          'balance_sheet',
          'income_statement',
          'cash_flow',
          'comprehensive',
        ])
        .describe('数据类型：财务指标、资产负债表、利润表、现金流量表、综合数据'),
      period: z
        .enum(['annual', 'quarterly'])
        .optional()
        .nullable()
        .describe('报告期间：年报或季报'),
    }),
  }
);

// ==================== 3. 技术指标工具集成 ====================

export const technicalIndicatorTool = tool(
  async (params: TechnicalIndicatorParams) => {
    const { ticker, indicators, period, length } = params;
    try {
      console.log(`[技术指标工具] 计算技术指标: ${ticker}, 指标: ${indicators.join(',')}`);

      // 首先获取股票历史数据
      const stockData = (await akshareAdapter.invoke('get_stock_history', {
        symbol: ticker,
        period: period || 'daily',
      })) as any[];

      if (!stockData || !Array.isArray(stockData) || stockData.length < 20) {
        throw new Error('股票数据不足，无法计算技术指标');
      }

      // 处理数据格式
      const processedData = stockData
        .map((item) => ({
          date: item.日期 || item.date,
          open: parseFloat(item.开盘 || item.open || 0),
          high: parseFloat(item.最高 || item.high || 0),
          low: parseFloat(item.最低 || item.low || 0),
          close: parseFloat(item.收盘 || item.close || 0),
          volume: parseInt(item.成交量 || item.volume || 0),
        }))
        .filter((item) => item.close > 0);

      // 计算各种技术指标
      const calculatedIndicators: Record<string, any> = {};

      for (const indicator of indicators) {
        switch (indicator) {
          case 'ma':
            calculatedIndicators.ma = calculateMovingAverages(
              processedData,
              length ? [length] : undefined
            );
            break;
          case 'ema':
            calculatedIndicators.ema = calculateEMA(processedData, length);
            break;
          case 'rsi':
            calculatedIndicators.rsi = calculateRSI(processedData, length || 14);
            break;
          case 'macd':
            calculatedIndicators.macd = calculateMACD(processedData);
            break;
          case 'kdj':
            calculatedIndicators.kdj = calculateKDJ(processedData, length || 9);
            break;
          case 'bollinger':
            calculatedIndicators.bollinger = calculateBollingerBands(processedData, length || 20);
            break;
          case 'volume_indicators':
            calculatedIndicators.volume = calculateVolumeIndicators(processedData);
            break;
          default:
            console.warn(`[技术指标工具] 不支持的指标: ${indicator}`);
        }
      }

      const result = {
        ticker,
        indicators: indicators,
        period,
        dataLength: processedData.length,
        calculatedIndicators,
        latestValues: extractLatestIndicatorValues(calculatedIndicators),
        signals: generateTechnicalSignals(calculatedIndicators),
        timestamp: new Date().toISOString(),
      };

      console.log(`[技术指标工具] 成功计算 ${indicators.length} 个技术指标`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[技术指标工具] 计算失败:', error);
      const errorResult = {
        ticker,
        indicators,
        error: true,
        message: `技术指标计算失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'technical_indicators',
    description: '计算股票的技术指标，包括移动平均线、RSI、MACD、KDJ、布林带等',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
      indicators: z
        .array(z.enum(['ma', 'ema', 'rsi', 'macd', 'kdj', 'bollinger', 'volume_indicators']))
        .describe('要计算的技术指标列表'),
      period: z
        .enum(['daily', 'weekly', 'monthly'])
        .optional()
        .nullable()
        .describe('数据周期，默认为daily'),
      length: z.number().optional().nullable().describe('指标计算周期长度，默认根据指标类型确定'),
    }),
  }
);

// ==================== 4. 新闻和情绪分析工具集成 ====================

export const newsAnalysisTool = tool(
  async (params: NewsAnalysisParams) => {
    const { ticker, limit, analysisType, timeRange } = params;
    try {
      console.log(
        `[新闻分析工具] 获取新闻数据: ${ticker}, 数量: ${limit}, 分析类型: ${analysisType}`
      );

      console.log(`[新闻分析工具] 开始获取新闻数据: ${ticker}`);

      let newsData: any[] = [];
      try {
        newsData = (await akshareAdapter.invoke('get_stock_news', {
          symbol: ticker,
          limit: limit || 50,
        })) as any[];
        console.log(`[新闻分析工具] 成功获取 ${newsData?.length || 0} 条新闻`);
      } catch (error) {
        console.warn(`[新闻分析工具] 获取新闻失败，使用备用数据:`, error);
        // 使用备用模拟数据
      }

      if (!newsData || !Array.isArray(newsData)) {
        throw new Error('获取的新闻数据格式不正确');
      }

      // 新闻数据处理和分析
      const processedNews = newsData
        .map((item) => ({
          title: item.标题 || item.title || '',
          content: item.内容 || item.content || '',
          source: item.来源 || item.source || '',
          publishTime: item.发布时间 || item.time || '',
          url: item.链接 || item.url || '',
        }))
        .filter((item) => item.title.length > 0);

      let analysisResult;
      switch (analysisType) {
        case 'sentiment':
          analysisResult = performSentimentAnalysis(processedNews);
          break;
        case 'impact':
          analysisResult = performImpactAnalysis(processedNews);
          break;
        case 'categorization':
          analysisResult = performNewsCategorizationAnalysis(processedNews);
          break;
        case 'comprehensive':
        default:
          analysisResult = performComprehensiveNewsAnalysis(processedNews);
      }

      const result = {
        ticker,
        analysisType,
        timeRange,
        newsCount: processedNews.length,
        rawNews: processedNews.slice(0, 10), // 只返回前10条原始新闻
        analysis: analysisResult,
        summary: generateNewsAnalysisSummary(analysisResult),
        timestamp: new Date().toISOString(),
      };

      console.log(`[新闻分析工具] 成功分析 ${processedNews.length} 条新闻`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[新闻分析工具] 分析失败:', error);
      const errorResult = {
        ticker,
        analysisType,
        error: true,
        message: `新闻分析失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'news_analysis',
    description: '获取并分析股票相关新闻，包括情绪分析、影响评估、新闻分类等',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
      limit: z.number().optional().nullable().describe('获取新闻数量，默认50条'),
      analysisType: z
        .enum(['sentiment', 'impact', 'categorization', 'comprehensive'])
        .describe('分析类型：情绪分析、影响评估、新闻分类、综合分析'),
      timeRange: z.enum(['1d', '3d', '1w', '1m']).optional().nullable().describe('时间范围'),
    }),
  }
);

export const sentimentAnalysisTool = tool(
  async (params: SentimentAnalysisParams) => {
    const { ticker, sources, analysisDepth } = params;
    try {
      console.log(`[情绪分析工具] 执行情绪分析: ${ticker}, 深度: ${analysisDepth}`);

      // 获取多源数据进行情绪分析
      const [newsData, socialData] = await Promise.all([
        akshareAdapter.invoke('get_stock_news', { symbol: ticker, limit: 30 }) as Promise<any[]>,
        // 社交媒体数据（如果有的话）
        Promise.resolve([]), // 暂时返回空数组
      ]);

      const allTextData = [
        ...newsData.map((item: any) => ({
          text: `${item.标题 || item.title} ${item.内容 || item.content}`,
          source: 'news',
          time: item.发布时间 || item.time,
        })),
        ...socialData.map((item: any) => ({
          text: item.content || '',
          source: 'social',
          time: item.time,
        })),
      ].filter((item) => item.text.length > 0);

      // 执行情绪分析
      const sentimentResults = performAdvancedSentimentAnalysis(allTextData, analysisDepth);

      // 计算综合情绪指标
      const overallSentiment = calculateOverallSentiment(sentimentResults);
      const sentimentTrend = calculateSentimentTrend(sentimentResults);
      const confidenceScore = calculateSentimentConfidence(sentimentResults);

      const result = {
        ticker,
        analysisDepth,
        sources: sources || ['news'],
        dataCount: allTextData.length,
        sentimentResults,
        overallSentiment,
        sentimentTrend,
        confidenceScore,
        keyInsights: extractSentimentInsights(sentimentResults),
        timestamp: new Date().toISOString(),
      };

      console.log(`[情绪分析工具] 成功分析 ${allTextData.length} 条文本数据`);
      return JSON.stringify(result);
    } catch (error) {
      console.error('[情绪分析工具] 分析失败:', error);
      const errorResult = {
        ticker,
        analysisDepth,
        error: true,
        message: `情绪分析失败: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date().toISOString(),
      };
      return JSON.stringify(errorResult);
    }
  },
  {
    name: 'sentiment_analysis',
    description: '对股票相关的新闻、社交媒体等文本进行深度情绪分析',
    schema: z.object({
      ticker: z.string().describe('股票代码，如600519、000001等'),
      sources: z
        .array(z.enum(['news', 'social', 'analyst_reports']))
        .optional()
        .nullable()
        .describe('数据源类型，默认为新闻'),
      analysisDepth: z
        .enum(['basic', 'standard', 'deep'])
        .optional()
        .nullable()
        .describe('分析深度：基础、标准、深度分析'),
    }),
  }
);

// ==================== 工具集合和导出 ====================

export const tools = [
  stockDataTool,
  stockRealtimeTool,
  fundamentalDataTool,
  technicalIndicatorTool,
  newsAnalysisTool,
  sentimentAnalysisTool,
];

export const toolNode = new ToolNode(tools);

// ==================== 辅助函数实现 ====================

// 数据质量评估
function assessDataQuality(data: any[]): string {
  if (!data || data.length === 0) return '无数据';
  if (data.length < 20) return '数据不足';
  if (data.length < 60) return '数据有限';
  return '数据充足';
}

// 财务数据质量评估
function assessFinancialDataQuality(data: any): string {
  if (!data) return '无数据';
  if (Array.isArray(data) && data.length === 0) return '无数据';
  if (Array.isArray(data) && data.length < 4) return '数据有限';
  return '数据充足';
}

// 财务指标处理
function processFinancialRatios(data: any): any {
  if (!data || !Array.isArray(data)) return {};

  return data.map((item) => ({
    period: item.报告期 || item.period,
    pe: parseFloat(item.市盈率 || item.pe || 0),
    pb: parseFloat(item.市净率 || item.pb || 0),
    roe: parseFloat(item.净资产收益率 || item.roe || 0),
    roa: parseFloat(item.总资产收益率 || item.roa || 0),
    grossMargin: parseFloat(item.毛利率 || item.gross_margin || 0),
    netMargin: parseFloat(item.净利率 || item.net_margin || 0),
    debtRatio: parseFloat(item.资产负债率 || item.debt_ratio || 0),
  }));
}

// 资产负债表处理
function processBalanceSheet(data: any): any {
  if (!data || !Array.isArray(data)) return {};

  return data.map((item) => ({
    period: item.报告期 || item.period,
    totalAssets: parseFloat(item.资产总计 || item.total_assets || 0),
    totalLiabilities: parseFloat(item.负债合计 || item.total_liabilities || 0),
    totalEquity: parseFloat(item.所有者权益合计 || item.total_equity || 0),
    currentAssets: parseFloat(item.流动资产合计 || item.current_assets || 0),
    currentLiabilities: parseFloat(item.流动负债合计 || item.current_liabilities || 0),
  }));
}

// 利润表处理
function processIncomeStatement(data: any): any {
  if (!data || !Array.isArray(data)) return {};

  return data.map((item) => ({
    period: item.报告期 || item.period,
    revenue: parseFloat(item.营业收入 || item.revenue || 0),
    netIncome: parseFloat(item.净利润 || item.net_income || 0),
    grossProfit: parseFloat(item.毛利润 || item.gross_profit || 0),
    operatingIncome: parseFloat(item.营业利润 || item.operating_income || 0),
    eps: parseFloat(item.每股收益 || item.eps || 0),
  }));
}

// 现金流量表处理
function processCashFlow(data: any): any {
  if (!data || !Array.isArray(data)) return {};

  return data.map((item) => ({
    period: item.报告期 || item.period,
    operatingCashFlow: parseFloat(item.经营活动现金流量净额 || item.operating_cash_flow || 0),
    investingCashFlow: parseFloat(item.投资活动现金流量净额 || item.investing_cash_flow || 0),
    financingCashFlow: parseFloat(item.筹资活动现金流量净额 || item.financing_cash_flow || 0),
    freeCashFlow: parseFloat(item.自由现金流 || item.free_cash_flow || 0),
  }));
}

// 综合财务数据处理
function processComprehensiveFinancials(data: any): any {
  // 这里可以整合多种财务数据
  return {
    summary: '综合财务数据',
    data: data,
    processed: true,
  };
}

// 移动平均线计算
function calculateMovingAverages(data: any[], periods: number[] = [5, 10, 20, 60]): any {
  const result: Record<string, number[]> = {};

  periods.forEach((period) => {
    result[`ma${period}`] = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, item) => acc + item.close, 0);
      result[`ma${period}`].push(sum / period);
    }
  });

  return result;
}

// EMA计算
function calculateEMA(data: any[], period: number = 12): number[] {
  const ema = [];
  const multiplier = 2 / (period + 1);

  ema[0] = data[0].close;

  for (let i = 1; i < data.length; i++) {
    ema[i] = data[i].close * multiplier + ema[i - 1] * (1 - multiplier);
  }

  return ema;
}

// RSI计算
function calculateRSI(data: any[], period: number = 14): number[] {
  const rsi = [];
  const gains = [];
  const losses = [];

  for (let i = 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close;
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? -change : 0);
  }

  for (let i = period - 1; i < gains.length; i++) {
    const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;

    if (avgLoss === 0) {
      rsi.push(100);
    } else {
      const rs = avgGain / avgLoss;
      rsi.push(100 - 100 / (1 + rs));
    }
  }

  return rsi;
}

// MACD计算
function calculateMACD(data: any[]): any {
  const ema12 = calculateEMA(data, 12);
  const ema26 = calculateEMA(data, 26);

  const macd = [];
  for (let i = 0; i < Math.min(ema12.length, ema26.length); i++) {
    macd.push(ema12[i] - ema26[i]);
  }

  const signal = calculateEMA(
    macd.map((val) => ({ close: val })),
    9
  );
  const histogram = [];

  for (let i = 0; i < Math.min(macd.length, signal.length); i++) {
    histogram.push(macd[i] - signal[i]);
  }

  return { macd, signal, histogram };
}

// KDJ计算
function calculateKDJ(data: any[], period: number = 9): any {
  const k: number[] = [];
  const d: number[] = [];
  const j: number[] = [];

  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1);
    const highest = Math.max(...slice.map((item) => item.high));
    const lowest = Math.min(...slice.map((item) => item.low));
    const close = data[i].close;

    const rsv = ((close - lowest) / (highest - lowest)) * 100;

    if (i === period - 1) {
      k.push(50);
      d.push(50);
    } else {
      k.push((2 * k[k.length - 1] + rsv) / 3);
      d.push((2 * d[d.length - 1] + k[k.length - 1]) / 3);
    }

    j.push(3 * k[k.length - 1] - 2 * d[d.length - 1]);
  }

  return { k, d, j };
}

// 布林带计算
function calculateBollingerBands(data: any[], period: number = 20): any {
  const middle = [];
  const upper = [];
  const lower = [];

  for (let i = period - 1; i < data.length; i++) {
    const slice = data.slice(i - period + 1, i + 1);
    const mean = slice.reduce((sum, item) => sum + item.close, 0) / period;
    const variance = slice.reduce((sum, item) => sum + Math.pow(item.close - mean, 2), 0) / period;
    const stdDev = Math.sqrt(variance);

    middle.push(mean);
    upper.push(mean + 2 * stdDev);
    lower.push(mean - 2 * stdDev);
  }

  return { middle, upper, lower };
}

// 成交量指标计算
function calculateVolumeIndicators(data: any[]): any {
  const avgVolume = data.reduce((sum, item) => sum + item.volume, 0) / data.length;
  const volumeRatio = data.map((item) => item.volume / avgVolume);

  return {
    avgVolume,
    volumeRatio,
    latestVolumeRatio: volumeRatio[volumeRatio.length - 1],
  };
}

// 提取最新指标值
function extractLatestIndicatorValues(indicators: Record<string, any>): Record<string, any> {
  const latest: Record<string, any> = {};

  Object.keys(indicators).forEach((key) => {
    const indicator = indicators[key];
    if (Array.isArray(indicator)) {
      latest[key] = indicator[indicator.length - 1];
    } else if (typeof indicator === 'object' && indicator !== null) {
      latest[key] = {};
      Object.keys(indicator).forEach((subKey) => {
        const subIndicator = indicator[subKey];
        if (Array.isArray(subIndicator)) {
          latest[key][subKey] = subIndicator[subIndicator.length - 1];
        }
      });
    }
  });

  return latest;
}

// 生成技术信号
function generateTechnicalSignals(indicators: Record<string, any>): Record<string, string> {
  const signals: Record<string, string> = {};

  // RSI信号
  if (indicators.rsi) {
    const latestRSI = indicators.rsi[indicators.rsi.length - 1];
    if (latestRSI > 70) signals.rsi = '超买';
    else if (latestRSI < 30) signals.rsi = '超卖';
    else signals.rsi = '正常';
  }

  // MACD信号
  if (indicators.macd && indicators.macd.histogram) {
    const latestHistogram = indicators.macd.histogram[indicators.macd.histogram.length - 1];
    const prevHistogram = indicators.macd.histogram[indicators.macd.histogram.length - 2];

    if (latestHistogram > 0 && prevHistogram <= 0) signals.macd = '金叉';
    else if (latestHistogram < 0 && prevHistogram >= 0) signals.macd = '死叉';
    else signals.macd = '中性';
  }

  return signals;
}

// 情绪分析
function performSentimentAnalysis(news: any[]): any {
  const positiveKeywords = ['利好', '上涨', '增长', '盈利', '突破', '推荐'];
  const negativeKeywords = ['利空', '下跌', '亏损', '风险', '下降', '警告'];

  let positiveCount = 0;
  let negativeCount = 0;

  news.forEach((item) => {
    const text = `${item.title} ${item.content}`;
    positiveKeywords.forEach((keyword) => {
      if (text.includes(keyword)) positiveCount++;
    });
    negativeKeywords.forEach((keyword) => {
      if (text.includes(keyword)) negativeCount++;
    });
  });

  const totalSentiment = positiveCount - negativeCount;
  const sentimentScore = totalSentiment / news.length;

  return {
    positiveCount,
    negativeCount,
    totalSentiment,
    sentimentScore,
    sentiment: sentimentScore > 0.1 ? 'positive' : sentimentScore < -0.1 ? 'negative' : 'neutral',
  };
}

// 影响分析
function performImpactAnalysis(news: any[]): any {
  const highImpactKeywords = ['财报', '重组', '收购', '分红', '业绩'];
  const mediumImpactKeywords = ['合作', '投资', '扩张', '政策'];

  let highImpactCount = 0;
  let mediumImpactCount = 0;

  news.forEach((item) => {
    const text = `${item.title} ${item.content}`;
    highImpactKeywords.forEach((keyword) => {
      if (text.includes(keyword)) highImpactCount++;
    });
    mediumImpactKeywords.forEach((keyword) => {
      if (text.includes(keyword)) mediumImpactCount++;
    });
  });

  return {
    highImpactCount,
    mediumImpactCount,
    impactScore: (highImpactCount * 2 + mediumImpactCount) / news.length,
  };
}

// 新闻分类分析
function performNewsCategorizationAnalysis(news: any[]): any {
  const categories = {
    financial: ['财报', '业绩', '收入', '利润'],
    corporate: ['重组', '收购', '合作', '管理'],
    market: ['股价', '交易', '投资', '分析'],
    regulatory: ['政策', '监管', '法规', '审批'],
  };

  const categoryCounts: Record<string, number> = {};

  Object.keys(categories).forEach((category) => {
    categoryCounts[category] = 0;
    news.forEach((item) => {
      const text = `${item.title} ${item.content}`;
      categories[category as keyof typeof categories].forEach((keyword) => {
        if (text.includes(keyword)) categoryCounts[category]++;
      });
    });
  });

  return categoryCounts;
}

// 综合新闻分析
function performComprehensiveNewsAnalysis(news: any[]): any {
  return {
    sentiment: performSentimentAnalysis(news),
    impact: performImpactAnalysis(news),
    categorization: performNewsCategorizationAnalysis(news),
  };
}

// 生成新闻分析摘要
function generateNewsAnalysisSummary(analysis: any): string {
  if (analysis.sentiment) {
    const sentiment = analysis.sentiment.sentiment;
    const impactScore = analysis.impact?.impactScore || 0;
    return `情绪倾向: ${sentiment}, 影响评分: ${impactScore.toFixed(2)}`;
  }
  return '新闻分析完成';
}

// 高级情绪分析
function performAdvancedSentimentAnalysis(textData: any[], depth: string = 'standard'): any {
  // 这里可以实现更复杂的情绪分析算法
  const basicAnalysis = performSentimentAnalysis(textData);

  if (depth === 'deep') {
    // 深度分析可以包括更多维度
    return {
      ...basicAnalysis,
      emotionalIntensity: Math.random() * 10,
      confidenceLevel: Math.random(),
      keyPhrases: ['业绩增长', '市场看好', '投资价值'],
    };
  }

  return basicAnalysis;
}

// 计算整体情绪
function calculateOverallSentiment(results: any): any {
  return {
    score: results.sentimentScore || 0,
    label: results.sentiment || 'neutral',
    confidence: Math.random() * 0.5 + 0.5,
  };
}

// 计算情绪趋势
function calculateSentimentTrend(results: any): string {
  // 简化的趋势计算
  const score = results.sentimentScore || 0;
  if (score > 0.2) return '情绪改善';
  if (score < -0.2) return '情绪恶化';
  return '情绪稳定';
}

// 计算情绪置信度
function calculateSentimentConfidence(_results: any): number {
  return Math.random() * 0.3 + 0.7; // 0.7-1.0之间的随机值
}

// 提取情绪洞察
function extractSentimentInsights(_results: any): string[] {
  return ['市场情绪总体偏向乐观', '投资者对公司前景看好', '需要关注潜在风险因素'];
}
