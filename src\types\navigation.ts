/**
 * Navigation type definitions for analysis navigation entry feature
 * Supports breadcrumb navigation, dropdown menus, keyboard shortcuts, and analysis statistics
 */

// Core navigation context interface
export interface NavigationContext {
  currentPath: string;
  breadcrumbs: BreadcrumbItem[];
  analysisStats: AnalysisStats;
  recentAnalyses: AnalysisItem[];
  shortcuts: KeyboardShortcut[];
}

// Analysis statistics interface
export interface AnalysisStats {
  total: number;
  completed: number;
  running: number;
  failed: number;
  todayCount: number;
  weekCount: number;
  successRate: number;
  avgDuration: number;
}

// Navigation event tracking interface
export interface NavigationEvent {
  eventType: 'click' | 'keyboard' | 'touch';
  target: string;
  path: string;
  timestamp: Date;
  userId?: string;
  sessionId: string;
}

// Analysis item interface for recent analyses
export interface AnalysisItem {
  id: string;
  ticker: string;
  title: string;
  status: 'completed' | 'running' | 'failed';
  createdAt: string;
  updatedAt?: string;
}

// Keyboard shortcut configuration interface
export interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  enabled: boolean;
  category?: 'navigation' | 'analysis' | 'general';
}

// Breadcrumb navigation interface
export interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
}

// Dropdown menu interfaces
export interface DropdownMenuItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  description?: string;
  disabled?: boolean;
}

export interface AnalysisDropdownProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
  recentAnalyses?: AnalysisItem[];
  stats?: AnalysisStats;
}

// Navigation state management interfaces
export interface NavigationState {
  currentPath: string;
  isAnalysisPage: boolean;
  analysisStats: AnalysisStats | null;
  recentAnalyses: AnalysisItem[];
  loading: boolean;
  error?: string;
}

export interface UseNavigationStateReturn extends NavigationState {
  refreshStats: () => Promise<void>;
  navigateToAnalysis: (id: string) => void;
  navigateToHistory: () => void;
  navigateToCompare: (ids?: string[]) => void;
  clearError: () => void;
}

// Enhanced header component props
export interface EnhancedHeaderProps {
  currentPath?: string;
  analysisStats?: AnalysisStats;
  className?: string;
}

// Navigation metrics for analytics
export interface NavigationMetrics {
  clickCounts: Record<string, number>;
  pathTransitions: Record<string, string[]>;
  shortcutUsage: Record<string, number>;
  errorRates: Record<string, number>;
}

// Keyboard shortcut configuration constants
export const KEYBOARD_SHORTCUTS = {
  ANALYSIS_HISTORY: 'Alt+H',
  ANALYSIS_COMPARE: 'Alt+C',
  BACK_TO_TASKS: 'Alt+T',
  TOGGLE_DROPDOWN: 'Alt+A',
} as const;

// Navigation item interface for menu configuration
export interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  shortcut?: string;
  description?: string;
  children?: NavigationItem[];
  requiresAuth?: boolean;
}
