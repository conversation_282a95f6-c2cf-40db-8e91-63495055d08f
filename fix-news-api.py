#!/usr/bin/env python3
"""
修复新闻API的临时脚本
创建一个简单的新闻API服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
from datetime import datetime, timedelta
import hashlib
import uvicorn

app = FastAPI(title="临时新闻API服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class StockNewsRequest(BaseModel):
    symbol: str
    limit: int = 20
    use_cache: bool = True


def generate_mock_news(symbol: str, limit: int):
    """生成模拟新闻数据"""
    news_templates = [
        f"{symbol} 公司发布最新财报，业绩超预期",
        f"{symbol} 股价异动，市场关注度提升",
        f"分析师上调 {symbol} 目标价，看好后市表现",
        f"{symbol} 获得重要合同，业务拓展顺利",
        f"机构调研 {symbol}，关注公司发展战略",
        f"{symbol} 技术创新获得突破，竞争优势明显",
        f"监管政策利好 {symbol} 所在行业发展",
        f"{symbol} 管理层变动，新团队上任",
        f"市场传言 {symbol} 将有重大并购动作",
        f"{symbol} 分红方案公布，股东回报丰厚",
    ]

    mock_news = []
    for i in range(min(limit, len(news_templates))):
        news_id = hashlib.sha256(f"{symbol}_{i}_{datetime.now()}".encode()).hexdigest()[
            :16
        ]
        publish_time = datetime.now() - timedelta(hours=i * 2)

        news_item = {
            "news_id": news_id,
            "title": news_templates[i],
            "content": f"这是关于 {symbol} 的详细新闻内容。{news_templates[i]}。更多详情请关注后续报道。",
            "source": "财经新闻网",
            "publish_time": publish_time.strftime("%Y-%m-%d %H:%M:%S"),
            "url": f"https://finance.example.com/news/{news_id}",
            "related_tickers": json.dumps([symbol]),
        }
        mock_news.append(news_item)

    return mock_news


@app.get("/")
async def root():
    return {"service": "临时新闻API服务", "status": "运行中"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "temp-news-service"}


@app.post("/api/stock/news")
async def get_stock_news(request: StockNewsRequest):
    """获取股票新闻（模拟数据）"""
    try:
        print(f"获取 {request.symbol} 的新闻，数量: {request.limit}")

        mock_news = generate_mock_news(request.symbol, request.limit)

        return {
            "success": True,
            "data": mock_news,
            "count": len(mock_news),
            "source": "mock",
            "note": "这是模拟数据，用于测试目的",
        }

    except Exception as e:
        print(f"获取新闻失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取新闻失败: {str(e)}")


if __name__ == "__main__":
    print("启动临时新闻API服务...")
    uvicorn.run("fix-news-api:app", host="0.0.0.0", port=5001, reload=True)
