/**
 * Risk Data Storage Hooks
 * 风险数据存储与历史记录React Hooks
 */

import { RiskAssessment, SaveRiskAssessmentRequest } from '@/types/langgraph-database';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useState } from 'react';

// ============================================================================
// API 调用函数
// ============================================================================

/**
 * 保存风险评估结果
 */
async function saveRiskAssessment(data: SaveRiskAssessmentRequest): Promise<{ risk_id: string }> {
  const response = await fetch('/api/risk-data', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to save risk assessment');
  }

  return response.json();
}

/**
 * 获取工作流的风险评估结果
 */
async function fetchRiskAssessment(workflowId: string): Promise<RiskAssessment | null> {
  const response = await fetch(`/api/risk-data?workflow_id=${workflowId}`);

  if (!response.ok) {
    throw new Error('Failed to fetch risk assessment');
  }

  const result = await response.json();
  return result.data;
}

/**
 * 获取风险评估历史记录
 */
async function fetchRiskAssessmentHistory(params: {
  ticker?: string;
  risk_level?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}): Promise<{
  assessments: RiskAssessment[];
  total: number;
  pagination: any;
}> {
  const searchParams = new URLSearchParams();
  searchParams.set('history', 'true');

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.set(key, value.toString());
    }
  });

  const response = await fetch(`/api/risk-data?${searchParams.toString()}`);

  if (!response.ok) {
    throw new Error('Failed to fetch risk assessment history');
  }

  return response.json();
}

/**
 * 获取风险评估统计信息
 */
async function fetchRiskStatistics(params: {
  ticker?: string;
  date_from?: string;
  date_to?: string;
}): Promise<any> {
  const searchParams = new URLSearchParams();
  searchParams.set('statistics', 'true');

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.set(key, value.toString());
    }
  });

  const response = await fetch(`/api/risk-data?${searchParams.toString()}`);

  if (!response.ok) {
    throw new Error('Failed to fetch risk statistics');
  }

  const result = await response.json();
  return result.data;
}

/**
 * 比较风险评估结果
 */
async function compareRiskAssessments(workflowIds: string[]): Promise<any> {
  const response = await fetch('/api/risk-data/compare', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ workflow_ids: workflowIds }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to compare risk assessments');
  }

  const result = await response.json();
  return result.data;
}

/**
 * 获取风险指标趋势
 */
async function fetchRiskMetricsTrend(params: {
  ticker: string;
  metric: string;
  days?: number;
}): Promise<any> {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.set(key, value.toString());
    }
  });

  const response = await fetch(`/api/risk-data/metrics?${searchParams.toString()}`);

  if (!response.ok) {
    throw new Error('Failed to fetch risk metrics trend');
  }

  const result = await response.json();
  return result.data;
}

// ============================================================================
// React Hooks
// ============================================================================

/**
 * 风险评估保存Hook
 * 需求 9.1: 当风险分析完成时，系统应将结果存储到数据库
 */
export function useRiskAssessmentSave() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saveRiskAssessment,
    onSuccess: (data, variables) => {
      // 更新相关查询缓存
      queryClient.invalidateQueries({
        queryKey: ['risk-assessment', variables.workflow_id],
      });
      queryClient.invalidateQueries({
        queryKey: ['risk-history'],
      });
      queryClient.invalidateQueries({
        queryKey: ['risk-statistics'],
      });
    },
  });
}

/**
 * 获取工作流风险评估Hook
 * 需求 9.4: 系统应支持风险报告的查询和检索
 */
export function useRiskAssessment(workflowId: string | null) {
  return useQuery({
    queryKey: ['risk-assessment', workflowId],
    queryFn: () => (workflowId ? fetchRiskAssessment(workflowId) : null),
    enabled: !!workflowId,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 风险评估历史记录Hook
 * 需求 9.3: 系统应记录风险评估的参数和配置
 */
export function useRiskAssessmentHistory(
  params: {
    ticker?: string;
    risk_level?: string;
    date_from?: string;
    date_to?: string;
    limit?: number;
    offset?: number;
  } = {}
) {
  return useQuery({
    queryKey: ['risk-history', params],
    queryFn: () => fetchRiskAssessmentHistory(params),
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 风险统计信息Hook
 */
export function useRiskStatistics(
  params: {
    ticker?: string;
    date_from?: string;
    date_to?: string;
  } = {}
) {
  return useQuery({
    queryKey: ['risk-statistics', params],
    queryFn: () => fetchRiskStatistics(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 风险对比Hook
 * 需求 9.5: 系统应提供风险分析的对比功能
 */
export function useRiskComparison() {
  return useMutation({
    mutationFn: compareRiskAssessments,
  });
}

/**
 * 风险指标趋势Hook
 * 需求 9.5: 系统应提供风险分析的对比功能
 */
export function useRiskMetricsTrend(
  params: {
    ticker: string;
    metric: string;
    days?: number;
  } | null
) {
  return useQuery({
    queryKey: ['risk-metrics-trend', params],
    queryFn: () => (params ? fetchRiskMetricsTrend(params) : null),
    enabled: !!params?.ticker && !!params?.metric,
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 风险数据管理综合Hook
 * 提供完整的风险数据管理功能
 */
export function useRiskDataManager() {
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [comparisonResult, setComparisonResult] = useState<any>(null);
  const [isComparing, setIsComparing] = useState(false);

  const saveAssessment = useRiskAssessmentSave();
  const compareAssessments = useRiskComparison();

  // 添加工作流到对比列表
  const addToComparison = useCallback((workflowId: string) => {
    setSelectedWorkflows((prev) => {
      if (prev.includes(workflowId)) {
        return prev;
      }
      return [...prev, workflowId].slice(0, 10); // 最多10个
    });
  }, []);

  // 从对比列表移除工作流
  const removeFromComparison = useCallback((workflowId: string) => {
    setSelectedWorkflows((prev) => prev.filter((id) => id !== workflowId));
  }, []);

  // 清空对比列表
  const clearComparison = useCallback(() => {
    setSelectedWorkflows([]);
    setComparisonResult(null);
  }, []);

  // 执行对比
  const performComparison = useCallback(async () => {
    if (selectedWorkflows.length < 2) {
      throw new Error('At least 2 workflows are required for comparison');
    }

    setIsComparing(true);
    try {
      const result = await compareAssessments.mutateAsync(selectedWorkflows);
      setComparisonResult(result);
      return result;
    } finally {
      setIsComparing(false);
    }
  }, [selectedWorkflows, compareAssessments]);

  return {
    // 状态
    selectedWorkflows,
    comparisonResult,
    isComparing,

    // 操作
    addToComparison,
    removeFromComparison,
    clearComparison,
    performComparison,

    // Mutations
    saveAssessment,
    compareAssessments,
  };
}
