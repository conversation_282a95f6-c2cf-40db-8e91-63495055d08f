/**
 * 风险管理师状态管理集成测试
 * 测试风险管理师与其他系统组件的集成
 */

import { riskManagerNode } from '../../app/api/langgraph/analysis/agents/risk_manager';
import { riskManagerStateManager } from '../risk-manager-state';

// Mock dependencies
jest.mock('@langchain/core/messages');
jest.mock('@langchain/openai');
jest.mock('../risk-data-collector');

describe('Risk Manager Integration', () => {
  const testWorkflowId = 'integration-test-workflow';

  beforeEach(() => {
    // 清理之前的状态
    riskManagerStateManager.cleanupState(testWorkflowId);
  });

  afterEach(() => {
    // 清理测试状态
    riskManagerStateManager.cleanupState(testWorkflowId);
  });

  describe('与 LangGraph 状态的集成', () => {
    it('应该正确处理 LangGraph 状态', async () => {
      // 创建模拟的 LangGraph 状态
      const mockState = {
        ticker: 'AAPL',
        date: '2024-01-01',
        config: {
          deepThinkLLM: 'gpt-4o',
          quickThinkLLM: 'gpt-4o-mini',
        },
        data: {},
        analysis: {
          fundamental: { summary: 'Good fundamentals' },
          technical: { summary: 'Bullish trend' },
          sentiment: { summary: 'Positive sentiment' },
          news: { summary: 'No major news' },
        },
        research: {
          bull: { summary: 'Strong buy case' },
          bear: { summary: 'Limited downside risks' },
        },
        risk: null,
        decision: null,
        currentStage: 'risk_assessment',
        messages: [],
        status: 'running',
        progress: 70,
        workflowId: testWorkflowId,
      };

      // Mock OpenAI response
      const mockOpenAI = require('@langchain/openai');
      mockOpenAI.ChatOpenAI = jest.fn().mockImplementation(() => ({
        invoke: jest.fn().mockResolvedValue({
          content: JSON.stringify({
            overall_risk_level: 'medium',
            risk_score: 5,
            summary: 'Moderate risk investment with balanced risk-reward profile',
            market_risk: {
              level: 'medium',
              score: 5,
              factors: ['Market volatility', 'Sector rotation risk'],
            },
            liquidity_risk: {
              level: 'low',
              score: 2,
              factors: ['High trading volume', 'Tight bid-ask spread'],
            },
            credit_risk: {
              level: 'low',
              score: 2,
              factors: ['Strong balance sheet', 'Stable cash flow'],
            },
            operational_risk: {
              level: 'medium',
              score: 4,
              factors: ['Regulatory changes', 'Competition'],
            },
            recommendations: [
              'Consider position sizing based on portfolio allocation',
              'Set stop-loss at 8% below entry price',
              'Monitor quarterly earnings closely',
            ],
            risk_controls: {
              position_size_limit: 15,
              stop_loss_percentage: 8,
              max_holding_period: '6个月',
            },
          }),
        }),
      }));

      // Mock risk data collector
      const mockRiskDataCollector = require('../risk-data-collector');
      mockRiskDataCollector.riskDataCollector = {
        collectRiskData: jest.fn().mockResolvedValue({
          market_data: {
            current_price: 150.0,
            volatility_data: { annualized_volatility: 0.25 },
            beta_data: { beta_coefficient: 1.2, correlation_with_market: 0.8 },
            price_statistics: {
              min_price_52w: 120.0,
              max_price_52w: 180.0,
              price_change_1d: 0.02,
              price_change_7d: 0.05,
              price_change_30d: 0.08,
            },
          },
          financial_data: {
            financial_health_score: 85,
            debt_metrics: { debt_to_equity: 0.3, current_ratio: 2.1 },
            profitability_metrics: { roe: 0.18, net_margin: 0.15 },
            cash_flow_metrics: { free_cash_flow: 50000000000 },
          },
          liquidity_data: {
            volume_data: { average_daily_volume: 50000000, volume_volatility: 0.15 },
            spread_data: { spread_percentage: 0.001 },
            liquidity_metrics: { amihud_ratio: 0.00001 },
          },
          macro_data: {
            market_indices: { market_beta: 1.0, sector_correlation: 0.7 },
            economic_indicators: { interest_rate_sensitivity: 0.5 },
          },
          data_quality: {
            overall_quality_score: 90,
            completeness_score: 95,
            timeliness_score: 85,
            missing_data_fields: [],
          },
        }),
      };

      // 监听状态变化
      const stateUpdates: any[] = [];
      const progressUpdates: any[] = [];

      riskManagerStateManager.on('stateUpdated', (state) => {
        if (state.workflowId === testWorkflowId) {
          stateUpdates.push(state);
        }
      });

      riskManagerStateManager.on('progressUpdate', (update) => {
        if (update.workflowId === testWorkflowId) {
          progressUpdates.push(update);
        }
      });

      // 执行风险管理师节点
      const result = await riskManagerNode(mockState as any);

      // 验证返回结果
      expect(result).toBeDefined();
      expect(result.risk).toBeDefined();
      expect(result.risk.overall_risk_level).toBe('medium');
      expect(result.risk.risk_score).toBe(5);
      expect(result.currentStage).toBe('risk_assessment_completed');
      expect(result.workflowId).toBe(testWorkflowId);

      // 验证状态管理器状态
      const finalState = riskManagerStateManager.getState(testWorkflowId);
      expect(finalState).toBeDefined();
      expect(finalState?.status).toBe('completed');
      expect(finalState?.progress).toBe(100);
      expect(finalState?.executionTimeMs).toBeGreaterThan(0);

      // 验证所有任务都已完成
      finalState?.tasks.forEach((task) => {
        expect(task.status).toBe('completed');
        expect(task.progress).toBe(100);
      });

      // 验证性能指标
      expect(finalState?.performance.dataCollectionTime).toBeGreaterThanOrEqual(0);
      expect(finalState?.performance.analysisTime).toBeGreaterThanOrEqual(0);
      expect(finalState?.performance.reportGenerationTime).toBeGreaterThanOrEqual(0);
      expect(finalState?.performance.totalTime).toBeGreaterThan(0);

      // 验证事件被触发
      expect(stateUpdates.length).toBeGreaterThan(0);
      expect(progressUpdates.length).toBeGreaterThan(0);

      // 验证最终状态更新
      const finalStateUpdate = stateUpdates[stateUpdates.length - 1];
      expect(finalStateUpdate.status).toBe('completed');
      expect(finalStateUpdate.progress).toBe(100);

      // 验证最终进度更新
      const finalProgressUpdate = progressUpdates[progressUpdates.length - 1];
      expect(finalProgressUpdate.status).toBe('completed');
      expect(finalProgressUpdate.progress).toBe(100);
    });

    it('应该正确处理错误情况', async () => {
      const mockState = {
        ticker: 'INVALID',
        workflowId: testWorkflowId,
        messages: [],
        analysis: {},
        research: {},
      };

      // Mock OpenAI to throw error
      const mockOpenAI = require('@langchain/openai');
      mockOpenAI.ChatOpenAI = jest.fn().mockImplementation(() => ({
        invoke: jest.fn().mockRejectedValue(new Error('API Error')),
      }));

      // Mock risk data collector to throw error
      const mockRiskDataCollector = require('../risk-data-collector');
      mockRiskDataCollector.riskDataCollector = {
        collectRiskData: jest.fn().mockRejectedValue(new Error('Data collection failed')),
      };

      // 监听错误状态
      let errorState: any = null;
      riskManagerStateManager.on('stateUpdated', (state) => {
        if (state.workflowId === testWorkflowId && state.status === 'failed') {
          errorState = state;
        }
      });

      // 执行风险管理师节点
      const result = await riskManagerNode(mockState as any);

      // 验证错误处理
      expect(result).toBeDefined();
      expect(result.risk.status).toBe('failed');
      expect(result.currentStage).toBe('risk_assessment_failed');

      // 验证状态管理器错误状态
      const finalState = riskManagerStateManager.getState(testWorkflowId);
      expect(finalState).toBeDefined();
      expect(finalState?.status).toBe('failed');
      expect(finalState?.error).toBeDefined();
      expect(finalState?.error?.message).toContain('API Error');

      // 验证错误事件被触发
      expect(errorState).toBeDefined();
      expect(errorState.status).toBe('failed');
    });
  });

  describe('性能监控', () => {
    it('应该正确记录性能指标', async () => {
      // 初始化状态
      const state = riskManagerStateManager.initializeState(testWorkflowId, {
        ticker: 'AAPL',
      });

      // 开始分析
      riskManagerStateManager.startAnalysis(testWorkflowId);

      // 模拟任务执行
      await new Promise((resolve) => setTimeout(resolve, 10)); // 等待10ms

      riskManagerStateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'running', 0);
      await new Promise((resolve) => setTimeout(resolve, 20)); // 等待20ms
      riskManagerStateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'completed', 100);

      riskManagerStateManager.updateTaskStatus(
        testWorkflowId,
        'market_risk_analysis',
        'running',
        0
      );
      await new Promise((resolve) => setTimeout(resolve, 15)); // 等待15ms
      riskManagerStateManager.updateTaskStatus(
        testWorkflowId,
        'market_risk_analysis',
        'completed',
        100
      );

      // 更新性能指标
      riskManagerStateManager.updatePerformanceMetrics(testWorkflowId, {
        dataCollectionTime: 100,
        analysisTime: 200,
        reportGenerationTime: 50,
      });

      // 完成分析
      riskManagerStateManager.completeAnalysis(testWorkflowId);

      // 验证性能指标
      const finalState = riskManagerStateManager.getState(testWorkflowId);
      expect(finalState?.performance.dataCollectionTime).toBe(100);
      expect(finalState?.performance.analysisTime).toBe(200);
      expect(finalState?.performance.reportGenerationTime).toBe(50);
      expect(finalState?.performance.totalTime).toBeGreaterThan(0);

      // 验证任务执行时间
      const dataCollectionTask = finalState?.tasks.find((t) => t.id === 'data_collection');
      expect(dataCollectionTask?.executionTimeMs).toBeGreaterThan(0);

      const analysisTask = finalState?.tasks.find((t) => t.id === 'market_risk_analysis');
      expect(analysisTask?.executionTimeMs).toBeGreaterThan(0);
    });
  });

  describe('统计信息', () => {
    it('应该正确计算统计信息', () => {
      // 创建多个工作流状态
      const workflows = ['wf1', 'wf2', 'wf3', 'wf4'];

      workflows.forEach((wfId) => {
        riskManagerStateManager.initializeState(wfId);
      });

      // 启动一些分析
      riskManagerStateManager.startAnalysis('wf1');
      riskManagerStateManager.startAnalysis('wf2');

      // 完成一些分析
      riskManagerStateManager.completeAnalysis('wf1');

      // 失败一些分析
      riskManagerStateManager.failAnalysis('wf2', 'Test error');

      // 获取统计信息
      const stats = riskManagerStateManager.getStatistics();

      expect(stats.total).toBe(4);
      expect(stats.pending).toBe(2); // wf3, wf4
      expect(stats.running).toBe(0);
      expect(stats.completed).toBe(1); // wf1
      expect(stats.failed).toBe(1); // wf2
      expect(stats.cancelled).toBe(0);

      // 清理
      workflows.forEach((wfId) => {
        riskManagerStateManager.cleanupState(wfId);
      });
    });
  });
});
