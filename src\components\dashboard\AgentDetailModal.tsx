'use client';

import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Modal } from '@/components/ui/Modal';
import {
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon,
  DocumentTextIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import { AnalystReport, ResearchReport } from './AgentStatusPanel';

interface AgentDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  agent: AnalystReport | ResearchReport | null;
  agentType: 'analyst' | 'researcher';
}

interface AgentLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  details?: string;
}

interface AgentMessage {
  id: string;
  timestamp: string;
  type: 'input' | 'output' | 'tool_call' | 'system';
  content: string;
  metadata?: any;
}

export function AgentDetailModal({ isOpen, onClose, agent, agentType }: AgentDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'logs' | 'messages' | 'results'>(
    'overview'
  );

  if (!agent) return null;

  // Mock data for demonstration - in real implementation, this would come from API
  const mockLogs: AgentLog[] = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      level: 'info',
      message: '开始执行分析任务',
      details: '初始化分析参数和数据源连接',
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 240000).toISOString(),
      level: 'info',
      message: '数据获取完成',
      details: '成功获取股票基本面数据和历史价格信息',
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 180000).toISOString(),
      level: 'warning',
      message: '部分指标数据缺失',
      details: '某些技术指标数据不完整，使用替代计算方法',
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 120000).toISOString(),
      level: 'success',
      message: '分析计算完成',
      details: '所有分析指标计算完毕，准备生成报告',
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 60000).toISOString(),
      level: 'info',
      message: '报告生成中',
      details: '正在整理分析结果并生成最终报告',
    },
  ];

  const mockMessages: AgentMessage[] = [
    {
      id: '1',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      type: 'system',
      content: '智能体启动，开始执行分析任务',
      metadata: { task_id: agent.report_id },
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 240000).toISOString(),
      type: 'tool_call',
      content: '调用数据获取工具',
      metadata: { tool: 'stock_data_fetcher', params: { ticker: 'AAPL' } },
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 180000).toISOString(),
      type: 'input',
      content: '请分析苹果公司的基本面数据，重点关注财务健康状况和增长潜力',
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 120000).toISOString(),
      type: 'output',
      content: '基于最新财务数据，苹果公司展现出强劲的财务健康状况...',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'pending':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'running':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
          />
        );
      case 'failed':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'success':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20';
      case 'error':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      case 'info':
      default:
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'input':
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';
      case 'output':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'tool_call':
        return 'text-purple-600 bg-purple-50 dark:bg-purple-900/20';
      case 'system':
        return 'text-slate-600 bg-slate-50 dark:bg-slate-900/20';
      default:
        return 'text-slate-600 bg-slate-50 dark:bg-slate-900/20';
    }
  };

  const agentName =
    agentType === 'analyst'
      ? getAnalystName((agent as AnalystReport).analyst_type)
      : getResearcherName((agent as ResearchReport).researcher_type);

  const progress =
    agent.progress || (agent.status === 'completed' ? 100 : agent.status === 'running' ? 50 : 0);

  function getAnalystName(type: string) {
    switch (type) {
      case 'fundamental':
        return '基本面分析师';
      case 'technical':
        return '技术分析师';
      case 'sentiment':
        return '情绪分析师';
      case 'news':
        return '新闻分析师';
      default:
        return type;
    }
  }

  function getResearcherName(type: string) {
    switch (type) {
      case 'bull':
        return '多头研究员';
      case 'bear':
        return '空头研究员';
      default:
        return type;
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-200 dark:border-slate-600">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <CpuChipIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                {agentName}详情
              </h2>
              <p className="text-sm text-slate-600 dark:text-slate-400">ID: {agent.report_id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant={getStatusColor(agent.status)}>
              {agent.status === 'completed'
                ? '已完成'
                : agent.status === 'running'
                ? '运行中'
                : agent.status === 'failed'
                ? '失败'
                : '等待中'}
            </Badge>
            <button
              onClick={onClose}
              className="p-2 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
            >
              <XMarkIcon className="h-5 w-5 text-slate-500" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-slate-200 dark:border-slate-600">
          {[
            { id: 'overview', label: '概览', icon: InformationCircleIcon },
            { id: 'logs', label: '执行日志', icon: DocumentTextIcon },
            { id: 'messages', label: '消息记录', icon: ChartBarIcon },
            { id: 'results', label: '结果预览', icon: CheckCircleIcon },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-50 dark:hover:bg-slate-700'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <AnimatePresence mode="wait">
            {activeTab === 'overview' && (
              <motion.div
                key="overview"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-6"
              >
                {/* Status Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <span>执行状态</span>
                      {getStatusIcon(agent.status)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                        <div className="text-lg font-bold text-slate-900 dark:text-white">
                          {progress}%
                        </div>
                        <div className="text-sm text-slate-600 dark:text-slate-400">进度</div>
                      </div>
                      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                        <div className="text-lg font-bold text-slate-900 dark:text-white">
                          {'execution_time_ms' in agent && agent.execution_time_ms
                            ? Math.round(agent.execution_time_ms / 1000)
                            : '-'}
                          s
                        </div>
                        <div className="text-sm text-slate-600 dark:text-slate-400">执行时间</div>
                      </div>
                      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                        <div className="text-lg font-bold text-slate-900 dark:text-white">
                          {new Date(agent.created_at).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-slate-600 dark:text-slate-400">创建日期</div>
                      </div>
                      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                        <div className="text-lg font-bold text-slate-900 dark:text-white">
                          {'confidence_level' in agent && agent.confidence_level
                            ? Math.round(agent.confidence_level * 100) + '%'
                            : '-'}
                        </div>
                        <div className="text-sm text-slate-600 dark:text-slate-400">置信度</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Progress Bar */}
                <Card>
                  <CardHeader>
                    <CardTitle>执行进度</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          当前任务: {agent.current_task || '等待中'}
                        </span>
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          {progress}%
                        </span>
                      </div>
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3">
                        <motion.div
                          className="h-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600"
                          initial={{ width: 0 }}
                          animate={{ width: `${progress}%` }}
                          transition={{ duration: 1, ease: 'easeOut' }}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Info for Research Reports */}
                {'confidence_level' in agent && (
                  <Card>
                    <CardHeader>
                      <CardTitle>研究详情</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {agent.target_price && (
                          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                            <div className="text-sm text-slate-600 dark:text-slate-400">
                              目标价格
                            </div>
                            <div className="text-lg font-semibold text-slate-900 dark:text-white">
                              ${agent.target_price.toFixed(2)}
                            </div>
                          </div>
                        )}
                        {agent.time_horizon && (
                          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                            <div className="text-sm text-slate-600 dark:text-slate-400">
                              时间范围
                            </div>
                            <div className="text-lg font-semibold text-slate-900 dark:text-white">
                              {agent.time_horizon}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            )}

            {activeTab === 'logs' && (
              <motion.div
                key="logs"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white">执行日志</h3>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    共 {mockLogs.length} 条记录
                  </span>
                </div>
                <div className="space-y-3">
                  {mockLogs.map((log, index) => (
                    <motion.div
                      key={log.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-4 border border-slate-200 dark:border-slate-600 rounded-lg"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <span
                              className={`px-2 py-1 text-xs font-medium rounded ${getLogLevelColor(
                                log.level
                              )}`}
                            >
                              {log.level.toUpperCase()}
                            </span>
                            <span className="text-sm text-slate-600 dark:text-slate-400">
                              {new Date(log.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm font-medium text-slate-900 dark:text-white mb-1">
                            {log.message}
                          </p>
                          {log.details && (
                            <p className="text-xs text-slate-600 dark:text-slate-400">
                              {log.details}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'messages' && (
              <motion.div
                key="messages"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white">消息记录</h3>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    共 {mockMessages.length} 条消息
                  </span>
                </div>
                <div className="space-y-3">
                  {mockMessages.map((message, index) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="p-4 border border-slate-200 dark:border-slate-600 rounded-lg"
                    >
                      <div className="flex items-start space-x-3">
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded ${getMessageTypeColor(
                            message.type
                          )}`}
                        >
                          {message.type.toUpperCase()}
                        </span>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-slate-600 dark:text-slate-400">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm text-slate-900 dark:text-white">
                            {message.content}
                          </p>
                          {message.metadata && (
                            <div className="mt-2 p-2 bg-slate-50 dark:bg-slate-800 rounded text-xs">
                              <pre className="text-slate-600 dark:text-slate-400">
                                {JSON.stringify(message.metadata, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'results' && (
              <motion.div
                key="results"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-medium text-slate-900 dark:text-white">结果预览</h3>
                {agent.summary ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>分析摘要</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">
                        {agent.summary}
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-8">
                    <DocumentTextIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                    <p className="text-slate-600 dark:text-slate-400">
                      {agent.status === 'completed'
                        ? '暂无结果摘要'
                        : '分析尚未完成，请等待结果生成'}
                    </p>
                  </div>
                )}

                {agent.error_message && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-red-600">错误信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-red-700 dark:text-red-300">
                        {agent.error_message}
                      </p>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </Modal>
  );
}
