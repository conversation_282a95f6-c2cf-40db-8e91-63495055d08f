/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-05 18:38:47
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 20:32:10
 * @FilePath: \trading-agents-frontend\src\app\api\database\tasks\route.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { error, success } from '@/lib/api-helpers';
import { getCurrentUser } from '@/lib/auth';
import { query } from '@/lib/db';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return error('未授权访问，请先登录', 401);
    }

    // 查询工作流表，并转换为兼容的任务格式
    const workflows = await query(
      'SELECT * FROM workflows WHERE created_by = ? ORDER BY created_at DESC',
      [currentUser.userId]
    );

    // 转换工作流数据为任务格式以保持向后兼容
    const tasks = (workflows as any[]).map((workflow) => ({
      ...workflow,
      task_id: workflow.workflow_id, // 映射字段
      // 从 config 中提取 research_depth 和 analysis_period
      research_depth: workflow.config?.researchDepth || 'medium',
      analysis_period: workflow.config?.analysisPeriod || '1d',
      priority: workflow.config?.priority || 1,
    }));

    return success(tasks);
  } catch (err) {
    console.error('查询任务失败:', err);
    return error('查询任务失败', 500);
  }
}
