/**
 * 语义结构验证工具
 * 用于验证页面的语义化 HTML 结构是否符合 SEO 和可访问性标准
 */

export interface ValidationResult {
  isValid: boolean;
  score: number; // 0-100 分
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

export interface ValidationError {
  type:
    | 'missing_h1'
    | 'multiple_h1'
    | 'heading_skip'
    | 'missing_alt'
    | 'missing_aria_label'
    | 'invalid_landmark';
  element?: string;
  message: string;
  severity: 'high' | 'medium' | 'low';
}

export interface ValidationWarning {
  type: 'long_title' | 'short_title' | 'missing_description' | 'duplicate_id' | 'empty_heading';
  element?: string;
  message: string;
}

export interface ValidationSuggestion {
  type: 'add_landmark' | 'improve_heading' | 'add_aria_label' | 'optimize_structure';
  element?: string;
  message: string;
  improvement: string;
}

export class SemanticValidator {
  private document: Document;

  constructor(document: Document) {
    this.document = document;
  }

  /**
   * 验证整个页面的语义结构
   */
  validatePage(): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    // 验证标题结构
    const headingValidation = this.validateHeadingStructure();
    errors.push(...headingValidation.errors);
    warnings.push(...headingValidation.warnings);
    suggestions.push(...headingValidation.suggestions);

    // 验证地标元素
    const landmarkValidation = this.validateLandmarks();
    errors.push(...landmarkValidation.errors);
    warnings.push(...landmarkValidation.warnings);
    suggestions.push(...landmarkValidation.suggestions);

    // 验证图片 alt 属性
    const imageValidation = this.validateImages();
    errors.push(...imageValidation.errors);
    warnings.push(...imageValidation.warnings);
    suggestions.push(...imageValidation.suggestions);

    // 验证表单可访问性
    const formValidation = this.validateForms();
    errors.push(...formValidation.errors);
    warnings.push(...formValidation.warnings);
    suggestions.push(...formValidation.suggestions);

    // 验证链接和按钮
    const interactiveValidation = this.validateInteractiveElements();
    errors.push(...interactiveValidation.errors);
    warnings.push(...interactiveValidation.warnings);
    suggestions.push(...interactiveValidation.suggestions);

    // 计算分数
    const score = this.calculateScore(errors, warnings);

    return {
      isValid: errors.filter((e) => e.severity === 'high').length === 0,
      score,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * 验证标题结构
   */
  private validateHeadingStructure(): {
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: ValidationSuggestion[];
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    const headings = Array.from(this.document.querySelectorAll('h1, h2, h3, h4, h5, h6'));

    // 检查 H1 标题
    const h1Elements = headings.filter((h) => h.tagName === 'H1');
    if (h1Elements.length === 0) {
      errors.push({
        type: 'missing_h1',
        message: '页面缺少 H1 标题，这对 SEO 和可访问性都很重要',
        severity: 'high',
      });
    } else if (h1Elements.length > 1) {
      errors.push({
        type: 'multiple_h1',
        message: `页面有 ${h1Elements.length} 个 H1 标题，建议只使用一个`,
        severity: 'medium',
      });
    }

    // 检查标题层级跳跃
    for (let i = 1; i < headings.length; i++) {
      const current = headings[i];
      const previous = headings[i - 1];
      const currentLevel = parseInt(current.tagName.charAt(1));
      const previousLevel = parseInt(previous.tagName.charAt(1));

      if (currentLevel > previousLevel + 1) {
        errors.push({
          type: 'heading_skip',
          element: current.tagName,
          message: `标题层级跳跃: ${previous.tagName} 后直接使用 ${current.tagName}`,
          severity: 'medium',
        });
      }
    }

    // 检查标题内容
    headings.forEach((heading, index) => {
      const text = heading.textContent?.trim() || '';

      if (text.length === 0) {
        warnings.push({
          type: 'empty_heading',
          element: heading.tagName,
          message: `第 ${index + 1} 个标题为空`,
        });
      } else if (text.length > 60) {
        warnings.push({
          type: 'long_title',
          element: heading.tagName,
          message: `标题过长 (${text.length} 字符): "${text.substring(0, 50)}..."`,
        });
      } else if (text.length < 3) {
        warnings.push({
          type: 'short_title',
          element: heading.tagName,
          message: `标题过短: "${text}"`,
        });
      }

      // 检查是否有 ID
      if (!heading.id) {
        suggestions.push({
          type: 'improve_heading',
          element: heading.tagName,
          message: `为标题添加 ID 属性以便锚点链接`,
          improvement: `添加 id="${this.generateId(text)}"`,
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 验证地标元素
   */
  private validateLandmarks(): {
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: ValidationSuggestion[];
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    // 检查必要的地标元素
    const requiredLandmarks = [
      { selector: 'main, [role="main"]', name: 'main', message: '页面缺少主要内容区域 (main)' },
      {
        selector: 'nav, [role="navigation"]',
        name: 'navigation',
        message: '页面缺少导航区域 (nav)',
      },
    ];

    requiredLandmarks.forEach((landmark) => {
      const elements = this.document.querySelectorAll(landmark.selector);
      if (elements.length === 0) {
        suggestions.push({
          type: 'add_landmark',
          message: landmark.message,
          improvement: `添加 <${landmark.name}> 元素或 role="${landmark.name}"`,
        });
      } else if (elements.length > 1 && landmark.name === 'main') {
        errors.push({
          type: 'invalid_landmark',
          element: 'main',
          message: '页面有多个主要内容区域，应该只有一个',
          severity: 'medium',
        });
      }
    });

    // 检查地标元素的 aria-label
    const landmarks = this.document.querySelectorAll(
      '[role="navigation"], [role="main"], [role="complementary"], [role="contentinfo"], [role="banner"]'
    );
    landmarks.forEach((landmark) => {
      const role = landmark.getAttribute('role');
      const ariaLabel = landmark.getAttribute('aria-label');
      const ariaLabelledBy = landmark.getAttribute('aria-labelledby');

      if (!ariaLabel && !ariaLabelledBy) {
        suggestions.push({
          type: 'add_aria_label',
          element: role || landmark.tagName,
          message: `地标元素缺少可访问性标签`,
          improvement: `添加 aria-label 或 aria-labelledby 属性`,
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 验证图片 alt 属性
   */
  private validateImages(): {
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: ValidationSuggestion[];
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    const images = this.document.querySelectorAll('img');
    images.forEach((img, index) => {
      const alt = img.getAttribute('alt');
      const src = img.getAttribute('src');

      if (alt === null) {
        errors.push({
          type: 'missing_alt',
          element: 'img',
          message: `图片缺少 alt 属性: ${src}`,
          severity: 'high',
        });
      } else if (alt.trim() === '' && !img.hasAttribute('aria-hidden')) {
        warnings.push({
          type: 'missing_description',
          element: 'img',
          message: `图片 alt 属性为空，如果是装饰性图片请添加 aria-hidden="true"`,
        });
      } else if (alt.length > 125) {
        warnings.push({
          type: 'long_title',
          element: 'img',
          message: `图片 alt 文本过长 (${alt.length} 字符)`,
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 验证表单可访问性
   */
  private validateForms(): {
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: ValidationSuggestion[];
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    // 检查表单控件的标签
    const inputs = this.document.querySelectorAll('input, select, textarea');
    inputs.forEach((input) => {
      const id = input.getAttribute('id');
      const ariaLabel = input.getAttribute('aria-label');
      const ariaLabelledBy = input.getAttribute('aria-labelledby');
      const type = input.getAttribute('type');

      // 跳过隐藏和提交按钮
      if (type === 'hidden' || type === 'submit' || type === 'button') {
        return;
      }

      let hasLabel = false;
      if (id) {
        const label = this.document.querySelector(`label[for="${id}"]`);
        hasLabel = !!label;
      }

      if (!hasLabel && !ariaLabel && !ariaLabelledBy) {
        errors.push({
          type: 'missing_aria_label',
          element: input.tagName,
          message: `表单控件缺少标签或 aria-label`,
          severity: 'high',
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 验证交互元素
   */
  private validateInteractiveElements(): {
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: ValidationSuggestion[];
  } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    // 检查按钮的可访问性
    const buttons = this.document.querySelectorAll('button');
    buttons.forEach((button) => {
      const text = button.textContent?.trim();
      const ariaLabel = button.getAttribute('aria-label');

      if (!text && !ariaLabel) {
        errors.push({
          type: 'missing_aria_label',
          element: 'button',
          message: '按钮缺少文本内容或 aria-label',
          severity: 'high',
        });
      }
    });

    // 检查链接的可访问性
    const links = this.document.querySelectorAll('a');
    links.forEach((link) => {
      const text = link.textContent?.trim();
      const ariaLabel = link.getAttribute('aria-label');
      const href = link.getAttribute('href');

      if (!text && !ariaLabel) {
        errors.push({
          type: 'missing_aria_label',
          element: 'a',
          message: '链接缺少文本内容或 aria-label',
          severity: 'high',
        });
      }

      if (text && (text === '点击这里' || text === 'click here' || text === '更多')) {
        suggestions.push({
          type: 'improve_heading',
          element: 'a',
          message: '链接文本不够描述性',
          improvement: '使用更具描述性的链接文本',
        });
      }
    });

    return { errors, warnings, suggestions };
  }

  /**
   * 计算 SEO 分数
   */
  private calculateScore(errors: ValidationError[], warnings: ValidationWarning[]): number {
    let score = 100;

    // 扣分规则
    errors.forEach((error) => {
      switch (error.severity) {
        case 'high':
          score -= 15;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    warnings.forEach(() => {
      score -= 2;
    });

    return Math.max(0, score);
  }

  /**
   * 生成 ID
   */
  private generateId(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }
}

/**
 * 创建语义验证器实例
 */
export function createSemanticValidator(document: Document): SemanticValidator {
  return new SemanticValidator(document);
}

/**
 * 验证页面语义结构的快捷函数
 */
export function validatePageSemantics(document: Document = window.document): ValidationResult {
  const validator = createSemanticValidator(document);
  return validator.validatePage();
}

/**
 * 生成验证报告
 */
export function generateValidationReport(result: ValidationResult): string {
  const { score, errors, warnings, suggestions } = result;

  let report = `# 语义结构验证报告\n\n`;
  report += `**总分**: ${score}/100\n`;
  report += `**状态**: ${result.isValid ? '✅ 通过' : '❌ 需要改进'}\n\n`;

  if (errors.length > 0) {
    report += `## 错误 (${errors.length})\n\n`;
    errors.forEach((error, index) => {
      const severity = error.severity === 'high' ? '🔴' : error.severity === 'medium' ? '🟡' : '🟢';
      report += `${index + 1}. ${severity} **${error.element || '页面'}**: ${error.message}\n`;
    });
    report += '\n';
  }

  if (warnings.length > 0) {
    report += `## 警告 (${warnings.length})\n\n`;
    warnings.forEach((warning, index) => {
      report += `${index + 1}. ⚠️ **${warning.element || '页面'}**: ${warning.message}\n`;
    });
    report += '\n';
  }

  if (suggestions.length > 0) {
    report += `## 建议 (${suggestions.length})\n\n`;
    suggestions.forEach((suggestion, index) => {
      report += `${index + 1}. 💡 **${suggestion.element || '页面'}**: ${suggestion.message}\n`;
      if (suggestion.improvement) {
        report += `   - 改进方案: ${suggestion.improvement}\n`;
      }
    });
  }

  return report;
}
