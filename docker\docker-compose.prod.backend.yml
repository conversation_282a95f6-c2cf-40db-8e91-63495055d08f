# 生产环境后端服务 Docker Compose 配置文件
# 专门用于部署 AKShare 后端数据服务
version: '3.8'

services:
  # AKShare 后端数据服务 - 使用阿里云镜像
  akshare-backend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/akshare-backend:latest
    container_name: tradingagents-akshare-backend
    ports:
      - '5000:5000'
    environment:
      - APP_NAME=AKShare数据服务
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=5000
      - LOG_LEVEL=INFO
      # 数据库连接配置
      - DB_HOST=${DB_HOST:-mysql}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${MYSQL_DATABASE:-trading_analysis}
      - DB_USER=${MYSQL_USER:-trading_user}
      - DB_PASSWORD=${MYSQL_PASSWORD:-trading123}
      # CORS配置 - 允许前端容器访问
      - ALLOWED_ORIGINS=["http://localhost:3000","http://tradingagents-frontend:3000","${FRONTEND_URL:-http://localhost:3000}"]
      # 缓存配置
      - CACHE_TTL=300
      # API限制配置
      - RATE_LIMIT_REQUESTS=100
      - RATE_LIMIT_WINDOW=60
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - mysql
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:5000/health || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

  # MySQL 数据库 - 使用阿里云镜像
  mysql:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
    container_name: tradingagents-mysql
    ports:
      - '13306:3306'
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-trading123}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-trading_analysis}
      - MYSQL_USER=${MYSQL_USER:-trading_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-trading123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network

volumes:
  mysql_data:
    driver: local
