// userStore 会话管理测试
import useUserStore from './userStore';

// 测试 userStore 会话管理功能
export function testUserStore() {
  console.log('🧪 Testing userStore session management...');

  const store = useUserStore.getState();

  // 测试初始状态
  console.log('✓ Initial state:', {
    user: store.user,
    sessionId: store.sessionId,
    isAuthenticated: store.isAuthenticated(),
  });

  // 测试设置会话
  const testSessionId = 'test-session-123';
  store.setSession(testSessionId);
  console.log('✓ Session set:', store.sessionId === testSessionId ? 'PASS' : 'FAIL');

  // 测试设置用户信息
  const testUser = { id: 1, username: 'testuser', email: '<EMAIL>' };
  store.setUser(testUser, 'new-session-456');
  console.log('✓ User set:', store.user?.username === 'testuser' ? 'PASS' : 'FAIL');
  console.log('✓ Session updated:', store.sessionId === 'new-session-456' ? 'PASS' : 'FAIL');

  // 测试认证状态
  console.log('✓ Is authenticated:', store.isAuthenticated() ? 'PASS' : 'FAIL');

  // 测试认证头
  const authHeaders = store.getAuthHeaders();
  console.log(
    '✓ Auth headers:',
    authHeaders['X-Session-ID'] === 'new-session-456' ? 'PASS' : 'FAIL'
  );

  // 测试清除用户
  store.clearUser();
  console.log('✓ User cleared:', store.user === null ? 'PASS' : 'FAIL');
  console.log('✓ Session cleared:', store.sessionId === null ? 'PASS' : 'FAIL');
  console.log('✓ Is authenticated after clear:', !store.isAuthenticated() ? 'PASS' : 'FAIL');

  console.log('🎉 userStore session management tests completed!');
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  testUserStore();
}
