import { useNavigationState } from '@/hooks/useNavigationState';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { AnalysisDropdown } from '../AnalysisDropdown';

// Mock the hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/hooks/useNavigationState', () => ({
  useNavigationState: jest.fn(),
}));

jest.mock('@/hooks/useKeyboardShortcuts', () => ({
  useKeyboardShortcuts: jest.fn(),
}));

const mockPush = jest.fn();
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseNavigationState = useNavigationState as jest.MockedFunction<typeof useNavigationState>;

const mockAnalysisStats = {
  total: 10,
  completed: 8,
  running: 1,
  failed: 1,
  todayCount: 3,
  weekCount: 7,
  successRate: 80,
  avgDuration: 120,
};

const mockRecentAnalyses = [
  {
    id: '1',
    ticker: 'AAPL',
    title: 'Apple Inc. 技术分析',
    status: 'completed' as const,
    createdAt: '2024-01-01T10:00:00Z',
  },
  {
    id: '2',
    ticker: 'GOOGL',
    title: 'Google 基本面分析',
    status: 'running' as const,
    createdAt: '2024-01-01T11:00:00Z',
  },
  {
    id: '3',
    ticker: 'MSFT',
    title: 'Microsoft 综合分析',
    status: 'failed' as const,
    createdAt: '2024-01-01T12:00:00Z',
  },
];

describe('AnalysisDropdown', () => {
  const defaultProps = {
    isOpen: true,
    onToggle: jest.fn(),
    onClose: jest.fn(),
  };

  beforeEach(() => {
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any);

    mockUseNavigationState.mockReturnValue({
      currentPath: '/',
      isAnalysisPage: false,
      analysisStats: mockAnalysisStats,
      recentAnalyses: mockRecentAnalyses,
      loading: false,
      refreshStats: jest.fn(),
      navigateToAnalysis: jest.fn(),
      navigateToHistory: jest.fn(),
      navigateToCompare: jest.fn(),
      clearError: jest.fn(),
    });

    jest.clearAllMocks();
  });

  it('renders dropdown when open', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    expect(screen.getByRole('menu', { name: '分析功能菜单' })).toBeInTheDocument();
    expect(screen.getByText('分析历史')).toBeInTheDocument();
    expect(screen.getByText('分析对比')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<AnalysisDropdown {...defaultProps} isOpen={false} />);

    expect(screen.queryByRole('menu')).not.toBeInTheDocument();
  });

  it('displays analysis statistics correctly', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    expect(screen.getByText('分析统计')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // total
    expect(screen.getByText('8')).toBeInTheDocument(); // completed
    expect(screen.getByText('1')).toBeInTheDocument(); // running
    expect(screen.getByText('3')).toBeInTheDocument(); // todayCount
  });

  it('displays recent analyses', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    expect(screen.getByText('最近分析')).toBeInTheDocument();
    expect(screen.getByText('AAPL')).toBeInTheDocument();
    expect(screen.getByText('GOOGL')).toBeInTheDocument();
    expect(screen.getByText('MSFT')).toBeInTheDocument();

    // Check status badges
    expect(screen.getByText('完成')).toBeInTheDocument();
    expect(screen.getByText('进行中')).toBeInTheDocument();
    expect(screen.getByText('失败')).toBeInTheDocument();
  });

  it('navigates to analysis history when clicked', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    const historyButton = screen.getByRole('menuitem', { name: /分析历史/ });
    fireEvent.click(historyButton);

    expect(mockPush).toHaveBeenCalledWith('/analysis/history');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('navigates to analysis compare when clicked', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    const compareButton = screen.getByRole('menuitem', { name: /分析对比/ });
    fireEvent.click(compareButton);

    expect(mockPush).toHaveBeenCalledWith('/analysis/compare');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('navigates to specific analysis when recent analysis is clicked', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    const analysisButton = screen.getByRole('menuitem', { name: /AAPL/ });
    fireEvent.click(analysisButton);

    expect(mockPush).toHaveBeenCalledWith('/analysis/1');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('navigates to create task when quick action is clicked', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    const createButton = screen.getByRole('menuitem', { name: /创建新分析/ });
    fireEvent.click(createButton);

    expect(mockPush).toHaveBeenCalledWith('/create-task');
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('closes dropdown when clicking outside', async () => {
    render(<AnalysisDropdown {...defaultProps} />);

    // Click outside the dropdown
    fireEvent.mouseDown(document.body);

    await waitFor(() => {
      expect(defaultProps.onClose).toHaveBeenCalled();
    });
  });

  it('handles keyboard navigation correctly', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    // Test Escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('displays keyboard shortcuts', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    expect(screen.getByText('Alt+H')).toBeInTheDocument();
    expect(screen.getByText('Alt+C')).toBeInTheDocument();
  });

  it('shows "view all" link when there are more than 3 recent analyses', () => {
    const manyAnalyses = [
      ...mockRecentAnalyses,
      {
        id: '4',
        ticker: 'TSLA',
        title: 'Tesla Analysis',
        status: 'completed' as const,
        createdAt: '2024-01-01T13:00:00Z',
      },
    ];

    mockUseNavigationState.mockReturnValue({
      currentPath: '/',
      isAnalysisPage: false,
      analysisStats: mockAnalysisStats,
      recentAnalyses: manyAnalyses,
      loading: false,
      refreshStats: jest.fn(),
      navigateToAnalysis: jest.fn(),
      navigateToHistory: jest.fn(),
      navigateToCompare: jest.fn(),
      clearError: jest.fn(),
    });

    render(<AnalysisDropdown {...defaultProps} />);

    expect(screen.getByText(/查看全部 4 个分析/)).toBeInTheDocument();
  });

  it('handles loading state gracefully', () => {
    mockUseNavigationState.mockReturnValue({
      currentPath: '/',
      isAnalysisPage: false,
      analysisStats: null,
      recentAnalyses: [],
      loading: true,
      refreshStats: jest.fn(),
      navigateToAnalysis: jest.fn(),
      navigateToHistory: jest.fn(),
      navigateToCompare: jest.fn(),
      clearError: jest.fn(),
    });

    render(<AnalysisDropdown {...defaultProps} />);

    // Should still render the dropdown structure
    expect(screen.getByRole('menu')).toBeInTheDocument();
    expect(screen.getByText('分析历史')).toBeInTheDocument();
    expect(screen.getByText('分析对比')).toBeInTheDocument();
  });

  it('has proper ARIA attributes', () => {
    render(<AnalysisDropdown {...defaultProps} />);

    const menu = screen.getByRole('menu');
    expect(menu).toHaveAttribute('aria-label', '分析功能菜单');
    expect(menu).toHaveAttribute('aria-expanded', 'true');

    const menuItems = screen.getAllByRole('menuitem');
    expect(menuItems.length).toBeGreaterThan(0);

    // Check that menu items have proper tabIndex
    menuItems.forEach((item) => {
      expect(item).toHaveAttribute('tabIndex', '-1');
    });
  });
});
