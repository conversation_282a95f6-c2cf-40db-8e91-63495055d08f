import React from 'react';
import Link from 'next/link';
import { HomeIcon, MagnifyingGlassIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

/**
 * Next.js App Router 404 页面
 * 当用户访问不存在的页面时会自动显示此页面
 * 这是一个服务端组件，不需要 'use client' 指令
 */
export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-slate-50 dark:bg-slate-900">
      <Card className="max-w-lg w-full shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-24 h-24 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
            <span className="text-4xl font-bold text-slate-400 dark:text-slate-500">404</span>
          </div>
          <CardTitle className="text-2xl text-slate-900 dark:text-slate-100">
            页面未找到
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6 text-center">
          <div className="text-slate-600 dark:text-slate-400">
            <p className="mb-4">
              抱歉，您访问的页面不存在或已被移动。
            </p>
            <p className="text-sm">
              可能的原因：
            </p>
            <ul className="list-disc list-inside text-sm mt-2 space-y-1 text-left max-w-sm mx-auto">
              <li>页面地址输入错误</li>
              <li>页面已被删除或移动</li>
              <li>您没有访问权限</li>
              <li>链接已过期</li>
            </ul>
          </div>
          
          {/* 建议的操作 */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
              您可以尝试：
            </p>
            
            <div className="grid gap-3">
              <Link href="/" className="w-full">
                <Button className="w-full flex items-center justify-center space-x-2">
                  <HomeIcon className="h-4 w-4" />
                  <span>返回首页</span>
                </Button>
              </Link>
              
              <Button 
                variant="secondary" 
                onClick={() => window.history.back()}
                className="w-full flex items-center justify-center space-x-2"
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span>返回上一页</span>
              </Button>
              
              <Link href="/tasks" className="w-full">
                <Button 
                  variant="outline" 
                  className="w-full flex items-center justify-center space-x-2"
                >
                  <MagnifyingGlassIcon className="h-4 w-4" />
                  <span>浏览任务</span>
                </Button>
              </Link>
            </div>
          </div>
          
          {/* 快速导航 */}
          <div className="pt-6 border-t border-slate-200 dark:border-slate-700">
            <p className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
              快速导航：
            </p>
            <div className="flex flex-wrap gap-2 justify-center">
              <Link 
                href="/tasks" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                任务管理
              </Link>
              <span className="text-slate-400">•</span>
              <Link 
                href="/analysis" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                数据分析
              </Link>
              <span className="text-slate-400">•</span>
              <Link 
                href="/create-task" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                创建任务
              </Link>
              <span className="text-slate-400">•</span>
              <Link 
                href="/messages" 
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                消息记录
              </Link>
            </div>
          </div>
          
          {/* 联系信息 */}
          <div className="text-center text-sm text-slate-500 dark:text-slate-400">
            如果您认为这是一个错误，请
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 dark:text-blue-400 hover:underline ml-1"
            >
              联系我们
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
