/**
 * 水合错误测试脚本
 * 用于检测 Next.js 应用中的水合不匹配问题
 */

// 在浏览器控制台中运行此脚本来检测水合错误
function checkHydrationErrors() {
  console.log('🔍 开始检查水合错误...');
  
  // 监听水合错误
  const originalError = console.error;
  let hydrationErrors = [];
  
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('Hydration failed') || 
        message.includes('hydration') || 
        message.includes('server rendered HTML')) {
      hydrationErrors.push(message);
      console.log('❌ 发现水合错误:', message);
    }
    originalError.apply(console, args);
  };
  
  // 检查常见的水合问题
  setTimeout(() => {
    console.log('📊 水合错误检查结果:');
    if (hydrationErrors.length === 0) {
      console.log('✅ 未发现水合错误！');
    } else {
      console.log(`❌ 发现 ${hydrationErrors.length} 个水合错误:`);
      hydrationErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    // 恢复原始的 console.error
    console.error = originalError;
  }, 5000);
}

// 如果在浏览器环境中，自动运行检查
if (typeof window !== 'undefined') {
  checkHydrationErrors();
} else {
  console.log('请在浏览器控制台中运行此脚本');
}
