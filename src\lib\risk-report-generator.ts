/**
 * 风险报告生成器
 * Risk Report Generator
 */

import {
  AlertLevel,
  RiskAlert,
  RiskAssessment,
  RiskControlRecommendation,
  RiskLevel,
  RiskMetric,
  RiskReport,
  RiskReportConfig,
  RiskScenarioResult,
  RiskType,
} from '@/types/risk-report';
import { riskControlRecommender } from './risk-control-recommender';
import { riskLevelAssessor } from './risk-level-assessor';
import { riskMetricsCalculator } from './risk-metrics-calculator';
import { riskScenarioAnalyzer } from './risk-scenario-analyzer';

/**
 * 风险报告生成器类
 */
export class RiskReportGenerator {
  /**
   * 生成完整的风险报告
   */
  async generateRiskReport(
    symbol: string,
    analysisId: string,
    marketData: any,
    config: RiskReportConfig = this.getDefaultConfig()
  ): Promise<RiskReport> {
    const startTime = Date.now();

    try {
      // 1. 收集和计算风险指标
      const riskMetrics = await this.collectRiskMetrics(symbol, marketData);

      // 2. 进行各类风险评估
      const riskAssessments = await this.performRiskAssessments(riskMetrics);

      // 3. 计算综合风险评分
      const overallRisk = riskLevelAssessor.calculateOverallRiskScore(riskAssessments);

      // 4. 生成风险预警
      const alerts = this.generateRiskAlerts(riskAssessments, riskMetrics, config);

      // 5. 生成风险控制建议
      const recommendations = await this.generateRiskRecommendations(
        riskAssessments,
        riskMetrics,
        overallRisk,
        marketData
      );

      // 6. 进行场景分析（如果启用）
      const scenarioAnalysis = config.includeScenarioAnalysis
        ? await this.performScenarioAnalysis(symbol, marketData)
        : [];

      // 7. 获取历史对比数据（如果启用）
      const historicalComparison = config.includeHistoricalComparison
        ? await this.getHistoricalComparison(symbol, overallRisk.score)
        : undefined;

      // 8. 构建完整报告
      const report: RiskReport = {
        id: this.generateReportId(),
        symbol,
        timestamp: new Date(),
        analysisId,
        overallRisk: {
          ...overallRisk,
          summary: this.generateOverallRiskSummary(overallRisk, riskAssessments),
        },
        riskAssessments,
        keyMetrics: this.selectKeyMetrics(riskMetrics),
        alerts,
        recommendations,
        scenarioAnalysis,
        historicalComparison,
        metadata: {
          version: '1.0.0',
          generatedBy: 'RiskReportGenerator',
          processingTime: Date.now() - startTime,
          dataQuality: this.assessDataQuality(riskMetrics),
          lastUpdated: new Date(),
        },
      };

      return report;
    } catch (error) {
      console.error('风险报告生成失败:', error);
      throw new Error(
        `风险报告生成失败: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 收集风险指标
   */
  private async collectRiskMetrics(
    symbol: string,
    marketData: any
  ): Promise<Record<string, RiskMetric>> {
    const metrics: Record<string, RiskMetric> = {};

    try {
      // 市场风险指标
      const marketMetrics = await riskMetricsCalculator.calculateRiskMetrics(marketData);
      Object.assign(
        metrics,
        this.formatMetrics(this.flattenRiskMetrics(marketMetrics), RiskType.MARKET)
      );

      // 流动性风险指标
      const liquidityMetrics = await riskMetricsCalculator.calculateRiskMetrics(marketData);
      Object.assign(
        metrics,
        this.formatMetrics(this.flattenRiskMetrics(liquidityMetrics), RiskType.LIQUIDITY)
      );

      // 信用风险指标
      const creditMetrics = await riskMetricsCalculator.calculateRiskMetrics(marketData);
      Object.assign(
        metrics,
        this.formatMetrics(this.flattenRiskMetrics(creditMetrics), RiskType.CREDIT)
      );
    } catch (error) {
      console.warn('部分风险指标计算失败:', error);
    }

    return metrics;
  }

  /**
   * 执行风险评估
   */
  private async performRiskAssessments(
    riskMetrics: Record<string, RiskMetric>
  ): Promise<RiskAssessment[]> {
    const assessments: RiskAssessment[] = [];

    // 市场风险评估
    const marketMetrics = this.extractMetricsByType(riskMetrics, RiskType.MARKET);
    if (Object.keys(marketMetrics).length > 0) {
      assessments.push(riskLevelAssessor.assessRisk(RiskType.MARKET, marketMetrics));
    }

    // 流动性风险评估
    const liquidityMetrics = this.extractMetricsByType(riskMetrics, RiskType.LIQUIDITY);
    if (Object.keys(liquidityMetrics).length > 0) {
      assessments.push(riskLevelAssessor.assessRisk(RiskType.LIQUIDITY, liquidityMetrics));
    }

    // 信用风险评估
    const creditMetrics = this.extractMetricsByType(riskMetrics, RiskType.CREDIT);
    if (Object.keys(creditMetrics).length > 0) {
      assessments.push(riskLevelAssessor.assessRisk(RiskType.CREDIT, creditMetrics));
    }

    // 操作风险评估（基于可用数据）
    const operationalMetrics = this.extractMetricsByType(riskMetrics, RiskType.OPERATIONAL);
    if (Object.keys(operationalMetrics).length > 0) {
      assessments.push(riskLevelAssessor.assessRisk(RiskType.OPERATIONAL, operationalMetrics));
    }

    // 系统性风险评估
    const systematicMetrics = this.extractMetricsByType(riskMetrics, RiskType.SYSTEMATIC);
    if (Object.keys(systematicMetrics).length > 0) {
      assessments.push(riskLevelAssessor.assessRisk(RiskType.SYSTEMATIC, systematicMetrics));
    }

    return assessments;
  }

  /**
   * 生成风险预警
   */
  private generateRiskAlerts(
    assessments: RiskAssessment[],
    metrics: Record<string, RiskMetric>,
    config: RiskReportConfig
  ): RiskAlert[] {
    const alerts: RiskAlert[] = [];

    // 基于风险评估生成预警
    for (const assessment of assessments) {
      if (assessment.level === RiskLevel.CRITICAL) {
        alerts.push({
          id: this.generateAlertId(),
          level: AlertLevel.CRITICAL,
          type: assessment.type,
          title: `${this.getRiskTypeName(assessment.type)}达到极高水平`,
          message: `${assessment.description}，建议立即采取风险控制措施。`,
          timestamp: new Date(),
          isActive: true,
          threshold: config.alertThresholds[assessment.type],
          currentValue: assessment.score,
          recommendations: assessment.recommendations.slice(0, 3),
        });
      } else if (assessment.level === RiskLevel.HIGH) {
        alerts.push({
          id: this.generateAlertId(),
          level: AlertLevel.DANGER,
          type: assessment.type,
          title: `${this.getRiskTypeName(assessment.type)}偏高`,
          message: `${assessment.description}，建议密切关注并考虑风险控制措施。`,
          timestamp: new Date(),
          isActive: true,
          threshold: config.alertThresholds[assessment.type],
          currentValue: assessment.score,
          recommendations: assessment.recommendations.slice(0, 2),
        });
      }
    }

    // 基于具体指标生成预警
    for (const [metricName, metric] of Object.entries(metrics)) {
      if (metric.threshold && this.isMetricAboveThreshold(metric)) {
        alerts.push({
          id: this.generateAlertId(),
          level: AlertLevel.WARNING,
          type: this.getMetricRiskType(metricName),
          title: `${metric.name}超出阈值`,
          message: `${metric.name}当前值为${metric.value}${metric.unit || ''}，超出预警阈值。`,
          timestamp: new Date(),
          isActive: true,
          threshold: metric.threshold.high,
          currentValue: metric.value,
          recommendations: [`密切监控${metric.name}变化`, '考虑调整投资策略'],
        });
      }
    }

    return alerts.sort((a, b) => this.getAlertPriority(b.level) - this.getAlertPriority(a.level));
  }

  /**
   * 生成风险控制建议
   */
  private async generateRiskRecommendations(
    assessments: RiskAssessment[],
    metrics: Record<string, RiskMetric>,
    overallRisk: { score: number; level: RiskLevel; confidence: number },
    marketData: any[]
  ): Promise<RiskControlRecommendation[]> {
    const recommendations: RiskControlRecommendation[] = [];

    try {
      // 使用风险控制建议器生成建议
      const riskMetrics = await riskMetricsCalculator.calculateRiskMetrics(marketData);
      const comprehensiveRecommendation =
        riskControlRecommender.generateComprehensiveRecommendation(
          riskMetrics,
          marketData,
          undefined, // stressTestResult
          0, // currentPosition
          100000, // portfolioValue
          0.02 // riskTolerance
        );

      // 将综合建议转换为基础建议格式
      const convertedRecommendations = this.convertComprehensiveToBasicRecommendations(
        comprehensiveRecommendation
      );
      recommendations.push(...convertedRecommendations);
    } catch (error) {
      console.warn('风险控制建议生成失败:', error);

      // 提供基础建议
      recommendations.push(...this.generateBasicRecommendations(overallRisk.level));
    }

    return recommendations;
  }

  /**
   * 执行场景分析
   */
  private async performScenarioAnalysis(
    symbol: string,
    marketData: any
  ): Promise<RiskScenarioResult[]> {
    try {
      const stressTestResults = await riskScenarioAnalyzer.runComprehensiveStressTest(marketData);
      // 将压力测试结果转换为场景分析结果格式
      return this.convertStressTestToScenarioResults(stressTestResults);
    } catch (error) {
      console.warn('场景分析失败:', error);
      return [];
    }
  }

  /**
   * 获取历史对比数据
   */
  private async getHistoricalComparison(
    symbol: string,
    currentScore: number
  ): Promise<RiskReport['historicalComparison']> {
    try {
      // 这里应该从数据库获取历史数据
      // 暂时返回模拟数据
      const previousScore = currentScore + (Math.random() - 0.5) * 20;
      const changePercentage = ((currentScore - previousScore) / previousScore) * 100;

      let trend: 'improving' | 'deteriorating' | 'stable';
      if (Math.abs(changePercentage) < 5) {
        trend = 'stable';
      } else if (changePercentage > 0) {
        trend = 'deteriorating';
      } else {
        trend = 'improving';
      }

      return {
        previousScore: Math.round(previousScore),
        trend,
        changePercentage: Math.round(changePercentage * 100) / 100,
        comparisonPeriod: '30天前',
      };
    } catch (error) {
      console.warn('历史对比数据获取失败:', error);
      return undefined;
    }
  }

  /**
   * 生成综合风险摘要
   */
  private generateOverallRiskSummary(
    overallRisk: { score: number; level: RiskLevel; confidence: number },
    assessments: RiskAssessment[]
  ): string {
    const levelNames = {
      [RiskLevel.LOW]: '较低',
      [RiskLevel.MEDIUM]: '中等',
      [RiskLevel.HIGH]: '较高',
      [RiskLevel.CRITICAL]: '极高',
    };

    const highRiskTypes = assessments
      .filter((a) => a.level === RiskLevel.HIGH || a.level === RiskLevel.CRITICAL)
      .map((a) => this.getRiskTypeName(a.type));

    let summary = `综合风险等级为${levelNames[overallRisk.level]}（评分：${
      overallRisk.score
    }），评估置信度为${overallRisk.confidence}%。`;

    if (highRiskTypes.length > 0) {
      summary += `主要风险来源包括：${highRiskTypes.join('、')}。`;
    }

    switch (overallRisk.level) {
      case RiskLevel.LOW:
        summary += '当前风险水平可控，可适当增加投资仓位。';
        break;
      case RiskLevel.MEDIUM:
        summary += '风险水平适中，建议保持谨慎并密切监控。';
        break;
      case RiskLevel.HIGH:
        summary += '风险水平偏高，建议降低仓位并实施风险控制措施。';
        break;
      case RiskLevel.CRITICAL:
        summary += '风险水平极高，强烈建议暂停投资或大幅减仓。';
        break;
    }

    return summary;
  }

  /**
   * 选择关键指标
   */
  private selectKeyMetrics(metrics: Record<string, RiskMetric>): RiskMetric[] {
    // 按重要性排序并选择前10个指标
    const sortedMetrics = Object.values(metrics).sort((a, b) => {
      const aImportance = this.getMetricImportance(a.name);
      const bImportance = this.getMetricImportance(b.name);
      return bImportance - aImportance;
    });

    return sortedMetrics.slice(0, 10);
  }

  /**
   * 评估数据质量
   */
  private assessDataQuality(metrics: Record<string, RiskMetric>): number {
    const totalMetrics = Object.keys(metrics).length;
    const validMetrics = Object.values(metrics).filter(
      (m) => m.value !== null && m.value !== undefined && !isNaN(m.value)
    ).length;

    return Math.round((validMetrics / Math.max(1, totalMetrics)) * 100);
  }

  // 辅助方法
  private getDefaultConfig(): RiskReportConfig {
    return {
      includeScenarioAnalysis: true,
      includeHistoricalComparison: true,
      alertThresholds: {
        [RiskType.MARKET]: 70,
        [RiskType.LIQUIDITY]: 60,
        [RiskType.CREDIT]: 75,
        [RiskType.OPERATIONAL]: 80,
        [RiskType.SYSTEMATIC]: 70,
      },
      confidenceThreshold: 70,
      timeHorizon: '1m',
    };
  }

  private generateReportId(): string {
    return `risk_report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private flattenRiskMetrics(riskMetrics: any): Record<string, number> {
    const flattened: Record<string, number> = {};

    const flatten = (obj: any, prefix = '') => {
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'number') {
          flattened[prefix ? `${prefix}_${key}` : key] = value;
        } else if (typeof value === 'object' && value !== null) {
          flatten(value, prefix ? `${prefix}_${key}` : key);
        }
      }
    };

    flatten(riskMetrics);
    return flattened;
  }

  private formatMetrics(
    rawMetrics: Record<string, number>,
    riskType: RiskType
  ): Record<string, RiskMetric> {
    const formatted: Record<string, RiskMetric> = {};

    for (const [key, value] of Object.entries(rawMetrics)) {
      formatted[key] = {
        name: this.getMetricDisplayName(key),
        value,
        unit: this.getMetricUnit(key),
        description: this.getMetricDescription(key),
        threshold: this.getMetricThreshold(key),
        trend: this.getMetricTrend(key, value),
      };
    }

    return formatted;
  }

  private extractMetricsByType(
    metrics: Record<string, RiskMetric>,
    riskType: RiskType
  ): Record<string, number> {
    const extracted: Record<string, number> = {};

    for (const [key, metric] of Object.entries(metrics)) {
      if (this.getMetricRiskType(key) === riskType) {
        extracted[key] = metric.value;
      }
    }

    return extracted;
  }

  private getRiskTypeName(riskType: RiskType): string {
    const names = {
      [RiskType.MARKET]: '市场风险',
      [RiskType.LIQUIDITY]: '流动性风险',
      [RiskType.CREDIT]: '信用风险',
      [RiskType.OPERATIONAL]: '操作风险',
      [RiskType.SYSTEMATIC]: '系统性风险',
    };
    return names[riskType] || '未知风险';
  }

  private getMetricRiskType(metricName: string): RiskType {
    if (['volatility', 'beta', 'var', 'maxDrawdown'].includes(metricName)) {
      return RiskType.MARKET;
    }
    if (['volume', 'spread', 'amihudRatio'].includes(metricName)) {
      return RiskType.LIQUIDITY;
    }
    if (['debtRatio', 'currentRatio', 'interestCoverage'].includes(metricName)) {
      return RiskType.CREDIT;
    }
    return RiskType.MARKET;
  }

  private getMetricDisplayName(key: string): string {
    const names: Record<string, string> = {
      volatility: '历史波动率',
      beta: 'Beta系数',
      var: '风险价值(VaR)',
      maxDrawdown: '最大回撤',
      volume: '平均交易量',
      spread: '买卖价差',
      amihudRatio: 'Amihud比率',
      debtRatio: '债务比率',
      currentRatio: '流动比率',
      interestCoverage: '利息覆盖率',
    };
    return names[key] || key;
  }

  private getMetricUnit(key: string): string {
    const units: Record<string, string> = {
      volatility: '%',
      beta: '',
      var: '%',
      maxDrawdown: '%',
      volume: '股',
      spread: '%',
      amihudRatio: '',
      debtRatio: '%',
      currentRatio: '',
      interestCoverage: '倍',
    };
    return units[key] || '';
  }

  private getMetricDescription(key: string): string {
    const descriptions: Record<string, string> = {
      volatility: '价格波动的标准差，反映价格变动的不确定性',
      beta: '相对于市场的系统性风险系数',
      var: '在给定置信水平下的最大可能损失',
      maxDrawdown: '从峰值到谷值的最大跌幅',
      volume: '日均交易量，反映流动性水平',
      spread: '买卖价差，反映交易成本',
      amihudRatio: '价格冲击指标，反映流动性风险',
      debtRatio: '总债务占总资产的比例',
      currentRatio: '流动资产与流动负债的比率',
      interestCoverage: '息税前利润与利息费用的比率',
    };
    return descriptions[key] || '风险相关指标';
  }

  private getMetricThreshold(key: string): RiskMetric['threshold'] {
    const thresholds: Record<string, RiskMetric['threshold']> = {
      volatility: { low: 0.15, medium: 0.25, high: 0.4 },
      beta: { low: 0.8, medium: 1.2, high: 1.8 },
      var: { low: 0.05, medium: 0.1, high: 0.2 },
      maxDrawdown: { low: 0.1, medium: 0.2, high: 0.3 },
      volume: { low: 100000, medium: 500000, high: 1000000 },
      spread: { low: 0.001, medium: 0.005, high: 0.01 },
      debtRatio: { low: 0.3, medium: 0.5, high: 0.7 },
      currentRatio: { low: 1.0, medium: 1.5, high: 2.0 },
    };
    return thresholds[key];
  }

  private getMetricTrend(key: string, value: number): RiskMetric['trend'] {
    // 这里应该基于历史数据计算趋势，暂时返回随机值
    const trends: RiskMetric['trend'][] = ['increasing', 'decreasing', 'stable'];
    return trends[Math.floor(Math.random() * trends.length)];
  }

  private getMetricImportance(metricName: string): number {
    const importance: Record<string, number> = {
      volatility: 10,
      beta: 9,
      var: 10,
      maxDrawdown: 9,
      volume: 7,
      spread: 6,
      amihudRatio: 6,
      debtRatio: 8,
      currentRatio: 7,
      interestCoverage: 8,
    };
    return importance[metricName] || 5;
  }

  private isMetricAboveThreshold(metric: RiskMetric): boolean {
    if (!metric.threshold) return false;
    return metric.value > metric.threshold.high;
  }

  private getAlertPriority(level: AlertLevel): number {
    const priorities = {
      [AlertLevel.CRITICAL]: 4,
      [AlertLevel.DANGER]: 3,
      [AlertLevel.WARNING]: 2,
      [AlertLevel.INFO]: 1,
    };
    return priorities[level] || 0;
  }

  private convertStressTestToScenarioResults(stressTestResults: any): RiskScenarioResult[] {
    const scenarios: RiskScenarioResult[] = [];

    // 如果有市场下跌场景
    if (stressTestResults.market_decline_scenarios) {
      stressTestResults.market_decline_scenarios.forEach((scenario: any, index: number) => {
        scenarios.push({
          scenario: `市场下跌 ${(index + 1) * 10}%`,
          probability: 0.1 - index * 0.02, // 递减概率
          potentialLoss: scenario.estimated_loss || 0,
          potentialLossPercentage: scenario.loss_percentage || 0,
          timeHorizon: '1个月',
          description: `在市场下跌 ${(index + 1) * 10}% 的情况下的预期损失`,
          mitigationStrategies: ['分散投资', '设置止损', '对冲策略'],
        });
      });
    }

    // 如果没有具体场景，提供默认场景
    if (scenarios.length === 0) {
      scenarios.push({
        scenario: '市场压力测试',
        probability: 0.2,
        potentialLoss: 10000,
        potentialLossPercentage: 10,
        timeHorizon: '1个月',
        description: '基于历史数据的压力测试结果',
        mitigationStrategies: ['风险控制', '资产配置优化'],
      });
    }

    return scenarios;
  }

  private convertComprehensiveToBasicRecommendations(
    comprehensive: any
  ): RiskControlRecommendation[] {
    const recommendations: RiskControlRecommendation[] = [];

    // 转换仓位建议
    if (comprehensive.position_recommendation) {
      recommendations.push({
        category: 'position',
        priority: 'high',
        action: `调整仓位至 ${comprehensive.position_recommendation.recommended_position || 0}%`,
        description:
          comprehensive.position_recommendation.reasoning || '基于风险评估的仓位调整建议',
        expectedImpact: '降低投资组合风险',
        implementation: ['逐步调整仓位', '监控市场变化'],
      });
    }

    // 转换止损建议
    if (comprehensive.stop_loss_recommendation) {
      recommendations.push({
        category: 'monitoring',
        priority: 'high',
        action: `设置止损位`,
        description: comprehensive.stop_loss_recommendation.reasoning || '基于技术分析的止损建议',
        expectedImpact: '限制最大损失',
        implementation: ['设置止损订单', '定期调整止损位'],
      });
    }

    // 转换对冲建议
    if (comprehensive.hedging_recommendation) {
      recommendations.push({
        category: 'hedging',
        priority: 'medium',
        action: '考虑对冲策略',
        description: comprehensive.hedging_recommendation.reasoning || '基于风险评估的对冲建议',
        expectedImpact: '降低系统性风险',
        implementation: ['评估对冲成本', '选择合适的对冲工具'],
      });
    }

    return recommendations;
  }

  private generateBasicRecommendations(level: RiskLevel): RiskControlRecommendation[] {
    const recommendations: RiskControlRecommendation[] = [];

    switch (level) {
      case RiskLevel.CRITICAL:
        recommendations.push({
          category: 'position',
          priority: 'high',
          action: '立即减仓',
          description: '风险水平极高，建议立即大幅减少仓位',
          expectedImpact: '显著降低潜在损失',
          implementation: ['减仓至20%以下', '设置紧急止损', '暂停新增投资'],
        });
        break;

      case RiskLevel.HIGH:
        recommendations.push({
          category: 'position',
          priority: 'high',
          action: '降低仓位',
          description: '风险水平偏高，建议适度降低仓位',
          expectedImpact: '降低投资风险暴露',
          implementation: ['减仓至50%以下', '设置止损位', '加强监控'],
        });
        break;

      case RiskLevel.MEDIUM:
        recommendations.push({
          category: 'monitoring',
          priority: 'medium',
          action: '密切监控',
          description: '风险水平适中，需要密切监控变化',
          expectedImpact: '及时发现风险变化',
          implementation: ['每日监控关键指标', '设置预警阈值', '准备应急预案'],
        });
        break;
    }

    return recommendations;
  }
}

// 导出单例实例
export const riskReportGenerator = new RiskReportGenerator();
