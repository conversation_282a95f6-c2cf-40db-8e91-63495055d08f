/**
 * SEO 中间件
 * 处理请求级别的 SEO 逻辑，包括语言检测、重定向和响应头设置
 */

import { NextRequest, NextResponse } from 'next/server';
import { SupportedLocale, LanguageDetectionResult } from '@/types/seo';

import { LanguageDetector, LANGUAGE_DETECTION_CONFIG, HREFLANG_CONFIG } from './i18n-seo';
import { applySEOSecurityHeaders } from './security-headers';
import { handleVerificationFile } from './verification-files';

/**
 * SEO 中间件配置
 */
interface SEOMiddlewareConfig {
  enableLanguageDetection: boolean;
  enableSEOHeaders: boolean;
  enableRedirects: boolean;
  defaultLocale: SupportedLocale;
  supportedLocales: SupportedLocale[];
}

const DEFAULT_SEO_CONFIG: SEOMiddlewareConfig = {
  enableLanguageDetection: true,
  enableSEOHeaders: true,
  enableRedirects: true,
  defaultLocale: 'zh',
  supportedLocales: ['zh', 'en'],
};

/**
 * 检测用户语言偏好和路径信息
 */
function detectUserLanguage(request: NextRequest): LanguageDetectionResult {
  const pathname = request.nextUrl.pathname;
  const acceptLanguage = request.headers.get('accept-language') || undefined;
  const cookieValue = request.cookies.get(LANGUAGE_DETECTION_CONFIG.cookieConfig.name)?.value;

  // 使用新的语言检测器
  const detection = LanguageDetector.detect({
    pathname,
    acceptLanguage,
    cookieValue,
  });

  // 检查是否需要重定向
  const shouldRedirect = shouldRedirectForLanguage(pathname, detection.locale);
  let redirectUrl: string | undefined;

  if (shouldRedirect) {
    const url = request.nextUrl.clone();
    const targetPrefix = HREFLANG_CONFIG.urlPatterns[detection.locale];
    url.pathname = `${targetPrefix}${detection.cleanPath === '/' ? '' : detection.cleanPath}`;
    redirectUrl = url.toString();
  }

  return {
    locale: detection.locale,
    cleanPath: detection.cleanPath,
    shouldRedirect,
    redirectUrl,
  };
}

/**
 * 检查是否需要语言重定向
 */
function shouldRedirectForLanguage(pathname: string, detectedLocale: SupportedLocale): boolean {
  const { locale: pathLocale } = LanguageDetector.detectFromPath(pathname);

  // 如果路径中的语言与检测到的语言一致，不需要重定向
  if (pathLocale === detectedLocale) {
    return false;
  }

  // 如果检测到英文但路径是中文（默认），需要重定向到英文路径
  if (detectedLocale === 'en' && pathLocale === 'zh') {
    return true;
  }

  // 如果检测到中文但路径是英文，需要重定向到中文路径
  if (detectedLocale === 'zh' && pathLocale === 'en') {
    return true;
  }

  return false;
}

/**
 * 生成 SEO 相关的响应头
 */
function generateSEOHeaders(
  request: NextRequest,
  locale: SupportedLocale,
  cleanPath: string
): Record<string, string> {
  const headers: Record<string, string> = {};
  const pathname = request.nextUrl.pathname;

  // 内容语言头
  headers['Content-Language'] = HREFLANG_CONFIG.localeToHreflang[locale];

  // 搜索引擎指令
  headers['X-Robots-Tag'] =
    'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1';

  // 安全头（有助于 SEO 和用户体验）
  headers['X-Content-Type-Options'] = 'nosniff';
  headers['X-Frame-Options'] = 'DENY';
  headers['X-XSS-Protection'] = '1; mode=block';
  headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';

  // 缓存控制策略
  if (pathname.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$/)) {
    // 静态资源长期缓存
    headers['Cache-Control'] = 'public, max-age=31536000, immutable';
  } else if (cleanPath === '/' || pathname === '/en') {
    // 首页适中缓存
    headers['Cache-Control'] = 'public, max-age=3600, s-maxage=3600, stale-while-revalidate=86400';
  } else {
    // 其他页面短期缓存
    headers['Cache-Control'] = 'public, max-age=1800, s-maxage=1800, stale-while-revalidate=3600';
  }

  // 预加载关键资源
  if (cleanPath === '/') {
    const preloadLinks = [
      '</tradingAgent.png>; rel=preload; as=image',
      '</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin',
    ];
    headers['Link'] = preloadLinks.join(', ');
  }

  // 语言相关的 Vary 头
  headers['Vary'] = 'Accept-Language, Cookie';

  // 添加自定义 SEO 头用于调试
  if (process.env.NODE_ENV === 'development') {
    headers['X-SEO-Locale'] = locale;
    headers['X-SEO-Clean-Path'] = cleanPath;
  }

  return headers;
}

/**
 * 处理 sitemap.xml 和其他 SEO 文件请求
 */
function handleSEOFileRequests(request: NextRequest): NextResponse | null {
  const pathname = request.nextUrl.pathname;

  if (pathname === '/sitemap.xml') {
    // 重定向到 API 路由
    const url = request.nextUrl.clone();
    url.pathname = '/api/sitemap';
    return NextResponse.rewrite(url);
  }

  if (pathname === '/robots.txt') {
    // 重定向到 API 路由
    const url = request.nextUrl.clone();
    url.pathname = '/api/robots';
    return NextResponse.rewrite(url);
  }

  // 处理搜索引擎验证文件
  const verificationFileMatch = pathname.match(/^\/([^\/]+\.(html|xml))$/);
  if (verificationFileMatch) {
    const filename = verificationFileMatch[1];
    const verificationResponse = handleVerificationFile(filename);
    if (verificationResponse) {
      return verificationResponse;
    }
  }

  return null;
}

/**
 * 处理 SEO 相关的重定向
 */
function handleSEORedirects(request: NextRequest): NextResponse | null {
  const pathname = request.nextUrl.pathname;
  const url = request.nextUrl.clone();

  // 处理尾部斜杠
  if (pathname.length > 1 && pathname.endsWith('/')) {
    url.pathname = pathname.slice(0, -1);
    return NextResponse.redirect(url, 301);
  }

  // 处理大小写重定向
  if (pathname !== pathname.toLowerCase()) {
    url.pathname = pathname.toLowerCase();
    return NextResponse.redirect(url, 301);
  }

  // 处理旧路径重定向（如果有的话）
  const redirects: Record<string, string> = {
    '/old-analysis': '/analysis',
    '/old-tasks': '/tasks',
    // 添加更多重定向规则
  };

  if (redirects[pathname]) {
    url.pathname = redirects[pathname];
    return NextResponse.redirect(url, 301);
  }

  return null;
}

/**
 * 主要的 SEO 中间件函数
 */
export function seoMiddleware(
  request: NextRequest,
  config: Partial<SEOMiddlewareConfig> = {}
): NextResponse {
  const seoConfig = { ...DEFAULT_SEO_CONFIG, ...config };

  try {
    // 1. 处理 SEO 文件请求（sitemap、robots.txt、验证文件等）
    const seoFileResponse = handleSEOFileRequests(request);
    if (seoFileResponse) {
      return applySEOSecurityHeaders(seoFileResponse, request);
    }

    // 2. 处理 SEO 重定向
    if (seoConfig.enableRedirects) {
      const redirectResponse = handleSEORedirects(request);
      if (redirectResponse) {
        return redirectResponse;
      }
    }

    // 3. 检测用户语言和处理重定向
    const languageResult = seoConfig.enableLanguageDetection
      ? detectUserLanguage(request)
      : {
          locale: seoConfig.defaultLocale,
          cleanPath: request.nextUrl.pathname,
          shouldRedirect: false,
        };

    // 4. 处理语言重定向
    if (seoConfig.enableRedirects && languageResult.shouldRedirect && languageResult.redirectUrl) {
      const response = NextResponse.redirect(languageResult.redirectUrl);

      // 设置语言 Cookie
      response.cookies.set(LANGUAGE_DETECTION_CONFIG.cookieConfig.name, languageResult.locale, {
        maxAge: LANGUAGE_DETECTION_CONFIG.cookieConfig.maxAge,
        path: LANGUAGE_DETECTION_CONFIG.cookieConfig.path,
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: LANGUAGE_DETECTION_CONFIG.cookieConfig.sameSite,
      });

      return applySEOSecurityHeaders(response, request);
    }

    // 5. 创建响应并添加 SEO 头
    const response = NextResponse.next();

    // 设置或更新语言 Cookie
    const currentCookie = request.cookies.get(LANGUAGE_DETECTION_CONFIG.cookieConfig.name);
    if (!currentCookie || currentCookie.value !== languageResult.locale) {
      response.cookies.set(LANGUAGE_DETECTION_CONFIG.cookieConfig.name, languageResult.locale, {
        maxAge: LANGUAGE_DETECTION_CONFIG.cookieConfig.maxAge,
        path: LANGUAGE_DETECTION_CONFIG.cookieConfig.path,
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: LANGUAGE_DETECTION_CONFIG.cookieConfig.sameSite,
      });
    }

    // 添加 SEO 响应头
    if (seoConfig.enableSEOHeaders) {
      const seoHeaders = generateSEOHeaders(
        request,
        languageResult.locale,
        languageResult.cleanPath
      );
      Object.entries(seoHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }

    // 应用安全头
    return applySEOSecurityHeaders(response, request);
  } catch (error) {
    console.error('SEO Middleware Error:', error);

    // 发生错误时返回基本响应
    const response = NextResponse.next();
    response.headers.set('X-SEO-Error', 'true');
    return response;
  }
}

/**
 * 创建带有自定义配置的 SEO 中间件
 */
export function createSEOMiddleware(config: Partial<SEOMiddlewareConfig>) {
  return (request: NextRequest) => seoMiddleware(request, config);
}

/**
 * 检查路径是否需要 SEO 处理
 */
export function shouldApplySEO(pathname: string): boolean {
  // 排除 API 路由、静态资源等
  const excludePatterns = [
    /^\/api\//,
    /^\/\_next\//,
    /^\/favicon\.ico$/,
    /\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$/,
  ];

  return !excludePatterns.some((pattern) => pattern.test(pathname));
}

/**
 * 获取页面的语言设置
 */
export function getPageLocale(pathname: string): SupportedLocale {
  const { locale } = LanguageDetector.detectFromPath(pathname);
  return locale;
}

/**
 * 清理路径（移除语言前缀）
 */
export function cleanPathname(pathname: string): string {
  const { cleanPath } = LanguageDetector.detectFromPath(pathname);
  return cleanPath;
}

/**
 * 生成语言切换 URL
 */
export function generateLanguageSwitchUrl(
  currentPath: string,
  targetLocale: SupportedLocale
): string {
  const { cleanPath } = LanguageDetector.detectFromPath(currentPath);
  const targetPrefix = HREFLANG_CONFIG.urlPatterns[targetLocale];
  return `${targetPrefix}${cleanPath === '/' ? '' : cleanPath}`;
}

/**
 * 检查路径是否为多语言路径
 */
export function isMultilingualPath(pathname: string): boolean {
  return Object.values(HREFLANG_CONFIG.urlPatterns).some(
    (prefix) => prefix && pathname.startsWith(prefix)
  );
}

/**
 * 获取用户偏好语言（从多个来源）
 */
export function getUserPreferredLocale(request: NextRequest): SupportedLocale {
  const cookieValue = request.cookies.get(LANGUAGE_DETECTION_CONFIG.cookieConfig.name)?.value;
  const acceptLanguage = request.headers.get('accept-language') || undefined;

  // 优先使用 Cookie 中的设置
  const cookieLocale = LanguageDetector.detectFromCookie(cookieValue);
  if (cookieLocale) {
    return cookieLocale;
  }

  // 其次使用浏览器语言
  return LanguageDetector.detectFromHeaders(acceptLanguage);
}
