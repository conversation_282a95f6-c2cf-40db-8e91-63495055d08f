'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface AnalysisHistoryItem {
  workflow_id: string;
  ticker: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  current_stage: string;
  progress: number;
  created_at: string;
  completed_at?: string;
  duration_seconds?: number;
  report_count: number;
  error_count: number;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface AnalysisHistoryListProps {
  data: AnalysisHistoryItem[];
  viewMode: 'card' | 'list';
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  loading?: boolean;
  comparisonMode?: boolean;
  onComparisonSelect?: (ids: string[]) => void;
}

// 状态配置
const STATUS_CONFIG = {
  pending: {
    label: '待处理',
    color: 'default',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
  },
  running: {
    label: '运行中',
    color: 'secondary',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
  },
  completed: {
    label: '已完成',
    color: 'success',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
  },
  failed: { label: '失败', color: 'destructive', bgColor: 'bg-red-100', textColor: 'text-red-800' },
  cancelled: {
    label: '已取消',
    color: 'outline',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-800',
  },
} as const;

export function AnalysisHistoryList({
  data,
  viewMode,
  pagination,
  onPageChange,
  loading = false,
  comparisonMode = false,
  onComparisonSelect,
}: AnalysisHistoryListProps) {
  const router = useRouter();
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // 格式化持续时间
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';

    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  };

  // 处理项目点击
  const handleItemClick = (workflowId: string) => {
    if (comparisonMode) {
      handleSelectItem(workflowId, {} as any);
    } else {
      router.push(`/analysis/${workflowId}`);
    }
  };

  // 处理选择
  const handleSelectItem = (workflowId: string, event: React.MouseEvent) => {
    event?.stopPropagation();
    const newSelectedItems = selectedItems.includes(workflowId)
      ? selectedItems.filter((id) => id !== workflowId)
      : [...selectedItems, workflowId];

    setSelectedItems(newSelectedItems);

    if (comparisonMode && onComparisonSelect) {
      onComparisonSelect(newSelectedItems);
    }
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(data.map((item) => item.workflow_id));
    }
  };

  // 渲染卡片视图
  const renderCardView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data.map((item) => {
        const statusConfig = STATUS_CONFIG[item.status];
        const isSelected = selectedItems.includes(item.workflow_id);

        return (
          <div
            key={item.workflow_id}
            className="cursor-pointer"
            onClick={() => handleItemClick(item.workflow_id)}
          >
            <Card
              className={`p-6 transition-all hover:shadow-lg ${
                isSelected ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {/* 卡片头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => handleSelectItem(item.workflow_id, e as any)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <span className="font-semibold text-lg text-blue-600">{item.ticker}</span>
                  </div>
                  <h3 className="font-medium text-gray-900 line-clamp-2">{item.title}</h3>
                </div>
                <Badge
                  variant={statusConfig.color}
                  className={`${statusConfig.bgColor} ${statusConfig.textColor}`}
                >
                  {statusConfig.label}
                </Badge>
              </div>

              {/* 进度条 */}
              {item.status === 'running' && (
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{item.current_stage}</span>
                    <span>{item.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${item.progress}%` }}
                    />
                  </div>
                </div>
              )}

              {/* 统计信息 */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="text-gray-500">报告数量:</span>
                  <span className="ml-1 font-medium">{item.report_count}</span>
                </div>
                <div>
                  <span className="text-gray-500">错误数量:</span>
                  <span
                    className={`ml-1 font-medium ${
                      item.error_count > 0 ? 'text-red-600' : 'text-gray-900'
                    }`}
                  >
                    {item.error_count}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">用时:</span>
                  <span className="ml-1 font-medium">{formatDuration(item.duration_seconds)}</span>
                </div>
                <div>
                  <span className="text-gray-500">创建:</span>
                  <span className="ml-1 font-medium">
                    {format(new Date(item.created_at), 'MM-dd HH:mm', { locale: zhCN })}
                  </span>
                </div>
              </div>

              {/* 完成时间 */}
              {item.completed_at && (
                <div className="text-sm text-gray-500">
                  完成于:{' '}
                  {format(new Date(item.completed_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                </div>
              )}
            </Card>
          </div>
        );
      })}
    </div>
  );

  // 渲染列表视图
  const renderListView = () => (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedItems.length === data.length && data.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                股票/标题
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                进度
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                报告数
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用时
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item) => {
              const statusConfig = STATUS_CONFIG[item.status];
              const isSelected = selectedItems.includes(item.workflow_id);

              return (
                <tr
                  key={item.workflow_id}
                  className={`hover:bg-gray-50 cursor-pointer ${isSelected ? 'bg-blue-50' : ''}`}
                  onClick={() => handleItemClick(item.workflow_id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => handleSelectItem(item.workflow_id, e as any)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-blue-600">{item.ticker}</div>
                      <div className="text-sm text-gray-500 max-w-xs truncate">{item.title}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge
                      variant={statusConfig.color}
                      className={`${statusConfig.bgColor} ${statusConfig.textColor}`}
                    >
                      {statusConfig.label}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {item.status === 'running' ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${item.progress}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600">{item.progress}%</span>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center space-x-2">
                      <span>{item.report_count}</span>
                      {item.error_count > 0 && (
                        <span className="text-red-600">({item.error_count} 错误)</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDuration(item.duration_seconds)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {format(new Date(item.created_at), 'MM-dd HH:mm', { locale: zhCN })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleItemClick(item.workflow_id);
                      }}
                    >
                      查看详情
                    </Button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </Card>
  );

  // 渲染分页
  const renderPagination = () => {
    if (pagination.totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const currentPage = pagination.page;
    const totalPages = pagination.totalPages;

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant={i === currentPage ? 'primary' : 'secondary'}
          size="sm"
          onClick={() => onPageChange(i)}
          disabled={loading}
        >
          {i}
        </Button>
      );
    }

    return (
      <div className="flex items-center justify-between mt-6">
        <div className="text-sm text-gray-700">
          显示第 {(currentPage - 1) * pagination.limit + 1} -{' '}
          {Math.min(currentPage * pagination.limit, pagination.total)} 条， 共 {pagination.total}{' '}
          条记录
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1 || loading}
          >
            上一页
          </Button>

          {startPage > 1 && (
            <>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => onPageChange(1)}
                disabled={loading}
              >
                1
              </Button>
              {startPage > 2 && <span className="text-gray-500">...</span>}
            </>
          )}

          {pages}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="text-gray-500">...</span>}
              <Button
                variant="secondary"
                size="sm"
                onClick={() => onPageChange(totalPages)}
                disabled={loading}
              >
                {totalPages}
              </Button>
            </>
          )}

          <Button
            variant="secondary"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages || loading}
          >
            下一页
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* 批量操作栏 */}
      {selectedItems.length > 0 && (
        <Card className="p-4 mb-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">已选择 {selectedItems.length} 项</span>
            <div className="flex items-center space-x-2">
              <Button variant="secondary" size="sm">
                批量导出
              </Button>
              <Button variant="secondary" size="sm">
                批量删除
              </Button>
              <Button variant="secondary" size="sm" onClick={() => setSelectedItems([])}>
                取消选择
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      )}

      {/* 列表内容 */}
      {!loading && (
        <>
          {viewMode === 'card' ? renderCardView() : renderListView()}
          {renderPagination()}
        </>
      )}
    </div>
  );
}
