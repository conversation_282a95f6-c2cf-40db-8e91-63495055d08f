/**
 * 增强的结构化数据生成器
 * 专门为金融服务和多智能体交易系统设计
 */

import {
  OrganizationSchema,
  WebsiteSchema,
  SoftwareApplicationSchema,
  BreadcrumbSchema,
  SupportedLocale,
  PageType,
} from '@/types/seo';
import { SEO_CONSTANTS, STRUCTURED_DATA_TEMPLATES } from './config';
import { SEOErrorHandler } from './error-handler';

// 金融服务相关的结构化数据类型
export interface FinancialServiceSchema {
  '@context': 'https://schema.org';
  '@type': 'FinancialProduct';
  name: string;
  description: string;
  provider: OrganizationSchema;
  category: string;
  feesAndCommissionsSpecification?: string;
  interestRate?: string;
  annualPercentageRate?: string;
}

export interface InvestmentServiceSchema {
  '@context': 'https://schema.org';
  '@type': 'Service';
  name: string;
  description: string;
  provider: OrganizationSchema;
  serviceType: 'Investment Advisory' | 'Portfolio Management' | 'Trading Platform';
  areaServed: string[];
  availableChannel: string[];
}

export interface TradingPlatformSchema {
  '@context': 'https://schema.org';
  '@type': 'SoftwareApplication';
  name: string;
  description: string;
  applicationCategory: 'FinanceApplication';
  operatingSystem: string[];
  softwareVersion?: string;
  releaseNotes?: string;
  screenshot?: string[];
  featureList?: string[];
  requirements?: string;
  downloadUrl?: string;
  installUrl?: string;
}

export interface AnalysisReportSchema {
  '@context': 'https://schema.org';
  '@type': 'Report';
  name: string;
  description: string;
  author: OrganizationSchema;
  datePublished: string;
  dateModified?: string;
  about: string;
  abstract: string;
  keywords: string[];
  inLanguage: string;
}

export interface StockAnalysisSchema {
  '@context': 'https://schema.org';
  '@type': 'AnalysisNewsArticle';
  headline: string;
  description: string;
  author: OrganizationSchema;
  datePublished: string;
  dateModified?: string;
  about: {
    '@type': 'Corporation';
    name: string;
    tickerSymbol: string;
  };
  analysisType: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk';
  recommendation?: 'Buy' | 'Sell' | 'Hold';
  targetPrice?: number;
  currency?: string;
}

export interface AIAgentSchema {
  '@context': 'https://schema.org';
  '@type': 'SoftwareAgent';
  name: string;
  description: string;
  creator: OrganizationSchema;
  applicationCategory: 'AI Assistant' | 'Financial Analyst' | 'Trading Bot';
  capabilities: string[];
  programmingLanguage?: string[];
  operatingSystem?: string[];
}

/**
 * 增强的结构化数据生成器类
 */
export class StructuredDataGenerator {
  private locale: SupportedLocale;
  private baseUrl: string;

  constructor(locale: SupportedLocale = 'zh') {
    this.locale = locale;
    this.baseUrl = SEO_CONSTANTS.SITE_URL;
  }

  /**
   * 生成组织信息结构化数据
   */
  generateOrganizationSchema(): OrganizationSchema {
    try {
      return {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: SEO_CONSTANTS.SITE_NAME,
        description:
          this.locale === 'zh'
            ? '多智能体大语言模型金融交易框架，提供专业的AI驱动金融分析和交易决策支持'
            : 'Multi-agent LLM financial trading framework providing professional AI-driven financial analysis and trading decision support',
        url: this.baseUrl,
        logo: `${this.baseUrl}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`,
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          availableLanguage: ['Chinese', 'English'],
        },
        sameAs: [
          'https://github.com/tradingagents',
          'https://twitter.com/tradingagents',
          'https://linkedin.com/company/tradingagents',
        ],
      };
    } catch (error) {
      return SEOErrorHandler.handleStructuredDataError(
        error as Error,
        'OrganizationSchema'
      ) as OrganizationSchema;
    }
  }

  /**
   * 生成金融服务结构化数据
   */
  generateFinancialServiceSchema(): FinancialServiceSchema {
    try {
      return {
        '@context': 'https://schema.org',
        '@type': 'FinancialProduct',
        name:
          this.locale === 'zh' ? 'AI智能交易分析服务' : 'AI Intelligent Trading Analysis Service',
        description:
          this.locale === 'zh'
            ? '基于多智能体大语言模型的专业金融分析服务，提供实时市场分析、风险评估和交易决策支持'
            : 'Professional financial analysis service based on multi-agent LLM, providing real-time market analysis, risk assessment and trading decision support',
        provider: this.generateOrganizationSchema(),
        category: this.locale === 'zh' ? '金融科技服务' : 'FinTech Service',
        feesAndCommissionsSpecification: this.locale === 'zh' ? '免费使用' : 'Free to use',
      };
    } catch (error) {
      console.error('Error generating FinancialServiceSchema:', error);
      return {} as FinancialServiceSchema;
    }
  }

  /**
   * 生成投资服务结构化数据
   */
  generateInvestmentServiceSchema(): InvestmentServiceSchema {
    try {
      return {
        '@context': 'https://schema.org',
        '@type': 'Service',
        name: this.locale === 'zh' ? 'AI投资分析服务' : 'AI Investment Analysis Service',
        description:
          this.locale === 'zh'
            ? '多智能体协作的投资分析服务，包括基本面分析、技术分析、情绪分析和风险管理'
            : 'Multi-agent collaborative investment analysis service including fundamental analysis, technical analysis, sentiment analysis and risk management',
        provider: this.generateOrganizationSchema(),
        serviceType: 'Investment Advisory',
        areaServed: ['China', 'Global'],
        availableChannel: ['Website', 'API', 'Mobile App'],
      };
    } catch (error) {
      console.error('Error generating InvestmentServiceSchema:', error);
      return {} as InvestmentServiceSchema;
    }
  }

  /**
   * 生成交易平台结构化数据
   */
  generateTradingPlatformSchema(): TradingPlatformSchema {
    try {
      return {
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        name: SEO_CONSTANTS.SITE_NAME,
        description:
          this.locale === 'zh'
            ? '基于多智能体大语言模型的金融交易分析平台，提供智能化的市场分析和交易决策支持'
            : 'Financial trading analysis platform based on multi-agent LLM, providing intelligent market analysis and trading decision support',
        applicationCategory: 'FinanceApplication',
        operatingSystem: ['Web Browser', 'Any'],
        softwareVersion: '1.0.0',
        featureList:
          this.locale === 'zh'
            ? [
                '多智能体协作分析',
                '实时市场数据',
                '技术指标分析',
                '基本面分析',
                '情绪分析',
                '风险评估',
                '交易决策支持',
              ]
            : [
                'Multi-agent collaborative analysis',
                'Real-time market data',
                'Technical indicator analysis',
                'Fundamental analysis',
                'Sentiment analysis',
                'Risk assessment',
                'Trading decision support',
              ],
        requirements: 'Modern web browser with JavaScript enabled',
        installUrl: this.baseUrl,
      };
    } catch (error) {
      console.error('Error generating TradingPlatformSchema:', error);
      return {} as TradingPlatformSchema;
    }
  }

  /**
   * 生成分析报告结构化数据
   */
  generateAnalysisReportSchema(
    stockSymbol?: string,
    analysisId?: string,
    publishDate?: string
  ): AnalysisReportSchema {
    try {
      const symbol = stockSymbol || 'STOCK';
      const reportName =
        this.locale === 'zh' ? `${symbol} 智能分析报告` : `${symbol} Intelligent Analysis Report`;

      return {
        '@context': 'https://schema.org',
        '@type': 'Report',
        name: reportName,
        description:
          this.locale === 'zh'
            ? `${symbol} 的多智能体协作分析报告，包含技术分析、基本面分析、情绪分析和风险评估`
            : `Multi-agent collaborative analysis report for ${symbol}, including technical analysis, fundamental analysis, sentiment analysis and risk assessment`,
        author: this.generateOrganizationSchema(),
        datePublished: publishDate || new Date().toISOString(),
        about: symbol,
        abstract:
          this.locale === 'zh'
            ? `基于多智能体大语言模型的 ${symbol} 综合分析报告`
            : `Comprehensive analysis report for ${symbol} based on multi-agent LLM`,
        keywords:
          this.locale === 'zh'
            ? ['股票分析', '技术分析', '基本面分析', '风险评估', 'AI分析', symbol]
            : [
                'stock analysis',
                'technical analysis',
                'fundamental analysis',
                'risk assessment',
                'AI analysis',
                symbol,
              ],
        inLanguage: this.locale === 'zh' ? 'zh-CN' : 'en-US',
      };
    } catch (error) {
      console.error('Error generating AnalysisReportSchema:', error);
      return {} as AnalysisReportSchema;
    }
  }

  /**
   * 生成股票分析结构化数据
   */
  generateStockAnalysisSchema(
    stockSymbol: string,
    analysisType: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk' = 'Technical',
    recommendation?: 'Buy' | 'Sell' | 'Hold',
    targetPrice?: number
  ): StockAnalysisSchema {
    try {
      const headline =
        this.locale === 'zh'
          ? `${stockSymbol} ${this.getAnalysisTypeLabel(analysisType)}分析`
          : `${stockSymbol} ${analysisType} Analysis`;

      return {
        '@context': 'https://schema.org',
        '@type': 'AnalysisNewsArticle',
        headline,
        description:
          this.locale === 'zh'
            ? `基于多智能体AI的 ${stockSymbol} ${this.getAnalysisTypeLabel(analysisType)}分析报告`
            : `Multi-agent AI-based ${analysisType.toLowerCase()} analysis report for ${stockSymbol}`,
        author: this.generateOrganizationSchema(),
        datePublished: new Date().toISOString(),
        about: {
          '@type': 'Corporation',
          name: stockSymbol,
          tickerSymbol: stockSymbol,
        },
        analysisType,
        recommendation,
        targetPrice,
        currency: 'USD',
      };
    } catch (error) {
      console.error('Error generating StockAnalysisSchema:', error);
      return {} as StockAnalysisSchema;
    }
  }

  /**
   * 生成AI智能体结构化数据
   */
  generateAIAgentSchema(
    agentName: string,
    agentType: 'AI Assistant' | 'Financial Analyst' | 'Trading Bot' = 'Financial Analyst'
  ): AIAgentSchema {
    try {
      return {
        '@context': 'https://schema.org',
        '@type': 'SoftwareAgent',
        name: agentName,
        description:
          this.locale === 'zh'
            ? `专业的AI金融分析智能体，具备${agentName}的专业能力`
            : `Professional AI financial analysis agent with ${agentName} expertise`,
        creator: this.generateOrganizationSchema(),
        applicationCategory: agentType,
        capabilities: this.getAgentCapabilities(agentName),
        programmingLanguage: ['Python', 'TypeScript', 'JavaScript'],
        operatingSystem: ['Web Browser', 'Cloud Platform'],
      };
    } catch (error) {
      console.error('Error generating AIAgentSchema:', error);
      return {} as AIAgentSchema;
    }
  }

  /**
   * 生成面包屑导航结构化数据
   */
  generateBreadcrumbSchema(page: PageType, dynamicData?: any): BreadcrumbSchema {
    try {
      const breadcrumbItems = this.getBreadcrumbItems(page, dynamicData);

      return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: breadcrumbItems.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.name,
          item: `${this.baseUrl}${item.url}`,
        })),
      };
    } catch (error) {
      console.error('Error generating BreadcrumbSchema:', error);
      return {} as BreadcrumbSchema;
    }
  }

  /**
   * 获取分析类型标签
   */
  private getAnalysisTypeLabel(type: string): string {
    const labels = {
      zh: {
        Technical: '技术',
        Fundamental: '基本面',
        Sentiment: '情绪',
        Risk: '风险',
      },
      en: {
        Technical: 'Technical',
        Fundamental: 'Fundamental',
        Sentiment: 'Sentiment',
        Risk: 'Risk',
      },
    };

    return labels[this.locale][type as keyof typeof labels.zh] || type;
  }

  /**
   * 获取智能体能力列表
   */
  private getAgentCapabilities(agentName: string): string[] {
    const capabilities = {
      zh: {
        基本面分析师: ['财务报表分析', '行业分析', '宏观经济分析', '估值模型'],
        技术分析师: ['图表分析', '技术指标', '趋势识别', '支撑阻力位'],
        情绪分析师: ['新闻情绪分析', '社交媒体监控', '市场情绪指标', '舆情分析'],
        风险管理师: ['风险评估', '风险控制', '投资组合优化', '压力测试'],
        交易员: ['订单执行', '流动性管理', '交易策略', '市场微观结构'],
      },
      en: {
        'Fundamental Analyst': [
          'Financial Statement Analysis',
          'Industry Analysis',
          'Macroeconomic Analysis',
          'Valuation Models',
        ],
        'Technical Analyst': [
          'Chart Analysis',
          'Technical Indicators',
          'Trend Identification',
          'Support and Resistance',
        ],
        'Sentiment Analyst': [
          'News Sentiment Analysis',
          'Social Media Monitoring',
          'Market Sentiment Indicators',
          'Public Opinion Analysis',
        ],
        'Risk Manager': [
          'Risk Assessment',
          'Risk Control',
          'Portfolio Optimization',
          'Stress Testing',
        ],
        Trader: [
          'Order Execution',
          'Liquidity Management',
          'Trading Strategies',
          'Market Microstructure',
        ],
      },
    } as const;

    const localeCapabilities = capabilities[this.locale];
    const agentCapabilities = localeCapabilities[agentName as keyof typeof localeCapabilities];

    return agentCapabilities || ['AI Analysis', 'Data Processing', 'Decision Support'];
  }

  /**
   * 获取面包屑导航项目
   */
  private getBreadcrumbItems(
    page: PageType,
    dynamicData?: any
  ): Array<{ name: string; url: string }> {
    const homeLabel = this.locale === 'zh' ? '首页' : 'Home';
    const items = [{ name: homeLabel, url: '/' }];

    const pageLabels = {
      zh: {
        analysis: '智能分析',
        tasks: '任务管理',
        messages: '消息查询',
        'create-task': '创建任务',
      },
      en: {
        analysis: 'Analysis',
        tasks: 'Tasks',
        messages: 'Messages',
        'create-task': 'Create Task',
      },
    };

    if (page !== 'home') {
      const pageLabel = pageLabels[this.locale][page] || page;
      const pageUrl = `/${page}`;
      items.push({ name: pageLabel, url: pageUrl });

      // 添加动态数据相关的面包屑
      if (dynamicData) {
        if (dynamicData.analysisId && page === 'analysis') {
          const analysisLabel = this.locale === 'zh' ? '分析详情' : 'Analysis Details';
          items.push({ name: analysisLabel, url: `${pageUrl}/${dynamicData.analysisId}` });
        }
        if (dynamicData.stockSymbol) {
          items.push({
            name: dynamicData.stockSymbol,
            url: `${pageUrl}/${dynamicData.stockSymbol}`,
          });
        }
      }
    }

    return items;
  }
}

/**
 * 导出便捷函数
 */
export const createStructuredDataGenerator = (locale: SupportedLocale = 'zh') => {
  return new StructuredDataGenerator(locale);
};

/**
 * 生成完整的页面结构化数据
 */
export const generatePageStructuredData = (
  page: PageType,
  locale: SupportedLocale = 'zh',
  dynamicData?: any
) => {
  const generator = new StructuredDataGenerator(locale);
  const structuredData: Record<string, any> = {};

  // 基础结构化数据（所有页面）
  structuredData.organization = generator.generateOrganizationSchema();
  structuredData.financialService = generator.generateFinancialServiceSchema();
  structuredData.investmentService = generator.generateInvestmentServiceSchema();

  // 页面特定的结构化数据
  switch (page) {
    case 'home':
      structuredData.website = STRUCTURED_DATA_TEMPLATES.website;
      structuredData.tradingPlatform = generator.generateTradingPlatformSchema();
      break;

    case 'analysis':
      if (dynamicData?.stockSymbol) {
        structuredData.analysisReport = generator.generateAnalysisReportSchema(
          dynamicData.stockSymbol,
          dynamicData.analysisId
        );
        structuredData.stockAnalysis = generator.generateStockAnalysisSchema(
          dynamicData.stockSymbol
        );
      }
      structuredData.breadcrumb = generator.generateBreadcrumbSchema(page, dynamicData);
      break;

    case 'tasks':
    case 'messages':
    case 'create-task':
      structuredData.breadcrumb = generator.generateBreadcrumbSchema(page, dynamicData);
      break;
  }

  return structuredData;
};
