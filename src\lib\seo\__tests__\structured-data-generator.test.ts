/**
 * 结构化数据生成器测试
 */

import {
  StructuredDataGenerator,
  generatePageStructuredData,
  createStructuredDataGenerator,
} from '../structured-data-generator';
import { validateStructuredData } from '../structured-data-validator';

describe('StructuredDataGenerator', () => {
  let generator: StructuredDataGenerator;

  beforeEach(() => {
    generator = new StructuredDataGenerator('zh');
  });

  describe('基础功能测试', () => {
    test('应该创建结构化数据生成器实例', () => {
      expect(generator).toBeInstanceOf(StructuredDataGenerator);
    });

    test('应该使用便捷函数创建生成器', () => {
      const gen = createStructuredDataGenerator('en');
      expect(gen).toBeInstanceOf(StructuredDataGenerator);
    });
  });

  describe('组织信息结构化数据', () => {
    test('应该生成有效的组织信息结构化数据', () => {
      const orgSchema = generator.generateOrganizationSchema();

      expect(orgSchema['@context']).toBe('https://schema.org');
      expect(orgSchema['@type']).toBe('Organization');
      expect(orgSchema.name).toBeDefined();
      expect(orgSchema.description).toBeDefined();
      expect(orgSchema.url).toBeDefined();
      expect(orgSchema.logo).toBeDefined();

      // 验证结构化数据
      const validation = validateStructuredData(orgSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该包含联系信息', () => {
      const orgSchema = generator.generateOrganizationSchema();

      expect(orgSchema.contactPoint).toBeDefined();
      expect(orgSchema.contactPoint?.['@type']).toBe('ContactPoint');
      expect(orgSchema.contactPoint?.availableLanguage).toContain('Chinese');
      expect(orgSchema.contactPoint?.availableLanguage).toContain('English');
    });

    test('应该包含社交媒体链接', () => {
      const orgSchema = generator.generateOrganizationSchema();

      expect(orgSchema.sameAs).toBeDefined();
      expect(Array.isArray(orgSchema.sameAs)).toBe(true);
      expect(orgSchema.sameAs?.length).toBeGreaterThan(0);
    });
  });

  describe('金融服务结构化数据', () => {
    test('应该生成有效的金融服务结构化数据', () => {
      const financialSchema = generator.generateFinancialServiceSchema();

      expect(financialSchema['@context']).toBe('https://schema.org');
      expect(financialSchema['@type']).toBe('FinancialProduct');
      expect(financialSchema.name).toBeDefined();
      expect(financialSchema.description).toBeDefined();
      expect(financialSchema.provider).toBeDefined();
      expect(financialSchema.category).toBeDefined();

      // 验证结构化数据
      const validation = validateStructuredData(financialSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该根据语言生成不同的内容', () => {
      const zhGenerator = new StructuredDataGenerator('zh');
      const enGenerator = new StructuredDataGenerator('en');

      const zhSchema = zhGenerator.generateFinancialServiceSchema();
      const enSchema = enGenerator.generateFinancialServiceSchema();

      expect(zhSchema.name).toContain('智能交易');
      expect(enSchema.name).toContain('Trading Analysis');
    });
  });

  describe('投资服务结构化数据', () => {
    test('应该生成有效的投资服务结构化数据', () => {
      const investmentSchema = generator.generateInvestmentServiceSchema();

      expect(investmentSchema['@context']).toBe('https://schema.org');
      expect(investmentSchema['@type']).toBe('Service');
      expect(investmentSchema.serviceType).toBe('Investment Advisory');
      expect(investmentSchema.areaServed).toContain('China');
      expect(investmentSchema.availableChannel).toContain('Website');

      // 验证结构化数据
      const validation = validateStructuredData(investmentSchema);
      expect(validation.isValid).toBe(true);
    });
  });

  describe('交易平台结构化数据', () => {
    test('应该生成有效的交易平台结构化数据', () => {
      const platformSchema = generator.generateTradingPlatformSchema();

      expect(platformSchema['@context']).toBe('https://schema.org');
      expect(platformSchema['@type']).toBe('SoftwareApplication');
      expect(platformSchema.applicationCategory).toBe('FinanceApplication');
      expect(platformSchema.featureList).toBeDefined();
      expect(Array.isArray(platformSchema.featureList)).toBe(true);

      // 验证结构化数据
      const validation = validateStructuredData(platformSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该包含功能列表', () => {
      const platformSchema = generator.generateTradingPlatformSchema();

      expect(platformSchema.featureList?.length).toBeGreaterThan(0);
      expect(platformSchema.featureList).toContain('多智能体协作分析');
    });
  });

  describe('分析报告结构化数据', () => {
    test('应该生成有效的分析报告结构化数据', () => {
      const reportSchema = generator.generateAnalysisReportSchema('AAPL', 'test-123');

      expect(reportSchema['@context']).toBe('https://schema.org');
      expect(reportSchema['@type']).toBe('Report');
      expect(reportSchema.name).toContain('AAPL');
      expect(reportSchema.datePublished).toBeDefined();
      expect(reportSchema.keywords).toBeDefined();
      expect(Array.isArray(reportSchema.keywords)).toBe(true);

      // 验证结构化数据
      const validation = validateStructuredData(reportSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该使用当前日期作为发布日期', () => {
      const reportSchema = generator.generateAnalysisReportSchema('TSLA');
      const publishDate = new Date(reportSchema.datePublished);
      const now = new Date();

      // 检查日期是否在合理范围内（1分钟内）
      const timeDiff = Math.abs(now.getTime() - publishDate.getTime());
      expect(timeDiff).toBeLessThan(60000); // 60秒
    });
  });

  describe('股票分析结构化数据', () => {
    test('应该生成有效的股票分析结构化数据', () => {
      const stockSchema = generator.generateStockAnalysisSchema('GOOGL', 'Technical', 'Buy', 150.5);

      expect(stockSchema['@context']).toBe('https://schema.org');
      expect(stockSchema['@type']).toBe('AnalysisNewsArticle');
      expect(stockSchema.headline).toContain('GOOGL');
      expect(stockSchema.analysisType).toBe('Technical');
      expect(stockSchema.recommendation).toBe('Buy');
      expect(stockSchema.targetPrice).toBe(150.5);
      expect(stockSchema.about.tickerSymbol).toBe('GOOGL');

      // 验证结构化数据
      const validation = validateStructuredData(stockSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该支持不同的分析类型', () => {
      const types: Array<'Technical' | 'Fundamental' | 'Sentiment' | 'Risk'> = [
        'Technical',
        'Fundamental',
        'Sentiment',
        'Risk',
      ];

      types.forEach((type) => {
        const schema = generator.generateStockAnalysisSchema('MSFT', type);
        expect(schema.analysisType).toBe(type);
      });
    });
  });

  describe('AI智能体结构化数据', () => {
    test('应该生成有效的AI智能体结构化数据', () => {
      const agentSchema = generator.generateAIAgentSchema('基本面分析师', 'Financial Analyst');

      expect(agentSchema['@context']).toBe('https://schema.org');
      expect(agentSchema['@type']).toBe('SoftwareAgent');
      expect(agentSchema.name).toBe('基本面分析师');
      expect(agentSchema.applicationCategory).toBe('Financial Analyst');
      expect(agentSchema.capabilities).toBeDefined();
      expect(Array.isArray(agentSchema.capabilities)).toBe(true);

      // 验证结构化数据
      const validation = validateStructuredData(agentSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该根据智能体类型提供不同的能力', () => {
      const fundamentalAgent = generator.generateAIAgentSchema('基本面分析师');
      const technicalAgent = generator.generateAIAgentSchema('技术分析师');

      expect(fundamentalAgent.capabilities).toContain('财务报表分析');
      expect(technicalAgent.capabilities).toContain('图表分析');
    });
  });

  describe('面包屑导航结构化数据', () => {
    test('应该生成有效的面包屑导航结构化数据', () => {
      const breadcrumbSchema = generator.generateBreadcrumbSchema('analysis', {
        analysisId: 'test-123',
        stockSymbol: 'AAPL',
      });

      expect(breadcrumbSchema['@context']).toBe('https://schema.org');
      expect(breadcrumbSchema['@type']).toBe('BreadcrumbList');
      expect(breadcrumbSchema.itemListElement).toBeDefined();
      expect(Array.isArray(breadcrumbSchema.itemListElement)).toBe(true);
      expect(breadcrumbSchema.itemListElement.length).toBeGreaterThan(1);

      // 验证结构化数据
      const validation = validateStructuredData(breadcrumbSchema);
      expect(validation.isValid).toBe(true);
    });

    test('应该包含正确的面包屑层级', () => {
      const breadcrumbSchema = generator.generateBreadcrumbSchema('tasks');

      expect(breadcrumbSchema.itemListElement[0].name).toBe('首页');
      expect(breadcrumbSchema.itemListElement[0].position).toBe(1);
      expect(breadcrumbSchema.itemListElement[1].name).toBe('任务管理');
      expect(breadcrumbSchema.itemListElement[1].position).toBe(2);
    });
  });

  describe('页面结构化数据生成', () => {
    test('应该为首页生成完整的结构化数据', () => {
      const pageData = generatePageStructuredData('home', 'zh');

      expect(pageData.organization).toBeDefined();
      expect(pageData.website).toBeDefined();
      expect(pageData.tradingPlatform).toBeDefined();
      expect(pageData.financialService).toBeDefined();
      expect(pageData.investmentService).toBeDefined();
    });

    test('应该为分析页面生成特定的结构化数据', () => {
      const pageData = generatePageStructuredData('analysis', 'zh', {
        stockSymbol: 'AAPL',
        analysisId: 'test-123',
      });

      expect(pageData.organization).toBeDefined();
      expect(pageData.analysisReport).toBeDefined();
      expect(pageData.stockAnalysis).toBeDefined();
      expect(pageData.breadcrumb).toBeDefined();
    });

    test('应该为其他页面生成基础结构化数据', () => {
      const pageData = generatePageStructuredData('tasks', 'zh');

      expect(pageData.organization).toBeDefined();
      expect(pageData.financialService).toBeDefined();
      expect(pageData.investmentService).toBeDefined();
      expect(pageData.breadcrumb).toBeDefined();
    });
  });

  describe('错误处理', () => {
    test('应该处理无效的输入数据', () => {
      expect(() => {
        generator.generateAnalysisReportSchema('', '');
      }).not.toThrow();
    });

    test('应该在错误时返回空对象而不是抛出异常', () => {
      const result = generator.generateFinancialServiceSchema();
      expect(typeof result).toBe('object');
    });
  });

  describe('多语言支持', () => {
    test('应该支持中文内容生成', () => {
      const zhGenerator = new StructuredDataGenerator('zh');
      const orgSchema = zhGenerator.generateOrganizationSchema();

      expect(orgSchema.description).toContain('多智能体');
    });

    test('应该支持英文内容生成', () => {
      const enGenerator = new StructuredDataGenerator('en');
      const orgSchema = enGenerator.generateOrganizationSchema();

      expect(orgSchema.description).toContain('Multi-agent');
    });

    test('应该根据语言生成不同的面包屑标签', () => {
      const zhGenerator = new StructuredDataGenerator('zh');
      const enGenerator = new StructuredDataGenerator('en');

      const zhBreadcrumb = zhGenerator.generateBreadcrumbSchema('analysis');
      const enBreadcrumb = enGenerator.generateBreadcrumbSchema('analysis');

      expect(zhBreadcrumb.itemListElement[0].name).toBe('首页');
      expect(enBreadcrumb.itemListElement[0].name).toBe('Home');
    });
  });
});
