'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  BriefcaseIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon,
  DocumentTextIcon,
  ExclamationCircleIcon,
  HeartIcon,
  InformationCircleIcon,
  NewspaperIcon,
  ScaleIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import { AgentDetailModal } from './AgentDetailModal';
import { RiskManagerStatusPanel } from './RiskManagerStatusPanel';

// Enhanced interfaces based on the new requirements
export interface AnalystReport {
  id: number;
  report_id: string;
  analyst_type: 'fundamental' | 'technical' | 'sentiment' | 'news';
  summary?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: string;
  progress?: number;
  current_task?: string;
  error_message?: string;
}

export interface ResearchReport {
  id: number;
  report_id: string;
  researcher_type: 'bull' | 'bear';
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  progress?: number;
  current_task?: string;
  error_message?: string;
}

export interface AgentStatusPanelProps {
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  workflowId?: string;
  className?: string;
}

export function AgentStatusPanel({
  analystReports,
  researchReports,
  workflowId,
  className,
}: AgentStatusPanelProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedAgentForModal, setSelectedAgentForModal] = useState<
    AnalystReport | ResearchReport | null
  >(null);
  const [selectedAgentType, setSelectedAgentType] = useState<'analyst' | 'researcher'>('analyst');

  // Enhanced agent configuration with better icons and descriptions
  const agentConfig = {
    fundamental: {
      name: '基本面分析师',
      description: '分析财务报表和公司基本面数据',
      icon: DocumentTextIcon,
      color: 'blue',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      iconColor: 'text-blue-600 dark:text-blue-400',
      progressColor: 'bg-blue-500',
      tasks: ['获取财务数据', '计算财务比率', '估值分析', '生成基本面报告'],
    },
    technical: {
      name: '技术分析师',
      description: '分析技术指标和价格走势',
      icon: ChartBarIcon,
      color: 'green',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      iconColor: 'text-green-600 dark:text-green-400',
      progressColor: 'bg-green-500',
      tasks: ['获取历史价格数据', '计算技术指标', '分析价格趋势', '生成技术分析报告'],
    },
    sentiment: {
      name: '情绪分析师',
      description: '分析市场情绪和投资者心理',
      icon: HeartIcon,
      color: 'pink',
      bgColor: 'bg-pink-50 dark:bg-pink-900/20',
      iconColor: 'text-pink-600 dark:text-pink-400',
      progressColor: 'bg-pink-500',
      tasks: ['收集情绪数据', '分析投资者心理', '评估市场恐慌指数', '生成情绪报告'],
    },
    news: {
      name: '新闻分析师',
      description: '分析新闻事件和市场影响',
      icon: NewspaperIcon,
      color: 'purple',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      iconColor: 'text-purple-600 dark:text-purple-400',
      progressColor: 'bg-purple-500',
      tasks: ['收集新闻数据', '事件影响分析', '舆论趋势分析', '生成新闻分析报告'],
    },
    bull: {
      name: '多头研究员',
      description: '寻找买入机会和积极因素',
      icon: ArrowTrendingUpIcon,
      color: 'emerald',
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      progressColor: 'bg-emerald-500',
      tasks: ['分析积极因素', '构建多头论点', '评估上涨潜力', '生成买入建议'],
    },
    bear: {
      name: '空头研究员',
      description: '识别风险因素和消极因素',
      icon: ArrowTrendingDownIcon,
      color: 'red',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      iconColor: 'text-red-600 dark:text-red-400',
      progressColor: 'bg-red-500',
      tasks: ['分析风险因素', '构建空头论点', '评估下跌风险', '生成卖出建议'],
    },
    debate_moderator: {
      name: '辩论主持人',
      description: '主持多空辩论并评估共识',
      icon: ScaleIcon,
      color: 'indigo',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
      iconColor: 'text-indigo-600 dark:text-indigo-400',
      progressColor: 'bg-indigo-500',
      tasks: ['组织辩论', '评估论点', '寻求共识', '生成辩论总结'],
    },
    risk_manager: {
      name: '风险管理师',
      description: '评估投资风险和制定风控策略',
      icon: ShieldCheckIcon,
      color: 'orange',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      iconColor: 'text-orange-600 dark:text-orange-400',
      progressColor: 'bg-orange-500',
      tasks: ['风险识别', '风险量化', '风控策略', '生成风险报告'],
    },
    portfolio_manager: {
      name: '投资组合经理',
      description: '制定最终投资决策',
      icon: BriefcaseIcon,
      color: 'violet',
      bgColor: 'bg-violet-50 dark:bg-violet-900/20',
      iconColor: 'text-violet-600 dark:text-violet-400',
      progressColor: 'bg-violet-500',
      tasks: ['综合分析', '决策制定', '仓位管理', '生成投资建议'],
    },
  };

  const getStatusIcon = (status: string, animated = true) => {
    switch (status) {
      case 'completed':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
          </motion.div>
        );
      case 'running':
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
          />
        );
      case 'failed':
        return (
          <motion.div
            initial={animated ? { scale: 0 } : false}
            animate={animated ? { scale: 1 } : false}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          >
            <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
          </motion.div>
        );
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20 shadow-green-100 dark:shadow-green-900/20';
      case 'running':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20 shadow-blue-100 dark:shadow-blue-900/20 ring-2 ring-blue-200 dark:ring-blue-800';
      case 'failed':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20 shadow-red-100 dark:shadow-red-900/20';
      case 'pending':
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
      default:
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'failed':
        return '失败';
      case 'pending':
        return '等待中';
      default:
        return status;
    }
  };

  // Handle opening agent detail modal
  const handleOpenAgentDetail = (
    agent: AnalystReport | ResearchReport,
    type: 'analyst' | 'researcher'
  ) => {
    setSelectedAgentForModal(agent);
    setSelectedAgentType(type);
    setDetailModalOpen(true);
  };

  const handleCloseAgentDetail = () => {
    setDetailModalOpen(false);
    setSelectedAgentForModal(null);
  };

  // Combine all agents for overview statistics
  const allAgents = [
    ...analystReports.map((report) => ({
      id: report.report_id,
      type: 'analyst',
      status: report.status,
      progress:
        report.progress ||
        (report.status === 'completed' ? 100 : report.status === 'running' ? 50 : 0),
    })),
    ...researchReports.map((report) => ({
      id: report.report_id,
      type: 'researcher',
      status: report.status,
      progress:
        report.progress ||
        (report.status === 'completed' ? 100 : report.status === 'running' ? 50 : 0),
    })),
  ];

  const completedCount = allAgents.filter((a) => a.status === 'completed').length;
  const runningCount = allAgents.filter((a) => a.status === 'running').length;
  const pendingCount = allAgents.filter((a) => a.status === 'pending').length;
  const failedCount = allAgents.filter((a) => a.status === 'failed').length;
  const totalCount = allAgents.length;
  const averageProgress =
    totalCount > 0
      ? Math.round(allAgents.reduce((sum, agent) => sum + agent.progress, 0) / totalCount)
      : 0;

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* 总体状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CpuChipIcon className="h-5 w-5 text-slate-600 dark:text-slate-400" />
              <span>智能体团队总览</span>
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <motion.div
                className="flex items-center space-x-1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>已完成: {completedCount}</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span>运行中: {runningCount}</span>
              </motion.div>
              <motion.div
                className="flex items-center space-x-1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                <span>等待中: {pendingCount}</span>
              </motion.div>
              {failedCount > 0 && (
                <motion.div
                  className="flex items-center space-x-1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>失败: {failedCount}</span>
                </motion.div>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <motion.div
              className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <motion.div
                className="text-2xl font-bold text-green-600"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 500, delay: 0.3 }}
              >
                {totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0}%
              </motion.div>
              <div className="text-sm text-slate-600 dark:text-slate-400">完成率</div>
            </motion.div>

            <motion.div
              className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <motion.div
                className="text-2xl font-bold text-blue-600"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 500, delay: 0.4 }}
              >
                {averageProgress}%
              </motion.div>
              <div className="text-sm text-slate-600 dark:text-slate-400">平均进度</div>
            </motion.div>

            <motion.div
              className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <motion.div
                className="text-2xl font-bold text-purple-600"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 500, delay: 0.5 }}
              >
                {totalCount}
              </motion.div>
              <div className="text-sm text-slate-600 dark:text-slate-400">总智能体数</div>
            </motion.div>

            <motion.div
              className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <motion.div
                className="text-2xl font-bold text-orange-600"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: 'spring', stiffness: 500, delay: 0.6 }}
              >
                {failedCount}
              </motion.div>
              <div className="text-sm text-slate-600 dark:text-slate-400">错误数</div>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* 分析师团队状态 */}
      {analystReports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DocumentTextIcon className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                <span>分析师团队状态</span>
              </div>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors"
              >
                {showDetails ? '隐藏详情' : '显示详情'}
              </button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {analystReports.map((report, index) => {
                const config = agentConfig[report.analyst_type];
                const progress =
                  report.progress ||
                  (report.status === 'completed' ? 100 : report.status === 'running' ? 50 : 0);

                if (!config) return null;

                return (
                  <motion.div
                    key={report.report_id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${getStatusColor(
                      report.status
                    )} ${
                      selectedAgent === report.report_id
                        ? 'ring-2 ring-blue-500 dark:ring-blue-400'
                        : ''
                    }`}
                    onClick={() =>
                      setSelectedAgent(selectedAgent === report.report_id ? null : report.report_id)
                    }
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${config.bgColor}`}>
                          <config.icon className={`h-5 w-5 ${config.iconColor}`} />
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {config.name}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {config.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <motion.span
                          className="text-xs text-slate-500"
                          animate={{ rotate: selectedAgent === report.report_id ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          ▼
                        </motion.span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          状态: {getStatusText(report.status)}
                        </span>
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          {progress}%
                        </span>
                      </div>

                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${config.progressColor}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${progress}%` }}
                          transition={{ duration: 0.8, ease: 'easeOut' }}
                        />
                      </div>

                      {report.current_task && (
                        <motion.p
                          className="text-xs text-slate-600 dark:text-slate-400 mt-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          💬 {report.current_task}
                        </motion.p>
                      )}

                      {report.error_message && (
                        <motion.p
                          className="text-xs text-red-600 dark:text-red-400 mt-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          ⚠️ {report.error_message}
                        </motion.p>
                      )}

                      <p className="text-xs text-slate-500 dark:text-slate-500">
                        🕒 创建时间: {new Date(report.created_at).toLocaleTimeString()}
                      </p>

                      {report.execution_time_ms && (
                        <p className="text-xs text-slate-500 dark:text-slate-500">
                          ⏱️ 执行时间: {Math.round(report.execution_time_ms / 1000)}秒
                        </p>
                      )}

                      {/* Detail Button for Analyst */}
                      <div className="mt-2 pt-2 border-t border-slate-200 dark:border-slate-600">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenAgentDetail(report, 'analyst');
                          }}
                          className="w-full text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors flex items-center justify-center space-x-1"
                        >
                          <InformationCircleIcon className="h-3 w-3" />
                          <span>查看详细信息</span>
                        </button>
                      </div>

                      {/* 展开的详细信息 */}
                      <AnimatePresence>
                        {selectedAgent === report.report_id && showDetails && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600"
                          >
                            <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                              任务清单:
                            </h5>
                            <div className="space-y-1">
                              {config.tasks.map((task, taskIndex) => {
                                const isCompleted =
                                  taskIndex < Math.floor((progress / 100) * config.tasks.length);
                                const isCurrent =
                                  taskIndex === Math.floor((progress / 100) * config.tasks.length);

                                return (
                                  <motion.div
                                    key={taskIndex}
                                    className="flex items-center space-x-2 text-xs"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: taskIndex * 0.1 }}
                                  >
                                    <motion.div
                                      className={`w-2 h-2 rounded-full ${
                                        isCompleted
                                          ? 'bg-green-500'
                                          : isCurrent
                                          ? 'bg-blue-500'
                                          : 'bg-slate-300'
                                      }`}
                                      animate={isCurrent ? { scale: [1, 1.2, 1] } : {}}
                                      transition={{ duration: 1, repeat: Infinity }}
                                    />
                                    <span
                                      className={`${
                                        isCompleted
                                          ? 'text-green-600 line-through'
                                          : isCurrent
                                          ? 'text-blue-600 font-medium'
                                          : 'text-slate-500'
                                      }`}
                                    >
                                      {task}
                                    </span>
                                  </motion.div>
                                );
                              })}
                            </div>

                            {report.summary && (
                              <div className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
                                <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                  分析摘要:
                                </h5>
                                <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                                  {report.summary}
                                </p>
                              </div>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 研究团队状态 */}
      {researchReports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ScaleIcon className="h-5 w-5 text-slate-600 dark:text-slate-400" />
              <span>研究团队状态</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {researchReports.map((report, index) => {
                const config = agentConfig[report.researcher_type];
                const progress =
                  report.progress ||
                  (report.status === 'completed' ? 100 : report.status === 'running' ? 50 : 0);

                if (!config) return null;

                return (
                  <motion.div
                    key={report.report_id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${getStatusColor(
                      report.status
                    )} ${
                      selectedAgent === report.report_id
                        ? 'ring-2 ring-blue-500 dark:ring-blue-400'
                        : ''
                    }`}
                    onClick={() =>
                      setSelectedAgent(selectedAgent === report.report_id ? null : report.report_id)
                    }
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${config.bgColor}`}>
                          <config.icon className={`h-5 w-5 ${config.iconColor}`} />
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {config.name}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {config.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <motion.span
                          className="text-xs text-slate-500"
                          animate={{ rotate: selectedAgent === report.report_id ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          ▼
                        </motion.span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          状态: {getStatusText(report.status)}
                        </span>
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          {progress}%
                        </span>
                      </div>

                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${config.progressColor}`}
                          initial={{ width: 0 }}
                          animate={{ width: `${progress}%` }}
                          transition={{ duration: 0.8, ease: 'easeOut' }}
                        />
                      </div>

                      {report.confidence_level && (
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-slate-600 dark:text-slate-400">置信度:</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-1">
                              <motion.div
                                className="bg-blue-500 h-1 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${report.confidence_level * 100}%` }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                              />
                            </div>
                            <span className="text-slate-600 dark:text-slate-400 font-medium">
                              {Math.round(report.confidence_level * 100)}%
                            </span>
                          </div>
                        </div>
                      )}

                      {report.target_price && (
                        <p className="text-xs text-slate-600 dark:text-slate-400">
                          🎯 目标价格: ${report.target_price.toFixed(2)}
                        </p>
                      )}

                      {report.time_horizon && (
                        <p className="text-xs text-slate-600 dark:text-slate-400">
                          ⏰ 时间范围: {report.time_horizon}
                        </p>
                      )}

                      {/* Detail Button for Researcher */}
                      <div className="mt-2 pt-2 border-t border-slate-200 dark:border-slate-600">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenAgentDetail(report, 'researcher');
                          }}
                          className="w-full text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 transition-colors flex items-center justify-center space-x-1"
                        >
                          <InformationCircleIcon className="h-3 w-3" />
                          <span>查看详细信息</span>
                        </button>
                      </div>

                      {report.current_task && (
                        <motion.p
                          className="text-xs text-slate-600 dark:text-slate-400 mt-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          💬 {report.current_task}
                        </motion.p>
                      )}

                      {report.error_message && (
                        <motion.p
                          className="text-xs text-red-600 dark:text-red-400 mt-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          ⚠️ {report.error_message}
                        </motion.p>
                      )}

                      <p className="text-xs text-slate-500 dark:text-slate-500">
                        🕒 创建时间: {new Date(report.created_at).toLocaleTimeString()}
                      </p>

                      {/* 展开的详细信息 */}
                      <AnimatePresence>
                        {selectedAgent === report.report_id && showDetails && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600"
                          >
                            <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                              研究任务:
                            </h5>
                            <div className="space-y-1">
                              {config.tasks.map((task, taskIndex) => {
                                const isCompleted =
                                  taskIndex < Math.floor((progress / 100) * config.tasks.length);
                                const isCurrent =
                                  taskIndex === Math.floor((progress / 100) * config.tasks.length);

                                return (
                                  <motion.div
                                    key={taskIndex}
                                    className="flex items-center space-x-2 text-xs"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: taskIndex * 0.1 }}
                                  >
                                    <motion.div
                                      className={`w-2 h-2 rounded-full ${
                                        isCompleted
                                          ? 'bg-green-500'
                                          : isCurrent
                                          ? 'bg-blue-500'
                                          : 'bg-slate-300'
                                      }`}
                                      animate={isCurrent ? { scale: [1, 1.2, 1] } : {}}
                                      transition={{ duration: 1, repeat: Infinity }}
                                    />
                                    <span
                                      className={`${
                                        isCompleted
                                          ? 'text-green-600 line-through'
                                          : isCurrent
                                          ? 'text-blue-600 font-medium'
                                          : 'text-slate-500'
                                      }`}
                                    >
                                      {task}
                                    </span>
                                  </motion.div>
                                );
                              })}
                            </div>

                            {report.summary && (
                              <div className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
                                <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                  研究摘要:
                                </h5>
                                <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                                  {report.summary}
                                </p>
                              </div>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 风险管理师状态 */}
      {workflowId && (
        <RiskManagerStatusPanel
          workflowId={workflowId}
          showDetailedTasks={showDetails}
          onProgressUpdate={(progress) => {
            console.log(`[Risk Manager] Progress: ${progress}%`);
          }}
          onError={(error) => {
            console.error('[Risk Manager] Error:', error);
          }}
        />
      )}

      {/* Agent Detail Modal */}
      <AgentDetailModal
        isOpen={detailModalOpen}
        onClose={handleCloseAgentDetail}
        agent={selectedAgentForModal}
        agentType={selectedAgentType}
      />
    </div>
  );
}
