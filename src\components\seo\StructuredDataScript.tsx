/**
 * 增强的结构化数据脚本组件
 * 用于在页面中注入 JSON-LD 结构化数据，包含金融服务相关的结构化数据
 */

import { MetadataGenerator } from '@/lib/seo/metadata-generator';
import { generatePageStructuredData } from '@/lib/seo/structured-data-generator';
import { validateStructuredData } from '@/lib/seo/structured-data-validator';
import { SEOUtils } from '@/lib/seo/utils';
import { PageSEOProps, PageType } from '@/types/seo';

interface StructuredDataScriptProps extends PageSEOProps {
  className?: string;
}

/**
 * 增强的结构化数据脚本组件
 * 在服务端渲染时注入结构化数据，包含金融服务相关的结构化数据
 */
export function StructuredDataScript({
  page,
  dynamicData,
  locale = 'zh',
  className,
}: StructuredDataScriptProps) {
  try {
    // 使用增强的结构化数据生成器
    const enhancedStructuredData = generatePageStructuredData(
      page as PageType,
      locale,
      dynamicData
    );

    const scripts: string[] = [];

    // 生成所有结构化数据的脚本
    Object.entries(enhancedStructuredData).forEach(([key, data]) => {
      if (data) {
        // 验证结构化数据
        const validation = validateStructuredData(data);

        if (validation.isValid) {
          const script = SEOUtils.generateJsonLdScript(data);
          if (script) {
            scripts.push(script);
          }
        } else {
          console.warn(`Invalid structured data for ${key}:`, validation.errors);
        }
      }
    });

    if (scripts.length === 0) {
      return null;
    }

    return (
      <>
        {scripts.map((script, index) => (
          <script
            key={index}
            type="application/ld+json"
            className={className}
            dangerouslySetInnerHTML={{ __html: script }}
          />
        ))}
      </>
    );
  } catch (error) {
    console.error('Error generating enhanced structured data scripts:', error);

    // 回退到基础实现
    try {
      const generator = new MetadataGenerator(locale);
      const jsonLdScripts = generator.generateJsonLdScripts(page, dynamicData);

      if (jsonLdScripts.length === 0) {
        return null;
      }

      return (
        <>
          {jsonLdScripts.map((script, index) => (
            <script
              key={index}
              type="application/ld+json"
              className={className}
              dangerouslySetInnerHTML={{ __html: script }}
            />
          ))}
        </>
      );
    } catch (fallbackError) {
      console.error('Error in fallback structured data generation:', fallbackError);
      return null;
    }
  }
}

/**
 * 根布局结构化数据组件
 * 专门用于根布局的结构化数据
 */
export function RootStructuredData() {
  return <StructuredDataScript page="home" locale="zh" className="seo-structured-data" />;
}

/**
 * 动态结构化数据组件
 * 用于特定页面的动态结构化数据
 */
export function DynamicStructuredData({
  page,
  dynamicData,
  locale = 'zh',
}: {
  page: PageType;
  dynamicData?: any;
  locale?: 'zh' | 'en';
}) {
  return (
    <StructuredDataScript
      page={page}
      dynamicData={dynamicData}
      locale={locale}
      className="seo-dynamic-structured-data"
    />
  );
}

/**
 * 分析页面结构化数据组件
 * 专门用于股票分析页面
 */
export function AnalysisStructuredData({
  stockSymbol,
  analysisId,
  analysisType = 'Technical',
  recommendation,
  targetPrice,
  locale = 'zh',
}: {
  stockSymbol: string;
  analysisId?: string;
  analysisType?: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk';
  recommendation?: 'Buy' | 'Sell' | 'Hold';
  targetPrice?: number;
  locale?: 'zh' | 'en';
}) {
  const dynamicData = {
    stockSymbol,
    analysisId,
    analysisType,
    recommendation,
    targetPrice,
  };

  return (
    <StructuredDataScript
      page="analysis"
      dynamicData={dynamicData}
      locale={locale}
      className="seo-analysis-structured-data"
    />
  );
}

/**
 * 任务页面结构化数据组件
 * 专门用于任务管理页面
 */
export function TaskStructuredData({
  taskId,
  taskTitle,
  locale = 'zh',
}: {
  taskId?: string;
  taskTitle?: string;
  locale?: 'zh' | 'en';
}) {
  const dynamicData = {
    taskId,
    taskTitle,
  };

  return (
    <StructuredDataScript
      page="tasks"
      dynamicData={dynamicData}
      locale={locale}
      className="seo-task-structured-data"
    />
  );
}
