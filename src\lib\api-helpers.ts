import { NextResponse } from 'next/server';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  message?: string;
  code?: number;
}

export function success<T>(
  data: T,
  message = 'Request successful',
  code = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
    code,
  });
}

export function error(
  message = 'An error occurred',
  code = 500,
  data?: any
): NextResponse<ApiResponse<null>> {
  return NextResponse.json(
    {
      success: false,
      data: data || null,
      message,
      code,
    },
    { status: code }
  );
}
