// 枚举值的中英文映射
// 数据库和API使用英文，前端显示中文

// 研究深度映射
export const RESEARCH_DEPTH_MAP = {
  shallow: '浅层分析',
  medium: '中等分析',
  deep: '深度分析',
  quick: '快速分析',
  standard: '标准分析',
} as const;

// 分析周期映射
export const ANALYSIS_PERIOD_MAP = {
  '1d': '1天',
  '1w': '1周',
  '1m': '1个月',
  '3m': '3个月',
  '6m': '6个月',
  '1y': '1年',
  custom: '自定义',
} as const;

// 任务状态映射
export const TASK_STATUS_MAP = {
  pending: '待处理',
  running: '运行中',
  completed: '已完成',
  failed: '失败',
  cancelled: '已取消',
} as const;

// 代理状态映射
export const AGENT_STATUS_MAP = {
  idle: '空闲',
  initializing: '初始化中',
  waiting: '等待中',
  active: '活跃',
  running: '运行中',
  completed: '已完成',
  error: '错误',
  failed: '失败',
} as const;

// 交易行动映射
export const TRADING_ACTION_MAP = {
  buy: '买入',
  sell: '卖出',
  hold: '持有',
} as const;

// 风险等级映射
export const RISK_LEVEL_MAP = {
  low: '低风险',
  medium: '中等风险',
  high: '高风险',
  very_high: '极高风险',
} as const;

// 消息类型映射
export const MESSAGE_TYPE_MAP = {
  human: '用户',
  ai: 'AI',
  system: '系统',
  tool: '工具',
} as const;

// 分析结果类型映射
export const RESULT_TYPE_MAP = {
  fundamental: '基本面',
  technical: '技术面',
  sentiment: '情绪面',
  decision: '决策',
  risk: '风险',
  comprehensive: '综合',
} as const;

// 步骤类型映射
export const STEP_TYPE_MAP = {
  data_collection: '数据收集',
  analysis: '分析',
  processing: '处理',
  validation: '验证',
  decision: '决策',
} as const;

// 系统状态映射
export const SYSTEM_STATUS_MAP = {
  healthy: '健康',
  degraded: '降级',
  down: '停机',
} as const;

// 情绪分析映射
export const SENTIMENT_MAP = {
  positive: '积极',
  negative: '消极',
  neutral: '中性',
} as const;

// 日志级别映射
export const LOG_LEVEL_MAP = {
  DEBUG: '调试',
  INFO: '信息',
  WARN: '警告',
  ERROR: '错误',
  FATAL: '致命',
} as const;

// 工具函数：获取映射值，如果没有找到则返回原值
export function getResearchDepthLabel(value: string): string {
  return RESEARCH_DEPTH_MAP[value as keyof typeof RESEARCH_DEPTH_MAP] || value;
}

export function getAnalysisPeriodLabel(value: string): string {
  return ANALYSIS_PERIOD_MAP[value as keyof typeof ANALYSIS_PERIOD_MAP] || value;
}

export function getTaskStatusLabel(value: string): string {
  return TASK_STATUS_MAP[value as keyof typeof TASK_STATUS_MAP] || value;
}

export function getAgentStatusLabel(value: string): string {
  return AGENT_STATUS_MAP[value as keyof typeof AGENT_STATUS_MAP] || value;
}

export function getTradingActionLabel(value: string): string {
  return TRADING_ACTION_MAP[value as keyof typeof TRADING_ACTION_MAP] || value;
}

export function getRiskLevelLabel(value: string): string {
  return RISK_LEVEL_MAP[value as keyof typeof RISK_LEVEL_MAP] || value;
}

export function getMessageTypeLabel(value: string): string {
  return MESSAGE_TYPE_MAP[value as keyof typeof MESSAGE_TYPE_MAP] || value;
}

export function getResultTypeLabel(value: string): string {
  return RESULT_TYPE_MAP[value as keyof typeof RESULT_TYPE_MAP] || value;
}

export function getStepTypeLabel(value: string): string {
  return STEP_TYPE_MAP[value as keyof typeof STEP_TYPE_MAP] || value;
}

export function getSystemStatusLabel(value: string): string {
  return SYSTEM_STATUS_MAP[value as keyof typeof SYSTEM_STATUS_MAP] || value;
}

export function getSentimentLabel(value: string): string {
  return SENTIMENT_MAP[value as keyof typeof SENTIMENT_MAP] || value;
}

export function getLogLevelLabel(value: string): string {
  return LOG_LEVEL_MAP[value as keyof typeof LOG_LEVEL_MAP] || value;
}

// 反向映射：从中文获取英文值
export function getResearchDepthValue(label: string): string {
  const entry = Object.entries(RESEARCH_DEPTH_MAP).find(([, v]) => v === label);
  return entry ? entry[0] : label;
}

export function getAnalysisPeriodValue(label: string): string {
  const entry = Object.entries(ANALYSIS_PERIOD_MAP).find(([, v]) => v === label);
  return entry ? entry[0] : label;
}

export function getTaskStatusValue(label: string): string {
  const entry = Object.entries(TASK_STATUS_MAP).find(([, v]) => v === label);
  return entry ? entry[0] : label;
}

export function getTradingActionValue(label: string): string {
  const entry = Object.entries(TRADING_ACTION_MAP).find(([, v]) => v === label);
  return entry ? entry[0] : label;
}

export function getRiskLevelValue(label: string): string {
  const entry = Object.entries(RISK_LEVEL_MAP).find(([, v]) => v === label);
  return entry ? entry[0] : label;
}

// 选项数组生成器（用于下拉框等）
export const researchDepthOptions = Object.entries(RESEARCH_DEPTH_MAP).map(([value, label]) => ({
  value,
  label,
}));

export const analysisPeriodOptions = Object.entries(ANALYSIS_PERIOD_MAP).map(([value, label]) => ({
  value,
  label,
}));

export const taskStatusOptions = Object.entries(TASK_STATUS_MAP).map(([value, label]) => ({
  value,
  label,
}));

export const tradingActionOptions = Object.entries(TRADING_ACTION_MAP).map(([value, label]) => ({
  value,
  label,
}));

export const riskLevelOptions = Object.entries(RISK_LEVEL_MAP).map(([value, label]) => ({
  value,
  label,
}));
