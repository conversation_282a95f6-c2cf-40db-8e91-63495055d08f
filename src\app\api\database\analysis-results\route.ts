import { error, success } from '@/lib/api-helpers';
import { query } from '@/lib/db';
import { SaveAnalysisResultRequest } from '@/types/database';
import { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const body: SaveAnalysisResultRequest = await request.json();

    const {
      task_id,
      result_type,
      result_data,
      summary,
      recommendations,
      confidence_score,
      risk_level,
      is_final = false,
    } = body;

    if (!task_id || !result_type || !result_data) {
      return error('缺少必要的参数: task_id, result_type, result_data', 400);
    }

    const result_id = uuidv4();

    const sql = `
      INSERT INTO analysis_results 
      (result_id, task_id, result_type, result_data, summary, recommendations, confidence_score, risk_level, is_final) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      result_id,
      task_id,
      result_type,
      JSON.stringify(result_data),
      summary,
      recommendations ? JSON.stringify(recommendations) : null,
      confidence_score,
      risk_level,
      is_final,
    ];

    const dbResult = await query(sql, params);

    return success({ result_id, dbResult });
  } catch (err) {
    console.error('保存分析结果失败:', err);
    const errorMessage = err instanceof Error ? err.message : '未知错误';
    return error(`保存分析结果失败: ${errorMessage}`, 500);
  }
}
