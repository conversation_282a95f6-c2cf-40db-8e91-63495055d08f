import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { ClientConfig } from '@langchain/mcp-adapters';
import { ChatOpenAI } from '@langchain/openai';

const askAI = async (
  config: Partial<ClientConfig>,
  question: string
): Promise<{ content: string; agent: any }> => {
  // Create client and connect to server
  //   const client = new MultiServerMCPClient({
  //     // Global tool configuration options
  //     // Whether to throw on errors if a tool fails to load (optional, default: true)
  //     throwOnLoadError: true,
  //     // Whether to prefix tool names with the server name (optional, default: true)
  //     prefixToolNameWithServerName: true,
  //     // Optional additional prefix for tool names (optional, default: "mcp")
  //     additionalToolNamePrefix: 'mcp',

  //     // Use standardized content block format in tool outputs
  //     useStandardContentBlocks: true,

  //     // Server configuration
  //     mcpServers: config?.mcpServers ?? {
  //       'tavily-mcp': {
  //         command: 'npx',
  //         args: ['-y', 'tavily-mcp@0.1.4'],
  //         env: {
  //           TAVILY_API_KEY: 'your-api-key-here',
  //         },
  //       },
  //     },
  //   });

  //   const tools = await client.getTools();

  // Create an OpenAI model
  const model = new ChatOpenAI({
    modelName: 'gpt-4.1-2025-04-14',
    configuration: {
      baseURL: process.env.OPENAI_API_BASE || 'https://api.nuwaapi.com',
    },
    apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  });

  // Create the React agent
  const agent = createReactAgent({
    llm: model,
    tools: [], // Add tools if needed
  });

  let res;
  // Run the agent
  console.log(question);
  try {
    res = await agent.invoke({
      messages: [{ role: 'user', content: question }],
    });
    console.log('Agent response:', res);
  } catch (error: any) {
    console.error('Error during agent execution:', error);
    // Tools throw ToolException for tool-specific errors
    if (error.name === 'ToolException') {
      console.error('Tool execution failed:', error.message);
    }
  } finally {
    // Close the client connection
    // await client.close();
    return {
      content: res?.messages?.[1]?.content as string,
      agent,
    };
  }
};

export default askAI;
