/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-31 22:52:44
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-31 22:54:21
 * @FilePath: \trading-agents-frontend\scripts\fix-collation.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// Script to fix database collation issues
// Run this script to ensure all tables use utf8mb4_unicode_ci collation consistently

const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixCollation() {
    let connection;

    try {
        console.log('🔧 Connecting to database...');
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '13306'),
            user: process.env.DB_USER || 'trading_user',
            password: process.env.DB_PASSWORD || 'trading123',
            database: process.env.DB_NAME || 'trading_analysis',
            charset: 'utf8mb4',
        });

        console.log('✅ Connected to database');

        // Fix database collation
        console.log('🔧 Fixing database collation...');
        await connection.query('ALTER DATABASE trading_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');

        // Get all tables
        const [tables] = await connection.query('SHOW TABLES');

        console.log(`🔧 Found ${tables.length} tables to fix...`);

        // Fix each table
        for (const table of tables) {
            const tableName = Object.values(table)[0];
            console.log(`🔧 Fixing table: ${tableName}`);

            try {
                await connection.query(`ALTER TABLE ${tableName} CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
                console.log(`✅ Fixed table: ${tableName}`);
            } catch (error) {
                console.log(`⚠️  Warning: Could not fix table ${tableName}: ${error.message}`);
            }
        }

        // Recreate the stored procedure with explicit collation
        console.log('🔧 Recreating UpdateWorkflowStatus procedure...');
        await connection.query('DROP PROCEDURE IF EXISTS UpdateWorkflowStatus');

        const procedureSQL = `
      CREATE PROCEDURE UpdateWorkflowStatus(
          IN p_workflow_id VARCHAR(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
          IN p_current_stage VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
          IN p_progress INT,
          IN p_status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
          IN p_error_message TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
      )
      BEGIN
          UPDATE workflows SET
              current_stage = p_current_stage,
              progress = p_progress,
              status = p_status,
              error_message = p_error_message,
              updated_at = NOW(),
              started_at = CASE WHEN started_at IS NULL AND p_status = 'running' THEN NOW() ELSE started_at END,
              completed_at = CASE WHEN p_status IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE NULL END
          WHERE workflow_id = p_workflow_id;
      END
    `;

        await connection.query(procedureSQL);
        console.log('✅ Recreated UpdateWorkflowStatus procedure');

        console.log('🎉 Collation fix completed successfully!');

    } catch (error) {
        console.error('❌ Error fixing collation:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

fixCollation();