import { renderHook } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { useNavigationHighlight } from '../../../hooks/useNavigationHighlight';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

// Mock usePageTitle hook
jest.mock('../../../hooks/usePageTitle', () => ({
  usePageTitle: jest.fn(() => ({
    title: 'Test Page - TradingAgents',
    description: 'Test description',
  })),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('Header Navigation Highlighting', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide correct navigation classes for analysis pages', () => {
    mockUsePathname.mockReturnValue('/analysis/history');
    const { result } = renderHook(() => useNavigationHighlight());

    // Analysis navigation should be highlighted
    const analysisClasses = result.current.getNavigationItemClasses('/analysis');
    expect(analysisClasses).toContain('text-blue-600');
    expect(analysisClasses).toContain('font-medium');

    // Tasks navigation should not be highlighted
    const tasksClasses = result.current.getNavigationItemClasses('/tasks');
    expect(tasksClasses).toContain('text-slate-600');
    expect(tasksClasses).toContain('hover:text-blue-600');
  });

  it('should provide correct navigation classes for tasks page', () => {
    mockUsePathname.mockReturnValue('/tasks');
    const { result } = renderHook(() => useNavigationHighlight());

    // Tasks navigation should be highlighted
    const tasksClasses = result.current.getNavigationItemClasses('/tasks');
    expect(tasksClasses).toContain('text-blue-600');
    expect(tasksClasses).toContain('font-medium');

    // Analysis navigation should not be highlighted
    const analysisClasses = result.current.getNavigationItemClasses('/analysis');
    expect(analysisClasses).toContain('text-slate-600');
    expect(analysisClasses).toContain('hover:text-blue-600');
  });

  it('should generate correct breadcrumbs for analysis pages', () => {
    mockUsePathname.mockReturnValue('/analysis/history');
    const { result } = renderHook(() => useNavigationHighlight());

    const breadcrumbs = result.current.generateBreadcrumbs();
    expect(breadcrumbs).toHaveLength(3);
    expect(breadcrumbs[0]).toEqual({
      label: '首页',
      href: '/',
      current: false,
    });
    expect(breadcrumbs[1]).toEqual({
      label: '分析中心',
      href: '/analysis',
      current: false,
    });
    expect(breadcrumbs[2]).toEqual({
      label: '分析历史',
      href: undefined,
      current: true,
    });
  });

  it('should detect analysis page types correctly', () => {
    // Test history page
    mockUsePathname.mockReturnValue('/analysis/history');
    const { result: historyResult } = renderHook(() => useNavigationHighlight());
    expect(historyResult.current.isAnalysisPage).toBe(true);
    expect(historyResult.current.analysisPageType).toBe('history');

    // Test compare page
    mockUsePathname.mockReturnValue('/analysis/compare');
    const { result: compareResult } = renderHook(() => useNavigationHighlight());
    expect(compareResult.current.isAnalysisPage).toBe(true);
    expect(compareResult.current.analysisPageType).toBe('compare');

    // Test detail page
    mockUsePathname.mockReturnValue('/analysis/abc123');
    const { result: detailResult } = renderHook(() => useNavigationHighlight());
    expect(detailResult.current.isAnalysisPage).toBe(true);
    expect(detailResult.current.analysisPageType).toBe('detail');
  });
});
