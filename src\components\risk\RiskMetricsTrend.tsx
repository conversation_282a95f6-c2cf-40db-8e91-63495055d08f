/**
 * Risk Metrics Trend Component
 * 风险指标趋势分析组件
 */

'use client';

import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useRiskMetricsTrend } from '@/hooks/useRiskDataStorage';
import { BarChart3, Calendar, TrendingUp } from 'lucide-react';
import React, { useState } from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface RiskMetricsTrendProps {
  className?: string;
  defaultTicker?: string;
  defaultMetric?: string;
}

/**
 * 风险指标趋势组件
 * 需求 9.5: 实现风险数据的对比和趋势分析
 */
export function RiskMetricsTrend({
  className,
  defaultTicker = '',
  defaultMetric = 'volatility',
}: RiskMetricsTrendProps) {
  const [ticker, setTicker] = useState(defaultTicker);
  const [metric, setMetric] = useState(defaultMetric);
  const [days, setDays] = useState(30);
  const [chartType, setChartType] = useState<'line' | 'area'>('line');

  // 查询参数
  const queryParams = ticker && metric ? { ticker, metric, days } : null;

  const { data, isLoading, error, refetch } = useRiskMetricsTrend(queryParams);

  // 风险指标选项
  const metricOptions = [
    { value: 'volatility', label: '波动率', unit: '%' },
    { value: 'beta', label: 'Beta系数', unit: '' },
    { value: 'var_95', label: 'VaR(95%)', unit: '%' },
    { value: 'var_99', label: 'VaR(99%)', unit: '%' },
    { value: 'max_drawdown', label: '最大回撤', unit: '%' },
    { value: 'sharpe_ratio', label: '夏普比率', unit: '' },
    { value: 'liquidity_ratio', label: '流动性比率', unit: '' },
  ];

  // 时间范围选项
  const dayOptions = [
    { value: 7, label: '7天' },
    { value: 30, label: '30天' },
    { value: 90, label: '90天' },
    { value: 180, label: '180天' },
    { value: 365, label: '1年' },
  ];

  // 获取指标信息
  const currentMetricInfo = metricOptions.find((opt) => opt.value === metric);

  // 处理查询
  const handleQuery = () => {
    if (ticker && metric) {
      refetch();
    }
  };

  // 格式化趋势数据
  const trendData =
    data?.trend_data?.map((item: any) => ({
      ...item,
      date: new Date(item.date).toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
      }),
      value: parseFloat(item.value.toFixed(4)),
    })) || [];

  // 计算统计信息
  const statistics = React.useMemo(() => {
    if (!trendData.length) return null;

    const values = trendData.map((d: any) => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = values.reduce((sum: number, val: number) => sum + val, 0) / values.length;

    // 计算趋势（简单线性回归斜率）
    const n = values.length;
    const sumX = values.reduce((sum: number, _: any, i: number) => sum + i, 0);
    const sumY = values.reduce((sum: number, val: number) => sum + val, 0);
    const sumXY = values.reduce((sum: number, val: number, i: number) => sum + i * val, 0);
    const sumXX = values.reduce((sum: number, _: any, i: number) => sum + i * i, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const trend = slope > 0.001 ? 'up' : slope < -0.001 ? 'down' : 'stable';

    return {
      min: min.toFixed(4),
      max: max.toFixed(4),
      avg: avg.toFixed(4),
      trend,
      change:
        values.length > 1
          ? (((values[values.length - 1] - values[0]) / values[0]) * 100).toFixed(2)
          : '0',
    };
  }, [trendData]);

  return (
    <div className={className}>
      {/* 查询控制器 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            风险指标趋势分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">股票代码</label>
              <Input
                placeholder="如: AAPL"
                value={ticker}
                onChange={(e) => setTicker(e.target.value.toUpperCase())}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">风险指标</label>
              <Select value={metric} onValueChange={setMetric}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {metricOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">时间范围</label>
              <Select value={days.toString()} onValueChange={(value) => setDays(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dayOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button onClick={handleQuery} disabled={!ticker || !metric || isLoading}>
                {isLoading ? '查询中...' : '查询趋势'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>加载趋势数据时出错</p>
              <Button onClick={() => refetch()} className="mt-2">
                重试
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 统计信息 */}
      {statistics && (
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {ticker} - {currentMetricInfo?.label}
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant={chartType === 'line' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setChartType('line')}
                >
                  线图
                </Button>
                <Button
                  variant={chartType === 'area' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setChartType('area')}
                >
                  面积图
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">
                  {statistics.avg}
                  {currentMetricInfo?.unit}
                </div>
                <div className="text-sm text-gray-600">平均值</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {statistics.min}
                  {currentMetricInfo?.unit}
                </div>
                <div className="text-sm text-gray-600">最小值</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-600">
                  {statistics.max}
                  {currentMetricInfo?.unit}
                </div>
                <div className="text-sm text-gray-600">最大值</div>
              </div>
              <div className="text-center">
                <div
                  className={`text-lg font-bold ${
                    statistics.trend === 'up'
                      ? 'text-red-600'
                      : statistics.trend === 'down'
                      ? 'text-green-600'
                      : 'text-gray-600'
                  }`}
                >
                  {statistics.trend === 'up' ? '↗' : statistics.trend === 'down' ? '↘' : '→'}
                </div>
                <div className="text-sm text-gray-600">趋势</div>
              </div>
              <div className="text-center">
                <div
                  className={`text-lg font-bold ${
                    parseFloat(statistics.change) > 0
                      ? 'text-red-600'
                      : parseFloat(statistics.change) < 0
                      ? 'text-green-600'
                      : 'text-gray-600'
                  }`}
                >
                  {statistics.change}%
                </div>
                <div className="text-sm text-gray-600">变化</div>
              </div>
            </div>

            {/* 趋势图表 */}
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                {chartType === 'line' ? (
                  <LineChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [
                        `${value}${currentMetricInfo?.unit}`,
                        currentMetricInfo?.label,
                      ]}
                    />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#2563eb"
                      strokeWidth={2}
                      dot={{ fill: '#2563eb', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6 }}
                    />
                  </LineChart>
                ) : (
                  <AreaChart data={trendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [
                        `${value}${currentMetricInfo?.unit}`,
                        currentMetricInfo?.label,
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      stroke="#2563eb"
                      fill="#2563eb"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                )}
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 数据表格 */}
      {trendData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              历史数据
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">日期</th>
                    <th className="text-left p-2">
                      {currentMetricInfo?.label} ({currentMetricInfo?.unit})
                    </th>
                    <th className="text-left p-2">工作流ID</th>
                  </tr>
                </thead>
                <tbody>
                  {trendData.map((item: any, index: number) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="p-2">{item.date}</td>
                      <td className="p-2 font-mono">{item.value}</td>
                      <td className="p-2 font-mono text-xs text-gray-600">
                        {item.workflow_id.substring(0, 12)}...
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 空状态 */}
      {!isLoading && !error && trendData.length === 0 && ticker && metric && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-gray-500">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>
                暂无 {ticker} 的 {currentMetricInfo?.label} 趋势数据
              </p>
              <p className="text-sm mt-1">请尝试其他股票代码或指标</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
