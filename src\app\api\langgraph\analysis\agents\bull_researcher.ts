import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { LangGraphError, logger, PerformanceMonitor, ValidationError } from '@/lib/logger';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const bullResearcherPrompt = PromptTemplate.fromTemplate(`
你是一位资深的多头研究员，专门从看涨角度深度挖掘投资机会。你拥有15年的A股投资研究经验，擅长发现被市场低估的价值和成长机会。你的任务是基于分析师团队的初步分析，从多头角度进行深度研究和论证。

【研究任务】
股票代码: {ticker}
分析日期: {date}
研究深度: {researchDepth}

【分析师团队报告】
{analysisReports}

【多头研究框架】
请从以下角度进行深度多头研究：

1. **价值发现论证**
   - 挖掘被市场忽视的价值点
   - 分析估值修复的催化剂
   - 识别潜在的价值重估机会
   - 对比同行业估值水平

2. **成长逻辑构建**
   - 分析业务增长的核心驱动力
   - 评估市场空间和渗透率提升
   - 识别新业务和新产品的增长潜力
   - 构建中长期成长路径

3. **竞争优势强化**
   - 深度分析公司的护城河
   - 评估技术壁垒和品牌价值
   - 分析规模效应和网络效应
   - 识别竞争优势的可持续性

4. **催化剂识别**
   - 政策利好和行业趋势
   - 公司治理改善和管理层变化
   - 新产品发布和市场拓展
   - 并购重组和资产注入机会

5. **技术面支撑**
   - 从技术角度论证上涨空间
   - 识别关键突破位和目标价位
   - 分析资金流入和主力行为
   - 评估技术形态的看涨信号

6. **风险对冲策略**
   - 识别主要风险点并提出应对策略
   - 分析最坏情况下的下行保护
   - 提出风险控制和仓位管理建议

7. **投资建议论证**
   - 明确的买入理由和逻辑链条
   - 目标价位和时间周期设定
   - 分阶段建仓和加仓策略
   - 关键监控指标和退出条件

【输出要求】
- 论证要有理有据，逻辑清晰
- 重点突出最具说服力的多头观点
- 提供具体的数据支撑和案例对比
- 承认风险但重点论证机会大于风险
- 为后续辩论准备充分的论据
`);

export async function bullResearcherNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, analysis, messages } = state;

  const context = {
    component: 'BullResearcher',
    ticker,
    date,
    workflowId: state.workflowId,
  };

  const monitor = new PerformanceMonitor('bull_research', context);

  logger.info('开始多头深度研究', context, {
    analysisAvailable: !!analysis,
    analysisKeys: analysis ? Object.keys(analysis) : [],
    configDepth: config.researchDepth,
  });

  try {
    // 检查分析师报告是否可用
    if (!analysis || Object.keys(analysis).length === 0) {
      throw new ValidationError('分析师团队报告不可用，无法进行深度研究', context);
    }

    // 验证必要的配置
    if (!ticker || !date) {
      throw new ValidationError('缺少必要的分析参数', { ...context, ticker, date });
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    if (!OPENAI_API_KEY) {
      throw new LangGraphError('OpenAI API密钥未配置', 'initialization', context);
    }

    logger.debug('初始化LLM模型', context, {
      model: config.deepThinkLLM || 'gpt-4o',
      baseURL: OPENAI_BASE_URL,
      hasApiKey: !!OPENAI_API_KEY,
    });

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.2, // 稍高的温度以获得更有创意的论证
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 整理分析师报告
    const analysisReports = formatAnalysisReports(analysis);

    if (!analysisReports || analysisReports.trim() === '分析师报告暂不可用') {
      logger.warn('分析师报告内容不足', context, undefined, { analysisReports });
    }

    const prompt = await bullResearcherPrompt.format({
      ticker,
      date,
      researchDepth: config.researchDepth || 'standard',
      analysisReports,
    });

    logger.info('开始LLM推理', context, {
      promptLength: prompt.length,
      researchDepth: config.researchDepth || 'standard',
    });

    const llmMonitor = new PerformanceMonitor('llm_inference', context);

    try {
      const response = await llm.invoke(prompt);
      const researchReport = response.content as string;

      llmMonitor.end(true, {
        responseLength: researchReport.length,
        hasContent: !!researchReport.trim(),
      });

      if (!researchReport || researchReport.trim().length < 100) {
        throw new LangGraphError('LLM返回的研究报告内容不足', 'llm_response', context);
      }

      logger.debug('LLM推理完成', context, {
        responseLength: researchReport.length,
        firstLine: researchReport.split('\n')[0]?.substring(0, 100),
      });

      // 提取关键论点和支撑数据
      const extractionMonitor = new PerformanceMonitor('data_extraction', context);

      const keyArguments = extractBullArguments(researchReport);
      const catalysts = extractCatalysts(researchReport);
      const targetPrice = extractTargetPrice(researchReport);
      const confidence = calculateBullConfidence(analysis, keyArguments);

      extractionMonitor.end(true, {
        keyArgumentsCount: keyArguments.length,
        catalystsCount: catalysts.length,
        hasTargetPrice: !!targetPrice,
        confidence,
      });

      logger.debug('数据提取完成', context, {
        keyArguments: keyArguments.length,
        catalysts: catalysts.length,
        targetPrice,
        confidence,
      });

      // 生成结构化摘要
      const summary = generateBullSummary(researchReport, keyArguments, targetPrice);

      const newMessages = [
        ...messages,
        new AIMessage({
          content: `【多头研究员报告】\n\n${researchReport}`,
          name: 'BullResearcher',
        }),
      ];

      // 更新研究状态
      const research = {
        ...state.research,
        bull: {
          summary,
          report: researchReport,
          keyArguments,
          catalysts,
          targetPrice,
          confidence,
          analyst: 'BullResearcher',
          timestamp: new Date().toISOString(),
        },
      };

      const result = {
        messages: newMessages,
        research,
        currentStage: 'bull_research_completed',
        progress: Math.min(state.progress + 15, 100),
      };

      monitor.end(true, {
        confidence,
        reportLength: researchReport.length,
        summaryLength: summary.length,
        progressUpdate: result.progress - state.progress,
      });

      logger.info('多头研究完成', context, {
        confidence: confidence.toFixed(2),
        keyArgumentsCount: keyArguments.length,
        catalystsCount: catalysts.length,
        targetPrice,
        progressUpdate: result.progress - state.progress,
      });

      return result;
    } catch (llmError) {
      llmMonitor.error(llmError as Error);
      throw llmError;
    }
  } catch (error) {
    monitor.error(error as Error, {
      analysisAvailable: !!analysis,
      configValid: !!(ticker && date),
    });

    let appError: Error;
    if (error instanceof ValidationError || error instanceof LangGraphError) {
      appError = error;
    } else if (error instanceof Error) {
      appError = new LangGraphError(
        `多头研究过程中发生错误: ${error.message}`,
        'bull_research',
        context
      );
    } else {
      appError = new LangGraphError('多头研究过程中发生未知错误', 'bull_research', context);
    }

    logger.error('多头研究失败', context, appError, {
      originalError: error instanceof Error ? error.message : String(error),
      errorType: error?.constructor?.name || 'Unknown',
    });

    const errorMessage = `多头研究失败: ${appError.message}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【多头研究员】${errorMessage}`,
        name: 'BullResearcher',
      }),
    ];

    const research = {
      ...state.research,
      bull: {
        summary: '多头研究失败',
        report: errorMessage,
        error: true,
        errorCode: (appError as any).code || 'UNKNOWN_ERROR',
        errorDetails:
          process.env.NODE_ENV === 'development'
            ? {
                stack: appError.stack,
                originalError: String(error),
              }
            : undefined,
        analyst: 'BullResearcher',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, research };
  }
}

// 辅助函数：格式化分析师报告
function formatAnalysisReports(analysis: any): string {
  try {
    let reports = '';
    let reportCount = 0;

    if (analysis.fundamental?.summary) {
      reports += `\n【基本面分析】\n${analysis.fundamental.summary}\n`;
      if (analysis.fundamental.keyMetrics) {
        try {
          reports += `关键指标: ${JSON.stringify(analysis.fundamental.keyMetrics, null, 2)}\n`;
        } catch (e) {
          logger.warn('基本面关键指标序列化失败', { component: 'BullResearcher' }, e as Error);
        }
      }
      reportCount++;
    }

    if (analysis.technical?.summary) {
      reports += `\n【技术分析】\n${analysis.technical.summary}\n`;
      if (analysis.technical.technicalSignals) {
        try {
          reports += `技术信号: ${JSON.stringify(analysis.technical.technicalSignals, null, 2)}\n`;
        } catch (e) {
          logger.warn('技术信号序列化失败', { component: 'BullResearcher' }, e as Error);
        }
      }
      reportCount++;
    }

    if (analysis.sentiment?.summary) {
      reports += `\n【情绪分析】\n${analysis.sentiment.summary}\n`;
      reportCount++;
    }

    if (analysis.news?.summary) {
      reports += `\n【新闻分析】\n${analysis.news.summary}\n`;
      reportCount++;
    }

    logger.debug(
      '分析师报告格式化完成',
      { component: 'BullResearcher' },
      {
        reportCount,
        totalLength: reports.length,
        hasReports: reportCount > 0,
      }
    );

    return reports || '分析师报告暂不可用';
  } catch (error) {
    logger.error('格式化分析师报告失败', { component: 'BullResearcher' }, error as Error);
    return '分析师报告格式化失败';
  }
}

// 辅助函数：提取多头论点
function extractBullArguments(report: string): string[] {
  try {
    if (!report || typeof report !== 'string') {
      logger.warn('无效的研究报告内容', { component: 'BullResearcher' }, undefined, {
        reportType: typeof report,
      });
      return [];
    }

    const keyArguments: string[] = [];

    // 使用正则表达式提取关键论点
    const patterns = [
      /价值[重估|修复|发现]/g,
      /成长[潜力|空间|驱动]/g,
      /竞争优势|护城河|壁垒/g,
      /催化剂|利好|机会/g,
      /突破|上涨|看涨/g,
    ];

    const lines = report.split('\n').filter((line) => line.trim());
    let matchCount = 0;

    for (const line of lines) {
      for (const pattern of patterns) {
        if (pattern.test(line) && line.length > 10 && line.length < 200) {
          keyArguments.push(line.trim());
          matchCount++;
          break;
        }
      }
    }

    logger.debug(
      '多头论点提取完成',
      { component: 'BullResearcher' },
      {
        totalLines: lines.length,
        matchCount,
        extractedCount: keyArguments.length,
      }
    );

    return keyArguments.slice(0, 5); // 返回前5个关键论点
  } catch (error) {
    logger.error('提取多头论点失败', { component: 'BullResearcher' }, error as Error);
    return [];
  }
}

// 辅助函数：提取催化剂
function extractCatalysts(report: string): string[] {
  try {
    if (!report || typeof report !== 'string') {
      logger.warn('无效的研究报告内容用于催化剂提取', { component: 'BullResearcher' });
      return [];
    }

    const catalysts: string[] = [];

    const catalystPatterns = [
      /政策[利好|支持]/g,
      /新产品|新业务/g,
      /并购|重组|注入/g,
      /业绩[改善|增长]/g,
      /市场[拓展|份额]/g,
    ];

    const lines = report.split('\n').filter((line) => line.trim());
    let matchCount = 0;

    for (const line of lines) {
      for (const pattern of catalystPatterns) {
        if (pattern.test(line) && line.length > 10 && line.length < 150) {
          catalysts.push(line.trim());
          matchCount++;
          break;
        }
      }
    }

    logger.debug(
      '催化剂提取完成',
      { component: 'BullResearcher' },
      {
        totalLines: lines.length,
        matchCount,
        extractedCount: catalysts.length,
      }
    );

    return catalysts.slice(0, 3); // 返回前3个催化剂
  } catch (error) {
    logger.error('提取催化剂失败', { component: 'BullResearcher' }, error as Error);
    return [];
  }
}

// 辅助函数：提取目标价位
function extractTargetPrice(report: string): number | null {
  try {
    if (!report || typeof report !== 'string') {
      logger.warn('无效的研究报告内容用于目标价位提取', { component: 'BullResearcher' });
      return null;
    }

    const pricePatterns = [
      /目标价[位]?[：:]\s*([0-9.]+)/,
      /目标[：:]\s*([0-9.]+)/,
      /上涨[至到]\s*([0-9.]+)/,
    ];

    for (const pattern of pricePatterns) {
      const match = report.match(pattern);
      if (match && match[1]) {
        const price = parseFloat(match[1]);
        if (!isNaN(price) && price > 0) {
          logger.debug(
            '目标价位提取成功',
            { component: 'BullResearcher' },
            {
              extractedPrice: price,
              matchedPattern: pattern.toString(),
            }
          );
          return price;
        }
      }
    }

    logger.debug('未找到有效的目标价位', { component: 'BullResearcher' });
    return null;
  } catch (error) {
    logger.error('提取目标价位失败', { component: 'BullResearcher' }, error as Error);
    return null;
  }
}

// 辅助函数：计算多头置信度
function calculateBullConfidence(analysis: any, keyArguments: string[]): number {
  try {
    let confidence = 0.5; // 基础置信度
    const factors: Record<string, number> = {};

    // 基于分析师报告的积极信号
    if (
      analysis.fundamental?.investmentRating === '推荐' ||
      analysis.fundamental?.investmentRating === '强烈推荐'
    ) {
      confidence += 0.15;
      factors.fundamentalRating = 0.15;
    }

    if (analysis.technical?.tradingSignal === '买入') {
      confidence += 0.15;
      factors.technicalSignal = 0.15;
    }

    if (analysis.sentiment?.overallSentiment === 'positive') {
      confidence += 0.1;
      factors.sentiment = 0.1;
    }

    if (
      analysis.news?.sentiment &&
      typeof analysis.news.sentiment === 'number' &&
      analysis.news.sentiment > 0.6
    ) {
      confidence += 0.1;
      factors.newsSentiment = 0.1;
    }

    // 基于论点数量和质量
    const argumentBonus = Math.min(keyArguments.length * 0.02, 0.1);
    confidence += argumentBonus;
    factors.argumentCount = argumentBonus;

    const finalConfidence = Math.min(confidence, 0.95); // 最高95%置信度

    logger.debug(
      '多头置信度计算完成',
      { component: 'BullResearcher' },
      {
        baseConfidence: 0.5,
        factors,
        keyArgumentsCount: keyArguments.length,
        finalConfidence,
      }
    );

    return finalConfidence;
  } catch (error) {
    logger.error('计算多头置信度失败', { component: 'BullResearcher' }, error as Error);
    return 0.5; // 返回基础置信度
  }
}

// 辅助函数：生成多头摘要
function generateBullSummary(
  report: string,
  keyArguments: string[],
  targetPrice: number | null
): string {
  try {
    if (!report || typeof report !== 'string') {
      logger.warn('无效的研究报告内容用于摘要生成', { component: 'BullResearcher' });
      return '多头研究摘要生成失败';
    }

    const lines = report.split('\n').filter((line) => line.trim());

    if (lines.length === 0) {
      logger.warn('研究报告内容为空', { component: 'BullResearcher' });
      return '多头研究报告内容为空';
    }

    const firstParagraph = lines.slice(0, 2).join(' ');
    let summary = `多头观点 - ${firstParagraph.substring(0, 120)}`;

    if (firstParagraph.length > 120) {
      summary += '...';
    }

    if (keyArguments.length > 0) {
      const firstArgument = keyArguments[0].substring(0, 50);
      summary += ` 核心论点: ${firstArgument}`;
      if (keyArguments[0].length > 50) {
        summary += '...';
      }
    }

    if (targetPrice && targetPrice > 0) {
      summary += ` 目标价: ${targetPrice}`;
    }

    logger.debug(
      '多头摘要生成完成',
      { component: 'BullResearcher' },
      {
        summaryLength: summary.length,
        hasKeyArguments: keyArguments.length > 0,
        hasTargetPrice: !!targetPrice,
      }
    );

    return summary;
  } catch (error) {
    logger.error('生成多头摘要失败', { component: 'BullResearcher' }, error as Error);
    return '多头研究摘要生成失败';
  }
}
