/**
 * Risk History Viewer Component
 * 风险分析历史记录查看器组件
 */

'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useRiskAssessmentHistory } from '@/hooks/useRiskDataStorage';
import { RiskAssessment } from '@/types/langgraph-database';
import { Calendar, Filter, Minus, TrendingDown, TrendingUp } from 'lucide-react';
import React, { useMemo, useState } from 'react';

interface RiskHistoryViewerProps {
  className?: string;
}

/**
 * 风险历史记录查看器
 * 需求 9.3: 实现风险分析历史记录功能
 */
export function RiskHistoryViewer({ className }: RiskHistoryViewerProps) {
  const [filters, setFilters] = useState({
    ticker: '',
    risk_level: '',
    date_from: '',
    date_to: '',
    limit: 20,
    offset: 0,
  });

  const [showFilters, setShowFilters] = useState(false);

  const { data, isLoading, error, refetch } = useRiskAssessmentHistory(filters);

  // 风险等级颜色映射
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 风险等级图标
  const getRiskLevelIcon = (level: string) => {
    switch (level) {
      case 'low':
        return <TrendingDown className="w-4 h-4" />;
      case 'medium':
        return <Minus className="w-4 h-4" />;
      case 'high':
        return <TrendingUp className="w-4 h-4" />;
      default:
        return <Minus className="w-4 h-4" />;
    }
  };

  // 处理筛选器变化
  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      offset: 0, // 重置分页
    }));
  };

  // 清空筛选器
  const clearFilters = () => {
    setFilters({
      ticker: '',
      risk_level: '',
      date_from: '',
      date_to: '',
      limit: 20,
      offset: 0,
    });
  };

  // 加载更多
  const loadMore = () => {
    setFilters((prev) => ({
      ...prev,
      offset: prev.offset + prev.limit,
    }));
  };

  // 统计信息
  const statistics = useMemo(() => {
    if (!data?.assessments) return null;

    const assessments = data.assessments;
    const riskLevelCounts = assessments.reduce((acc, assessment) => {
      acc[assessment.overall_risk_level] = (acc[assessment.overall_risk_level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgRiskScore =
      assessments.length > 0
        ? assessments.reduce((sum, a) => sum + a.risk_score, 0) / assessments.length
        : 0;

    return {
      total: data.total,
      riskLevelCounts,
      avgRiskScore: avgRiskScore.toFixed(1),
    };
  }, [data]);

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>加载风险历史记录时出错</p>
            <Button onClick={() => refetch()} className="mt-2">
              重试
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* 筛选器 */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              风险分析历史记录
            </CardTitle>
            <Button variant="secondary" size="sm" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
          </div>
        </CardHeader>

        {showFilters && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">股票代码</label>
                <Input
                  placeholder="如: AAPL"
                  value={filters.ticker}
                  onChange={(e) => handleFilterChange('ticker', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">风险等级</label>
                <Select
                  value={filters.risk_level}
                  onValueChange={(value) => handleFilterChange('risk_level', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择风险等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部</SelectItem>
                    <SelectItem value="low">低风险</SelectItem>
                    <SelectItem value="medium">中风险</SelectItem>
                    <SelectItem value="high">高风险</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">开始日期</label>
                <Input
                  type="date"
                  value={filters.date_from}
                  onChange={(e) => handleFilterChange('date_from', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">结束日期</label>
                <Input
                  type="date"
                  value={filters.date_to}
                  onChange={(e) => handleFilterChange('date_to', e.target.value)}
                />
              </div>
            </div>

            <div className="flex justify-end mt-4 gap-2">
              <Button variant="secondary" onClick={clearFilters}>
                清空筛选
              </Button>
              <Button onClick={() => refetch()}>应用筛选</Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* 统计信息 */}
      {statistics && (
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{statistics.total}</div>
                <div className="text-sm text-gray-600">总记录数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {statistics.riskLevelCounts.low || 0}
                </div>
                <div className="text-sm text-gray-600">低风险</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {statistics.riskLevelCounts.medium || 0}
                </div>
                <div className="text-sm text-gray-600">中风险</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {statistics.riskLevelCounts.high || 0}
                </div>
                <div className="text-sm text-gray-600">高风险</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 历史记录列表 */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : data?.assessments?.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <p>暂无风险分析记录</p>
            </div>
          ) : (
            <div className="divide-y">
              {data?.assessments?.map((assessment) => (
                <RiskAssessmentItem
                  key={assessment.risk_id}
                  assessment={assessment}
                  getRiskLevelColor={getRiskLevelColor}
                  getRiskLevelIcon={getRiskLevelIcon}
                />
              ))}
            </div>
          )}

          {/* 加载更多按钮 */}
          {data && data.pagination?.has_more && (
            <div className="p-4 text-center border-t">
              <Button onClick={loadMore} variant="secondary">
                加载更多
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 风险评估项组件
 */
function RiskAssessmentItem({
  assessment,
  getRiskLevelColor,
  getRiskLevelIcon,
}: {
  assessment: RiskAssessment;
  getRiskLevelColor: (level: string) => string;
  getRiskLevelIcon: (level: string) => React.ReactNode;
}) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="p-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Badge className={getRiskLevelColor(assessment.overall_risk_level)}>
            {getRiskLevelIcon(assessment.overall_risk_level)}
            <span className="ml-1">
              {assessment.overall_risk_level === 'low' && '低风险'}
              {assessment.overall_risk_level === 'medium' && '中风险'}
              {assessment.overall_risk_level === 'high' && '高风险'}
            </span>
          </Badge>

          <div>
            <div className="font-medium">风险评分: {assessment.risk_score}/10</div>
            <div className="text-sm text-gray-600">工作流: {assessment.workflow_id}</div>
          </div>
        </div>

        <div className="text-right">
          <div className="text-sm text-gray-600">
            {new Date(assessment.created_at).toLocaleString('zh-CN')}
          </div>
          <Button variant="ghost" size="sm" onClick={() => setExpanded(!expanded)}>
            {expanded ? '收起' : '展开'}
          </Button>
        </div>
      </div>

      {expanded && (
        <div className="mt-4 pl-4 border-l-2 border-gray-200">
          {assessment.summary && (
            <div className="mb-3">
              <h4 className="font-medium text-gray-900 mb-1">风险摘要</h4>
              <p className="text-sm text-gray-600">{assessment.summary}</p>
            </div>
          )}

          {assessment.risk_metrics && (
            <div className="mb-3">
              <h4 className="font-medium text-gray-900 mb-1">关键指标</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                {Object.entries(assessment.risk_metrics).map(([key, value]) => (
                  <div key={key} className="bg-gray-100 p-2 rounded">
                    <div className="font-medium">{key}</div>
                    <div className="text-gray-600">{String(value)}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {assessment.recommendations && (
            <div>
              <h4 className="font-medium text-gray-900 mb-1">风险建议</h4>
              <div className="text-sm text-gray-600">
                {Array.isArray(assessment.recommendations)
                  ? assessment.recommendations.join(', ')
                  : JSON.stringify(assessment.recommendations)}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
