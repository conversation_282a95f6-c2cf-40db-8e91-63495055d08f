'use client';

import {
  AnalysisItem,
  AnalysisStats,
  NavigationState,
  UseNavigationStateReturn,
} from '@/types/navigation';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

/**
 * Custom hook for managing navigation state with React Query integration
 * Handles current path tracking, analysis statistics, and navigation actions
 */
export function useNavigationState(): UseNavigationStateReturn {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | undefined>();

  // Determine if current page is analysis-related
  const isAnalysisPage = pathname?.startsWith('/analysis') || false;

  // Fetch analysis statistics with React Query
  const {
    data: analysisStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useQuery<AnalysisStats>({
    queryKey: ['analysisStats'],
    queryFn: async () => {
      const response = await fetch('/api/analysis/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch analysis statistics');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });

  // Fetch recent analyses
  const {
    data: recentAnalyses = [],
    isLoading: analysesLoading,
    error: analysesError,
  } = useQuery<AnalysisItem[]>({
    queryKey: ['recentAnalyses'],
    queryFn: async () => {
      const response = await fetch('/api/analysis/recent?limit=5');
      if (!response.ok) {
        throw new Error('Failed to fetch recent analyses');
      }
      return response.json();
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Combined loading state
  const loading = statsLoading || analysesLoading;

  // Set error from query errors
  useEffect(() => {
    if (statsError || analysesError) {
      setError('Failed to load navigation data');
    }
  }, [statsError, analysesError]);

  // Navigation action handlers
  const navigateToAnalysis = useCallback(
    (id: string) => {
      try {
        router.push(`/analysis/${id}`);
        // Track navigation event
        trackNavigationEvent('click', 'analysis-detail', `/analysis/${id}`);
      } catch (error) {
        console.error('Failed to navigate to analysis:', error);
        setError('Failed to navigate to analysis');
      }
    },
    [router]
  );

  const navigateToHistory = useCallback(() => {
    try {
      router.push('/analysis/history');
      trackNavigationEvent('click', 'analysis-history', '/analysis/history');
    } catch (error) {
      console.error('Failed to navigate to history:', error);
      setError('Failed to navigate to analysis history');
    }
  }, [router]);

  const navigateToCompare = useCallback(
    (ids?: string[]) => {
      try {
        const url =
          ids && ids.length > 0 ? `/analysis/compare?ids=${ids.join(',')}` : '/analysis/compare';
        router.push(url);
        trackNavigationEvent('click', 'analysis-compare', url);
      } catch (error) {
        console.error('Failed to navigate to compare:', error);
        setError('Failed to navigate to analysis compare');
      }
    },
    [router]
  );

  // Refresh statistics manually
  const refreshStats = useCallback(async () => {
    try {
      await refetchStats();
      await queryClient.invalidateQueries({ queryKey: ['recentAnalyses'] });
      setError(undefined);
    } catch (error) {
      console.error('Failed to refresh stats:', error);
      setError('Failed to refresh navigation data');
    }
  }, [refetchStats, queryClient]);

  // Clear error state
  const clearError = useCallback(() => {
    setError(undefined);
  }, []);

  // Navigation state object
  const navigationState: NavigationState = {
    currentPath: pathname || '',
    isAnalysisPage,
    analysisStats: analysisStats || null,
    recentAnalyses,
    loading,
    error,
  };

  return {
    ...navigationState,
    refreshStats,
    navigateToAnalysis,
    navigateToHistory,
    navigateToCompare,
    clearError,
  };
}

/**
 * Track navigation events for analytics
 */
function trackNavigationEvent(
  eventType: 'click' | 'keyboard' | 'touch',
  target: string,
  path: string
) {
  try {
    // Send navigation event to analytics
    const event = {
      eventType,
      target,
      path,
      timestamp: new Date(),
      sessionId: getSessionId(),
    };

    // Store in localStorage for now (can be replaced with proper analytics)
    const events = JSON.parse(localStorage.getItem('navigationEvents') || '[]');
    events.push(event);

    // Keep only last 100 events
    if (events.length > 100) {
      events.splice(0, events.length - 100);
    }

    localStorage.setItem('navigationEvents', JSON.stringify(events));
  } catch (error) {
    console.error('Failed to track navigation event:', error);
  }
}

/**
 * Get or create session ID
 */
function getSessionId(): string {
  let sessionId = sessionStorage.getItem('navigationSessionId');
  if (!sessionId) {
    sessionId = `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('navigationSessionId', sessionId);
  }
  return sessionId;
}
