/**
 * 统一的日志和错误处理工具
 * 提供结构化日志记录和错误追踪功能
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogContext {
  component?: string;
  userId?: string;
  sessionId?: string;
  ticker?: string;
  taskId?: string;
  requestId?: string;
  [key: string]: any;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  metadata?: Record<string, any>;
}

class Logger {
  private minLevel: LogLevel;
  private isDevelopment: boolean;

  constructor() {
    this.minLevel = this.getLogLevel();
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  private getLogLevel(): LogLevel {
    const level = process.env.LOG_LEVEL?.toUpperCase() || 'INFO';
    switch (level) {
      case 'DEBUG':
        return LogLevel.DEBUG;
      case 'INFO':
        return LogLevel.INFO;
      case 'WARN':
        return LogLevel.WARN;
      case 'ERROR':
        return LogLevel.ERROR;
      case 'FATAL':
        return LogLevel.FATAL;
      default:
        return LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.minLevel;
  }

  private formatLogEntry(entry: LogEntry): string {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;

    let logLine = `[${timestamp}] [${levelName}]`;

    if (entry.context?.component) {
      logLine += ` [${entry.context.component}]`;
    }

    logLine += ` ${entry.message}`;

    if (entry.context && Object.keys(entry.context).length > 0) {
      const contextStr = JSON.stringify(entry.context, null, this.isDevelopment ? 2 : 0);
      logLine += ` | Context: ${contextStr}`;
    }

    if (entry.error) {
      logLine += ` | Error: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.code) {
        logLine += ` (Code: ${entry.error.code})`;
      }
      if (this.isDevelopment && entry.error.stack) {
        logLine += `\nStack: ${entry.error.stack}`;
      }
    }

    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      const metadataStr = JSON.stringify(entry.metadata, null, this.isDevelopment ? 2 : 0);
      logLine += ` | Metadata: ${metadataStr}`;
    }

    return logLine;
  }

  private log(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error,
    metadata?: Record<string, any>
  ): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      metadata,
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
      };
    }

    const logLine = this.formatLogEntry(entry);

    // 根据日志级别选择输出方式
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logLine);
        break;
      case LogLevel.INFO:
        console.info(logLine);
        break;
      case LogLevel.WARN:
        console.warn(logLine);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(logLine);
        break;
    }

    // 在生产环境中，可以添加外部日志服务集成
    if (!this.isDevelopment && level >= LogLevel.ERROR) {
      this.sendToExternalLogger(entry);
    }
  }

  private sendToExternalLogger(entry: LogEntry): void {
    // 这里可以集成外部日志服务，如 Sentry, LogRocket, DataDog 等
    // 暂时留空，可以根据需要实现
  }

  debug(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, undefined, metadata);
  }

  info(message: string, context?: LogContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, undefined, metadata);
  }

  warn(message: string, context?: LogContext, error?: Error, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, error, metadata);
  }

  error(
    message: string,
    context?: LogContext,
    error?: Error,
    metadata?: Record<string, any>
  ): void {
    this.log(LogLevel.ERROR, message, context, error, metadata);
  }

  fatal(
    message: string,
    context?: LogContext,
    error?: Error,
    metadata?: Record<string, any>
  ): void {
    this.log(LogLevel.FATAL, message, context, error, metadata);
  }

  // 创建带有上下文的子日志器
  child(context: LogContext): Logger {
    const childLogger = new Logger();
    const originalLog = childLogger.log.bind(childLogger);

    childLogger.log = (
      level: LogLevel,
      message: string,
      childContext?: LogContext,
      error?: Error,
      metadata?: Record<string, any>
    ) => {
      const mergedContext = { ...context, ...childContext };
      originalLog(level, message, mergedContext, error, metadata);
    };

    return childLogger;
  }
}

// 导出单例实例
export const logger = new Logger();

// 错误处理工具类
export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly context?: LogContext;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: LogContext
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.context = context;

    // 确保堆栈跟踪正确
    Error.captureStackTrace(this, AppError);
  }
}

// 常用错误类型
export class ValidationError extends AppError {
  constructor(message: string, context?: LogContext) {
    super(message, 'VALIDATION_ERROR', 400, true, context);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, context?: LogContext) {
    super(message, 'NOT_FOUND', 404, true, context);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string, context?: LogContext) {
    super(message, 'UNAUTHORIZED', 401, true, context);
    this.name = 'UnauthorizedError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string, service: string, context?: LogContext) {
    super(message, 'EXTERNAL_SERVICE_ERROR', 502, true, { ...context, service });
    this.name = 'ExternalServiceError';
  }
}

export class LangGraphError extends AppError {
  constructor(message: string, operation: string, context?: LogContext) {
    super(message, 'LANGGRAPH_ERROR', 500, true, { ...context, operation });
    this.name = 'LangGraphError';
  }
}

// 错误处理中间件
export function handleApiError(
  error: unknown,
  context?: LogContext
): {
  error: string;
  code: string;
  statusCode: number;
  details?: any;
} {
  let appError: AppError;

  if (error instanceof AppError) {
    appError = error;
  } else if (error instanceof Error) {
    appError = new AppError(error.message, 'UNEXPECTED_ERROR', 500, false, context);
  } else {
    appError = new AppError('An unknown error occurred', 'UNKNOWN_ERROR', 500, false, context);
  }

  // 记录错误日志
  logger.error(appError.message, { ...context, ...appError.context }, appError);

  return {
    error: appError.message,
    code: appError.code,
    statusCode: appError.statusCode,
    details:
      process.env.NODE_ENV === 'development'
        ? {
            stack: appError.stack,
            context: appError.context,
          }
        : undefined,
  };
}

// 性能监控工具
export class PerformanceMonitor {
  private startTime: number;
  private context: LogContext;

  constructor(operation: string, context?: LogContext) {
    this.startTime = Date.now();
    this.context = { ...context, operation };

    logger.debug(`开始执行: ${operation}`, this.context);
  }

  end(success: boolean = true, metadata?: Record<string, any>): void {
    const duration = Date.now() - this.startTime;
    const message = `执行完成: ${this.context.operation} (${duration}ms)`;

    const logMetadata = {
      ...metadata,
      duration,
      success,
    };

    if (success) {
      logger.info(message, this.context, logMetadata);
    } else {
      logger.warn(message, this.context, undefined, logMetadata);
    }
  }

  error(error: Error, metadata?: Record<string, any>): void {
    const duration = Date.now() - this.startTime;
    const message = `执行失败: ${this.context.operation} (${duration}ms)`;

    const logMetadata = {
      ...metadata,
      duration,
      success: false,
    };

    logger.error(message, this.context, error, logMetadata);
  }
}
