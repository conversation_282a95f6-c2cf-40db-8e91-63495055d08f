import { akshareAdapter } from '@/lib/akshare/adapter';
import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AKShareNewsItem } from '@/types';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

// 扩展的情绪关键词库
const POSITIVE_KEYWORDS = [
  '利好',
  '上涨',
  '盈利',
  '增长',
  '新高',
  '推荐',
  '买入',
  '看涨',
  '突破',
  '强势',
  '回升',
  '反弹',
  '涨停',
  '放量',
  '资金流入',
  '机构看好',
  '业绩增长',
  '政策支持',
  '市场看好',
  '投资价值',
  '估值合理',
  '前景乐观',
];

const NEGATIVE_KEYWORDS = [
  '利空',
  '下跌',
  '亏损',
  '下降',
  '新低',
  '风险',
  '卖出',
  '看跌',
  '跌破',
  '弱势',
  '回调',
  '暴跌',
  '跌停',
  '缩量',
  '资金流出',
  '机构减持',
  '业绩下滑',
  '政策收紧',
  '市场担忧',
  '估值过高',
  '泡沫',
  '前景堪忧',
  '预警',
];

const NEUTRAL_KEYWORDS = ['横盘', '震荡', '整理', '观望', '等待', '谨慎', '中性', '平稳', '维持'];

// 高级情绪分析提示模板
const sentimentAnalysisPrompt = PromptTemplate.fromTemplate(`
你是一位专业的市场情绪分析师，专门分析股票相关新闻和社交媒体情绪对股价的影响。你拥有深厚的行为金融学背景和丰富的A股市场经验。

【分析任务】
请对以下股票的市场情绪进行全面分析：

股票代码: {ticker}
分析日期: {date}
新闻数据量: {newsCount}条

【新闻数据】
{newsData}

【分析框架】
请按照以下框架进行情绪分析：

1. **整体情绪倾向**
   - 市场整体情绪：极度乐观/乐观/中性/悲观/极度悲观
   - 情绪强度评分（1-10分）
   - 情绪变化趋势：上升/下降/稳定

2. **新闻情绪分析**
   - 正面新闻占比和主要内容
   - 负面新闻占比和主要风险点
   - 中性新闻的关键信息
   - 新闻来源权威性评估

3. **关键情绪驱动因素**
   - 基本面因素：业绩、政策、行业变化
   - 技术面因素：价格突破、成交量变化
   - 市场面因素：资金流向、机构动向
   - 外部因素：宏观环境、国际影响

4. **投资者行为分析**
   - 散户情绪：恐慌/贪婪指数
   - 机构态度：增持/减持/观望
   - 市场预期：过度乐观/理性/过度悲观

5. **情绪对股价的影响预测**
   - 短期影响：1-3天内可能的价格反应
   - 中期影响：1-2周内的趋势影响
   - 风险提示：情绪极端化的风险

6. **投资建议**
   - 基于情绪分析的操作建议
   - 情绪反转的关键信号
   - 风险控制建议

【输出要求】
- 客观分析市场情绪，避免主观偏见
- 量化情绪指标，提供具体数据支持
- 识别情绪极端点和反转信号
- 结合历史情绪数据进行对比分析
`);

function advancedSentimentAnalysis(text: string): {
  score: number;
  sentiment: 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative';
  confidence: number;
} {
  let score = 0;
  let keywordCount = 0;

  // 计算正面情绪
  for (const keyword of POSITIVE_KEYWORDS) {
    const matches = (text.match(new RegExp(keyword, 'g')) || []).length;
    score += matches * 1;
    keywordCount += matches;
  }

  // 计算负面情绪
  for (const keyword of NEGATIVE_KEYWORDS) {
    const matches = (text.match(new RegExp(keyword, 'g')) || []).length;
    score -= matches * 1;
    keywordCount += matches;
  }

  // 计算置信度
  const confidence = Math.min(keywordCount / 10, 1);

  // 确定情绪等级
  let sentiment: 'very_positive' | 'positive' | 'neutral' | 'negative' | 'very_negative';
  if (score >= 3) sentiment = 'very_positive';
  else if (score >= 1) sentiment = 'positive';
  else if (score <= -3) sentiment = 'very_negative';
  else if (score <= -1) sentiment = 'negative';
  else sentiment = 'neutral';

  return { score, sentiment, confidence };
}

export async function sentimentAnalystNode(
  state: typeof TradingAgentAnnotation.State
): Promise<Partial<typeof TradingAgentAnnotation.State>> {
  const { ticker, date, config, data, messages } = state;

  console.log(`[情绪分析师] 开始分析股票: ${ticker}`);

  try {
    // 优先使用状态中的新闻数据，如果没有则重新获取
    let newsData = data.newsData;

    if (!newsData || !Array.isArray(newsData) || newsData.length === 0) {
      console.log(`[情绪分析师] 重新获取新闻数据...`);
      newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
        symbol: ticker,
        limit: 50, // 获取更多新闻以提高分析准确性
      });
    }

    if (!Array.isArray(newsData) || newsData.length === 0) {
      throw new Error('无法获取有效的新闻数据');
    }

    console.log(`[情绪分析师] 分析 ${newsData.length} 条新闻...`);

    // 进行基础情绪分析
    let totalScore = 0;
    const analyzedArticles = newsData.map((article) => {
      const title = article.标题 || article.title || '';
      const content = article.内容 || article.content || '';
      const fullText = `${title} ${content}`;

      const sentimentResult = advancedSentimentAnalysis(fullText);
      totalScore += sentimentResult.score;

      return {
        ...article,
        sentiment: sentimentResult,
        analyzedText: title,
      };
    });

    // 使用LLM进行深度情绪分析
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    let deepAnalysisReport = '';
    let sentimentMetrics = {};

    if (OPENAI_API_KEY) {
      try {
        const llm = new ChatOpenAI({
          modelName: config.quickThinkLLM || 'gpt-4o-mini',
          temperature: 0.3,
          apiKey: OPENAI_API_KEY,
          configuration: {
            baseURL: OPENAI_BASE_URL,
          },
        });

        // 准备新闻摘要用于LLM分析
        const newsSummary = analyzedArticles
          .slice(0, 20)
          .map(
            (article, index) =>
              `${index + 1}. ${article.标题 || article.title} (情绪: ${
                article.sentiment.sentiment
              }, 分数: ${article.sentiment.score})`
          )
          .join('\n');

        const prompt = await sentimentAnalysisPrompt.format({
          ticker,
          date,
          newsCount: newsData.length,
          newsData: newsSummary,
        });

        console.log(`[情绪分析师] 正在进行深度情绪分析...`);
        const response = await llm.invoke(prompt);
        deepAnalysisReport = response.content as string;

        // 提取情绪指标
        sentimentMetrics = extractSentimentMetrics(deepAnalysisReport);
      } catch (llmError) {
        console.warn(`[情绪分析师] LLM分析失败，使用基础分析: ${llmError}`);
        deepAnalysisReport = generateBasicSentimentReport(analyzedArticles, totalScore);
      }
    } else {
      deepAnalysisReport = generateBasicSentimentReport(analyzedArticles, totalScore);
    }

    // 计算综合情绪指标
    const overallSentiment = calculateOverallSentiment(analyzedArticles);
    const sentimentTrend = calculateSentimentTrend(analyzedArticles);
    const marketMood = assessMarketMood(totalScore, newsData.length);

    // 生成摘要
    const summary = generateSentimentSummary(overallSentiment, marketMood, newsData.length);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【情绪分析师报告】\n\n${deepAnalysisReport}`,
        name: 'SentimentAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      sentiment: {
        summary,
        report: deepAnalysisReport,
        overallSentiment,
        sentimentTrend,
        marketMood,
        sentimentMetrics,
        analyzedArticles: analyzedArticles.slice(0, 10), // 只保存前10条详细分析
        totalScore,
        newsCount: newsData.length,
        analyst: 'SentimentAnalyst',
        timestamp: new Date().toISOString(),
        confidence: calculateSentimentConfidence(analyzedArticles),
      },
    };

    console.log(`[情绪分析师] 分析完成，整体情绪: ${overallSentiment.sentiment}`);
    return {
      messages: newMessages,
      analysis,
      currentStage: 'sentiment_analysis_completed',
      progress: Math.min(state.progress + 20, 100),
    };
  } catch (error) {
    console.error('[情绪分析师] 分析失败:', error);
    const errorMessage = `情绪分析失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【情绪分析师】${errorMessage}`,
        name: 'SentimentAnalyst',
      }),
    ];

    const analysis = {
      ...state.analysis,
      sentiment: {
        summary: '情绪分析失败',
        report: errorMessage,
        error: true,
        analyst: 'SentimentAnalyst',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, analysis };
  }
}

// 辅助函数：计算整体情绪
function calculateOverallSentiment(articles: any[]): {
  sentiment: string;
  score: number;
  distribution: Record<string, number>;
} {
  const distribution = {
    very_positive: 0,
    positive: 0,
    neutral: 0,
    negative: 0,
    very_negative: 0,
  };

  let totalScore = 0;
  articles.forEach((article) => {
    const sentiment = article.sentiment.sentiment;
    if (sentiment in distribution) {
      distribution[sentiment as keyof typeof distribution]++;
    }
    totalScore += article.sentiment.score;
  });

  // 确定主导情绪
  const maxCount = Math.max(...Object.values(distribution));
  const dominantSentiment =
    Object.keys(distribution).find(
      (key) => distribution[key as keyof typeof distribution] === maxCount
    ) || 'neutral';

  return {
    sentiment: dominantSentiment,
    score: totalScore / articles.length,
    distribution,
  };
}

// 辅助函数：计算情绪趋势
function calculateSentimentTrend(articles: any[]): string {
  if (articles.length < 5) return '数据不足';

  const recent = articles.slice(0, Math.floor(articles.length / 2));
  const older = articles.slice(Math.floor(articles.length / 2));

  const recentScore = recent.reduce((sum, a) => sum + a.sentiment.score, 0) / recent.length;
  const olderScore = older.reduce((sum, a) => sum + a.sentiment.score, 0) / older.length;

  if (recentScore > olderScore + 0.5) return '情绪改善';
  if (recentScore < olderScore - 0.5) return '情绪恶化';
  return '情绪稳定';
}

// 辅助函数：评估市场情绪
function assessMarketMood(totalScore: number, newsCount: number): string {
  const avgScore = totalScore / newsCount;

  if (avgScore >= 2) return '极度乐观';
  if (avgScore >= 1) return '乐观';
  if (avgScore <= -2) return '极度悲观';
  if (avgScore <= -1) return '悲观';
  return '中性';
}

// 辅助函数：生成基础情绪报告
function generateBasicSentimentReport(articles: any[], totalScore: number): string {
  const positive = articles.filter((a) => a.sentiment.score > 0).length;
  const negative = articles.filter((a) => a.sentiment.score < 0).length;
  const neutral = articles.length - positive - negative;

  return `基础情绪分析报告：
  
总计分析 ${articles.length} 条新闻：
- 正面新闻：${positive} 条 (${((positive / articles.length) * 100).toFixed(1)}%)
- 负面新闻：${negative} 条 (${((negative / articles.length) * 100).toFixed(1)}%)
- 中性新闻：${neutral} 条 (${((neutral / articles.length) * 100).toFixed(1)}%)

整体情绪得分：${totalScore.toFixed(2)}
平均情绪得分：${(totalScore / articles.length).toFixed(2)}

主要正面因素：业绩增长、政策支持、市场看好
主要负面因素：市场风险、业绩压力、政策不确定性

建议：基于当前情绪分析，建议保持谨慎乐观态度，关注情绪变化趋势。`;
}

// 辅助函数：提取情绪指标
function extractSentimentMetrics(report: string): Record<string, any> {
  const metrics: Record<string, any> = {};

  // 提取情绪强度
  const intensityMatch = report.match(/情绪强度[：:]?\s*([0-9.]+)/);
  if (intensityMatch) metrics.intensity = parseFloat(intensityMatch[1]);

  // 提取恐慌贪婪指数
  const fearGreedMatch = report.match(/恐慌.*贪婪.*[：:]?\s*([0-9.]+)/);
  if (fearGreedMatch) metrics.fearGreedIndex = parseFloat(fearGreedMatch[1]);

  return metrics;
}

// 辅助函数：生成情绪摘要
function generateSentimentSummary(
  overallSentiment: any,
  marketMood: string,
  newsCount: number
): string {
  return `${marketMood} - 分析${newsCount}条新闻，整体情绪${
    overallSentiment.sentiment
  }，平均得分${overallSentiment.score.toFixed(2)}`;
}

// 辅助函数：计算情绪分析置信度
function calculateSentimentConfidence(articles: any[]): number {
  if (articles.length < 5) return 0.3;
  if (articles.length < 20) return 0.6;

  // 基于情绪一致性计算置信度
  const avgConfidence =
    articles.reduce((sum, a) => sum + a.sentiment.confidence, 0) / articles.length;
  return Math.min(0.7 + avgConfidence * 0.2, 0.9);
}
