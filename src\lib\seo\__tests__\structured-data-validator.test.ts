/**
 * 结构化数据验证器测试
 */

import {
  StructuredDataValidator,
  validateStructuredData,
  validateMultipleStructuredData,
  generateValidationReport,
} from '../structured-data-validator';
import { StructuredDataGenerator } from '../structured-data-generator';

describe('StructuredDataValidator', () => {
  let validator: StructuredDataValidator;
  let generator: StructuredDataGenerator;

  beforeEach(() => {
    validator = new StructuredDataValidator();
    generator = new StructuredDataGenerator('zh');
  });

  describe('基础验证功能', () => {
    test('应该创建验证器实例', () => {
      expect(validator).toBeInstanceOf(StructuredDataValidator);
    });

    test('应该验证基础结构', () => {
      const validData = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Test',
      };

      const result = validator.validateBasicStructure(validData);
      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(0);
    });

    test('应该检测缺失的必需字段', () => {
      const invalidData = {
        name: 'Test',
      };

      const result = validator.validateBasicStructure(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required @context field');
      expect(result.errors).toContain('Missing required @type field');
    });
  });

  describe('组织信息验证', () => {
    test('应该验证有效的组织信息', () => {
      const orgSchema = generator.generateOrganizationSchema();
      const result = validator.validateOrganizationSchema(orgSchema);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该检测缺失的必需字段', () => {
      const invalidOrg = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        // 缺少 name, description, url
      };

      const result = validator.validateOrganizationSchema(invalidOrg as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required field: name');
      expect(result.errors).toContain('Missing required field: description');
      expect(result.errors).toContain('Missing required field: url');
    });

    test('应该验证URL格式', () => {
      const orgWithInvalidUrl = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Test',
        description: 'Test description',
        url: 'invalid-url',
      };

      const result = validator.validateOrganizationSchema(orgWithInvalidUrl as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid URL format in url field');
    });

    test('应该验证社交媒体链接', () => {
      const orgWithInvalidSameAs = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Test',
        description: 'Test description',
        url: 'https://example.com',
        sameAs: ['invalid-url', 'https://valid.com'],
      };

      const result = validator.validateOrganizationSchema(orgWithInvalidSameAs as any);
      expect(result.warnings).toContain('Invalid URLs in sameAs: invalid-url');
    });
  });

  describe('网站信息验证', () => {
    test('应该验证有效的网站信息', () => {
      const websiteSchema = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'Test Site',
        url: 'https://example.com',
        description: 'Test description',
      };

      const result = validator.validateWebsiteSchema(websiteSchema as any);
      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证搜索功能', () => {
      const websiteWithSearch = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'Test Site',
        url: 'https://example.com',
        description: 'Test description',
        potentialAction: {
          '@type': 'SearchAction',
          target: 'https://example.com/search?q={search_term_string}',
          'query-input': 'required name=search_term_string',
        },
      };

      const result = validator.validateWebsiteSchema(websiteWithSearch as any);
      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(70);
    });
  });

  describe('软件应用验证', () => {
    test('应该验证有效的软件应用', () => {
      const appSchema = generator.generateTradingPlatformSchema();
      const result = validator.validateSoftwareApplicationSchema(appSchema as any);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该检查应用类别', () => {
      const appWithWrongCategory = {
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        name: 'Test App',
        description: 'Test description',
        applicationCategory: 'GameApplication',
      };

      const result = validator.validateSoftwareApplicationSchema(appWithWrongCategory as any);
      expect(result.warnings).toContain(
        'applicationCategory should be "FinanceApplication" for financial apps'
      );
    });
  });

  describe('金融服务验证', () => {
    test('应该验证有效的金融服务', () => {
      const financialSchema = generator.generateFinancialServiceSchema();
      const result = validator.validateFinancialServiceSchema(financialSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证提供商信息', () => {
      const financialWithInvalidProvider = {
        '@context': 'https://schema.org',
        '@type': 'FinancialProduct',
        name: 'Test Service',
        description: 'Test description',
        category: 'Test Category',
        provider: {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          // 缺少必需字段
        },
      };

      const result = validator.validateFinancialServiceSchema(financialWithInvalidProvider as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid provider organization data');
    });
  });

  describe('投资服务验证', () => {
    test('应该验证有效的投资服务', () => {
      const investmentSchema = generator.generateInvestmentServiceSchema();
      const result = validator.validateInvestmentServiceSchema(investmentSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证服务类型', () => {
      const investmentWithInvalidType = {
        '@context': 'https://schema.org',
        '@type': 'Service',
        name: 'Test Service',
        description: 'Test description',
        provider: generator.generateOrganizationSchema(),
        serviceType: 'Invalid Type',
      };

      const result = validator.validateInvestmentServiceSchema(investmentWithInvalidType as any);
      expect(result.warnings).toContain(
        'serviceType should be one of: Investment Advisory, Portfolio Management, Trading Platform'
      );
    });
  });

  describe('分析报告验证', () => {
    test('应该验证有效的分析报告', () => {
      const reportSchema = generator.generateAnalysisReportSchema('AAPL', 'test-123');
      const result = validator.validateAnalysisReportSchema(reportSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证日期格式', () => {
      const reportWithInvalidDate = {
        '@context': 'https://schema.org',
        '@type': 'Report',
        name: 'Test Report',
        description: 'Test description',
        author: generator.generateOrganizationSchema(),
        datePublished: 'invalid-date',
      };

      const result = validator.validateAnalysisReportSchema(reportWithInvalidDate as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('datePublished should be in ISO 8601 format');
    });
  });

  describe('股票分析验证', () => {
    test('应该验证有效的股票分析', () => {
      const stockSchema = generator.generateStockAnalysisSchema('AAPL', 'Technical', 'Buy');
      const result = validator.validateStockAnalysisSchema(stockSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证分析类型', () => {
      const stockWithInvalidType = {
        '@context': 'https://schema.org',
        '@type': 'AnalysisNewsArticle',
        headline: 'Test Analysis',
        description: 'Test description',
        author: generator.generateOrganizationSchema(),
        datePublished: new Date().toISOString(),
        about: { '@type': 'Corporation', name: 'AAPL', tickerSymbol: 'AAPL' },
        analysisType: 'Invalid Type',
      };

      const result = validator.validateStockAnalysisSchema(stockWithInvalidType as any);
      expect(result.warnings).toContain(
        'analysisType should be one of: Technical, Fundamental, Sentiment, Risk'
      );
    });

    test('应该验证推荐类型', () => {
      const stockWithInvalidRecommendation = {
        '@context': 'https://schema.org',
        '@type': 'AnalysisNewsArticle',
        headline: 'Test Analysis',
        description: 'Test description',
        author: generator.generateOrganizationSchema(),
        datePublished: new Date().toISOString(),
        about: { '@type': 'Corporation', name: 'AAPL', tickerSymbol: 'AAPL' },
        recommendation: 'Invalid Recommendation',
      };

      const result = validator.validateStockAnalysisSchema(stockWithInvalidRecommendation as any);
      expect(result.warnings).toContain('recommendation should be one of: Buy, Sell, Hold');
    });
  });

  describe('AI智能体验证', () => {
    test('应该验证有效的AI智能体', () => {
      const agentSchema = generator.generateAIAgentSchema('基本面分析师', 'Financial Analyst');
      const result = validator.validateAIAgentSchema(agentSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该验证应用类别', () => {
      const agentWithInvalidCategory = {
        '@context': 'https://schema.org',
        '@type': 'SoftwareAgent',
        name: 'Test Agent',
        description: 'Test description',
        creator: generator.generateOrganizationSchema(),
        applicationCategory: 'Invalid Category',
      };

      const result = validator.validateAIAgentSchema(agentWithInvalidCategory as any);
      expect(result.warnings).toContain(
        'applicationCategory should be one of: AI Assistant, Financial Analyst, Trading Bot'
      );
    });
  });

  describe('面包屑导航验证', () => {
    test('应该验证有效的面包屑导航', () => {
      const breadcrumbSchema = generator.generateBreadcrumbSchema('analysis');
      const result = validator.validateBreadcrumbSchema(breadcrumbSchema);

      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(30);
    });

    test('应该验证面包屑项目结构', () => {
      const invalidBreadcrumb = {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            // 缺少 @type, position, name, item
          },
        ],
      };

      const result = validator.validateBreadcrumbSchema(invalidBreadcrumb as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Item 1: @type should be "ListItem"');
      expect(result.errors).toContain('Item 1: position should be a number');
      expect(result.errors).toContain('Item 1: name is required');
      expect(result.errors).toContain('Item 1: item URL is required');
    });
  });

  describe('批量验证', () => {
    test('应该批量验证多个结构化数据', () => {
      const dataArray = [
        generator.generateOrganizationSchema(),
        generator.generateFinancialServiceSchema(),
        generator.generateInvestmentServiceSchema(),
      ];

      const result = validator.validateMultipleStructuredData(dataArray);
      expect(result.isValid).toBe(true);
      expect(result.score).toBeGreaterThan(50);
    });

    test('应该合并多个验证结果', () => {
      const validData = generator.generateOrganizationSchema();
      const invalidData = { name: 'Invalid' }; // 缺少必需字段

      const result = validator.validateMultipleStructuredData([validData, invalidData]);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('便捷函数', () => {
    test('应该使用便捷函数验证单个数据', () => {
      const data = generator.generateOrganizationSchema();
      const result = validateStructuredData(data);

      expect(result.isValid).toBe(true);
    });

    test('应该使用便捷函数批量验证', () => {
      const dataArray = [
        generator.generateOrganizationSchema(),
        generator.generateFinancialServiceSchema(),
      ];

      const result = validateMultipleStructuredData(dataArray);
      expect(result.isValid).toBe(true);
    });

    test('应该生成验证报告', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: ['Test warning'],
        score: 85,
      };

      const report = generateValidationReport(result);
      expect(report).toContain('✅ 通过');
      expect(report).toContain('85/100');
      expect(report).toContain('Test warning');
    });

    test('应该生成失败验证报告', () => {
      const result = {
        isValid: false,
        errors: ['Test error'],
        warnings: ['Test warning'],
        score: 25,
      };

      const report = generateValidationReport(result);
      expect(report).toContain('❌ 失败');
      expect(report).toContain('25/100');
      expect(report).toContain('Test error');
      expect(report).toContain('Test warning');
    });
  });

  describe('验证选项', () => {
    test('应该支持严格模式', () => {
      const strictValidator = new StructuredDataValidator({ strict: true });
      expect(strictValidator).toBeInstanceOf(StructuredDataValidator);
    });

    test('应该支持URL检查选项', () => {
      const validatorWithoutUrlCheck = new StructuredDataValidator({ checkUrls: false });

      const orgWithInvalidUrl = {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Test',
        description: 'Test description',
        url: 'invalid-url',
      };

      const result = validatorWithoutUrlCheck.validateOrganizationSchema(orgWithInvalidUrl as any);
      // 不应该报告URL错误，因为URL检查被禁用
      expect(result.errors).not.toContain('Invalid URL format in url field');
    });

    test('应该支持日期验证选项', () => {
      const validatorWithoutDateCheck = new StructuredDataValidator({ validateDates: false });

      const reportWithInvalidDate = {
        '@context': 'https://schema.org',
        '@type': 'Report',
        name: 'Test Report',
        description: 'Test description',
        author: generator.generateOrganizationSchema(),
        datePublished: 'invalid-date',
      };

      const result = validatorWithoutDateCheck.validateAnalysisReportSchema(
        reportWithInvalidDate as any
      );
      // 不应该报告日期错误，因为日期验证被禁用
      expect(result.errors).not.toContain('datePublished should be in ISO 8601 format');
    });
  });

  describe('错误处理', () => {
    test('应该处理空数据', () => {
      const result = validator.validateStructuredDataObject(null);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Data should be a valid object');
    });

    test('应该处理非对象数据', () => {
      const result = validator.validateStructuredDataObject('invalid');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Data should be a valid object');
    });

    test('应该处理未知类型', () => {
      const unknownTypeData = {
        '@context': 'https://schema.org',
        '@type': 'UnknownType',
        name: 'Test',
      };

      const result = validator.validateStructuredDataObject(unknownTypeData);
      expect(result.isValid).toBe(true); // 基础验证通过
      expect(result.score).toBeGreaterThan(0);
    });
  });
});
