-- TradingAgents Frontend 安全数据恢复脚本
-- 备份时间: 2025-07-29T04:20:00.000Z
-- 数据库: trading_analysis
-- 备份方式: MCP MySQL 连接
-- 说明: 安全的数据恢复脚本，可在现有数据库中执行，不会产生重复键错误

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

USE trading_analysis;

-- ============================================================================
-- 用户系统数据恢复
-- ============================================================================

-- 用户表数据恢复 (3 条记录)
-- 如果记录已存在则更新，不存在则插入
INSERT INTO users (id, email, username, password_hash, email_verified, verification_token, reset_token, reset_token_expires, avatar_url, role, is_active, last_login_at, created_at, updated_at) VALUES
(1, '<EMAIL>', '测试用户', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NULL, NULL, NULL, NULL, 'user', 1, NULL, '2025-07-19 01:49:13', '2025-07-19 01:49:13'),
(2, '<EMAIL>', '管理员', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NULL, NULL, NULL, NULL, 'admin', 1, NULL, '2025-07-19 01:49:13', '2025-07-19 01:49:13'),
(3, '<EMAIL>', 'ezreal', '$2b$12$k5xayxPgLQ9W8JUFcri/C.p0uuGmuqdIHR1SeOjfNsFZ60WSBpA2e', 0, '6d299a8a-5b4d-40b2-a0b4-014f3a166d86', NULL, NULL, NULL, 'user', 1, '2025-07-29 03:31:33', '2025-07-26 02:25:24', '2025-07-29 03:31:33')
ON DUPLICATE KEY UPDATE
    email = VALUES(email),
    username = VALUES(username),
    password_hash = VALUES(password_hash),
    email_verified = VALUES(email_verified),
    verification_token = VALUES(verification_token),
    reset_token = VALUES(reset_token),
    reset_token_expires = VALUES(reset_token_expires),
    avatar_url = VALUES(avatar_url),
    role = VALUES(role),
    is_active = VALUES(is_active),
    last_login_at = VALUES(last_login_at),
    updated_at = VALUES(updated_at);

-- ============================================================================
-- 任务系统数据恢复
-- ============================================================================

-- 任务表数据恢复 (6 条记录)
-- 如果记录已存在则更新，不存在则插入
INSERT INTO tasks (id, task_id, ticker, title, description, status, research_depth, analysis_period, config, priority, created_by, created_at, updated_at, started_at, completed_at, error_message) VALUES
(1, '9861aad8-6485-11f0-8629-4e351929dff3', 'AAPL', 'Apple股票分析', '分析Apple公司股票的投资价值', 'pending', 'medium', '3m', '{"time_horizon": "3_months", "analysis_type": "comprehensive"}', 0, 'user1', '2025-07-19 01:48:59', '2025-07-19 01:48:59', NULL, NULL, NULL),
(2, '9861c7aa-6485-11f0-8629-4e351929dff3', 'TSLA', 'Tesla技术分析', '重点关注Tesla的技术面分析', 'pending', 'deep', '1m', '{"indicators": ["RSI", "MACD", "MA"], "analysis_type": "technical"}', 0, 'user2', '2025-07-19 01:48:59', '2025-07-19 01:48:59', NULL, NULL, NULL),
(3, '9861cb02-6485-11f0-8629-4e351929dff3', 'NVDA', 'NVIDIA基本面分析', '分析NVIDIA的基本面和未来前景', 'pending', 'deep', '6m', '{"focus": ["earnings", "market_share"], "analysis_type": "fundamental"}', 0, 'user1', '2025-07-19 01:48:59', '2025-07-19 01:48:59', NULL, NULL, NULL),
(4, '9861cc80-6485-11f0-8629-4e351929dff3', 'MSFT', 'Microsoft综合分析', '微软公司全面投资分析', 'pending', 'medium', '1m', '{"analysis_type": "comprehensive", "include_sentiment": true}', 0, 'user3', '2025-07-19 01:48:59', '2025-07-19 01:48:59', NULL, NULL, NULL),
(5, '9861cfbc-6485-11f0-8629-4e351929dff3', 'GOOGL', 'Google快速分析', 'Alphabet公司快速投资评估', 'pending', 'shallow', '1w', '{"focus": ["technical"], "analysis_type": "quick"}', 0, 'user2', '2025-07-19 01:48:59', '2025-07-19 01:48:59', NULL, NULL, NULL),
(6, '3e7e8462-8264-45ab-84b1-37c7b00295c0', '688111', '688111股票市场简要分析报告', '股票分析任务', 'completed', 'shallow', '1d', '{"ticker": "688111", "userId": 3, "timestamp": "2025-07-26T10:40:03.702Z", "researchDepth": "shallow", "analysisPeriod": "1d", "selectedAnalysts": ["market"]}', 0, '3', '2025-07-26 02:40:06', '2025-07-28 08:05:13', '2025-07-28 08:03:47', '2025-07-26 02:40:18', NULL)
ON DUPLICATE KEY UPDATE
    task_id = VALUES(task_id),
    ticker = VALUES(ticker),
    title = VALUES(title),
    description = VALUES(description),
    status = VALUES(status),
    research_depth = VALUES(research_depth),
    analysis_period = VALUES(analysis_period),
    config = VALUES(config),
    priority = VALUES(priority),
    created_by = VALUES(created_by),
    updated_at = VALUES(updated_at),
    started_at = VALUES(started_at),
    completed_at = VALUES(completed_at),
    error_message = VALUES(error_message);

-- ============================================================================
-- 分析结果数据恢复
-- ============================================================================

-- 分析结果表数据恢复 (1 条记录)
-- 如果记录已存在则更新，不存在则插入
INSERT INTO analysis_results (id, result_id, task_id, workflow_id, result_type, analyst_type, result_data, analysis_result, confidence_score, summary, key_findings, recommendations, risk_level, risk_assessment, version, is_final, execution_time_ms, status, error_message, created_at, completed_at) VALUES
(1, 'result_1753718713237_jwb0022', '3e7e8462-8264-45ab-84b1-37c7b00295c0', NULL, 'comprehensive', NULL, '{"news": {"report": "### 688111 股票新闻分析（2025-07-28）\\n\\n#### 1. 新闻分类与权重\\n- **重大利好消息**：0条\\n- **重大利空消息**：0条\\n- **中性消息**：20条\\n- **评分**：\\n  - 重大利好消息：0分\\n  - 重大利空消息：0分\\n  - 中性消息：6分\\n\\n#### 2. 关键新闻深度解读\\n选择以下几条新闻进行深度分析：\\n\\n1. **43只科创板股票跻身百元股阵营**\\n   - **真实性与可信度**：来源于东方财富，可信度高。\\n   - **基本面影响**：688111作为科创板股票，跻身百元股阵营可能提升市场关注度，吸引更多投资者。\\n   - **市场反应预测**：短期内可能会有小幅上涨，因市场情绪提升。\\n\\n2. **世界人工智能大会火热召开**\\n   - **真实性与可信度**：大型会议的召开通常会吸引媒体关注，信息可信。\\n   - **基本面影响**：作为AI相关股票，688111可能受益于行业整体的积极氛围。\\n   - **市场反应预测**：市场可能会对AI相关股票产生积极反应，688111有望受益。\\n\\n3. **国产AI Agent产业链正逐步成熟**\\n   - **真实性与可信度**：行业动态，可信度高。\\n   - **基本面影响**：若产业链成熟，688111可能会获得更多市场份额。\\n   - **市场反应预测**：中期内可能会受到正面影响，吸引投资者关注。\\n\\n4. **百元股数量达96只，一日减少3只**\\n   - **真实性与可信度**：数据统计，可信度高。\\n   - **基本面影响**：688111作为百元股，可能会受到市场流动性变化的影响。\\n   - **市场反应预测**：短期内可能会有波动，但整体影响有限。\\n\\n5. **交投放量，人工智能ETF单日成交3.58亿元**\\n   - **真实性与可信度**：市场成交数据，可信度高。\\n   - **基本面影响**：高交投量可能表明市场对AI相关股票的关注度提升。\\n   - **市场反应预测**：688111可能会受到资金流入的正面影响。\\n\\n#### 3. 时效性分析\\n- **新闻发布时间分布**：所有新闻均在同一天发布，集中性强。\\n- **市场反应的时间窗口**：预计市场将在接下来的1-3个交易日内对这些新闻作出反应。\\n- **新闻影响的持续性预测**：短期内影响较为明显，但中期和长期影响需关注后续行业动态。\\n\\n#### 4. 市场影响评估\\n- **短期影响（1-3个交易日）**：预计688111可能小幅上涨，波动幅度在2%-5%之间。\\n- **中期影响（1-4周）**：若AI行业持续向好，688111有望保持稳定增长，波动幅度在5%-10%之间。\\n- **长期影响（1-3个月）**：若公司基本面未发生重大变化，688111可能会在行业整体向好的背景下实现稳健增长，波动幅度在10%-20%之间。\\n\\n#### 5. 信息质量评估\\n- **新闻来源的权威性**：东方财富为知名财经媒体，信息权威性高。\\n- **信息的完整性和准确性**：信息较为全面，涵盖了行业动态和市场表现。\\n- **是否存在信息不对称**：无明显信息不对称，市场普遍关注AI行业动态。\\n- **市场预期与实际情况的差异**：市场对AI行业的预期较高，实际情况与预期基本一致。\\n\\n#### 6. 投资建议\\n- **操作建议**：短期内可考虑适度增持688111，关注市场情绪变化。\\n- **关键关注点和风险提示**：需关注后续AI行业政策变化及市场资金流向，避免因市场波动导致的风险。\\n- **后续需要跟踪的新闻动向**：关注人工智能大会后续成果及相关政策的发布，可能对688111产生重大影响。\\n\\n### 总结\\n688111在当前的市场环境中，受益于AI行业的整体向好趋势，短期内可能会有积极的市场反应。投资者应密切关注行业动态及市场情绪变化，以便及时调整投资策略。", "analyst": "NewsAnalyst", "keyNews": [{"url": "https://example.com/news/5feceb66ffc86f38", "title": "深沪北百元股排行榜", "source": "东方财富", "content": "金山办公301.53-1.310.01计算机300033同花顺292.191.063.43计算机605499东鹏饮料290.20", "news_id": "5feceb66ffc86f38", "importance": 0, "publish_time": "2025-07-28 16:46:49", "related_tickers": "[\\"688111\\"]"}], "summary": "中性影响 - 分析20条新闻，影响评分0.15。重点关注：深沪北百元股排行榜...", "timestamp": "2025-07-28T16:04:03.393Z", "totalNews": 20, "confidence": 0.5}}', NULL, NULL, '综合分析产出', NULL, NULL, NULL, NULL, 1, 0, NULL, 'completed', NULL, '2025-07-28 08:05:13', NULL)
ON DUPLICATE KEY UPDATE
    result_id = VALUES(result_id),
    task_id = VALUES(task_id),
    workflow_id = VALUES(workflow_id),
    result_type = VALUES(result_type),
    analyst_type = VALUES(analyst_type),
    result_data = VALUES(result_data),
    analysis_result = VALUES(analysis_result),
    confidence_score = VALUES(confidence_score),
    summary = VALUES(summary),
    key_findings = VALUES(key_findings),
    recommendations = VALUES(recommendations),
    risk_level = VALUES(risk_level),
    risk_assessment = VALUES(risk_assessment),
    version = VALUES(version),
    is_final = VALUES(is_final),
    execution_time_ms = VALUES(execution_time_ms),
    status = VALUES(status),
    error_message = VALUES(error_message),
    completed_at = VALUES(completed_at);

-- ============================================================================
-- 数据统计信息
-- ============================================================================

SET FOREIGN_KEY_CHECKS = 1;

-- 恢复完成信息
SELECT 'TradingAgents Frontend 安全数据恢复完成!' as status,
       '用户数据: 3条记录 (已处理重复)' as users_restore,
       '任务数据: 6条记录 (已处理重复)' as tasks_restore,
       '分析结果: 1条记录 (已处理重复)' as analysis_restore,
       '恢复时间: 2025-07-29T04:20:00.000Z' as restore_time,
       '数据库: trading_analysis' as database_name;

-- 使用说明
-- 1. 此脚本可安全地在现有数据库中执行
-- 2. 使用 ON DUPLICATE KEY UPDATE 处理重复记录
-- 3. 如果记录已存在则更新，不存在则插入
-- 4. 不会产生重复键错误