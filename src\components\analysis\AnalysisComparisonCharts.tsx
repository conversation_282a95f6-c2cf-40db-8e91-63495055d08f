'use client';

import { Badge } from '@/components/ui/Badge';
import { Card } from '@/components/ui/Card';
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  PieChart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface ComparisonData {
  comparisons: any[];
  summary: {
    totalAnalyses: number;
    tickers: string[];
    statuses: Record<string, number>;
    avgDuration: number;
    successRate: number;
  };
}

interface AnalysisComparisonChartsProps {
  data: ComparisonData;
}

const STATUS_COLORS = {
  pending: '#6B7280',
  running: '#3B82F6',
  completed: '#10B981',
  failed: '#EF4444',
  cancelled: '#F59E0B',
};

const DECISION_COLORS = {
  buy: '#10B981',
  sell: '#EF4444',
  hold: '#F59E0B',
  avoid: '#6B7280',
};

export function AnalysisComparisonCharts({ data }: AnalysisComparisonChartsProps) {
  // 准备性能对比数据
  const performanceData = data.comparisons.map((comparison) => ({
    name: `${comparison.workflow.ticker}`,
    duration: comparison.metrics.duration || 0,
    reports: comparison.metrics.totalReports,
    errors: comparison.metrics.errorCount,
    confidence: comparison.finalDecision?.confidence_level * 100 || 0,
  }));

  // 准备状态分布数据
  const statusData = Object.entries(data.summary.statuses).map(([status, count]) => ({
    name: status,
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || '#6B7280',
  }));

  // 准备决策分布数据
  const decisionData = data.comparisons.reduce((acc, comparison) => {
    if (comparison.finalDecision) {
      const decision = comparison.finalDecision.decision_type;
      acc[decision] = (acc[decision] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  const decisionChartData = Object.entries(decisionData).map(([decision, count]) => ({
    name: decision,
    value: count,
    color: DECISION_COLORS[decision as keyof typeof DECISION_COLORS] || '#6B7280',
  }));

  // 准备雷达图数据
  const radarData = data.comparisons.map((comparison) => {
    const workflow = comparison.workflow;
    const metrics = comparison.metrics;

    return {
      ticker: workflow.ticker,
      performance: Math.min(100, (3600 - (metrics.duration || 3600)) / 36), // 性能分数 (基于用时)
      quality: Math.min(
        100,
        ((metrics.totalReports - metrics.errorCount) / Math.max(1, metrics.totalReports)) * 100
      ), // 质量分数
      completeness: metrics.hasDecision ? 100 : 50, // 完整性分数
      confidence: comparison.finalDecision?.confidence_level * 100 || 0, // 信心度
      efficiency: Math.min(100, metrics.totalReports * 10), // 效率分数 (基于报告数量)
    };
  });

  // 准备时间线数据
  const timelineData = data.comparisons
    .filter((c) => c.workflow.created_at)
    .sort(
      (a, b) =>
        new Date(a.workflow.created_at).getTime() - new Date(b.workflow.created_at).getTime()
    )
    .map((comparison, index) => ({
      index: index + 1,
      ticker: comparison.workflow.ticker,
      duration: comparison.metrics.duration || 0,
      reports: comparison.metrics.totalReports,
      date: new Date(comparison.workflow.created_at).toLocaleDateString(),
    }));

  return (
    <div className="space-y-6">
      {/* 性能对比图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用时对比 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">分析用时对比</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip
                formatter={(value: number) => [
                  `${Math.floor(value / 60)}分${value % 60}秒`,
                  '用时',
                ]}
              />
              <Bar dataKey="duration" fill="#3B82F6" />
            </BarChart>
          </ResponsiveContainer>
        </Card>

        {/* 报告数量对比 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">报告数量对比</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="reports" fill="#10B981" name="总报告数" />
              <Bar dataKey="errors" fill="#EF4444" name="错误数" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* 分布图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 状态分布 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">状态分布</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>

        {/* 决策分布 */}
        {decisionChartData.length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">决策分布</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={decisionChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {decisionChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        )}
      </div>

      {/* 综合评估雷达图 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">综合评估对比</h3>
        <ResponsiveContainer width="100%" height={400}>
          <RadarChart data={radarData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="ticker" />
            <PolarRadiusAxis angle={90} domain={[0, 100]} />
            {data.comparisons.map((_, index) => (
              <Radar
                key={index}
                name={radarData[index]?.ticker}
                dataKey={`performance`}
                stroke={`hsl(${index * 60}, 70%, 50%)`}
                fill={`hsl(${index * 60}, 70%, 50%)`}
                fillOpacity={0.1}
              />
            ))}
            <Legend />
            <Tooltip />
          </RadarChart>
        </ResponsiveContainer>
        <div className="mt-4 text-sm text-gray-600">
          <p>评估维度说明:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>性能: 基于分析用时计算的性能分数</li>
            <li>质量: 基于错误率计算的质量分数</li>
            <li>完整性: 是否生成最终决策</li>
            <li>信心度: 决策的信心水平</li>
            <li>效率: 基于生成报告数量的效率分数</li>
          </ul>
        </div>
      </Card>

      {/* 信心度对比 */}
      {performanceData.some((d) => d.confidence > 0) && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">决策信心度对比</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={performanceData.filter((d) => d.confidence > 0)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis domain={[0, 100]} />
              <Tooltip formatter={(value: number) => [`${value.toFixed(1)}%`, '信心度']} />
              <Bar dataKey="confidence" fill="#8B5CF6" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      )}

      {/* 时间线趋势 */}
      {timelineData.length > 1 && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">分析趋势</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={timelineData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="duration"
                stroke="#3B82F6"
                name="用时(秒)"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="reports"
                stroke="#10B981"
                name="报告数"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </Card>
      )}

      {/* 详细指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {data.comparisons.map((comparison) => {
          const workflow = comparison.workflow;
          const metrics = comparison.metrics;
          const finalDecision = comparison.finalDecision;

          return (
            <Card key={workflow.workflow_id} className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-blue-600">{workflow.ticker}</h4>
                <Badge variant={workflow.status === 'completed' ? 'success' : 'default'}>
                  {workflow.status}
                </Badge>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">用时:</span>
                  <span className="font-medium">
                    {metrics.duration
                      ? `${Math.floor(metrics.duration / 60)}分${metrics.duration % 60}秒`
                      : '-'}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">报告:</span>
                  <span className="font-medium">{metrics.totalReports}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">错误:</span>
                  <span
                    className={`font-medium ${
                      metrics.errorCount > 0 ? 'text-red-600' : 'text-gray-900'
                    }`}
                  >
                    {metrics.errorCount}
                  </span>
                </div>

                {finalDecision && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">决策:</span>
                      <span className="font-medium">{finalDecision.decision_type}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-gray-600">信心度:</span>
                      <span className="font-medium">
                        {(finalDecision.confidence_level * 100).toFixed(1)}%
                      </span>
                    </div>
                  </>
                )}
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
