# 错误处理和日志系统使用指南

## 概述

本项目实现了一套完整的错误处理和日志系统，旨在解决服务器上错误不够明显、难以排查问题的情况。系统包含以下核心组件：

- **统一日志系统** (`src/lib/logger.ts`)
- **API 错误处理中间件** (`src/lib/api-middleware.ts`)
- **错误监控和告警** (`src/lib/monitoring.ts`)
- **健康检查端点** (`src/app/api/health/route.ts`)

## 核心特性

### 1. 结构化日志记录

- 支持多种日志级别：DEBUG, INFO, WARN, ERROR, FATAL
- 包含上下文信息：组件名、用户 ID、请求 ID 等
- 自动记录错误堆栈和元数据
- 开发环境和生产环境的不同输出格式

### 2. 统一错误处理

- 自定义错误类型：ValidationError, NotFoundError, LangGraphError 等
- 自动错误分类和状态码映射
- 开发环境显示详细错误信息，生产环境隐藏敏感信息
- 错误上下文追踪

### 3. 性能监控

- 自动记录 API 响应时间
- 慢请求检测和告警
- 端点级别的性能统计
- 内存使用监控

### 4. 实时监控和告警

- 错误率监控和阈值告警
- 系统健康状态评估
- 支持 Slack、邮件等告警渠道
- 监控报告生成

## 快速开始

### 1. 环境配置

复制 `.env.example.improved` 到 `.env.local` 并配置相关参数：

```bash
# 日志级别配置
LOG_LEVEL=INFO

# 性能监控配置
ENABLE_PERFORMANCE_MONITORING=true
API_TIMEOUT_MS=30000
RESPONSE_TIME_THRESHOLD=5000

# 告警配置
ERROR_RATE_THRESHOLD=0.05
SLACK_WEBHOOK_URL=your-slack-webhook-url
```

### 2. 在 API 路由中使用

使用 `withApiHandler` 包装你的 API 处理函数：

```typescript
// src/app/api/example/route.ts
import { NextRequest } from 'next/server';
import { withApiHandler, validateRequestBody } from '@/lib/api-middleware';
import { logger, ValidationError } from '@/lib/logger';

export const POST = withApiHandler(async (request: NextRequest, context) => {
  // 验证请求体
  const body = await validateRequestBody(
    request,
    (data) => {
      if (!data.ticker) {
        throw new ValidationError('股票代码不能为空', context);
      }
      return data;
    },
    context
  );

  // 业务逻辑
  logger.info('开始处理请求', context, { ticker: body.ticker });

  const result = await processRequest(body);

  logger.info('请求处理完成', context, { resultId: result.id });

  return result;
});
```

### 3. 在业务逻辑中使用日志

```typescript
import { logger, PerformanceMonitor, LangGraphError } from '@/lib/logger';

export async function processAnalysis(ticker: string, config: any) {
  const context = {
    component: 'AnalysisService',
    ticker,
    operation: 'process_analysis',
  };

  const monitor = new PerformanceMonitor('analysis_processing', context);

  try {
    logger.info('开始分析处理', context, { ticker, config });

    // 业务逻辑
    const result = await performAnalysis(ticker, config);

    monitor.end(true, { resultSize: result.length });
    logger.info('分析处理完成', context, { ticker, resultCount: result.length });

    return result;
  } catch (error) {
    monitor.error(error as Error);

    if (error instanceof Error && error.message.includes('API_LIMIT')) {
      throw new LangGraphError('API调用限制', 'api_limit', context);
    }

    throw error;
  }
}
```

## 日志级别使用指南

### DEBUG

用于详细的调试信息，仅在开发环境或调试时启用：

```typescript
logger.debug('LLM推理参数', context, {
  model: 'gpt-4o',
  temperature: 0.2,
  promptLength: prompt.length,
});
```

### INFO

用于记录正常的业务流程和重要事件：

```typescript
logger.info('用户登录成功', context, {
  userId: user.id,
  loginMethod: 'oauth',
});
```

### WARN

用于记录潜在问题或异常情况：

```typescript
logger.warn('API响应时间较长', context, undefined, {
  responseTime: 5200,
  threshold: 5000,
});
```

### ERROR

用于记录错误和异常：

```typescript
logger.error('数据库连接失败', context, error, {
  retryCount: 3,
  lastAttempt: new Date().toISOString(),
});
```

### FATAL

用于记录严重的系统错误：

```typescript
logger.fatal('系统内存不足', context, error, {
  memoryUsage: process.memoryUsage(),
  availableMemory: os.freemem(),
});
```

## 错误类型使用指南

### ValidationError

用于请求参数验证错误：

```typescript
if (!ticker || ticker.length === 0) {
  throw new ValidationError('股票代码不能为空', context);
}
```

### NotFoundError

用于资源不存在的错误：

```typescript
const analysis = await getAnalysis(id);
if (!analysis) {
  throw new NotFoundError(`分析记录不存在: ${id}`, context);
}
```

### UnauthorizedError

用于权限验证错误：

```typescript
if (!user || !user.hasPermission('analysis')) {
  throw new UnauthorizedError('无权限访问分析功能', context);
}
```

### ExternalServiceError

用于外部服务调用错误：

```typescript
try {
  const response = await openaiClient.chat.completions.create(params);
} catch (error) {
  throw new ExternalServiceError('OpenAI API调用失败', 'OpenAI', context);
}
```

### LangGraphError

用于 LangGraph 相关的错误：

```typescript
if (!analysisResult || analysisResult.length < 100) {
  throw new LangGraphError('分析结果不完整', 'incomplete_analysis', context);
}
```

## 监控和告警

### 健康检查

访问 `/api/health` 获取系统健康状态：

```bash
curl http://localhost:3000/api/health
```

响应示例：

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": 3600,
    "memory": {
      "used": 134217728,
      "total": 268435456
    },
    "errors": {
      "totalErrors": 5,
      "errorRate": 0.02,
      "errorsByType": {
        "ValidationError": 3,
        "LangGraphError": 2
      }
    },
    "performance": {
      "averageResponseTime": 245.6,
      "slowRequests": 2,
      "totalRequests": 250
    }
  }
}
```

### 监控报告

获取详细的监控报告：

```bash
curl -X POST http://localhost:3000/api/health \
  -H "Content-Type: application/json" \
  -d '{"includeReport": true}'
```

### 告警配置

在生产环境中配置告警渠道：

```bash
# Slack告警
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# 邮件告警
ALERT_EMAIL=<EMAIL>

# 告警阈值
ERROR_RATE_THRESHOLD=0.05
RESPONSE_TIME_THRESHOLD=5000
```

## 最佳实践

### 1. 上下文信息

始终提供有意义的上下文信息：

```typescript
const context = {
  component: 'BullResearcher',
  ticker: 'AAPL',
  userId: user.id,
  requestId: generateRequestId(),
};
```

### 2. 性能监控

对关键操作使用性能监控：

```typescript
const monitor = new PerformanceMonitor('llm_inference', context);
try {
  const result = await llm.invoke(prompt);
  monitor.end(true, { responseLength: result.length });
  return result;
} catch (error) {
  monitor.error(error as Error);
  throw error;
}
```

### 3. 错误分类

使用合适的错误类型，便于错误统计和处理：

```typescript
// 好的做法
throw new ValidationError('参数验证失败', context);

// 避免的做法
throw new Error('参数验证失败');
```

### 4. 敏感信息保护

避免在日志中记录敏感信息：

```typescript
// 好的做法
logger.info('用户认证成功', context, {
  userId: user.id,
  hasApiKey: !!user.apiKey,
});

// 避免的做法
logger.info('用户认证成功', context, {
  userId: user.id,
  apiKey: user.apiKey, // 敏感信息
});
```

## 故障排查

### 1. 查看日志

开发环境中，日志会输出到控制台。生产环境中，建议配置日志收集系统。

### 2. 健康检查

定期检查 `/api/health` 端点，监控系统状态。

### 3. 错误统计

通过监控报告查看错误分布和趋势：

```bash
curl -X POST http://localhost:3000/api/health \
  -H "Content-Type: application/json" \
  -d '{"includeReport": true}' | jq '.data.report'
```

### 4. 性能分析

关注慢请求和响应时间异常：

- 平均响应时间超过阈值
- 特定端点的错误率过高
- 内存使用异常增长

## 扩展和集成

### 1. 外部日志服务

集成 Sentry、LogRocket、DataDog 等服务：

```typescript
// 在 logger.ts 中的 sendToExternalLogger 方法中实现
private sendToExternalLogger(entry: LogEntry): void {
  if (process.env.SENTRY_DSN) {
    Sentry.captureException(entry.error);
  }

  if (process.env.DATADOG_API_KEY) {
    // 发送到 DataDog
  }
}
```

### 2. 自定义告警渠道

在 `monitoring.ts` 中添加新的告警渠道：

```typescript
private async sendCustomAlert(alertType: string, data: any): Promise<void> {
  // 实现自定义告警逻辑
}
```

### 3. 监控指标扩展

添加业务相关的监控指标：

```typescript
// 在 MonitoringService 中添加新的指标收集方法
recordBusinessMetric(metric: string, value: number, context?: LogContext): void {
  // 实现业务指标收集
}
```

## 总结

通过使用这套错误处理和日志系统，你可以：

1. **快速定位问题**：结构化的日志和错误信息
2. **监控系统健康**：实时的性能和错误监控
3. **及时响应异常**：自动告警和通知
4. **优化系统性能**：详细的性能统计和分析

这将大大提高系统的可观测性和可维护性，让问题排查变得更加容易和高效。
