# 多语言 SEO 支持实现文档

## 概述

本文档描述了 TradingAgents 项目中多语言 SEO 支持的实现，包括语言检测、内容管理、hreflang 标签生成和 SEO 中间件处理。

## 功能特性

### ✅ 已实现功能

1. **多语言内容管理**

   - 中英文 SEO 内容配置
   - 动态内容生成（支持变量替换）
   - 社交媒体优化内容

2. **语言检测系统**

   - URL 路径检测（优先级最高）
   - Cookie 偏好检测
   - Accept-Language 头检测
   - 智能回退机制

3. **hreflang 标签支持**

   - 自动生成 hreflang 链接
   - Next.js alternates 配置
   - 搜索引擎友好的 URL 结构

4. **SEO 中间件增强**
   - 语言重定向处理
   - 多语言响应头设置
   - Cookie 管理
   - 性能优化

## 文件结构

```
src/lib/seo/
├── i18n-seo.ts              # 多语言 SEO 核心实现
├── middleware.ts             # 增强的 SEO 中间件
├── runtime-config.ts         # 运行时配置管理
├── verify-i18n.ts           # 验证脚本
├── __tests__/
│   ├── i18n-seo.test.ts      # 单元测试
│   └── i18n-integration.test.ts # 集成测试
└── I18N_README.md            # 本文档
```

## 使用方法

### 1. 语言检测

```typescript
import { LanguageDetector } from '@/lib/seo/i18n-seo';

// 从多个来源检测语言
const result = LanguageDetector.detect({
  pathname: '/en/analysis',
  acceptLanguage: 'zh-CN,zh;q=0.9,en;q=0.8',
  cookieValue: 'zh',
});

console.log(result.locale); // 'en'
console.log(result.cleanPath); // '/analysis'
```

### 2. 获取多语言内容

```typescript
import { I18nContentSwitcher } from '@/lib/seo/i18n-seo';

// 获取页面内容
const content = I18nContentSwitcher.getPageContent('home', 'zh');
console.log(content.title); // 'TradingAgents - 多智能体大语言模型金融交易框架'

// 获取动态内容
const dynamicContent = I18nContentSwitcher.getDynamicContent('analysis', 'zh', {
  stockSymbol: 'AAPL',
});
console.log(dynamicContent.title); // 'AAPL - TradingAgents'
```

### 3. 生成 hreflang 链接

```typescript
import { HreflangGenerator } from '@/lib/seo/i18n-seo';

// 生成 hreflang 链接
const links = HreflangGenerator.generateHreflangLinks('/analysis', 'zh');
/*
[
  { hreflang: 'zh-CN', href: 'https://tradingagents.com/analysis' },
  { hreflang: 'en-US', href: 'https://tradingagents.com/en/analysis' },
  { hreflang: 'x-default', href: 'https://tradingagents.com/analysis' }
]
*/

// 生成 Next.js alternates 配置
const alternates = HreflangGenerator.generateAlternates('/analysis', 'zh');
/*
{
  canonical: 'https://tradingagents.com/analysis',
  languages: {
    zh: 'https://tradingagents.com/analysis',
    en: 'https://tradingagents.com/en/analysis',
    'x-default': 'https://tradingagents.com/analysis'
  }
}
*/
```

### 4. 生成语言切换链接

```typescript
import { I18nContentSwitcher } from '@/lib/seo/i18n-seo';

const switchLinks = I18nContentSwitcher.generateLanguageSwitchLinks('/analysis', 'zh');
/*
[
  { locale: 'zh', label: '中文', href: '/analysis', active: true },
  { locale: 'en', label: 'English', href: '/en/analysis', active: false }
]
*/
```

### 5. 在组件中使用

```tsx
// 在 React 组件中使用
import { I18nContentSwitcher, getPageLocale } from '@/lib/seo';

function LanguageSwitcher({ currentPath }: { currentPath: string }) {
  const currentLocale = getPageLocale(currentPath);
  const switchLinks = I18nContentSwitcher.generateLanguageSwitchLinks(currentPath, currentLocale);

  return (
    <div className="language-switcher">
      {switchLinks.map((link) => (
        <a key={link.locale} href={link.href} className={link.active ? 'active' : ''}>
          {link.label}
        </a>
      ))}
    </div>
  );
}
```

## 配置说明

### 语言检测配置

```typescript
export const LANGUAGE_DETECTION_CONFIG = {
  supportedLocales: ['zh', 'en'],
  defaultLocale: 'zh',
  localeMapping: {
    zh: 'zh-CN',
    'zh-CN': 'zh',
    en: 'en-US',
    'en-US': 'en',
  },
  cookieConfig: {
    name: 'tradingagents-locale',
    maxAge: 365 * 24 * 60 * 60, // 1年
    path: '/',
    sameSite: 'lax',
  },
};
```

### hreflang 配置

```typescript
export const HREFLANG_CONFIG = {
  localeToHreflang: {
    zh: 'zh-CN',
    en: 'en-US',
  },
  defaultHreflang: 'x-default',
  urlPatterns: {
    zh: '', // 中文为默认，无前缀
    en: '/en', // 英文有前缀
  },
};
```

## URL 结构

### 中文（默认语言）

- 首页: `/`
- 分析页: `/analysis`
- 任务页: `/tasks`
- 消息页: `/messages`
- 创建任务: `/create-task`

### 英文

- 首页: `/en`
- 分析页: `/en/analysis`
- 任务页: `/en/tasks`
- 消息页: `/en/messages`
- 创建任务: `/en/create-task`

## 中间件处理流程

1. **SEO 处理**: 处理 sitemap.xml、robots.txt 等
2. **语言检测**: 从 URL、Cookie、Accept-Language 检测语言
3. **重定向处理**: 根据语言偏好进行重定向
4. **响应头设置**: 设置 Content-Language、缓存等头
5. **Cookie 管理**: 保存用户语言偏好

## 性能优化

1. **缓存策略**: 不同类型页面使用不同的缓存策略
2. **预加载**: 关键资源预加载
3. **压缩**: 启用 gzip 压缩
4. **CDN 支持**: 支持 CDN 配置

## 测试和验证

### 运行验证脚本

```bash
# 在 Node.js 环境中运行验证
node -r ts-node/register src/lib/seo/verify-i18n.ts
```

### 手动测试

1. **语言检测测试**:

   - 访问 `/` 应显示中文内容
   - 访问 `/en` 应显示英文内容
   - 设置浏览器语言为英文，访问 `/` 应重定向到 `/en`

2. **hreflang 测试**:

   - 查看页面源码，应包含正确的 hreflang 链接
   - 使用 Google Search Console 验证 hreflang 实现

3. **SEO 测试**:
   - 使用 Google PageSpeed Insights 测试性能
   - 使用 Google Rich Results Test 验证结构化数据

## 故障排除

### 常见问题

1. **语言检测不正确**:

   - 检查 Cookie 设置
   - 验证 Accept-Language 头
   - 确认 URL 路径格式

2. **重定向循环**:

   - 检查中间件配置
   - 验证 URL 模式匹配

3. **hreflang 错误**:
   - 确认 URL 结构正确
   - 检查语言代码映射

### 调试方法

1. **开发环境调试头**:

   ```
   X-SEO-Locale: zh
   X-SEO-Clean-Path: /analysis
   ```

2. **日志输出**:
   - 中间件会在控制台输出调试信息
   - 检查浏览器开发者工具的网络面板

## 最佳实践

1. **URL 设计**:

   - 保持 URL 结构简洁
   - 使用语言前缀而非子域名
   - 确保 URL 可读性

2. **内容管理**:

   - 为每种语言提供完整的内容
   - 保持关键词的本地化
   - 优化社交媒体分享内容

3. **性能考虑**:
   - 使用适当的缓存策略
   - 避免不必要的重定向
   - 优化图片和资源加载

## 未来扩展

1. **更多语言支持**: 可以轻松添加更多语言
2. **地区化支持**: 支持同一语言的不同地区
3. **动态语言切换**: 支持无刷新语言切换
4. **SEO 分析**: 集成更多 SEO 分析工具

## 相关文档

- [SEO 基础实现文档](./README.md)
- [Next.js 国际化文档](https://nextjs.org/docs/advanced-features/i18n)
- [Google hreflang 指南](https://developers.google.com/search/docs/advanced/crawling/localized-versions)
