// 数据库设置脚本
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function setupDatabase() {
  console.log('🚀 开始设置数据库...');
  
  const config = {
    host: process.env.DB_HOST || '127.0.0.1',
    port: Number(process.env.DB_PORT) || 13306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '12345678',
  };

  const dbName = process.env.DB_NAME || 'trading_analysis';

  console.log('📋 配置信息:');
  console.log('- Host:', config.host);
  console.log('- Port:', config.port);
  console.log('- User:', config.user);
  console.log('- Database:', dbName);

  let connection;

  try {
    // 1. 连接到MySQL服务器（不指定数据库）
    console.log('\n1️⃣ 连接到MySQL服务器...');
    connection = await mysql.createConnection(config);
    console.log('✅ 连接成功');

    // 2. 创建数据库（如果不存在）
    console.log('\n2️⃣ 创建数据库...');
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 "${dbName}" 已创建或已存在`);

    // 3. 选择数据库
    await connection.execute(`USE \`${dbName}\``);
    console.log(`✅ 已选择数据库 "${dbName}"`);

    // 4. 读取并执行schema.sql
    console.log('\n3️⃣ 执行数据库架构脚本...');
    const schemaPath = path.join(__dirname, 'database', 'schema.sql');
    
    if (fs.existsSync(schemaPath)) {
      const schemaSql = fs.readFileSync(schemaPath, 'utf8');
      
      // 分割SQL语句（以分号分隔）
      const statements = schemaSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await connection.execute(statement);
          } catch (error) {
            // 忽略表已存在的错误
            if (!error.message.includes('already exists')) {
              console.warn('⚠️  执行语句时出现警告:', error.message);
            }
          }
        }
      }
      console.log('✅ 数据库架构创建完成');
    } else {
      console.log('⚠️  schema.sql 文件不存在，跳过架构创建');
    }

    // 5. 验证表是否创建成功
    console.log('\n4️⃣ 验证表结构...');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 已创建的表:');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // 6. 检查是否有示例数据
    try {
      const [taskCount] = await connection.execute('SELECT COUNT(*) as count FROM tasks');
      const count = taskCount[0].count;
      console.log(`\n📊 tasks表中有 ${count} 条记录`);
      
      if (count === 0) {
        console.log('💡 提示: 可以运行示例数据脚本来添加测试数据');
      }
    } catch (error) {
      console.log('⚠️  无法检查示例数据:', error.message);
    }

    console.log('\n🎉 数据库设置完成！');

  } catch (error) {
    console.error('\n❌ 数据库设置失败:');
    console.error('错误代码:', error.code);
    console.error('错误信息:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决建议:');
      console.log('1. 确保MySQL服务正在运行');
      console.log('2. 检查端口配置是否正确');
      console.log('3. 尝试以下命令启动MySQL:');
      console.log('   - Windows: net start mysql');
      console.log('   - macOS: brew services start mysql');
      console.log('   - Linux: sudo systemctl start mysql');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupDatabase().catch(console.error);
}

module.exports = { setupDatabase };