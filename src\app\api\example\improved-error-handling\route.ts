/**
 * 改进的API路由示例
 * 展示如何使用新的错误处理和日志系统
 */

import { validateRequestBody, withApiHandler } from '@/lib/api-middleware';
import { ExternalServiceError, logger, ValidationError } from '@/lib/logger';
import { NextRequest } from 'next/server';
import { z } from 'zod';

// 请求体验证模式
const AnalysisRequestSchema = z.object({
  ticker: z.string().min(1, '股票代码不能为空').max(10, '股票代码过长'),
  researchDepth: z.enum(['basic', 'standard', 'deep']).optional().default('standard'),
  analysisPeriod: z.number().min(1).max(365).optional().default(30),
  config: z
    .object({
      deepThinkLLM: z.string().optional(),
      enableRiskAnalysis: z.boolean().optional().default(true),
    })
    .optional(),
});

type AnalysisRequest = z.infer<typeof AnalysisRequestSchema>;

// 使用新的错误处理中间件包装API处理器
export const POST = withApiHandler(async (request: NextRequest, context) => {
  // 验证请求体
  const body = await validateRequestBody(
    request,
    (data) => {
      try {
        return AnalysisRequestSchema.parse(data);
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errorMessages = error.errors
            .map((e) => `${e.path.join('.')}: ${e.message}`)
            .join(', ');
          throw new ValidationError(`请求参数验证失败: ${errorMessages}`, context);
        }
        throw new ValidationError('请求体格式错误', context);
      }
    },
    context
  );

  logger.info('开始处理分析请求', context, {
    ticker: body.ticker,
    researchDepth: body.researchDepth,
    analysisPeriod: body.analysisPeriod,
  });

  // 模拟业务逻辑
  const result = await processAnalysisRequest(body, context);

  logger.info('分析请求处理完成', context, {
    ticker: body.ticker,
    resultId: result.analysisId,
    status: result.status,
  });

  return result;
});

// 业务逻辑处理函数
async function processAnalysisRequest(
  request: AnalysisRequest,
  context: any
): Promise<{
  analysisId: string;
  status: string;
  message: string;
  timestamp: string;
}> {
  const { ticker, researchDepth, analysisPeriod } = request;

  // 验证股票代码格式
  if (!/^[A-Z0-9]{2,10}$/.test(ticker.toUpperCase())) {
    throw new ValidationError('股票代码格式不正确', { ...context, ticker });
  }

  // 检查API密钥配置
  const apiKey = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
  if (!apiKey) {
    throw new ExternalServiceError('OpenAI API密钥未配置', 'OpenAI', context);
  }

  // 模拟外部服务调用
  try {
    logger.debug('调用外部数据服务', context, { ticker, researchDepth });

    // 模拟可能失败的外部服务调用
    if (ticker === 'FAIL') {
      throw new Error('External service unavailable');
    }

    // 模拟异步处理
    await new Promise((resolve) => setTimeout(resolve, 100));

    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      analysisId,
      status: 'started',
      message: '分析已启动',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    if (error instanceof Error && error.message.includes('unavailable')) {
      throw new ExternalServiceError('外部数据服务暂时不可用，请稍后重试', 'DataService', {
        ...context,
        ticker,
      });
    }
    throw error;
  }
}

// GET 请求示例 - 获取分析状态
export const GET = withApiHandler(async (request: NextRequest, context) => {
  const url = new URL(request.url);
  const analysisId = url.searchParams.get('analysisId');

  if (!analysisId) {
    throw new ValidationError('缺少分析ID参数', context);
  }

  logger.info('查询分析状态', context, { analysisId });

  // 模拟状态查询
  const status = {
    analysisId,
    status: 'running',
    progress: Math.floor(Math.random() * 100),
    currentStage: 'technical_analysis',
    timestamp: new Date().toISOString(),
  };

  logger.debug('分析状态查询完成', context, status);

  return status;
});
