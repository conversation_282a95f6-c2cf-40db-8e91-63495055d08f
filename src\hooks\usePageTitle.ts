'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useMemo } from 'react';

interface PageTitleConfig {
  title: string;
  description?: string;
}

const PAGE_TITLES: Record<string, PageTitleConfig> = {
  '/': {
    title: 'TradingAgents - 多智能体大语言模型金融交易框架',
    description: '基于多智能体大语言模型的金融交易分析框架',
  },
  '/tasks': {
    title: '任务列表 - TradingAgents',
    description: '管理和查看所有分析任务',
  },
  '/create-task': {
    title: '创建任务 - TradingAgents',
    description: '创建新的分析任务',
  },
  '/analysis/history': {
    title: '分析历史 - TradingAgents',
    description: '查看和管理所有历史分析记录',
  },
  '/analysis/compare': {
    title: '分析对比 - TradingAgents',
    description: '对比多个分析结果，发现差异和趋势',
  },
  '/login': {
    title: '登录 - TradingAgents',
    description: '登录到您的账户',
  },
  '/register': {
    title: '注册 - TradingAgents',
    description: '创建新账户',
  },
  '/profile': {
    title: '个人资料 - TradingAgents',
    description: '管理您的个人信息',
  },
};

export function usePageTitle(customTitle?: string, customDescription?: string) {
  const pathname = usePathname();

  const pageConfig = useMemo(() => {
    // If custom title is provided, use it
    if (customTitle) {
      return {
        title: customTitle.includes('TradingAgents')
          ? customTitle
          : `${customTitle} - TradingAgents`,
        description: customDescription,
      };
    }

    // Check for exact path match
    if (pathname && PAGE_TITLES[pathname]) {
      return PAGE_TITLES[pathname];
    }

    // Handle dynamic routes
    if (
      pathname &&
      pathname.startsWith('/analysis/') &&
      pathname !== '/analysis/history' &&
      pathname !== '/analysis/compare'
    ) {
      const analysisId = pathname.split('/')[2];
      return {
        title: `分析详情 - TradingAgents`,
        description: `查看分析 ${analysisId} 的详细结果`,
      };
    }

    // Default fallback
    return {
      title: 'TradingAgents - 多智能体大语言模型金融交易框架',
      description: '基于多智能体大语言模型的金融交易分析框架',
    };
  }, [pathname, customTitle, customDescription]);

  // Update document title
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = pageConfig.title;

      // Update meta description if it exists
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription && pageConfig.description) {
        metaDescription.setAttribute('content', pageConfig.description);
      }
    }
  }, [pageConfig.title, pageConfig.description]);

  return pageConfig;
}
