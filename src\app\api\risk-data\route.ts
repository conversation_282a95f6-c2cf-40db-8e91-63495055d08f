/**
 * Risk Data API Routes
 * 风险数据存储与检索API接口
 */

import {
  exportRiskData,
  getRiskAssessmentByWorkflowId,
  getRiskAssessmentHistory,
  getRiskAssessmentStatistics,
  saveRiskAssessment,
} from '@/lib/risk-data-storage';
import { SaveRiskAssessmentRequest } from '@/types/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/risk-data - 保存风险评估结果
 * 需求 9.1: 当风险分析完成时，系统应将结果存储到数据库
 */
export async function POST(request: NextRequest) {
  try {
    const body: SaveRiskAssessmentRequest = await request.json();

    // 验证必需字段
    if (!body.workflow_id || !body.overall_risk_level || body.risk_score === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: workflow_id, overall_risk_level, risk_score' },
        { status: 400 }
      );
    }

    // 验证风险评分范围
    if (body.risk_score < 1 || body.risk_score > 10) {
      return NextResponse.json({ error: 'Risk score must be between 1 and 10' }, { status: 400 });
    }

    const riskId = await saveRiskAssessment(body);

    return NextResponse.json({
      success: true,
      risk_id: riskId,
      message: 'Risk assessment saved successfully',
    });
  } catch (error) {
    console.error('Error saving risk assessment:', error);
    return NextResponse.json({ error: 'Failed to save risk assessment' }, { status: 500 });
  }
}

/**
 * GET /api/risk-data - 获取风险评估数据
 * 支持多种查询模式：
 * - ?workflow_id=xxx - 获取特定工作流的风险评估
 * - ?history=true - 获取历史记录
 * - ?statistics=true - 获取统计信息
 * - ?export=true - 导出数据
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 获取特定工作流的风险评估
    const workflowId = searchParams.get('workflow_id');
    if (workflowId) {
      const assessment = await getRiskAssessmentByWorkflowId(workflowId);
      return NextResponse.json({
        success: true,
        data: assessment,
      });
    }

    // 获取历史记录
    const isHistory = searchParams.get('history') === 'true';
    if (isHistory) {
      const ticker = searchParams.get('ticker') || undefined;
      const riskLevel = searchParams.get('risk_level') as 'low' | 'medium' | 'high' | undefined;
      const dateFrom = searchParams.get('date_from')
        ? new Date(searchParams.get('date_from')!)
        : undefined;
      const dateTo = searchParams.get('date_to')
        ? new Date(searchParams.get('date_to')!)
        : undefined;
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = parseInt(searchParams.get('offset') || '0');

      const result = await getRiskAssessmentHistory({
        ticker,
        riskLevel,
        dateFrom,
        dateTo,
        limit,
        offset,
      });

      return NextResponse.json({
        success: true,
        data: result.assessments,
        total: result.total,
        pagination: {
          limit,
          offset,
          has_more: result.total > offset + limit,
        },
      });
    }

    // 获取统计信息
    const isStatistics = searchParams.get('statistics') === 'true';
    if (isStatistics) {
      const ticker = searchParams.get('ticker') || undefined;
      const dateFrom = searchParams.get('date_from')
        ? new Date(searchParams.get('date_from')!)
        : undefined;
      const dateTo = searchParams.get('date_to')
        ? new Date(searchParams.get('date_to')!)
        : undefined;

      const statistics = await getRiskAssessmentStatistics({
        ticker,
        dateFrom,
        dateTo,
      });

      return NextResponse.json({
        success: true,
        data: statistics,
      });
    }

    // 导出数据
    const isExport = searchParams.get('export') === 'true';
    if (isExport) {
      const ticker = searchParams.get('ticker') || undefined;
      const dateFrom = searchParams.get('date_from')
        ? new Date(searchParams.get('date_from')!)
        : undefined;
      const dateTo = searchParams.get('date_to')
        ? new Date(searchParams.get('date_to')!)
        : undefined;
      const format = (searchParams.get('format') as 'json' | 'csv') || 'json';

      const exportResult = await exportRiskData({
        ticker,
        dateFrom,
        dateTo,
        format,
      });

      return NextResponse.json({
        success: true,
        data: exportResult,
      });
    }

    return NextResponse.json(
      { error: 'Invalid query parameters. Use workflow_id, history, statistics, or export.' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error retrieving risk data:', error);
    return NextResponse.json({ error: 'Failed to retrieve risk data' }, { status: 500 });
  }
}
