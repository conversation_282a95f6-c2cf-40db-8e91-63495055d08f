/**
 * SEO 错误处理器
 * 提供 SEO 相关错误的处理和恢复机制
 */

import { Metadata } from 'next';
import { DEFAULT_SEO_CONFIG } from './config';

/**
 * SEO 错误处理类
 */
export class SEOErrorHandler {
  /**
   * 处理元数据生成错误
   */
  static handleMetadataError(error: Error, fallback?: Partial<Metadata>): Metadata {
    console.error('SEO Metadata Error:', error);

    // 返回安全的后备元数据
    const safeFallback: Metadata = {
      title: fallback?.title || DEFAULT_SEO_CONFIG.title,
      description: fallback?.description || DEFAULT_SEO_CONFIG.description,
      keywords: fallback?.keywords || DEFAULT_SEO_CONFIG.keywords.join(', '),
      robots: {
        index: true,
        follow: true,
      },
    };

    return safeFallback;
  }

  /**
   * 处理结构化数据错误
   */
  static handleStructuredDataError(error: Error, context?: string): Record<string, any> | null {
    console.error(`Structured Data Error${context ? ` in ${context}` : ''}:`, error);

    // 结构化数据错误时返回null，不影响页面渲染
    return null;
  }

  /**
   * 处理图片优化错误
   */
  static handleImageError(error: Error, fallbackSrc?: string): string {
    console.error('SEO Image Error:', error);

    // 返回默认图片或空字符串
    return fallbackSrc || '/tradingAgent.png';
  }

  /**
   * 处理 URL 生成错误
   */
  static handleUrlError(error: Error, fallbackUrl?: string): string {
    console.error('SEO URL Error:', error);

    // 返回默认 URL
    return fallbackUrl || process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagents.com';
  }

  /**
   * 验证并修复 SEO 配置
   */
  static validateAndFixSEOConfig(config: any): any {
    try {
      const fixedConfig = { ...config };

      // 修复标题
      if (!fixedConfig.title || typeof fixedConfig.title !== 'string') {
        fixedConfig.title = DEFAULT_SEO_CONFIG.title;
      } else if (fixedConfig.title.length > 60) {
        fixedConfig.title = fixedConfig.title.substring(0, 57) + '...';
      }

      // 修复描述
      if (!fixedConfig.description || typeof fixedConfig.description !== 'string') {
        fixedConfig.description = DEFAULT_SEO_CONFIG.description;
      } else if (fixedConfig.description.length > 160) {
        fixedConfig.description = fixedConfig.description.substring(0, 157) + '...';
      }

      // 修复关键词
      if (!Array.isArray(fixedConfig.keywords)) {
        fixedConfig.keywords = DEFAULT_SEO_CONFIG.keywords;
      }

      return fixedConfig;
    } catch (error) {
      console.error('Error validating SEO config:', error);
      return DEFAULT_SEO_CONFIG;
    }
  }

  /**
   * 记录 SEO 警告
   */
  static logSEOWarning(message: string, context?: any): void {
    console.warn(`SEO Warning: ${message}`, context);
  }

  /**
   * 记录 SEO 信息
   */
  static logSEOInfo(message: string, context?: any): void {
    console.info(`SEO Info: ${message}`, context);
  }
}
