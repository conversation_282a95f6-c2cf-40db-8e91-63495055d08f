'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useState } from 'react';

interface ComparisonData {
  comparisons: any[];
  summary: {
    totalAnalyses: number;
    tickers: string[];
    statuses: Record<string, number>;
    avgDuration: number;
    successRate: number;
  };
}

interface AnalysisComparisonTableProps {
  data: ComparisonData;
}

const STATUS_CONFIG = {
  pending: {
    label: '待处理',
    color: 'default',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
  },
  running: {
    label: '运行中',
    color: 'secondary',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
  },
  completed: {
    label: '已完成',
    color: 'success',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
  },
  failed: { label: '失败', color: 'destructive', bgColor: 'bg-red-100', textColor: 'text-red-800' },
  cancelled: {
    label: '已取消',
    color: 'outline',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-800',
  },
} as const;

const DECISION_CONFIG = {
  buy: { label: '买入', color: 'success', icon: '📈' },
  sell: { label: '卖出', color: 'destructive', icon: '📉' },
  hold: { label: '持有', color: 'outline', icon: '⏸️' },
  avoid: { label: '避免', color: 'default', icon: '🚫' },
} as const;

export function AnalysisComparisonTable({ data }: AnalysisComparisonTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'basic',
    'performance',
    'decision',
  ]);

  // 格式化持续时间
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';

    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  };

  // 切换行展开状态
  const toggleRowExpansion = (workflowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(workflowId)) {
      newExpanded.delete(workflowId);
    } else {
      newExpanded.add(workflowId);
    }
    setExpandedRows(newExpanded);
  };

  // 切换指标显示
  const toggleMetric = (metric: string) => {
    if (selectedMetrics.includes(metric)) {
      setSelectedMetrics((prev) => prev.filter((m) => m !== metric));
    } else {
      setSelectedMetrics((prev) => [...prev, metric]);
    }
  };

  return (
    <div className="space-y-6">
      {/* 对比摘要 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">对比摘要</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{data.summary.totalAnalyses}</div>
            <div className="text-sm text-gray-600">分析数量</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {(data.summary.successRate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">成功率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {formatDuration(Math.round(data.summary.avgDuration))}
            </div>
            <div className="text-sm text-gray-600">平均用时</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{data.summary.tickers.length}</div>
            <div className="text-sm text-gray-600">涉及股票</div>
          </div>
        </div>
      </Card>

      {/* 指标选择器 */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900">显示指标</h4>
          <div className="flex space-x-2">
            {[
              { key: 'basic', label: '基本信息' },
              { key: 'performance', label: '性能指标' },
              { key: 'decision', label: '决策信息' },
              { key: 'reports', label: '报告详情' },
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => toggleMetric(key)}
                className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                  selectedMetrics.includes(key)
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </Card>

      {/* 对比表格 */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  分析信息
                </th>
                {selectedMetrics.includes('basic') && (
                  <>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                  </>
                )}
                {selectedMetrics.includes('performance') && (
                  <>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用时
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      报告数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      错误数
                    </th>
                  </>
                )}
                {selectedMetrics.includes('decision') && (
                  <>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      决策
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      信心度
                    </th>
                  </>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.comparisons.map((comparison) => {
                const workflow = comparison.workflow;
                const metrics = comparison.metrics;
                const finalDecision = comparison.finalDecision;
                const statusConfig = STATUS_CONFIG[workflow.status as keyof typeof STATUS_CONFIG];
                const isExpanded = expandedRows.has(workflow.workflow_id);

                return (
                  <>
                    {/* 主行 */}
                    <tr key={workflow.workflow_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-blue-600">{workflow.ticker}</div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {workflow.title}
                          </div>
                        </div>
                      </td>

                      {selectedMetrics.includes('basic') && (
                        <>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge
                              variant={statusConfig.color}
                              className={`${statusConfig.bgColor} ${statusConfig.textColor}`}
                            >
                              {statusConfig.label}
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {format(new Date(workflow.created_at), 'MM-dd HH:mm', { locale: zhCN })}
                          </td>
                        </>
                      )}

                      {selectedMetrics.includes('performance') && (
                        <>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDuration(metrics.duration)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {metrics.totalReports}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span
                              className={metrics.errorCount > 0 ? 'text-red-600' : 'text-gray-900'}
                            >
                              {metrics.errorCount}
                            </span>
                          </td>
                        </>
                      )}

                      {selectedMetrics.includes('decision') && (
                        <>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {finalDecision ? (
                              <div className="flex items-center space-x-2">
                                <span className="text-lg">
                                  {DECISION_CONFIG[
                                    finalDecision.decision_type as keyof typeof DECISION_CONFIG
                                  ]?.icon || '❓'}
                                </span>
                                <Badge
                                  variant={
                                    DECISION_CONFIG[
                                      finalDecision.decision_type as keyof typeof DECISION_CONFIG
                                    ]?.color || 'default'
                                  }
                                >
                                  {DECISION_CONFIG[
                                    finalDecision.decision_type as keyof typeof DECISION_CONFIG
                                  ]?.label || finalDecision.decision_type}
                                </Badge>
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {finalDecision?.confidence_level
                              ? `${(finalDecision.confidence_level * 100).toFixed(1)}%`
                              : '-'}
                          </td>
                        </>
                      )}

                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => toggleRowExpansion(workflow.workflow_id)}
                          >
                            {isExpanded ? '收起' : '展开'}
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() =>
                              window.open(`/analysis/${workflow.workflow_id}`, '_blank')
                            }
                          >
                            查看
                          </Button>
                        </div>
                      </td>
                    </tr>

                    {/* 展开行 */}
                    {isExpanded && (
                      <tr>
                        <td colSpan={8} className="px-6 py-4 bg-gray-50">
                          <div className="space-y-4">
                            {/* 决策详情 */}
                            {finalDecision && (
                              <div>
                                <h5 className="font-medium text-gray-900 mb-2">决策详情</h5>
                                <div className="bg-white p-4 rounded-lg border">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                      <span className="text-sm text-gray-500">决策理由:</span>
                                      <p className="text-sm text-gray-900 mt-1">
                                        {finalDecision.decision_rationale || '无'}
                                      </p>
                                    </div>
                                    <div className="space-y-2">
                                      {finalDecision.entry_price_range && (
                                        <div>
                                          <span className="text-sm text-gray-500">入场价格:</span>
                                          <span className="text-sm text-gray-900 ml-2">
                                            {JSON.stringify(finalDecision.entry_price_range)}
                                          </span>
                                        </div>
                                      )}
                                      {finalDecision.stop_loss_price && (
                                        <div>
                                          <span className="text-sm text-gray-500">止损价格:</span>
                                          <span className="text-sm text-gray-900 ml-2">
                                            ${finalDecision.stop_loss_price}
                                          </span>
                                        </div>
                                      )}
                                      {finalDecision.take_profit_price && (
                                        <div>
                                          <span className="text-sm text-gray-500">止盈价格:</span>
                                          <span className="text-sm text-gray-900 ml-2">
                                            ${finalDecision.take_profit_price}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* 报告摘要 */}
                            {selectedMetrics.includes('reports') && (
                              <div>
                                <h5 className="font-medium text-gray-900 mb-2">报告摘要</h5>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  {/* 分析师报告 */}
                                  <div>
                                    <h6 className="text-sm font-medium text-gray-700 mb-2">
                                      分析师报告
                                    </h6>
                                    <div className="space-y-2">
                                      {comparison.analystReports?.map((report: any) => (
                                        <div
                                          key={report.report_id}
                                          className="bg-white p-3 rounded border"
                                        >
                                          <div className="flex items-center justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-900">
                                              {report.analyst_type}
                                            </span>
                                            <Badge
                                              variant={
                                                report.status === 'completed'
                                                  ? 'success'
                                                  : 'destructive'
                                              }
                                            >
                                              {report.status}
                                            </Badge>
                                          </div>
                                          <p className="text-xs text-gray-600 line-clamp-2">
                                            {report.summary || '无摘要'}
                                          </p>
                                        </div>
                                      )) || <p className="text-sm text-gray-500">无分析师报告</p>}
                                    </div>
                                  </div>

                                  {/* 研究报告 */}
                                  <div>
                                    <h6 className="text-sm font-medium text-gray-700 mb-2">
                                      研究报告
                                    </h6>
                                    <div className="space-y-2">
                                      {comparison.researchReports?.map((report: any) => (
                                        <div
                                          key={report.report_id}
                                          className="bg-white p-3 rounded border"
                                        >
                                          <div className="flex items-center justify-between mb-1">
                                            <span className="text-sm font-medium text-gray-900">
                                              {report.researcher_type === 'bull' ? '多头' : '空头'}
                                              研究员
                                            </span>
                                            <div className="flex items-center space-x-2">
                                              <span className="text-xs text-gray-500">
                                                信心度: {(report.confidence_level * 100).toFixed(1)}
                                                %
                                              </span>
                                              <Badge
                                                variant={
                                                  report.status === 'completed'
                                                    ? 'success'
                                                    : 'destructive'
                                                }
                                              >
                                                {report.status}
                                              </Badge>
                                            </div>
                                          </div>
                                          <p className="text-xs text-gray-600 line-clamp-2">
                                            {report.summary || '无摘要'}
                                          </p>
                                        </div>
                                      )) || <p className="text-sm text-gray-500">无研究报告</p>}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </>
                );
              })}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}
