'use client';

import { AnalysisHistoryFilters } from '@/components/analysis/AnalysisHistoryFilters';
import { AnalysisHistoryList } from '@/components/analysis/AnalysisHistoryList';
import { Breadcrumb } from '@/components/navigation/Breadcrumb';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { usePageTitle } from '@/hooks/usePageTitle';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

interface AnalysisHistoryItem {
  workflow_id: string;
  ticker: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  current_stage: string;
  progress: number;
  created_at: string;
  completed_at?: string;
  duration_seconds?: number;
  report_count: number;
  error_count: number;
}

interface HistoryResponse {
  workflows: AnalysisHistoryItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface FilterOptions {
  ticker?: string;
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
}

function AnalysisHistoryPageContent() {
  const [data, setData] = useState<HistoryResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [filters, setFilters] = useState<FilterOptions>({});
  const [comparisonMode, setComparisonMode] = useState(false);
  const [selectedForComparison, setSelectedForComparison] = useState<string[]>([]);

  const searchParams = useSearchParams();
  const router = useRouter();

  // Set page title and description
  usePageTitle('分析历史', '查看和管理所有历史分析记录');

  const currentPage = parseInt(searchParams?.get('page') || '1');
  const limit = parseInt(searchParams?.get('limit') || '12');

  // 加载历史数据
  const loadHistory = async (page: number = currentPage, newFilters?: FilterOptions) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const activeFilters = newFilters || filters;

      if (activeFilters.ticker) {
        params.append('ticker', activeFilters.ticker);
      }
      if (activeFilters.status && activeFilters.status.length > 0) {
        params.append('status', activeFilters.status.join(','));
      }
      if (activeFilters.dateFrom) {
        params.append('date_from', activeFilters.dateFrom);
      }
      if (activeFilters.dateTo) {
        params.append('date_to', activeFilters.dateTo);
      }

      const response = await fetch(`/api/analysis/history?${params}`);

      if (!response.ok) {
        throw new Error('获取历史数据失败');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '获取历史数据失败');
      }

      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadHistory();
  }, []);

  // 处理筛选
  const handleFilter = (newFilters: FilterOptions) => {
    setFilters(newFilters);
    loadHistory(1, newFilters);

    // 更新URL参数
    const params = new URLSearchParams();
    params.set('page', '1');
    if (newFilters.ticker) params.set('ticker', newFilters.ticker);
    if (newFilters.status?.length) params.set('status', newFilters.status.join(','));
    if (newFilters.dateFrom) params.set('date_from', newFilters.dateFrom);
    if (newFilters.dateTo) params.set('date_to', newFilters.dateTo);

    router.push(`/analysis/history?${params}`);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    loadHistory(page);

    const params = new URLSearchParams(searchParams || undefined);
    params.set('page', page.toString());
    router.push(`/analysis/history?${params}`);
  };

  // 处理刷新
  const handleRefresh = () => {
    loadHistory(currentPage);
  };

  // 处理对比选择
  const handleComparisonSelect = (ids: string[]) => {
    setSelectedForComparison(ids);
  };

  // 开始对比
  const handleStartComparison = () => {
    if (selectedForComparison.length >= 2) {
      router.push(`/analysis/compare?ids=${selectedForComparison.join(',')}`);
    }
  };

  // 切换对比模式
  const toggleComparisonMode = () => {
    setComparisonMode(!comparisonMode);
    setSelectedForComparison([]);
  };

  if (loading && !data) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-slate-600 dark:text-slate-400">加载历史分析数据...</p>
        </div>
      </div>
    );
  }

  if (error && !data) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <Card className="p-8 max-w-md">
          <div className="text-center">
            <div className="text-red-500 text-5xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">加载失败</h2>
            <p className="text-slate-600 dark:text-slate-400 mb-4">{error}</p>
            <Button onClick={handleRefresh}>重试</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 面包屑导航 */}
        <div className="mb-4">
          <Breadcrumb />
        </div>

        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">分析历史</h1>
          <p className="mt-2 text-gray-600">查看和管理所有历史分析记录</p>
        </div>

        {/* 筛选器 */}
        <div className="mb-6">
          <AnalysisHistoryFilters filters={filters} onFilter={handleFilter} loading={loading} />
        </div>

        {/* 视图切换和统计 */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {data ? `共 ${data.pagination.total} 条记录` : ''}
            </span>
            {loading && (
              <div className="flex items-center space-x-2">
                <LoadingSpinner size="sm" />
                <span className="text-sm text-gray-500">加载中...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {!comparisonMode && (
              <>
                <Button
                  variant={viewMode === 'card' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('card')}
                >
                  卡片视图
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  列表视图
                </Button>
              </>
            )}

            <Button
              variant={comparisonMode ? 'primary' : 'secondary'}
              size="sm"
              onClick={toggleComparisonMode}
            >
              {comparisonMode ? '退出对比' : '对比模式'}
            </Button>

            {comparisonMode && selectedForComparison.length >= 2 && (
              <Button size="sm" onClick={handleStartComparison}>
                开始对比 ({selectedForComparison.length})
              </Button>
            )}

            <Button variant="secondary" size="sm" onClick={handleRefresh} disabled={loading}>
              刷新
            </Button>
          </div>
        </div>

        {/* 分析列表 */}
        {data && (
          <AnalysisHistoryList
            data={data.workflows}
            viewMode={viewMode}
            pagination={data.pagination}
            onPageChange={handlePageChange}
            loading={loading}
            comparisonMode={comparisonMode}
            onComparisonSelect={handleComparisonSelect}
          />
        )}

        {/* 空状态 */}
        {data && data.workflows.length === 0 && (
          <Card className="p-12">
            <div className="text-center">
              <div className="text-gray-400 text-6xl mb-4">📊</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无分析记录</h3>
              <p className="text-gray-600 mb-4">
                {Object.keys(filters).length > 0
                  ? '当前筛选条件下没有找到分析记录，请尝试调整筛选条件。'
                  : '还没有任何分析记录，开始创建您的第一个分析任务吧！'}
              </p>
              <div className="space-x-2">
                {Object.keys(filters).length > 0 && (
                  <Button variant="secondary" onClick={() => handleFilter({})}>
                    清除筛选
                  </Button>
                )}
                <Button onClick={() => router.push('/create-task')}>创建分析任务</Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}

export default function AnalysisHistoryPage() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <AnalysisHistoryPageContent />
    </Suspense>
  );
}
