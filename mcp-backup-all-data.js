// 使用 MCP 备份所有重要数据的脚本
// 注意：这个脚本需要在 Kiro IDE 中运行，因为它依赖 MCP 连接

const fs = require('fs').promises;
const path = require('path');

// 需要备份的表和它们的重要性
const TABLES_TO_BACKUP = [
  // 用户系统 - 高优先级
  { name: 'users', priority: 'high', description: '用户基本信息' },
  { name: 'user_sessions', priority: 'medium', description: '用户会话' },
  { name: 'user_activity_logs', priority: 'low', description: '用户活动日志' },
  
  // 任务系统 - 高优先级
  { name: 'tasks', priority: 'high', description: '分析任务' },
  { name: 'analysis_results', priority: 'high', description: '分析结果' },
  { name: 'analysis_steps', priority: 'high', description: '分析步骤' },
  { name: 'messages', priority: 'medium', description: '任务消息' },
  { name: 'tool_calls', priority: 'medium', description: '工具调用' },
  
  // LangGraph 工作流 - 中优先级
  { name: 'langgraph_workflows', priority: 'medium', description: 'LangGraph工作流' },
  { name: 'workflow_configs', priority: 'medium', description: '工作流配置' },
  { name: 'researcher_results', priority: 'medium', description: '研究员结果' },
  { name: 'debate_records', priority: 'medium', description: '辩论记录' },
  { name: 'consensus_evaluations', priority: 'medium', description: '共识评估' },
  { name: 'risk_assessments', priority: 'medium', description: '风险评估' },
  { name: 'trading_decisions', priority: 'medium', description: '交易决策' },
  
  // 系统日志 - 低优先级
  { name: 'system_logs', priority: 'low', description: '系统日志' }
];

function escapeValue(value) {
  if (value === null) {
    return 'NULL';
  } else if (typeof value === 'string') {
    const escapedValue = value.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
    return `'${escapedValue}'`;
  } else if (typeof value === 'object' && value !== null) {
    const jsonStr = JSON.stringify(value).replace(/\\/g, '\\\\').replace(/'/g, "\\'");
    return `'${jsonStr}'`;
  } else if (value instanceof Date) {
    return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
  } else {
    return value.toString();
  }
}

async function generateBackupSQL() {
  console.log('🚀 开始生成完整数据备份...\n');
  
  // 生成 SQL 头部
  let fullSQL = `-- TradingAgents Frontend 完整数据备份\n`;
  fullSQL += `-- 备份时间: ${new Date().toISOString()}\n`;
  fullSQL += `-- 备份工具: MCP MySQL 连接\n`;
  fullSQL += `-- 数据库: trading_analysis\n\n`;
  fullSQL += `SET NAMES utf8mb4;\n`;
  fullSQL += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
  fullSQL += `USE trading_analysis;\n\n`;
  
  const backupSummary = {
    backupTime: new Date().toISOString(),
    tables: [],
    totalRecords: 0
  };
  
  // 按优先级分组备份
  const priorityGroups = {
    high: TABLES_TO_BACKUP.filter(t => t.priority === 'high'),
    medium: TABLES_TO_BACKUP.filter(t => t.priority === 'medium'),
    low: TABLES_TO_BACKUP.filter(t => t.priority === 'low')
  };
  
  for (const [priority, tables] of Object.entries(priorityGroups)) {
    if (tables.length === 0) continue;
    
    fullSQL += `-- ============================================================================\n`;
    fullSQL += `-- ${priority.toUpperCase()} PRIORITY TABLES\n`;
    fullSQL += `-- ============================================================================\n\n`;
    
    for (const table of tables) {
      console.log(`📦 处理表: ${table.name} (${table.description})`);
      
      try {
        // 这里需要手动添加每个表的数据
        // 由于无法在脚本中直接调用 MCP，我们需要手动处理
        fullSQL += `-- 表 ${table.name} - ${table.description}\n`;
        fullSQL += `-- 注意：需要手动使用 MCP 查询数据并填充\n`;
        fullSQL += `-- SELECT * FROM ${table.name};\n\n`;
        
        backupSummary.tables.push({
          name: table.name,
          description: table.description,
          priority: table.priority,
          status: 'pending'
        });
        
      } catch (error) {
        console.error(`❌ 处理表 ${table.name} 时出错:`, error.message);
        fullSQL += `-- 表 ${table.name} 备份失败: ${error.message}\n\n`;
      }
    }
  }
  
  fullSQL += `SET FOREIGN_KEY_CHECKS = 1;\n\n`;
  fullSQL += `-- 备份完成\n`;
  fullSQL += `SELECT 'Complete data backup template generated!' as status;\n`;
  
  // 保存模板文件
  const backupDir = 'database/backups';
  await fs.mkdir(backupDir, { recursive: true });
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const templateFile = path.join(backupDir, `backup-template-${timestamp}.sql`);
  
  await fs.writeFile(templateFile, fullSQL, 'utf8');
  console.log(`\n✅ 备份模板已生成: ${templateFile}`);
  
  // 保存摘要
  const summaryFile = templateFile.replace('.sql', '-summary.json');
  await fs.writeFile(summaryFile, JSON.stringify(backupSummary, null, 2), 'utf8');
  console.log(`📊 备份摘要: ${summaryFile}`);
  
  // 生成 MCP 查询指令
  const instructionsFile = templateFile.replace('.sql', '-instructions.md');
  let instructions = `# MCP 数据备份指令\n\n`;
  instructions += `生成时间: ${new Date().toISOString()}\n\n`;
  instructions += `## 使用说明\n\n`;
  instructions += `请在 Kiro IDE 中依次执行以下 MCP 查询，并将结果整理成 INSERT 语句：\n\n`;
  
  for (const table of TABLES_TO_BACKUP) {
    instructions += `### ${table.name} - ${table.description}\n\n`;
    instructions += `\`\`\`sql\n`;
    instructions += `-- 1. 检查表结构\n`;
    instructions += `DESCRIBE ${table.name};\n\n`;
    instructions += `-- 2. 查询数据\n`;
    instructions += `SELECT * FROM ${table.name};\n\n`;
    instructions += `-- 3. 获取记录数\n`;
    instructions += `SELECT COUNT(*) as count FROM ${table.name};\n`;
    instructions += `\`\`\`\n\n`;
  }
  
  await fs.writeFile(instructionsFile, instructions, 'utf8');
  console.log(`📋 MCP 查询指令: ${instructionsFile}`);
  
  console.log('\n🎉 备份模板和指令生成完成！');
  console.log('\n📝 下一步：');
  console.log('1. 在 Kiro IDE 中使用 MCP 查询各表数据');
  console.log('2. 将查询结果转换为 INSERT 语句');
  console.log('3. 替换模板文件中的占位符');
  
  return {
    templateFile,
    summaryFile,
    instructionsFile,
    tables: TABLES_TO_BACKUP
  };
}

// 运行生成器
if (require.main === module) {
  generateBackupSQL().catch(error => {
    console.error('生成备份模板失败:', error);
    process.exit(1);
  });
}

module.exports = { generateBackupSQL, TABLES_TO_BACKUP, escapeValue };