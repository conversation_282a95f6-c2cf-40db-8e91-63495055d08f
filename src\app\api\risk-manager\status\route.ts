/**
 * 风险管理师状态 API 端点
 * 提供风险管理师状态查询和管理功能
 */

import { riskManagerStateManager } from '@/lib/risk-manager-state';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflow_id');

    if (workflowId) {
      // 获取特定工作流的风险管理师状态
      const state = riskManagerStateManager.getState(workflowId);

      if (!state) {
        return NextResponse.json(
          {
            success: false,
            message: `Risk manager state not found for workflow ${workflowId}`,
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: state,
      });
    } else {
      // 获取所有风险管理师状态
      const allStates = riskManagerStateManager.getAllStates();
      const statistics = riskManagerStateManager.getStatistics();

      return NextResponse.json({
        success: true,
        data: {
          states: allStates,
          statistics,
        },
      });
    }
  } catch (error) {
    console.error('[Risk Manager Status API] Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, workflow_id, ...params } = body;

    if (!workflow_id) {
      return NextResponse.json(
        {
          success: false,
          message: 'workflow_id is required',
        },
        { status: 400 }
      );
    }

    switch (action) {
      case 'initialize': {
        const { metadata } = params;
        const state = riskManagerStateManager.initializeState(workflow_id, metadata);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk manager state initialized successfully',
        });
      }

      case 'start': {
        riskManagerStateManager.startAnalysis(workflow_id);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis started successfully',
        });
      }

      case 'pause': {
        riskManagerStateManager.pauseAnalysis(workflow_id);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis paused successfully',
        });
      }

      case 'resume': {
        riskManagerStateManager.resumeAnalysis(workflow_id);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis resumed successfully',
        });
      }

      case 'cancel': {
        riskManagerStateManager.cancelAnalysis(workflow_id);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis cancelled successfully',
        });
      }

      case 'complete': {
        const { result } = params;
        riskManagerStateManager.completeAnalysis(workflow_id, result);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis completed successfully',
        });
      }

      case 'fail': {
        const { error } = params;
        riskManagerStateManager.failAnalysis(workflow_id, error || 'Unknown error');
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Risk analysis marked as failed',
        });
      }

      case 'update_task': {
        const { task_id, status, progress, result, error } = params;

        if (!task_id || !status) {
          return NextResponse.json(
            {
              success: false,
              message: 'task_id and status are required for update_task action',
            },
            { status: 400 }
          );
        }

        riskManagerStateManager.updateTaskStatus(
          workflow_id,
          task_id,
          status,
          progress,
          result,
          error
        );

        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Task status updated successfully',
        });
      }

      case 'update_performance': {
        const { metrics } = params;

        if (!metrics) {
          return NextResponse.json(
            {
              success: false,
              message: 'metrics are required for update_performance action',
            },
            { status: 400 }
          );
        }

        riskManagerStateManager.updatePerformanceMetrics(workflow_id, metrics);
        const state = riskManagerStateManager.getState(workflow_id);
        return NextResponse.json({
          success: true,
          data: state,
          message: 'Performance metrics updated successfully',
        });
      }

      case 'cleanup': {
        riskManagerStateManager.cleanupState(workflow_id);
        return NextResponse.json({
          success: true,
          message: 'Risk manager state cleaned up successfully',
        });
      }

      default:
        return NextResponse.json(
          {
            success: false,
            message: `Unknown action: ${action}`,
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('[Risk Manager Status API] Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflow_id');

    if (!workflowId) {
      return NextResponse.json(
        {
          success: false,
          message: 'workflow_id is required',
        },
        { status: 400 }
      );
    }

    riskManagerStateManager.cleanupState(workflowId);

    return NextResponse.json({
      success: true,
      message: 'Risk manager state deleted successfully',
    });
  } catch (error) {
    console.error('[Risk Manager Status API] Error:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    );
  }
}
