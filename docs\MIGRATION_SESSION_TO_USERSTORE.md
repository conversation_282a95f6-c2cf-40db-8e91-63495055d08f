# 会话管理迁移指南：从 SessionManager 到 userStore

## 概述

本项目已将会话管理功能从独立的 `SessionManager` 类迁移到 Zustand 状态管理库的 `userStore` 中。这样做的好处包括：

- 统一的状态管理
- 更好的 React 集成
- 自动的状态同步
- 更简洁的 API

## 迁移对照表

### 旧的 SessionManager 用法（已删除）

```typescript
// ❌ 旧方式 - SessionManager 类已被删除
import { sessionManager } from '@/lib/session'; // 此文件已删除

// 设置会话
sessionManager.setSession('session-123');

// 获取会话
const sessionId = sessionManager.getSession();

// 设置用户
sessionManager.setUser(user);

// 获取用户
const user = sessionManager.getUser();

// 检查认证状态
const isAuth = sessionManager.isAuthenticated();

// 获取认证头
const headers = sessionManager.getAuthHeaders();

// 清除会话
sessionManager.clearSession();
```

### 新的 userStore 用法

```typescript
import useUserStore from '@/store/userStore';

// 在 React 组件中
function MyComponent() {
  const { user, sessionId, setUser, setSession, clearUser, isAuthenticated, getAuthHeaders } =
    useUserStore();

  // 设置会话
  setSession('session-123');

  // 设置用户（会同时设置会话）
  setUser(user, 'session-123');

  // 检查认证状态
  const isAuth = isAuthenticated();

  // 获取认证头
  const headers = getAuthHeaders();

  // 清除用户和会话
  clearUser();

  return <div>用户: {user?.username}</div>;
}

// 在非 React 代码中
import useUserStore from '@/store/userStore';

const store = useUserStore.getState();
store.setSession('session-123');
const isAuth = store.isAuthenticated();
```

## 主要变化

### 1. 导入方式

```typescript
// ❌ 旧方式 - 文件已删除
import { sessionManager } from '@/lib/session';

// ✅ 新方式
import useUserStore from '@/store/userStore';
```

### 2. 使用方式

```typescript
// 旧方式 - 静态方法调用
sessionManager.setSession(sessionId);

// 新方式 - Hook 或 store 方法
const { setSession } = useUserStore();
setSession(sessionId);

// 或在非组件中
useUserStore.getState().setSession(sessionId);
```

### 3. 状态访问

```typescript
// 旧方式 - 每次调用都从 localStorage 读取
const user = sessionManager.getUser();
const sessionId = sessionManager.getSession();

// 新方式 - 从内存状态读取，自动同步
const { user, sessionId } = useUserStore();
```

## 完全迁移

`SessionManager` 类和 `src/lib/session.ts` 文件已被完全删除。所有会话管理功能现在统一在 `userStore` 中。

使用 `userStore` 的优势：

1. 更好的性能（内存状态 vs localStorage 读取）
2. 自动的 React 状态更新
3. 更好的 TypeScript 支持
4. 统一的状态管理
5. 更简洁的代码结构

## 迁移步骤

1. **更新导入**：将 `sessionManager` 导入替换为 `useUserStore`
2. **更新使用方式**：使用 Hook 或 `getState()` 方法
3. **测试功能**：确保认证流程正常工作
4. **清理代码**：删除所有对 `sessionManager` 的引用

**注意**：`src/lib/session.ts` 文件已被删除，无法再使用 `SessionManager` 类。

## 注意事项

- 在 React 组件中使用 Hook 形式：`useUserStore()`
- 在非 React 代码中使用：`useUserStore.getState()`
- localStorage 同步是自动的，无需手动管理
- 状态变化会自动触发组件重新渲染

## 示例：完整的登录流程

```typescript
// 登录组件
function LoginPage() {
  const { setUser, user } = useUserStore();

  const handleLogin = async (credentials) => {
    const response = await authApi.login(credentials);
    if (response.success) {
      // 自动存储到 localStorage 和内存状态
      setUser(response.data.user, response.data.sessionId);
      router.push('/dashboard');
    }
  };

  // 如果已登录，重定向
  useEffect(() => {
    if (user) {
      router.push('/dashboard');
    }
  }, [user]);

  return <LoginForm onSubmit={handleLogin} />;
}

// API 拦截器
api.interceptors.request.use((config) => {
  const authHeaders = useUserStore.getState().getAuthHeaders();
  Object.assign(config.headers, authHeaders);
  return config;
});
```

这样的迁移使得会话管理更加现代化和高效！
