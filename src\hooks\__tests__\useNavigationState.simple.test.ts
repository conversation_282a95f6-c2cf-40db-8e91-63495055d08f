import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook } from '@testing-library/react';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
import { useNavigationState } from '../useNavigationState';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);

  return TestWrapper;
};

describe('useNavigationState - Core Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (usePathname as jest.Mock).mockReturnValue('/');

    // Mock successful API responses
    (fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/analysis/stats')) {
        return Promise.resolve({
          ok: true,
          json: () =>
            Promise.resolve({
              total: 10,
              completed: 7,
              running: 2,
              failed: 1,
              todayCount: 3,
              weekCount: 8,
              successRate: 70,
              avgDuration: 300,
            }),
        });
      }
      if (url.includes('/api/analysis/recent')) {
        return Promise.resolve({
          ok: true,
          json: () =>
            Promise.resolve([
              {
                id: '1',
                ticker: 'AAPL',
                title: 'AAPL Analysis',
                status: 'completed',
                createdAt: '2024-01-01T10:00:00Z',
              },
            ]),
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    expect(result.current.currentPath).toBe('/');
    expect(result.current.isAnalysisPage).toBe(false);
    expect(result.current.analysisStats).toBe(null);
    expect(result.current.recentAnalyses).toEqual([]);
  });

  it('should detect analysis pages correctly', () => {
    (usePathname as jest.Mock).mockReturnValue('/analysis/history');

    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    expect(result.current.currentPath).toBe('/analysis/history');
    expect(result.current.isAnalysisPage).toBe(true);
  });

  it('should handle navigation to analysis page', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.navigateToAnalysis('test-id');
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/test-id');
  });

  it('should handle navigation to history page', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.navigateToHistory();
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/history');
  });

  it('should handle navigation to compare page', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.navigateToCompare(['id1', 'id2']);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/compare?ids=id1,id2');
  });

  it('should handle navigation to compare page without IDs', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.navigateToCompare();
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/compare');
  });

  it('should clear error state', () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    // Manually set an error to test clearing
    act(() => {
      // This would normally be set by an error condition
      result.current.clearError();
    });

    expect(result.current.error).toBeUndefined();
  });
});
