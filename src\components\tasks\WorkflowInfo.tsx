'use client';

import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface WorkflowInfoProps {
  status: AnalysisStatus;
  onViewAnalysis?: () => void;
}

export function WorkflowInfo({ status, onViewAnalysis }: WorkflowInfoProps) {
  const getStageIcon = (completed: boolean, isActive: boolean = false) => {
    if (completed) {
      return (
        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    } else if (isActive) {
      return (
        <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse">
          <div className="w-full h-full bg-blue-400 rounded-full animate-ping"></div>
        </div>
      );
    } else {
      return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>;
    }
  };

  const stages = [
    { key: 'fundamental', name: '基本面分析', completed: status.stages.fundamental },
    { key: 'technical', name: '技术分析', completed: status.stages.technical },
    { key: 'sentiment', name: '情绪分析', completed: status.stages.sentiment },
    { key: 'news', name: '新闻分析', completed: status.stages.news },
    { key: 'bull', name: '多头研究', completed: status.stages.bull },
    { key: 'bear', name: '空头研究', completed: status.stages.bear },
    { key: 'consensus', name: '共识评估', completed: status.stages.consensus },
    { key: 'risk', name: '风险评估', completed: status.stages.risk },
    { key: 'decision', name: '最终决策', completed: status.stages.decision },
  ];

  const getCurrentStageIndex = () => {
    const stageMap: Record<string, number> = {
      fundamental_analysis: 0,
      technical_analysis: 1,
      sentiment_analysis: 2,
      news_analysis: 3,
      bull_research: 4,
      bear_research: 5,
      consensus_evaluation: 6,
      risk_assessment: 7,
      final_decision: 8,
    };
    return stageMap[status.currentStage] ?? -1;
  };

  const currentStageIndex = getCurrentStageIndex();

  return (
    <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6 space-y-6">
      {/* 工作流基本信息 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">工作流信息</h3>
          <p className="text-sm text-gray-600">
            工作流ID: <span className="font-mono">{status.workflowId}</span>
          </p>
        </div>
        {onViewAnalysis && (
          <button
            onClick={onViewAnalysis}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm"
          >
            查看完整分析
          </button>
        )}
      </div>

      {/* 整体进度 */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">整体进度</span>
          <span className="text-sm text-gray-600">{status.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${status.progress}%` }}
          />
        </div>
        <div className="text-xs text-gray-500">当前阶段: {status.currentStage}</div>
      </div>

      {/* 阶段进度 */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700">分析阶段</h4>
        <div className="grid grid-cols-1 gap-2">
          {stages.map((stage, index) => (
            <div key={stage.key} className="flex items-center space-x-3">
              {getStageIcon(stage.completed, index === currentStageIndex)}
              <span
                className={`text-sm ${
                  stage.completed
                    ? 'text-green-700 font-medium'
                    : index === currentStageIndex
                    ? 'text-blue-700 font-medium'
                    : 'text-gray-500'
                }`}
              >
                {stage.name}
              </span>
              {stage.completed && <span className="text-xs text-green-600">✓</span>}
              {index === currentStageIndex && status.status === 'running' && (
                <span className="text-xs text-blue-600 animate-pulse">进行中...</span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t">
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">
            {status.statistics.totalMessages}
          </div>
          <div className="text-xs text-gray-500">总消息数</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">
            {status.statistics.debateRounds}
          </div>
          <div className="text-xs text-gray-500">辩论轮次</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900">
            {status.statistics.duration > 0
              ? `${Math.round(status.statistics.duration / 60)}m`
              : '-'}
          </div>
          <div className="text-xs text-gray-500">耗时</div>
        </div>
      </div>

      {/* 时间线 */}
      {(status.startedAt || status.completedAt) && (
        <div className="pt-4 border-t space-y-2">
          <h4 className="text-sm font-medium text-gray-700">时间线</h4>
          <div className="space-y-1 text-xs text-gray-600">
            {status.createdAt && (
              <div>
                创建: {format(new Date(status.createdAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
              </div>
            )}
            {status.startedAt && (
              <div>
                开始: {format(new Date(status.startedAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
              </div>
            )}
            {status.completedAt && (
              <div>
                完成:{' '}
                {format(new Date(status.completedAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
