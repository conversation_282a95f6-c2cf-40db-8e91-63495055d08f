/**
 * 内容 SEO 检查器组件
 * 提供实时的内容 SEO 分析和优化建议
 */

'use client';

import {
  createContentOptimizer,
  ContentSEOConfig,
  ContentAnalysisResult,
} from '@/lib/seo/content-optimizer';
import { useState, useEffect, useMemo } from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface ContentSEOCheckerProps {
  content: string;
  config: ContentSEOConfig;
  metadata?: {
    title?: string;
    description?: string;
    headings?: string[];
    altTexts?: string[];
  };
  showDetails?: boolean;
  className?: string;
}

export function ContentSEOChecker({
  content,
  config,
  metadata,
  showDetails = true,
  className = '',
}: ContentSEOCheckerProps) {
  const [analysis, setAnalysis] = useState<ContentAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const optimizer = useMemo(() => createContentOptimizer(config), [config]);

  // 分析内容
  useEffect(() => {
    if (!content.trim()) {
      setAnalysis(null);
      return;
    }

    setIsAnalyzing(true);

    // 使用 setTimeout 来避免阻塞 UI
    const timer = setTimeout(() => {
      try {
        const result = optimizer.analyzeContent(content, metadata);
        setAnalysis(result);
      } catch (error) {
        console.error('Content analysis failed:', error);
      } finally {
        setIsAnalyzing(false);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [content, metadata, optimizer]);

  if (!content.trim()) {
    return (
      <div className={`content-seo-checker ${className}`}>
        <div className="text-center text-gray-500 py-4">输入内容以开始 SEO 分析</div>
      </div>
    );
  }

  if (isAnalyzing) {
    return (
      <div className={`content-seo-checker ${className}`}>
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">分析中...</span>
        </div>
      </div>
    );
  }

  if (!analysis) {
    return null;
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return 'bg-green-100 border-green-200';
    if (score >= 60) return 'bg-yellow-100 border-yellow-200';
    return 'bg-red-100 border-red-200';
  };

  return (
    <div
      className={`content-seo-checker bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}
    >
      {/* 总分显示 */}
      <div
        className={`p-4 border-b border-gray-200 dark:border-gray-700 ${getScoreBackground(
          analysis.score
        )}`}
      >
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">内容 SEO 分析</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              内容长度: {analysis.contentLength} 字符 | 可读性:{' '}
              {Math.round(analysis.readabilityScore)}
            </p>
          </div>
          <div className="text-right">
            <div className={`text-3xl font-bold ${getScoreColor(analysis.score)}`}>
              {Math.round(analysis.score)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">/ 100</div>
          </div>
        </div>
      </div>

      {showDetails && (
        <div className="p-4 space-y-4">
          {/* 关键词密度 */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">关键词密度</h4>
            <div className="space-y-2">
              {Object.entries(analysis.keywordDensity).map(([keyword, density]) => (
                <div key={keyword} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{keyword}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          density > 3
                            ? 'bg-red-500'
                            : density > 1
                            ? 'bg-green-500'
                            : 'bg-yellow-500'
                        }`}
                        style={{ width: `${Math.min(100, density * 20)}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                      {density.toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 问题列表 */}
          {analysis.issues.length > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                需要改进 ({analysis.issues.length})
              </h4>
              <div className="space-y-2">
                {analysis.issues.map((issue, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      issue.severity === 'high'
                        ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                        : issue.severity === 'medium'
                        ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                        : 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                    }`}
                  >
                    <div className="flex items-start">
                      <div
                        className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 mr-3 ${
                          issue.severity === 'high'
                            ? 'bg-red-500'
                            : issue.severity === 'medium'
                            ? 'bg-yellow-500'
                            : 'bg-blue-500'
                        }`}
                      />
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 dark:text-gray-200">{issue.message}</p>
                        {issue.element && (
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            元素: {issue.element}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 优化建议 */}
          {analysis.suggestions.length > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                <InformationCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                优化建议 ({analysis.suggestions.length})
              </h4>
              <div className="space-y-2">
                {analysis.suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg border bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"
                  >
                    <div className="flex items-start">
                      <div
                        className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 mr-3 ${
                          suggestion.priority === 'high'
                            ? 'bg-red-500'
                            : suggestion.priority === 'medium'
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                        }`}
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                          {suggestion.message}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {suggestion.improvement}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 关键词分析详情 */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">关键词分析</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <div className="text-gray-600 dark:text-gray-400">缺失关键词</div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {analysis.keywordAnalysis.missingKeywords.length}
                </div>
                {analysis.keywordAnalysis.missingKeywords.length > 0 && (
                  <div className="text-xs text-gray-500 mt-1">
                    {analysis.keywordAnalysis.missingKeywords.slice(0, 3).join(', ')}
                    {analysis.keywordAnalysis.missingKeywords.length > 3 && '...'}
                  </div>
                )}
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <div className="text-gray-600 dark:text-gray-400">过度使用</div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {analysis.keywordAnalysis.overusedKeywords.length}
                </div>
                {analysis.keywordAnalysis.overusedKeywords.length > 0 && (
                  <div className="text-xs text-gray-500 mt-1">
                    {analysis.keywordAnalysis.overusedKeywords.slice(0, 3).join(', ')}
                    {analysis.keywordAnalysis.overusedKeywords.length > 3 && '...'}
                  </div>
                )}
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                <div className="text-gray-600 dark:text-gray-400">总关键词</div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {Object.keys(analysis.keywordAnalysis.occurrences).length}
                </div>
              </div>
            </div>
          </div>

          {/* 成功指标 */}
          {analysis.score >= 80 && (
            <div className="bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  内容 SEO 优化良好！继续保持这个水平。
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 简化版的 SEO 分数显示组件
 */
interface SEOScoreBadgeProps {
  score: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export function SEOScoreBadge({
  score,
  size = 'md',
  showLabel = true,
  className = '',
}: SEOScoreBadgeProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100 border-green-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    return 'text-red-600 bg-red-100 border-red-200';
  };

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2',
  };

  return (
    <div
      className={`inline-flex items-center rounded-full border font-medium ${getScoreColor(
        score
      )} ${sizeClasses[size]} ${className}`}
    >
      <span>{Math.round(score)}</span>
      {showLabel && <span className="ml-1">SEO</span>}
    </div>
  );
}

/**
 * 关键词密度可视化组件
 */
interface KeywordDensityVisualizerProps {
  keywordDensity: Record<string, number>;
  className?: string;
}

export function KeywordDensityVisualizer({
  keywordDensity,
  className = '',
}: KeywordDensityVisualizerProps) {
  const sortedKeywords = Object.entries(keywordDensity)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10); // 只显示前 10 个关键词

  return (
    <div className={`keyword-density-visualizer ${className}`}>
      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">关键词密度分布</h4>
      <div className="space-y-2">
        {sortedKeywords.map(([keyword, density]) => (
          <div key={keyword} className="flex items-center">
            <div className="w-20 text-xs text-gray-600 dark:text-gray-400 truncate">{keyword}</div>
            <div className="flex-1 mx-3">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    density > 3 ? 'bg-red-500' : density > 1 ? 'bg-green-500' : 'bg-yellow-500'
                  }`}
                  style={{ width: `${Math.min(100, density * 20)}%` }}
                />
              </div>
            </div>
            <div className="w-12 text-xs text-gray-600 dark:text-gray-400 text-right">
              {density.toFixed(1)}%
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
