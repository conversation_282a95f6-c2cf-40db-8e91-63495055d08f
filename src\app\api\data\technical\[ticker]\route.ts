import { akshareAdapter } from '@/lib/akshare/adapter';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 获取技术指标数据 API
 * GET /api/data/technical/{ticker}?indicators=macd,rsi,ma&period=daily
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const { ticker } = await params;
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const indicatorsParam = searchParams.get('indicators') || 'ma,macd,rsi,kdj';
    const period = searchParams.get('period') || 'daily';
    const days = parseInt(searchParams.get('days') || '60');

    const indicators = indicatorsParam.split(',').map((ind) => ind.trim());

    console.log(`[技术指标API] 获取技术指标: ${ticker}, indicators: ${indicators.join(',')}`);

    // 首先获取股票历史数据作为基础
    const stockData = await akshareAdapter.invoke('get_stock_history', {
      symbol: ticker,
      period,
    });

    if (!stockData || !Array.isArray(stockData)) {
      return NextResponse.json(
        {
          error: '无法获取股票数据',
          ticker,
          message: '数据格式错误或数据为空',
        },
        { status: 404 }
      );
    }

    // 取最近指定天数的数据
    const recentData = stockData.slice(-days);

    // 格式化基础数据
    const formattedData = recentData.map((item: any) => ({
      date: item.日期 || item.date,
      open: parseFloat(item.开盘 || item.open || 0),
      high: parseFloat(item.最高 || item.high || 0),
      low: parseFloat(item.最低 || item.low || 0),
      close: parseFloat(item.收盘 || item.close || 0),
      volume: parseInt(item.成交量 || item.volume || 0),
    }));

    // 计算各种技术指标
    const technicalIndicators: { [key: string]: any[] } = {};

    if (indicators.includes('ma')) {
      technicalIndicators.ma = calculateMovingAverages(formattedData);
    }

    if (indicators.includes('macd')) {
      technicalIndicators.macd = calculateMACD(formattedData);
    }

    if (indicators.includes('rsi')) {
      technicalIndicators.rsi = calculateRSI(formattedData);
    }

    if (indicators.includes('kdj')) {
      technicalIndicators.kdj = calculateKDJ(formattedData);
    }

    if (indicators.includes('bollinger')) {
      technicalIndicators.bollinger = calculateBollingerBands(formattedData);
    }

    if (indicators.includes('volume')) {
      technicalIndicators.volume = calculateVolumeIndicators(formattedData);
    }

    // 生成技术分析摘要
    const analysis = generateTechnicalAnalysis(formattedData, technicalIndicators);

    return NextResponse.json({
      ticker,
      name: getStockName(ticker),
      period,
      indicators: technicalIndicators,
      analysis,
      dataPoints: formattedData.length,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('[技术指标API] 错误:', error);

    return NextResponse.json(
      {
        error: '获取技术指标失败',
        ticker: (await params).ticker,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 计算移动平均线
 */
function calculateMovingAverages(data: any[]) {
  const periods = [5, 10, 20, 60];

  return data.map((item, index) => {
    const result: any = {
      date: item.date,
      close: item.close,
    };

    periods.forEach((period) => {
      if (index >= period - 1) {
        const sum = data
          .slice(index - period + 1, index + 1)
          .reduce((acc, curr) => acc + curr.close, 0);
        result[`ma${period}`] = Math.round((sum / period) * 100) / 100;
      } else {
        result[`ma${period}`] = null;
      }
    });

    return result;
  });
}

/**
 * 计算MACD指标
 */
function calculateMACD(data: any[]) {
  const ema12 = calculateEMA(
    data.map((d) => d.close),
    12
  );
  const ema26 = calculateEMA(
    data.map((d) => d.close),
    26
  );

  const macdLine = ema12.map((val, i) => val - ema26[i]);
  const signalLine = calculateEMA(macdLine, 9);
  const histogram = macdLine.map((val, i) => val - signalLine[i]);

  return data.map((item, index) => ({
    date: item.date,
    macd: Math.round(macdLine[index] * 1000) / 1000,
    signal: Math.round(signalLine[index] * 1000) / 1000,
    histogram: Math.round(histogram[index] * 1000) / 1000,
  }));
}

/**
 * 计算RSI指标
 */
function calculateRSI(data: any[], period: number = 14) {
  const changes = data.slice(1).map((item, index) => item.close - data[index].close);

  return data.map((item, index) => {
    if (index < period) {
      return { date: item.date, rsi: null };
    }

    const recentChanges = changes.slice(index - period, index);
    const gains = recentChanges.filter((change) => change > 0);
    const losses = recentChanges.filter((change) => change < 0).map((loss) => Math.abs(loss));

    const avgGain = gains.length > 0 ? gains.reduce((sum, gain) => sum + gain, 0) / period : 0;
    const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / period : 0;

    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
    const rsi = 100 - 100 / (1 + rs);

    return {
      date: item.date,
      rsi: Math.round(rsi * 100) / 100,
    };
  });
}

/**
 * 计算KDJ指标
 */
function calculateKDJ(data: any[], period: number = 9) {
  return data.map((item, index) => {
    if (index < period - 1) {
      return { date: item.date, k: null, d: null, j: null };
    }

    const recentData = data.slice(index - period + 1, index + 1);
    const highestHigh = Math.max(...recentData.map((d) => d.high));
    const lowestLow = Math.min(...recentData.map((d) => d.low));

    const rsv = ((item.close - lowestLow) / (highestHigh - lowestLow)) * 100;

    // 简化的KDJ计算 (实际应该使用前一天的K和D值)
    const k = rsv;
    const d = k; // 简化处理
    const j = 3 * k - 2 * d;

    return {
      date: item.date,
      k: Math.round(k * 100) / 100,
      d: Math.round(d * 100) / 100,
      j: Math.round(j * 100) / 100,
    };
  });
}

/**
 * 计算布林带
 */
function calculateBollingerBands(data: any[], period: number = 20, multiplier: number = 2) {
  return data.map((item, index) => {
    if (index < period - 1) {
      return { date: item.date, upper: null, middle: null, lower: null };
    }

    const recentPrices = data.slice(index - period + 1, index + 1).map((d) => d.close);
    const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;

    const variance =
      recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const stdDev = Math.sqrt(variance);

    return {
      date: item.date,
      upper: Math.round((sma + multiplier * stdDev) * 100) / 100,
      middle: Math.round(sma * 100) / 100,
      lower: Math.round((sma - multiplier * stdDev) * 100) / 100,
    };
  });
}

/**
 * 计算成交量指标
 */
function calculateVolumeIndicators(data: any[]) {
  const volumeMA5 = calculateSimpleMA(
    data.map((d) => d.volume),
    5
  );
  const volumeMA10 = calculateSimpleMA(
    data.map((d) => d.volume),
    10
  );

  return data.map((item, index) => ({
    date: item.date,
    volume: item.volume,
    volumeMA5: volumeMA5[index],
    volumeMA10: volumeMA10[index],
    volumeRatio: volumeMA5[index] ? Math.round((item.volume / volumeMA5[index]) * 100) / 100 : null,
  }));
}

/**
 * 计算指数移动平均线
 */
function calculateEMA(prices: number[], period: number): number[] {
  const multiplier = 2 / (period + 1);
  const ema = [prices[0]];

  for (let i = 1; i < prices.length; i++) {
    ema[i] = prices[i] * multiplier + ema[i - 1] * (1 - multiplier);
  }

  return ema;
}

/**
 * 计算简单移动平均线
 */
function calculateSimpleMA(values: number[], period: number): (number | null)[] {
  return values.map((_, index) => {
    if (index < period - 1) return null;

    const sum = values.slice(index - period + 1, index + 1).reduce((acc, val) => acc + val, 0);
    return Math.round((sum / period) * 100) / 100;
  });
}

/**
 * 生成技术分析摘要
 */
function generateTechnicalAnalysis(data: any[], indicators: any) {
  const latestData = data[data.length - 1];
  const analysis: any = {
    currentPrice: latestData.close,
    trend: 'neutral',
    signals: [],
    support: null,
    resistance: null,
  };

  // 移动平均线分析
  if (indicators.ma) {
    const latestMA = indicators.ma[indicators.ma.length - 1];
    if (latestMA.ma5 && latestMA.ma20) {
      if (latestData.close > latestMA.ma5 && latestMA.ma5 > latestMA.ma20) {
        analysis.trend = 'bullish';
        analysis.signals.push('价格位于短期均线之上，趋势偏多');
      } else if (latestData.close < latestMA.ma5 && latestMA.ma5 < latestMA.ma20) {
        analysis.trend = 'bearish';
        analysis.signals.push('价格位于短期均线之下，趋势偏空');
      }
    }
  }

  // RSI分析
  if (indicators.rsi) {
    const latestRSI = indicators.rsi[indicators.rsi.length - 1];
    if (latestRSI.rsi) {
      if (latestRSI.rsi > 70) {
        analysis.signals.push('RSI超买，可能面临回调压力');
      } else if (latestRSI.rsi < 30) {
        analysis.signals.push('RSI超卖，可能出现反弹机会');
      }
    }
  }

  // MACD分析
  if (indicators.macd) {
    const latestMACD = indicators.macd[indicators.macd.length - 1];
    const prevMACD = indicators.macd[indicators.macd.length - 2];

    if (latestMACD.histogram > 0 && prevMACD.histogram <= 0) {
      analysis.signals.push('MACD金叉，买入信号');
    } else if (latestMACD.histogram < 0 && prevMACD.histogram >= 0) {
      analysis.signals.push('MACD死叉，卖出信号');
    }
  }

  return analysis;
}

/**
 * 获取股票名称
 */
function getStockName(ticker: string): string {
  const stockNames: { [key: string]: string } = {
    '000001': '平安银行',
    '000002': '万科A',
    '600000': '浦发银行',
    '600036': '招商银行',
    '600519': '贵州茅台',
    '000858': '五粮液',
    '002415': '海康威视',
    '300059': '东方财富',
  };

  return stockNames[ticker] || ticker;
}
