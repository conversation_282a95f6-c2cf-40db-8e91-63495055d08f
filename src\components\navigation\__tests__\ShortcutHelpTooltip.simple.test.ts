import { KeyboardShortcut } from '@/types/navigation';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { ShortcutHelpTooltip } from '../ShortcutHelpTooltip';

const mockShortcuts: KeyboardShortcut[] = [
  {
    key: 'Alt+H',
    description: '打开分析历史页面',
    action: jest.fn(),
    enabled: true,
    category: 'navigation',
  },
  {
    key: 'Alt+C',
    description: '打开分析对比页面',
    action: jest.fn(),
    enabled: true,
    category: 'navigation',
  },
];

describe('ShortcutHelpTooltip - Core Functionality', () => {
  it('should render help button', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: mockShortcuts }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    expect(helpButton).toBeInTheDocument();
  });

  it('should show tooltip on hover', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: mockShortcuts }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Initially tooltip should not be visible
    expect(screen.queryByText('键盘快捷键')).not.toBeInTheDocument();

    // Hover over button
    fireEvent.mouseEnter(helpButton);

    // Tooltip should be visible
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();
  });

  it('should hide tooltip on mouse leave', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: mockShortcuts }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Show tooltip
    fireEvent.mouseEnter(helpButton);
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();

    // Hide tooltip
    fireEvent.mouseLeave(helpButton);
    expect(screen.queryByText('键盘快捷键')).not.toBeInTheDocument();
  });

  it('should display shortcuts grouped by category', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: mockShortcuts }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Check category headers
    expect(screen.getByText('导航')).toBeInTheDocument();

    // Check shortcuts in navigation category
    expect(screen.getByText('打开分析历史页面')).toBeInTheDocument();
    expect(screen.getByText('打开分析对比页面')).toBeInTheDocument();
  });

  it('should format shortcut keys correctly', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: mockShortcuts }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Check formatted keys
    expect(screen.getByText('⌥ H')).toBeInTheDocument(); // Alt+H
    expect(screen.getByText('⌥ C')).toBeInTheDocument(); // Alt+C
  });

  it('should show empty state when no shortcuts provided', () => {
    render(React.createElement(ShortcutHelpTooltip, { shortcuts: [] }));

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    expect(screen.getByText('暂无可用的键盘快捷键')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(
      React.createElement(ShortcutHelpTooltip, {
        shortcuts: mockShortcuts,
        className: 'custom-class',
      })
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });
});
