import { AnalysisItem, AnalysisStats } from '@/types/navigation';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { act, renderHook, waitFor } from '@testing-library/react';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
import { useNavigationState } from '../useNavigationState';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

const mockAnalysisStats: AnalysisStats = {
  total: 10,
  completed: 7,
  running: 2,
  failed: 1,
  todayCount: 3,
  weekCount: 8,
  successRate: 70,
  avgDuration: 300,
};

const mockRecentAnalyses: AnalysisItem[] = [
  {
    id: '1',
    ticker: 'AAPL',
    title: 'AAPL Analysis',
    status: 'completed',
    createdAt: '2024-01-01T10:00:00Z',
  },
  {
    id: '2',
    ticker: 'GOOGL',
    title: 'GOOGL Analysis',
    status: 'running',
    createdAt: '2024-01-01T09:00:00Z',
  },
];

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const TestWrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);

  return TestWrapper;
};

describe('useNavigationState', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (usePathname as jest.Mock).mockReturnValue('/');

    // Mock successful API responses
    (fetch as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('/api/analysis/stats')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAnalysisStats),
        });
      }
      if (url.includes('/api/analysis/recent')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockRecentAnalyses),
        });
      }
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  it('should initialize with correct default state', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    expect(result.current.currentPath).toBe('/');
    expect(result.current.isAnalysisPage).toBe(false);
    expect(result.current.loading).toBe(true);
    expect(result.current.analysisStats).toBe(null);
    expect(result.current.recentAnalyses).toEqual([]);
  });

  it('should detect analysis pages correctly', () => {
    (usePathname as jest.Mock).mockReturnValue('/analysis/history');

    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    expect(result.current.currentPath).toBe('/analysis/history');
    expect(result.current.isAnalysisPage).toBe(true);
  });

  it('should fetch and return analysis stats', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.analysisStats).toEqual(mockAnalysisStats);
    expect(result.current.recentAnalyses).toEqual(mockRecentAnalyses);
  });

  it('should handle navigation to analysis page', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.navigateToAnalysis('test-id');

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/test-id');
  });

  it('should handle navigation to history page', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.navigateToHistory();

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/history');
  });

  it('should handle navigation to compare page', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.navigateToCompare(['id1', 'id2']);

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/compare?ids=id1,id2');
  });

  it('should handle navigation to compare page without IDs', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.navigateToCompare();

    expect(mockRouter.push).toHaveBeenCalledWith('/analysis/compare');
  });

  it('should handle API errors gracefully', async () => {
    (fetch as jest.Mock).mockImplementation(() => Promise.reject(new Error('API Error')));

    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(
      () => {
        expect(result.current.loading).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.error).toBeDefined();
    expect(result.current.analysisStats).toBe(null);
  });

  it('should refresh stats correctly', async () => {
    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear the mock to track new calls
    (fetch as jest.Mock).mockClear();

    await result.current.refreshStats();

    expect(fetch).toHaveBeenCalledWith('/api/analysis/stats');
  });

  it('should clear error state', async () => {
    // First cause an error
    (fetch as jest.Mock).mockImplementation(() => Promise.reject(new Error('API Error')));

    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(
      () => {
        expect(result.current.error).toBeDefined();
      },
      { timeout: 3000 }
    );

    // Clear the error
    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeUndefined();
  });

  it('should handle navigation errors', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    mockRouter.push.mockImplementation(() => {
      throw new Error('Navigation failed');
    });

    const { result } = renderHook(() => useNavigationState(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    act(() => {
      result.current.navigateToAnalysis('test-id');
    });

    await waitFor(() => {
      expect(result.current.error).toBe('Failed to navigate to analysis');
    });

    consoleSpy.mockRestore();
  });
});
