# 风险分析增强设计文档

## 概述

本设计文档详细描述了 TradingAgents Web 应用中风险分析增强功能的技术实现方案。基于现有的 LangGraph 分析框架和智能体架构，本设计将完善风险管理师（Risk Manager）智能体的实现，包括分析逻辑、提示词设计、数据处理、状态管理和前端展示等方面。

系统将通过扩展现有的分析流程，在适当的阶段集成风险评估，确保投资决策的安全性和合理性。风险管理师将与其他智能体协同工作，提供专业的风险分析和控制建议。

## 架构设计

### 整体架构

风险分析增强系统在现有架构基础上，扩展风险管理师智能体的功能：

```mermaid
graph TD
    subgraph "LangGraph 分析流程"
        Start[开始分析] --> DataCollection[数据收集]
        DataCollection --> AnalystTeam[分析师团队]
        AnalystTeam --> ResearchTeam[研究团队]
        ResearchTeam --> RiskManager[风险管理师]
        RiskManager --> FinalDecision[最终决策]
        FinalDecision --> End[分析完成]
    end

    subgraph "风险管理师智能体"
        RiskAgent[风险智能体]
        RiskPrompts[风险分析提示词]
        RiskCalculator[风险指标计算器]
        RiskScenario[场景分析器]
        RiskReporter[风险报告生成器]
    end

    subgraph "数据层"
        MarketData[市场数据]
        FinancialData[财务数据]
        HistoricalData[历史数据]
        RiskMetrics[风险指标]
    end

    subgraph "前端展示"
        RiskPanel[风险状态面板]
        RiskReport[风险报告组件]
        RiskCharts[风险图表]
        RiskAlerts[风险预警]
    end

    RiskManager --> RiskAgent
    RiskAgent --> RiskPrompts
    RiskAgent --> RiskCalculator
    RiskAgent --> RiskScenario
    RiskAgent --> RiskReporter

    RiskCalculator --> MarketData
    RiskCalculator --> FinancialData
    RiskCalculator --> HistoricalData
    RiskCalculator --> RiskMetrics

    RiskReporter -->
```
