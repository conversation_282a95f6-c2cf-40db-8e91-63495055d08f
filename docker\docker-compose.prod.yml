# 生产环境 Docker Compose 配置文件
# 使用阿里云容器镜像服务的镜像
version: '3.8'

services:
  # AKShare 后端数据服务 - 使用阿里云镜像
  akshare-backend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/akshare-backend:latest
    container_name: tradingagents-akshare-backend
    ports:
      - '5000:5000'
    environment:
      - APP_NAME=AKShare数据服务
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=5000
      - LOG_LEVEL=WARNING # 生产环境减少日志输出
      - FILTER_HEALTH_CHECKS=true # 过滤健康检查日志
      - BUSINESS_LOGS_ONLY=false # 是否只显示业务日志
      # 数据库连接配置
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${MYSQL_DATABASE:-trading_analysis}
      - DB_USER=${MYSQL_USER:-trading_user}
      - DB_PASSWORD=${MYSQL_PASSWORD:-trading123}
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - mysql
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://localhost:5000/health || exit 1']
      interval: 2m # 生产环境减少健康检查频率
      timeout: 10s
      retries: 3
      start_period: 60s

  # TradingAgents Frontend - 使用阿里云镜像
  frontend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
    container_name: tradingagents-frontend
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_API_BACKEND_BASE_URL=${NEXT_PUBLIC_API_BACKEND_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      - BACK_END_URL=${BACK_END_URL:-http://tradingagents-akshare-backend:5000}
      - AKSHARE_API_URL=${AKSHARE_API_URL:-http://tradingagents-akshare-backend:5000}
      # 数据库连接配置
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${MYSQL_DATABASE:-trading_analysis}
      - DB_USER=${MYSQL_USER:-trading_user}
      - DB_PASSWORD=${MYSQL_PASSWORD:-trading123}
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - akshare-backend
      - mysql
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1']
      interval: 2m # 生产环境减少健康检查频率
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL 数据库 - 使用阿里云镜像
  mysql:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/mysql:8.0.28
    container_name: tradingagents-mysql
    ports:
      - '13306:3306'
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-trading123}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-trading_analysis}
      - MYSQL_USER=${MYSQL_USER:-trading_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-trading123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 2m # 生产环境减少健康检查频率
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx 反向代理 (可选) - 使用官方镜像
  nginx:
    image: nginx:alpine
    container_name: tradingagents-nginx
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - frontend
    profiles:
      - nginx

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network

volumes:
  mysql_data:
    driver: local
  nginx-ssl:
    driver: local
