# 分析数据存储与前端展示设计文档

## 概述

本设计文档详细描述了 TradingAgents Web 应用中分析数据存储和前端展示功能的技术实现方案。基于现有的工作流中心数据库设计和 LangGraph 分析框架，本设计将完善分析结果的完整存储机制，并提供直观的前端展示界面。

系统将通过优化的数据库操作、实时状态同步、丰富的可视化组件和高效的缓存机制，为用户提供完整的分析体验，从任务创建到结果展示的全流程支持。

## 架构设计

### 整体架构

分析数据存储与展示系统采用分层架构，包括数据存储层、业务逻辑层、API 接口层和前端展示层：

```mermaid
graph TD
    Frontend[前端展示层] --> API[API接口层]
    API --> Business[业务逻辑层]
    Business --> Storage[数据存储层]
    Business --> Cache[缓存层]
    Business --> LangGraph[LangGraph分析引擎]

    subgraph "前端展示层"
        Dashboard[分析仪表板]
        Reports[报告展示]
        History[历史查询]
        Realtime[实时状态]
    end

    subgraph "API接口层"
        WorkflowAPI[工作流API]
        ReportsAPI[报告API]
        StatusAPI[状态API]
        WebSocket[WebSocket]
    end

    subgraph "业务逻辑层"
        WorkflowService[工作流服务]
        AnalysisService[分析服务]
        ReportService[报告服务]
        NotificationService[通知服务]
    end

    subgraph "数据存储层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
    end
```

### 数据流设计

分析数据的完整流程从 LangGraph 执行到数据库存储再到前端展示：

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端界面
    participant API as API服务
    participant LangGraph as LangGraph引擎
    participant DB as 数据库
    participant Cache as 缓存
    participant WS as WebSocket

    Note over User,WS: 任务已通过 /api/tasks 创建
    User->>Frontend: 点击"开始分析"按钮
    Frontend->>API: POST /api/analysis/execute {taskId}
    API->>DB: 查询任务信息
    API->>DB: 创建工作流记录
    API->>DB: 更新任务状态为running
    API->>LangGraph: 启动分析图执行
    API->>WS: 建立实时连接

    loop 分析执行过程
        LangGraph->>API: 智能体状态更新
        API->>DB: 存储状态和报告
        API->>Cache: 更新缓存
        API->>WS: 推送状态更新
        WS->>Frontend: 实时状态展示
    end

    LangGraph->>API: 分析完成
    API->>DB: 存储最终结果
    API->>Cache: 更新结果缓存
    API->>WS: 推送完成通知
    WS->>Frontend: 展示完整结果
    Frontend->>User: 显示分析报告
```

## 组件设计

### 数据库操作组件

基于现有的 `LangGraphDatabase` 类，完善分析数据的存储和查询功能：

```typescript
// 扩展现有的 LangGraphDatabase 类
export class EnhancedLangGraphDatabase extends LangGraphDatabase {
  // 完整的工作流创建，包含所有必要的初始化
  static async createCompleteWorkflow(request: CreateCompleteWorkflowRequest): Promise<string> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        // 1. 创建工作流
        const workflowId = await this.createWorkflow(request);

        // 2. 初始化智能体状态
        await this.initializeAgentStates(connection, workflowId, request.config);

        // 3. 记录初始事件
        await this.logWorkflowEvent({
          workflow_id: workflowId,
          stage_name: 'initialization',
          event_type: 'log',
          content: '工作流初始化完成',
          metadata: { config: request.config },
        });

        await connection.commit();
        return workflowId;
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  // 批量更新智能体状态
  static async batchUpdateAgentStates(updates: AgentStateUpdate[]): Promise<void> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        for (const update of updates) {
          await connection.execute(
            'UPDATE analyst_reports SET status = ?, execution_time_ms = ? WHERE workflow_id = ? AND analyst_type = ?',
            [update.status, update.execution_time_ms, update.workflow_id, update.analyst_type]
          );
        }
        await connection.commit();
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  // 获取完整的工作流状态
  static async getCompleteWorkflowStatus(workflow_id: string): Promise<CompleteWorkflowStatus> {
    return this.withConnection(async (connection) => {
      // 获取工作流基本信息
      const workflow = await this.getWorkflow(workflow_id);
      if (!workflow) throw new Error('工作流不存在');

      // 获取所有分析报告
      const [analystReports] = await connection.execute(
        'SELECT * FROM analyst_reports WHERE workflow_id = ? ORDER BY created_at',
        [workflow_id]
      );

      // 获取所有研究报告
      const [researchReports] = await connection.execute(
        'SELECT * FROM research_reports WHERE workflow_id = ? ORDER BY created_at',
        [workflow_id]
      );

      // 获取最终决策
      const [decisions] = await connection.execute(
        'SELECT * FROM final_decisions WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 1',
        [workflow_id]
      );

      // 获取最近的事件
      const [events] = await connection.execute(
        'SELECT * FROM workflow_events WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 50',
        [workflow_id]
      );

      return {
        workflow,
        analystReports: analystReports as any[],
        researchReports: researchReports as any[],
        finalDecision: decisions.length > 0 ? decisions[0] : null,
        recentEvents: events as any[],
      };
    });
  }

  // 获取分析统计信息
  static async getAnalysisStatistics(options: StatisticsOptions = {}): Promise<AnalysisStatistics> {
    return this.withConnection(async (connection) => {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (options.date_from) {
        whereClause += ' AND created_at >= ?';
        params.push(options.date_from);
      }

      if (options.date_to) {
        whereClause += ' AND created_at <= ?';
        params.push(options.date_to);
      }

      if (options.ticker) {
        whereClause += ' AND ticker = ?';
        params.push(options.ticker);
      }

      // 获取基本统计
      const [basicStats] = await connection.execute(
        `
        SELECT 
          COUNT(*) as total_workflows,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_workflows,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_workflows,
          AVG(CASE WHEN status = 'completed' THEN TIMESTAMPDIFF(SECOND, created_at, completed_at) END) as avg_duration_seconds
        FROM workflows ${whereClause}
      `,
        params
      );

      // 获取成功率统计
      const [successRates] = await connection.execute(
        `
        SELECT 
          ticker,
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as success_count
        FROM workflows ${whereClause}
        GROUP BY ticker
        ORDER BY total_count DESC
        LIMIT 10
      `,
        params
      );

      return {
        basic: basicStats[0] as any,
        successRates: successRates as any[],
      };
    });
  }
}
```

### 分析服务组件

创建专门的分析服务来处理 LangGraph 执行和数据存储的协调：

```typescript
export class AnalysisService {
  constructor(
    private database: EnhancedLangGraphDatabase,
    private langGraphService: LangGraphService,
    private cacheService: CacheService,
    private notificationService: NotificationService
  ) {}

  // 执行分析流程（基于已存在的任务）
  async executeAnalysis(request: ExecuteAnalysisRequest): Promise<AnalysisResult> {
    try {
      // 1. 获取现有任务并验证
      const task = await this.getTaskById(request.taskId);
      if (!task) {
        throw new AnalysisError('任务不存在', 'TASK_NOT_FOUND', 404);
      }

      if (task.status !== 'pending') {
        throw new AnalysisError('任务状态不允许执行分析', 'INVALID_TASK_STATUS', 400);
      }

      // 2. 创建工作流记录（基于任务信息）
      const workflowId = await this.database.createCompleteWorkflow({
        ticker: task.ticker,
        title: task.title,
        description: task.description,
        config: task.config || request.config,
        created_by: task.created_by,
      });

      // 3. 更新任务状态为运行中，并关联工作流ID
      await this.updateTaskStatus(request.taskId, 'running', workflowId);

      // 4. 启动 LangGraph 分析
      const analysisPromise = this.langGraphService.analyzeStock(
        workflowId,
        task.ticker,
        task.config || request.config
      );

      // 3. 设置状态监听
      this.setupAnalysisMonitoring(workflowId);

      // 4. 异步执行分析
      analysisPromise
        .then((result) => this.handleAnalysisSuccess(workflowId, result))
        .catch((error) => this.handleAnalysisError(workflowId, error));

      return {
        workflowId,
        taskId: request.taskId,
        status: 'started',
        message: '分析已启动',
      };
    } catch (error) {
      console.error('执行分析失败:', error);
      // 如果分析启动失败，恢复任务状态
      if (request.taskId) {
        await this.updateTaskStatus(request.taskId, 'pending');
      }
      throw new AnalysisError(`执行分析失败: ${error.message}`);
    }
  }

  // 获取任务信息
  private async getTaskById(taskId: string): Promise<Task | null> {
    // 从数据库获取任务信息
    // 这里应该调用任务管理服务或直接查询数据库
    return null; // 实际实现中需要查询数据库
  }

  // 更新任务状态
  private async updateTaskStatus(
    taskId: string,
    status: string,
    workflowId?: string
  ): Promise<void> {
    // 更新任务状态，可能包括关联的工作流ID
    // 这里应该调用任务管理服务或直接更新数据库
  }

  // 设置分析监控
  private setupAnalysisMonitoring(workflowId: string): void {
    // 监听 LangGraph 状态变化
    this.langGraphService.onStateChange(workflowId, async (state) => {
      await this.handleStateChange(workflowId, state);
    });

    // 监听智能体完成事件
    this.langGraphService.onAgentComplete(workflowId, async (agentResult) => {
      await this.handleAgentComplete(workflowId, agentResult);
    });
  }

  // 处理状态变化
  private async handleStateChange(workflowId: string, state: any): Promise<void> {
    try {
      // 更新工作流状态
      await this.database.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: state.currentStage,
        progress: state.progress,
        status: state.status,
        error_message: state.error,
      });

      // 更新缓存
      await this.cacheService.updateWorkflowStatus(workflowId, state);

      // 发送实时更新
      await this.notificationService.broadcastStatusUpdate(workflowId, state);

      // 记录事件
      await this.database.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: state.currentStage,
        event_type: 'state_change',
        content: `状态变更: ${state.status}`,
        metadata: state,
      });
    } catch (error) {
      console.error('处理状态变化失败:', error);
    }
  }

  // 处理智能体完成
  private async handleAgentComplete(workflowId: string, agentResult: any): Promise<void> {
    try {
      // 根据智能体类型存储结果
      if (agentResult.type === 'analyst') {
        await this.database.saveAnalystReport({
          workflow_id: workflowId,
          analyst_type: agentResult.agentType,
          summary: agentResult.summary,
          details: agentResult.details,
          status: 'completed',
          execution_time_ms: agentResult.executionTime,
        });
      } else if (agentResult.type === 'researcher') {
        await this.database.saveResearchReport({
          workflow_id: workflowId,
          researcher_type: agentResult.agentType,
          summary: agentResult.summary,
          arguments: agentResult.arguments,
          confidence_level: agentResult.confidence,
          target_price: agentResult.targetPrice,
          time_horizon: agentResult.timeHorizon,
          status: 'completed',
        });
      }

      // 更新缓存
      await this.cacheService.updateAgentResult(workflowId, agentResult);

      // 发送实时更新
      await this.notificationService.broadcastAgentComplete(workflowId, agentResult);
    } catch (error) {
      console.error('处理智能体完成失败:', error);
    }
  }

  // 处理分析成功
  private async handleAnalysisSuccess(workflowId: string, result: any): Promise<void> {
    try {
      // 存储最终决策
      if (result.finalDecision) {
        await this.database.saveFinalDecision({
          workflow_id: workflowId,
          decision_type: result.finalDecision.action,
          confidence_level: result.finalDecision.confidence,
          decision_rationale: result.finalDecision.reasoning,
          entry_price_range: result.finalDecision.priceRange,
          stop_loss_price: result.finalDecision.stopLoss,
          take_profit_price: result.finalDecision.takeProfit,
          position_size_percentage: result.finalDecision.positionSize,
        });
      }

      // 更新工作流为完成状态
      await this.database.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: 'completed',
        progress: 100,
        status: 'completed',
        error_message: null,
      });

      // 发送完成通知
      await this.notificationService.sendAnalysisCompleteNotification(workflowId);
    } catch (error) {
      console.error('处理分析成功失败:', error);
      await this.handleAnalysisError(workflowId, error);
    }
  }

  // 处理分析错误
  private async handleAnalysisError(workflowId: string, error: any): Promise<void> {
    try {
      // 更新工作流为失败状态
      await this.database.updateWorkflowStatus({
        workflow_id: workflowId,
        current_stage: 'failed',
        progress: 0,
        status: 'failed',
        error_message: error.message,
      });

      // 记录错误事件
      await this.database.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: 'error',
        event_type: 'error',
        content: `分析失败: ${error.message}`,
        metadata: { error: error.stack },
      });

      // 发送错误通知
      await this.notificationService.sendAnalysisErrorNotification(workflowId, error);
    } catch (dbError) {
      console.error('处理分析错误失败:', dbError);
    }
  }

  // 获取分析状态
  async getAnalysisStatus(workflowId: string): Promise<AnalysisStatus> {
    try {
      // 先尝试从缓存获取
      const cachedStatus = await this.cacheService.getWorkflowStatus(workflowId);
      if (cachedStatus) {
        return cachedStatus;
      }

      // 从数据库获取完整状态
      const completeStatus = await this.database.getCompleteWorkflowStatus(workflowId);

      // 更新缓存
      await this.cacheService.setWorkflowStatus(workflowId, completeStatus);

      return this.transformToAnalysisStatus(completeStatus);
    } catch (error) {
      console.error('获取分析状态失败:', error);
      throw new AnalysisError(`获取状态失败: ${error.message}`);
    }
  }

  // 转换为前端需要的状态格式
  private transformToAnalysisStatus(completeStatus: CompleteWorkflowStatus): AnalysisStatus {
    return {
      workflowId: completeStatus.workflow.workflow_id,
      status: completeStatus.workflow.status,
      progress: completeStatus.workflow.progress,
      currentStage: completeStatus.workflow.current_stage,
      ticker: completeStatus.workflow.ticker,
      title: completeStatus.workflow.title,
      createdAt: completeStatus.workflow.created_at,
      startedAt: completeStatus.workflow.started_at,
      completedAt: completeStatus.workflow.completed_at,
      analystReports: completeStatus.analystReports.map(this.transformAnalystReport),
      researchReports: completeStatus.researchReports.map(this.transformResearchReport),
      finalDecision: completeStatus.finalDecision
        ? this.transformFinalDecision(completeStatus.finalDecision)
        : null,
      recentEvents: completeStatus.recentEvents.map(this.transformEvent),
    };
  }
}
```

### 前端展示组件

创建专门的分析展示组件，提供丰富的可视化界面：

```typescript
// 分析详情页面组件
export function AnalysisDetailPage({ workflowId }: { workflowId: string }) {
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 使用 WebSocket 实时更新
  const { socket, isConnected } = useWebSocket(`/ws/analysis/${workflowId}`);

  useEffect(() => {
    // 初始加载分析状态
    loadAnalysisStatus();

    // 设置 WebSocket 事件监听
    if (socket) {
      socket.on('status_update', handleStatusUpdate);
      socket.on('agent_complete', handleAgentComplete);
      socket.on('analysis_complete', handleAnalysisComplete);
      socket.on('error', handleError);
    }

    return () => {
      if (socket) {
        socket.off('status_update');
        socket.off('agent_complete');
        socket.off('analysis_complete');
        socket.off('error');
      }
    };
  }, [workflowId, socket]);

  const loadAnalysisStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/analysis/${workflowId}/status`);
      if (!response.ok) throw new Error('获取分析状态失败');

      const data = await response.json();
      setAnalysisStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = (update: any) => {
    setAnalysisStatus((prev) =>
      prev
        ? {
            ...prev,
            status: update.status,
            progress: update.progress,
            currentStage: update.currentStage,
          }
        : null
    );
  };

  const handleAgentComplete = (agentResult: any) => {
    setAnalysisStatus((prev) => {
      if (!prev) return null;

      // 更新对应的报告
      if (agentResult.type === 'analyst') {
        const updatedReports = [...prev.analystReports];
        const existingIndex = updatedReports.findIndex(
          (r) => r.analystType === agentResult.agentType
        );

        if (existingIndex >= 0) {
          updatedReports[existingIndex] = {
            ...updatedReports[existingIndex],
            ...agentResult,
            status: 'completed',
          };
        } else {
          updatedReports.push(agentResult);
        }

        return { ...prev, analystReports: updatedReports };
      }

      return prev;
    });
  };

  if (loading) {
    return <AnalysisLoadingScreen />;
  }

  if (error) {
    return <AnalysisErrorScreen error={error} onRetry={loadAnalysisStatus} />;
  }

  if (!analysisStatus) {
    return <AnalysisNotFoundScreen workflowId={workflowId} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 分析概览 */}
        <AnalysisOverview status={analysisStatus} isConnected={isConnected} />

        {/* 进度指示器 */}
        <AnalysisProgressIndicator
          progress={analysisStatus.progress}
          currentStage={analysisStatus.currentStage}
          status={analysisStatus.status}
        />

        {/* 智能体状态面板 */}
        <AgentStatusPanel
          analystReports={analysisStatus.analystReports}
          researchReports={analysisStatus.researchReports}
        />

        {/* 分析报告展示 */}
        <AnalysisReportsSection
          analystReports={analysisStatus.analystReports}
          researchReports={analysisStatus.researchReports}
        />

        {/* 最终决策 */}
        {analysisStatus.finalDecision && (
          <FinalDecisionSection decision={analysisStatus.finalDecision} />
        )}

        {/* 事件日志 */}
        <EventLogSection events={analysisStatus.recentEvents} />
      </div>
    </div>
  );
}

// 分析概览组件
function AnalysisOverview({
  status,
  isConnected,
}: {
  status: AnalysisStatus;
  isConnected: boolean;
}) {
  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{status.title}</h1>
          <p className="text-sm text-gray-500 mt-1">工作流ID: {status.workflowId}</p>
        </div>

        <div className="flex items-center space-x-4">
          {/* 连接状态指示器 */}
          <div className="flex items-center">
            <div
              className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}
            />
            <span className="text-sm text-gray-600">{isConnected ? '实时连接' : '连接断开'}</span>
          </div>

          {/* 状态标签 */}
          <StatusBadge status={status.status} />
        </div>
      </div>

      <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <dt className="text-sm font-medium text-gray-500">股票代码</dt>
          <dd className="text-lg font-semibold text-gray-900">{status.ticker}</dd>
        </div>
        <div>
          <dt className="text-sm font-medium text-gray-500">创建时间</dt>
          <dd className="text-sm text-gray-900">
            {format(new Date(status.createdAt), 'yyyy-MM-dd HH:mm')}
          </dd>
        </div>
        <div>
          <dt className="text-sm font-medium text-gray-500">开始时间</dt>
          <dd className="text-sm text-gray-900">
            {status.startedAt ? format(new Date(status.startedAt), 'yyyy-MM-dd HH:mm') : '-'}
          </dd>
        </div>
        <div>
          <dt className="text-sm font-medium text-gray-500">完成时间</dt>
          <dd className="text-sm text-gray-900">
            {status.completedAt ? format(new Date(status.completedAt), 'yyyy-MM-dd HH:mm') : '-'}
          </dd>
        </div>
      </div>
    </div>
  );
}

// 进度指示器组件
function AnalysisProgressIndicator({
  progress,
  currentStage,
  status,
}: {
  progress: number;
  currentStage: string;
  status: string;
}) {
  const stages = [
    { key: 'start', name: '开始', description: '初始化分析任务' },
    { key: 'data_collection', name: '数据收集', description: '获取股票相关数据' },
    { key: 'analyst_analysis', name: '分析师分析', description: '多个分析师并行分析' },
    { key: 'research_debate', name: '研究辩论', description: '多头空头观点辩论' },
    { key: 'risk_assessment', name: '风险评估', description: '评估投资风险' },
    { key: 'final_decision', name: '最终决策', description: '生成投资建议' },
    { key: 'completed', name: '完成', description: '分析任务完成' },
  ];

  const currentStageIndex = stages.findIndex((stage) => stage.key === currentStage);

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-gray-900">分析进度</h2>
        <span className="text-sm text-gray-500">{progress}%</span>
      </div>

      {/* 进度条 */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* 阶段指示器 */}
      <div className="flex justify-between">
        {stages.map((stage, index) => (
          <div key={stage.key} className="flex flex-col items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index <= currentStageIndex ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
              }`}
            >
              {index + 1}
            </div>
            <div className="mt-2 text-center">
              <div className="text-xs font-medium text-gray-900">{stage.name}</div>
              <div className="text-xs text-gray-500 max-w-20">{stage.description}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 接口设计

### RESTful API 接口

完善现有的 API 接口，提供完整的分析数据访问功能：

```typescript
// 执行分析 API（基于已存在的任务）
app.post('/api/analysis/execute', async (req, res) => {
  try {
    const { taskId, config } = req.body;

    if (!taskId) {
      return res.status(400).json({
        success: false,
        error: '任务ID不能为空',
        timestamp: new Date().toISOString(),
      });
    }

    const analysisService = new AnalysisService();
    const result = await analysisService.executeAnalysis({
      taskId,
      config: config || {},
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(error.statusCode || 500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 分析状态 API
app.get('/api/analysis/:workflowId/status', async (req, res) => {
  try {
    const { workflowId } = req.params;
    const analysisService = new AnalysisService();

    const status = await analysisService.getAnalysisStatus(workflowId);

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 分析报告 API
app.get('/api/analysis/:workflowId/reports', async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { type } = req.query; // analyst, research, all

    const reports = await EnhancedLangGraphDatabase.getAnalysisReports(workflowId, type);

    res.json({
      success: true,
      data: reports,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 历史分析查询 API
app.get('/api/analysis/history', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      ticker,
      status,
      date_from,
      date_to,
      sort_by = 'created_at',
      sort_order = 'desc',
    } = req.query;

    const options = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      ticker: ticker as string,
      status: status ? (status as string).split(',') : undefined,
      date_from: date_from as string,
      date_to: date_to as string,
      sort_by: sort_by as string,
      sort_order: sort_order as string,
    };

    const result = await EnhancedLangGraphDatabase.queryWorkflows(options);

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 分析统计 API
app.get('/api/analysis/statistics', async (req, res) => {
  try {
    const { date_from, date_to, ticker } = req.query;

    const statistics = await EnhancedLangGraphDatabase.getAnalysisStatistics({
      date_from: date_from as string,
      date_to: date_to as string,
      ticker: ticker as string,
    });

    res.json({
      success: true,
      data: statistics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});
```

### WebSocket 事件设计

扩展现有的 WebSocket 事件，提供更丰富的实时更新：

```typescript
// WebSocket 事件处理器
export class AnalysisWebSocketHandler {
  constructor(private io: Server) {
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log('客户端连接:', socket.id);

      // 加入分析房间
      socket.on('join_analysis', (workflowId: string) => {
        socket.join(`analysis_${workflowId}`);
        console.log(`客户端 ${socket.id} 加入分析房间: ${workflowId}`);
      });

      // 离开分析房间
      socket.on('leave_analysis', (workflowId: string) => {
        socket.leave(`analysis_${workflowId}`);
        console.log(`客户端 ${socket.id} 离开分析房间: ${workflowId}`);
      });

      // 请求当前状态
      socket.on('request_status', async (workflowId: string) => {
        try {
          const analysisService = new AnalysisService();
          const status = await analysisService.getAnalysisStatus(workflowId);
          socket.emit('status_update', status);
        } catch (error) {
          socket.emit('error', { message: '获取状态失败', error: error.message });
        }
      });

      socket.on('disconnect', () => {
        console.log('客户端断开连接:', socket.id);
      });
    });
  }

  // 广播状态更新
  broadcastStatusUpdate(workflowId: string, status: any) {
    this.io.to(`analysis_${workflowId}`).emit('status_update', {
      type: 'status_update',
      workflowId,
      data: status,
      timestamp: new Date().toISOString(),
    });
  }

  // 广播智能体完成
  broadcastAgentComplete(workflowId: string, agentResult: any) {
    this.io.to(`analysis_${workflowId}`).emit('agent_complete', {
      type: 'agent_complete',
      workflowId,
      data: agentResult,
      timestamp: new Date().toISOString(),
    });
  }

  // 广播分析完成
  broadcastAnalysisComplete(workflowId: string, result: any) {
    this.io.to(`analysis_${workflowId}`).emit('analysis_complete', {
      type: 'analysis_complete',
      workflowId,
      data: result,
      timestamp: new Date().toISOString(),
    });
  }

  // 广播错误
  broadcastError(workflowId: string, error: any) {
    this.io.to(`analysis_${workflowId}`).emit('error', {
      type: 'error',
      workflowId,
      data: {
        message: error.message,
        code: error.code || 'unknown_error',
      },
      timestamp: new Date().toISOString(),
    });
  }
}
```

## 数据模型设计

### 扩展的数据类型定义

基于现有的数据库结构，定义完整的 TypeScript 类型：

```typescript
// 执行分析请求
export interface ExecuteAnalysisRequest {
  taskId: string;
  config?: any;
}

// 分析结果
export interface AnalysisResult {
  workflowId: string;
  taskId: string;
  status: 'started' | 'completed' | 'failed';
  message: string;
}

// 任务信息
export interface Task {
  task_id: string;
  ticker: string;
  title: string;
  description?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  config?: any;
  created_by: string;
  created_at: string;
}

// 完整的工作流状态
export interface CompleteWorkflowStatus {
  workflow: Workflow;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  finalDecision: FinalDecision | null;
  recentEvents: WorkflowEvent[];
}

// 分析状态（前端使用）
export interface AnalysisStatus {
  workflowId: string;
  status: WorkflowStatus;
  progress: number;
  currentStage: string;
  ticker: string;
  title: string;
  createdAt: string;
  startedAt: string | null;
  completedAt: string | null;
  analystReports: AnalystReportDisplay[];
  researchReports: ResearchReportDisplay[];
  finalDecision: FinalDecisionDisplay | null;
  recentEvents: WorkflowEventDisplay[];
}

// 分析师报告展示格式
export interface AnalystReportDisplay {
  reportId: string;
  analystType: 'fundamental' | 'technical' | 'sentiment' | 'news';
  analystName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  summary: string;
  details: any;
  executionTime: number | null;
  createdAt: string;
}

// 研究报告展示格式
export interface ResearchReportDisplay {
  reportId: string;
  researcherType: 'bull' | 'bear';
  researcherName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  summary: string;
  arguments: ResearchArgument[];
  confidenceLevel: number | null;
  targetPrice: number | null;
  timeHorizon: string | null;
  createdAt: string;
}

// 最终决策展示格式
export interface FinalDecisionDisplay {
  decisionId: string;
  decisionType: 'buy' | 'sell' | 'hold' | 'avoid';
  confidenceLevel: number;
  reasoning: string;
  entryPriceRange: { min: number; max: number } | null;
  stopLossPrice: number | null;
  takeProfitPrice: number | null;
  positionSizePercentage: number | null;
  createdAt: string;
}

// 工作流事件展示格式
export interface WorkflowEventDisplay {
  eventId: string;
  stageName: string | null;
  eventType: 'message' | 'tool_call' | 'state_change' | 'error' | 'log';
  content: string;
  metadata: any;
  createdAt: string;
}

// 分析统计信息
export interface AnalysisStatistics {
  basic: {
    total_workflows: number;
    completed_workflows: number;
    failed_workflows: number;
    avg_duration_seconds: number;
  };
  successRates: {
    ticker: string;
    total_count: number;
    success_count: number;
  }[];
}
```

## 缓存设计

### Redis 缓存策略

实现高效的缓存机制，提升系统性能：

```typescript
export class CacheService {
  constructor(private redis: Redis) {}

  // 缓存工作流状态
  async setWorkflowStatus(workflowId: string, status: any, ttl: number = 3600): Promise<void> {
    const key = `workflow:status:${workflowId}`;
    await this.redis.setex(key, ttl, JSON.stringify(status));
  }

  // 获取缓存的工作流状态
  async getWorkflowStatus(workflowId: string): Promise<any | null> {
    const key = `workflow:status:${workflowId}`;
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  // 缓存分析报告
  async setAnalysisReports(workflowId: string, reports: any[], ttl: number = 7200): Promise<void> {
    const key = `analysis:reports:${workflowId}`;
    await this.redis.setex(key, ttl, JSON.stringify(reports));
  }

  // 获取缓存的分析报告
  async getAnalysisReports(workflowId: string): Promise<any[] | null> {
    const key = `analysis:reports:${workflowId}`;
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  // 更新智能体结果缓存
  async updateAgentResult(workflowId: string, agentResult: any): Promise<void> {
    const statusKey = `workflow:status:${workflowId}`;
    const cached = await this.redis.get(statusKey);

    if (cached) {
      const status = JSON.parse(cached);
      // 更新对应的智能体结果
      if (agentResult.type === 'analyst') {
        const reports = status.analystReports || [];
        const existingIndex = reports.findIndex(
          (r: any) => r.analystType === agentResult.agentType
        );

        if (existingIndex >= 0) {
          reports[existingIndex] = { ...reports[existingIndex], ...agentResult };
        } else {
          reports.push(agentResult);
        }

        status.analystReports = reports;
      }

      await this.redis.setex(statusKey, 3600, JSON.stringify(status));
    }
  }

  // 清除相关缓存
  async clearWorkflowCache(workflowId: string): Promise<void> {
    const keys = [
      `workflow:status:${workflowId}`,
      `analysis:reports:${workflowId}`,
      `analysis:decision:${workflowId}`,
    ];

    await this.redis.del(...keys);
  }

  // 批量获取工作流状态
  async batchGetWorkflowStatus(workflowIds: string[]): Promise<Record<string, any>> {
    const keys = workflowIds.map((id) => `workflow:status:${id}`);
    const values = await this.redis.mget(...keys);

    const result: Record<string, any> = {};
    workflowIds.forEach((id, index) => {
      if (values[index]) {
        result[id] = JSON.parse(values[index]);
      }
    });

    return result;
  }
}
```

## 错误处理设计

### 分层错误处理策略

实现完整的错误处理机制：

```typescript
// 自定义错误类
export class AnalysisError extends Error {
  constructor(
    message: string,
    public code: string = 'ANALYSIS_ERROR',
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'AnalysisError';
  }
}

export class DatabaseError extends AnalysisError {
  constructor(message: string, details?: any) {
    super(message, 'DATABASE_ERROR', 500, details);
    this.name = 'DatabaseError';
  }
}

export class LangGraphError extends AnalysisError {
  constructor(message: string, details?: any) {
    super(message, 'LANGGRAPH_ERROR', 500, details);
    this.name = 'LangGraphError';
  }
}

// 错误处理中间件
export function errorHandler(error: Error, req: Request, res: Response, next: NextFunction) {
  console.error('API错误:', error);

  if (error instanceof AnalysisError) {
    return res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // 默认错误处理
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: '服务器内部错误',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    },
    timestamp: new Date().toISOString(),
  });
}

// 前端错误处理
export class FrontendErrorHandler {
  static handleApiError(error: any): string {
    if (error.response?.data?.error) {
      return error.response.data.error.message || '请求失败';
    }

    if (error.code === 'NETWORK_ERROR') {
      return '网络连接失败，请检查网络设置';
    }

    return error.message || '未知错误';
  }

  static showErrorNotification(error: any) {
    const message = this.handleApiError(error);
    toast.error(message);
  }
}
```

## 测试策略

### 单元测试

```typescript
// 数据库操作测试
describe('EnhancedLangGraphDatabase', () => {
  beforeEach(async () => {
    // 设置测试数据库
    await setupTestDatabase();
  });

  afterEach(async () => {
    // 清理测试数据
    await cleanupTestDatabase();
  });

  describe('createCompleteWorkflow', () => {
    it('应该成功创建完整的工作流', async () => {
      const request = {
        ticker: 'TEST001',
        title: '测试分析',
        description: '测试描述',
        config: { analysisType: 'comprehensive' },
        created_by: 'test_user',
      };

      const workflowId = await EnhancedLangGraphDatabase.createCompleteWorkflow(request);

      expect(workflowId).toBeDefined();
      expect(workflowId).toMatch(/^wf_/);

      // 验证工作流是否正确创建
      const workflow = await EnhancedLangGraphDatabase.getWorkflow(workflowId);
      expect(workflow).toBeDefined();
      expect(workflow.ticker).toBe('TEST001');
    });
  });

  describe('getCompleteWorkflowStatus', () => {
    it('应该返回完整的工作流状态', async () => {
      // 创建测试工作流
      const workflowId = await createTestWorkflow();

      const status = await EnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId);

      expect(status).toBeDefined();
      expect(status.workflow).toBeDefined();
      expect(status.analystReports).toBeInstanceOf(Array);
      expect(status.researchReports).toBeInstanceOf(Array);
    });
  });
});

// 分析服务测试
describe('AnalysisService', () => {
  let analysisService: AnalysisService;
  let mockDatabase: jest.Mocked<EnhancedLangGraphDatabase>;
  let mockLangGraphService: jest.Mocked<LangGraphService>;

  beforeEach(() => {
    mockDatabase = createMockDatabase();
    mockLangGraphService = createMockLangGraphService();
    analysisService = new AnalysisService(mockDatabase, mockLangGraphService);
  });

  describe('startAnalysis', () => {
    it('应该成功启动分析', async () => {
      const request = {
        ticker: 'TEST001',
        title: '测试分析',
        config: { analysisType: 'comprehensive' },
        user_id: 'test_user',
      };

      mockDatabase.createCompleteWorkflow.mockResolvedValue('test_workflow_id');
      mockLangGraphService.analyzeStock.mockResolvedValue({});

      const result = await analysisService.startAnalysis(request);

      expect(result.workflowId).toBe('test_workflow_id');
      expect(result.status).toBe('started');
      expect(mockDatabase.createCompleteWorkflow).toHaveBeenCalledWith(
        expect.objectContaining({
          ticker: 'TEST001',
          title: '测试分析',
        })
      );
    });
  });
});
```

### 集成测试

```typescript
// API 集成测试
describe('Analysis API Integration', () => {
  let app: Express;
  let testWorkflowId: string;

  beforeAll(async () => {
    app = await createTestApp();
    testWorkflowId = await createTestWorkflow();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('GET /api/analysis/:workflowId/status', () => {
    it('应该返回分析状态', async () => {
      const response = await request(app).get(`/api/analysis/${testWorkflowId}/status`).expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.workflowId).toBe(testWorkflowId);
    });

    it('应该处理不存在的工作流', async () => {
      const response = await request(app).get('/api/analysis/nonexistent/status').expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });
});

// WebSocket 集成测试
describe('WebSocket Integration', () => {
  let server: Server;
  let clientSocket: Socket;

  beforeAll(async () => {
    server = await createTestServer();
    clientSocket = createTestClient();
  });

  afterAll(async () => {
    await server.close();
    clientSocket.close();
  });

  it('应该正确处理分析房间加入', (done) => {
    const testWorkflowId = 'test_workflow_id';

    clientSocket.emit('join_analysis', testWorkflowId);

    clientSocket.on('status_update', (data) => {
      expect(data.workflowId).toBe(testWorkflowId);
      done();
    });

    // 模拟状态更新
    setTimeout(() => {
      server.emit('status_update', {
        workflowId: testWorkflowId,
        status: 'running',
        progress: 50,
      });
    }, 100);
  });
});
```

## 部署考虑

### 数据库优化

```sql
-- 添加必要的索引以优化查询性能
CREATE INDEX idx_workflows_ticker_status ON workflows(ticker, status);
CREATE INDEX idx_workflows_created_at_status ON workflows(created_at, status);
CREATE INDEX idx_analyst_reports_workflow_type ON analyst_reports(workflow_id, analyst_type);
CREATE INDEX idx_research_reports_workflow_type ON research_reports(workflow_id, researcher_type);
CREATE INDEX idx_workflow_events_workflow_created ON workflow_events(workflow_id, created_at);

-- 添加分区表以处理大量历史数据
ALTER TABLE workflow_events
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 监控和日志

```typescript
// 性能监控
export class PerformanceMonitor {
  static trackAnalysisPerformance(workflowId: string, startTime: number) {
    const duration = Date.now() - startTime;

    // 记录性能指标
    console.log(`分析性能 [${workflowId}]: ${duration}ms`);

    // 发送到监控系统
    if (process.env.MONITORING_ENABLED === 'true') {
      // 发送到 Prometheus 或其他监控系统
    }
  }

  static trackDatabaseQuery(query: string, duration: number) {
    if (duration > 1000) {
      console.warn(`慢查询检测: ${query} - ${duration}ms`);
    }
  }
}

// 业务日志
export class BusinessLogger {
  static logAnalysisStart(workflowId: string, ticker: string, userId: string) {
    console.log(`[ANALYSIS_START] 工作流: ${workflowId}, 股票: ${ticker}, 用户: ${userId}`);
  }

  static logAnalysisComplete(workflowId: string, duration: number, success: boolean) {
    console.log(`[ANALYSIS_COMPLETE] 工作流: ${workflowId}, 耗时: ${duration}ms, 成功: ${success}`);
  }

  static logError(workflowId: string, error: Error, context: any) {
    console.error(`[ANALYSIS_ERROR] 工作流: ${workflowId}, 错误: ${error.message}`, {
      error: error.stack,
      context,
    });
  }
}
```

这个设计文档提供了完整的分析数据存储和前端展示功能的技术实现方案，涵盖了数据库操作、业务逻辑、API 接口、前端组件、缓存策略、错误处理和测试等各个方面。通过这个设计，系统将能够完整地存储和展示分析过程和结果，为用户提供优秀的分析体验。
