import { render, screen } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem } from '../Breadcrumb';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('Breadcrumb', () => {
  beforeEach(() => {
    mockUsePathname.mockClear();
  });

  describe('with custom items', () => {
    it('renders custom breadcrumb items correctly', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析中心', href: '/analysis' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByLabelText('面包屑导航')).toBeInTheDocument();
      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析历史')).toBeInTheDocument();
    });

    it('renders home icon for first item', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '任务列表', current: true },
      ];

      render(<Breadcrumb items={items} />);

      // Check that home icon is present (using aria-hidden attribute)
      const homeIcon = document.querySelector('[aria-hidden="true"]');
      expect(homeIcon).toBeInTheDocument();
    });

    it('renders links for non-current items', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析中心', href: '/analysis' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const homeLink = screen.getByRole('link', { name: /首页/ });
      const analysisLink = screen.getByRole('link', { name: /分析中心/ });

      expect(homeLink).toHaveAttribute('href', '/');
      expect(analysisLink).toHaveAttribute('href', '/analysis');
    });

    it('renders current item as span without link', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const currentItem = screen.getByText('分析历史');
      expect(currentItem).not.toHaveAttribute('href');
      expect(currentItem.tagName).toBe('SPAN');
      expect(currentItem).toHaveAttribute('aria-current', 'page');
    });

    it('renders chevron separators between items', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析中心', href: '/analysis' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      // Should have 2 chevron icons for 3 items
      const chevrons = document.querySelectorAll('[aria-hidden="true"]');
      // Filter out the home icon to count only chevrons
      const chevronIcons = Array.from(chevrons).filter((el) => el.classList.contains('mx-2'));
      expect(chevronIcons).toHaveLength(2);
    });
  });

  describe('with auto-generated items', () => {
    it('generates breadcrumbs for tasks page', () => {
      mockUsePathname.mockReturnValue('/tasks');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('任务列表')).toBeInTheDocument();
    });

    it('generates breadcrumbs for analysis history page', () => {
      mockUsePathname.mockReturnValue('/analysis/history');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析历史')).toBeInTheDocument();
    });

    it('generates breadcrumbs for analysis compare page', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析对比')).toBeInTheDocument();
    });

    it('generates breadcrumbs for analysis detail page', () => {
      mockUsePathname.mockReturnValue('/analysis/abc123');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析详情 (abc123)')).toBeInTheDocument();
    });

    it('generates breadcrumbs for create-task page', () => {
      mockUsePathname.mockReturnValue('/create-task');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('创建任务')).toBeInTheDocument();
    });

    it('handles nested paths correctly', () => {
      mockUsePathname.mockReturnValue('/analysis/history/filter');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析历史')).toBeInTheDocument();
      expect(screen.getByText('filter')).toBeInTheDocument();
    });

    it('returns null for root path only', () => {
      mockUsePathname.mockReturnValue('/');

      const { container } = render(<Breadcrumb />);

      expect(container.firstChild).toBeNull();
    });

    it('handles empty path segments', () => {
      mockUsePathname.mockReturnValue('/analysis//history');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析历史')).toBeInTheDocument();
      // Should not render empty segments - check that we only have 3 breadcrumb items
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems).toHaveLength(3);
    });
  });

  describe('accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveAttribute('aria-label', '面包屑导航');
      expect(nav).toHaveAttribute('role', 'navigation');
    });

    it('marks current page with aria-current', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const currentItem = screen.getByText('分析历史');
      expect(currentItem).toHaveAttribute('aria-current', 'page');
    });

    it('does not mark non-current items with aria-current', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const homeLink = screen.getByRole('link', { name: /首页/ });
      expect(homeLink).not.toHaveAttribute('aria-current');
    });

    it('hides decorative icons from screen readers', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析中心', href: '/analysis' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb />);

      const hiddenElements = document.querySelectorAll('[aria-hidden="true"]');
      expect(hiddenElements.length).toBeGreaterThan(0);
    });
  });

  describe('styling and responsive design', () => {
    it('applies custom className', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} className="custom-class" />);

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('custom-class');
    });

    it('applies default styling classes', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('flex');
    });

    it('applies hover styles to links', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const homeLink = screen.getByRole('link', { name: /首页/ });
      expect(homeLink).toHaveClass('hover:text-blue-600');
      expect(homeLink).toHaveClass('dark:hover:text-blue-400');
    });

    it('applies different styles for current item', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const currentItem = screen.getByText('分析历史');
      expect(currentItem).toHaveClass('text-slate-900');
      expect(currentItem).toHaveClass('dark:text-white');
    });
  });

  describe('edge cases', () => {
    it('handles undefined items gracefully', () => {
      mockUsePathname.mockReturnValue('/tasks');

      render(<Breadcrumb items={undefined} />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('任务列表')).toBeInTheDocument();
    });

    it('handles empty items array', () => {
      render(<Breadcrumb items={[]} />);

      const { container } = render(<Breadcrumb items={[]} />);
      expect(container.firstChild).toBeNull();
    });

    it('handles single item (should not render)', () => {
      const items: BreadcrumbItem[] = [{ label: '首页', href: '/' }];

      const { container } = render(<Breadcrumb items={items} />);
      expect(container.firstChild).toBeNull();
    });

    it('handles items without href', () => {
      const items: BreadcrumbItem[] = [
        { label: '首页', href: '/' },
        { label: '分析中心' }, // No href
        { label: '分析历史', current: true },
      ];

      render(<Breadcrumb items={items} />);

      const analysisCenter = screen.getByText('分析中心');
      expect(analysisCenter.tagName).toBe('SPAN');
      expect(analysisCenter).not.toHaveAttribute('href');
    });

    it('handles special characters in path segments', () => {
      mockUsePathname.mockReturnValue('/analysis/test-123_abc');

      render(<Breadcrumb />);

      expect(screen.getByText('首页')).toBeInTheDocument();
      expect(screen.getByText('分析中心')).toBeInTheDocument();
      expect(screen.getByText('分析详情 (test-123_abc)')).toBeInTheDocument();
    });
  });
});
