'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  ArrowDownTrayIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  ClockIcon,
  DocumentTextIcon,
  HeartIcon,
  NewspaperIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface ComprehensiveReportViewerProps {
  workflowId: string;
  ticker: string;
  title: string;
}

export function ComprehensiveReportViewer({
  workflowId,
  ticker,
  title,
}: ComprehensiveReportViewerProps) {
  const [activeTab, setActiveTab] = useState<'summary' | 'detailed' | 'timeline'>('summary');
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const tabs = [
    { id: 'summary', name: '综合摘要', icon: DocumentTextIcon },
    { id: 'detailed', name: '详细报告', icon: ChartBarIcon },
    { id: 'timeline', name: '分析时间线', icon: ClockIcon },
  ];

  const loadComprehensiveReport = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analysis/${workflowId}/comprehensive-report`);
      if (response.ok) {
        const data = await response.json();
        setReportData(data);
      }
    } catch (error) {
      console.error('Failed to load comprehensive report:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format: 'pdf' | 'markdown' | 'json') => {
    try {
      const response = await fetch(`/api/analysis/${workflowId}/export?format=${format}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${ticker}_analysis_report.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export report:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* 报告头部 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <DocumentTextIcon className="h-6 w-6" />
                <span>综合分析报告</span>
              </CardTitle>
              <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                {title} ({ticker})
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => exportReport('pdf')}
                className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 text-sm"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>导出 PDF</span>
              </button>
              <button
                onClick={() => exportReport('markdown')}
                className="px-3 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors flex items-center space-x-2 text-sm"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>导出 MD</span>
              </button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 标签页导航 */}
      <Card>
        <CardContent className="p-0">
          <div className="border-b border-slate-200 dark:border-slate-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-slate-500 hover:text-slate-700 dark:hover:text-slate-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* 标签页内容 */}
          <div className="p-6">
            {activeTab === 'summary' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                {/* 执行摘要 */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                    执行摘要
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <ChartBarIcon className="h-5 w-5 text-blue-600" />
                        <span className="font-medium text-blue-800 dark:text-blue-200">
                          基本面分析
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        财务状况良好，估值合理
                      </p>
                    </div>
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <ArrowTrendingUpIcon className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-800 dark:text-green-200">
                          技术分析
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        技术指标显示上升趋势
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <NewspaperIcon className="h-5 w-5 text-purple-600" />
                        <span className="font-medium text-purple-800 dark:text-purple-200">
                          新闻分析
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        市场情绪中性偏积极
                      </p>
                    </div>
                    <div className="p-4 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <HeartIcon className="h-5 w-5 text-pink-600" />
                        <span className="font-medium text-pink-800 dark:text-pink-200">
                          情绪分析
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">投资者情绪稳定</p>
                    </div>
                  </div>
                </div>

                {/* 关键发现 */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                    关键发现
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-green-800 dark:text-green-200">积极因素</p>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          公司基本面稳健，技术指标显示上升趋势，市场情绪积极
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-yellow-800 dark:text-yellow-200">关注点</p>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          需要关注市场整体波动性和行业竞争加剧的风险
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                      <div>
                        <p className="font-medium text-red-800 dark:text-red-200">风险因素</p>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          宏观经济不确定性可能影响公司业绩表现
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 投资建议 */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-4">
                    投资建议
                  </h3>
                  <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-blue-600 rounded-lg">
                        <ArrowTrendingUpIcon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-blue-900 dark:text-blue-100">
                          买入建议
                        </h4>
                        <p className="text-blue-700 dark:text-blue-300">基于综合分析结果</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          $125.50
                        </div>
                        <div className="text-sm text-blue-700 dark:text-blue-300">目标价格</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          85%
                        </div>
                        <div className="text-sm text-blue-700 dark:text-blue-300">置信度</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          6-12月
                        </div>
                        <div className="text-sm text-blue-700 dark:text-blue-300">投资期限</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'detailed' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div className="text-center text-slate-500 py-12">
                  <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <p>详细报告功能开发中...</p>
                  <button
                    onClick={loadComprehensiveReport}
                    disabled={loading}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                  >
                    {loading ? '加载中...' : '加载详细报告'}
                  </button>
                </div>
              </motion.div>
            )}

            {activeTab === 'timeline' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div className="text-center text-slate-500 py-12">
                  <ClockIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <p>分析时间线功能开发中...</p>
                </div>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
