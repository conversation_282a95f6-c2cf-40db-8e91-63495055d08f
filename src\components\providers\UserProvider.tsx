'use client';

import useUserStore from '@/store/userStore';
import { useEffect, useState } from 'react';

interface UserProviderProps {
  children: React.ReactNode;
}

/**
 * UserProvider 组件
 * 确保用户状态在服务端和客户端渲染时保持一致，避免水合错误
 */
export function UserProvider({ children }: UserProviderProps) {
  const [isMounted, setIsMounted] = useState(false);
  const { fetchUser, initialized } = useUserStore();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted && !initialized) {
      fetchUser();
    }
  }, [isMounted, initialized, fetchUser]);

  // 在服务端渲染时，不渲染依赖用户状态的内容
  if (!isMounted) {
    return <>{children}</>;
  }

  return <>{children}</>;
}
