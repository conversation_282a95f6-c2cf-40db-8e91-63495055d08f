/**
 * SEO 配置文件和常量定义
 * 包含默认配置、多语言内容和 SEO 最佳实践参数
 */

import { SEOConfig, SupportedLocale, PageType } from '@/types/seo';

// SEO 相关常量
export const SEO_CONSTANTS = {
  // 元数据长度限制
  TITLE_MAX_LENGTH: 60,
  DESCRIPTION_MAX_LENGTH: 160,
  KEYWORDS_MAX_COUNT: 10,

  // 图片尺寸
  OG_IMAGE_WIDTH: 1200,
  OG_IMAGE_HEIGHT: 630,
  TWITTER_IMAGE_WIDTH: 1200,
  TWITTER_IMAGE_HEIGHT: 600,

  // 默认图片
  DEFAULT_OG_IMAGE: '/tradingAgent.png',
  DEFAULT_FAVICON: '/tradingAgent.ico',

  // 网站信息
  SITE_NAME: 'TradingAgents',
  SITE_URL: 'https://tradingagents.com', // 默认值，运行时会被环境变量覆盖

  // 社交媒体
  TWITTER_HANDLE: '@tradingagents',

  // 搜索引擎验证码（运行时设置）
  GOOGLE_VERIFICATION: '',
  BAIDU_VERIFICATION: '',
} as const;

// 默认 SEO 配置
export const DEFAULT_SEO_CONFIG: SEOConfig = {
  title: 'TradingAgents - 多智能体大语言模型金融交易框架',
  description:
    '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。',
  keywords: ['多智能体', '金融交易', 'AI交易', '量化投资', '大语言模型', '智能分析'],
  canonical: SEO_CONSTANTS.SITE_URL,
  openGraph: {
    title: 'TradingAgents - 多智能体金融交易框架',
    description:
      '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。',
    image: `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`,
    type: 'website',
    locale: 'zh_CN',
    siteName: SEO_CONSTANTS.SITE_NAME,
    url: SEO_CONSTANTS.SITE_URL,
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TradingAgents - 多智能体金融交易框架',
    description:
      '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。',
    image: `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`,
    site: SEO_CONSTANTS.TWITTER_HANDLE,
  },
};

// 多语言 SEO 内容配置
export const SEO_CONTENT: Record<
  SupportedLocale,
  Record<PageType, Omit<SEOConfig, 'openGraph' | 'twitter'>>
> = {
  zh: {
    home: {
      title: 'TradingAgents - 多智能体大语言模型金融交易框架',
      description:
        '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。',
      keywords: ['多智能体', '金融交易', 'AI交易', '量化投资', '大语言模型', '智能分析'],
      canonical: `${SEO_CONSTANTS.SITE_URL}/`,
    },
    analysis: {
      title: '智能分析 - TradingAgents',
      description:
        '实时多智能体协作分析，包含基本面分析、技术分析、情绪分析和风险评估，为您的交易决策提供全方位支持。',
      keywords: ['股票分析', '技术分析', '基本面分析', '风险评估', '交易决策', '多智能体协作'],
      canonical: `${SEO_CONSTANTS.SITE_URL}/analysis`,
    },
    tasks: {
      title: '任务管理 - TradingAgents',
      description:
        '管理和监控您的分析任务，查看任务状态、执行进度和分析结果，高效管理您的投资研究工作流。',
      keywords: ['任务管理', '分析任务', '投资研究', '工作流管理', '任务监控'],
      canonical: `${SEO_CONSTANTS.SITE_URL}/tasks`,
    },
    messages: {
      title: '消息查询 - TradingAgents',
      description: '查看和管理系统消息、分析报告和智能体通信记录，全面了解分析过程和决策依据。',
      keywords: ['消息查询', '分析报告', '智能体通信', '系统消息', '决策记录'],
      canonical: `${SEO_CONSTANTS.SITE_URL}/messages`,
    },
    'create-task': {
      title: '创建任务 - TradingAgents',
      description: '创建新的分析任务，配置股票代码、分析参数和智能体设置，开始您的专业投资分析。',
      keywords: ['创建任务', '分析配置', '股票分析', '投资研究', '智能体设置'],
      canonical: `${SEO_CONSTANTS.SITE_URL}/create-task`,
    },
  },
  en: {
    home: {
      title: 'TradingAgents - Multi-Agent LLM Financial Trading Framework',
      description:
        'Professional financial trading analysis framework based on multi-agent large language models, providing intelligent market analysis, risk assessment and trading decision support.',
      keywords: [
        'multi-agent',
        'financial trading',
        'AI trading',
        'quantitative investment',
        'LLM',
        'intelligent analysis',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en`,
    },
    analysis: {
      title: 'Intelligent Analysis - TradingAgents',
      description:
        'Real-time multi-agent collaborative analysis including fundamental analysis, technical analysis, sentiment analysis and risk assessment for comprehensive trading decision support.',
      keywords: [
        'stock analysis',
        'technical analysis',
        'fundamental analysis',
        'risk assessment',
        'trading decisions',
        'multi-agent collaboration',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/analysis`,
    },
    tasks: {
      title: 'Task Management - TradingAgents',
      description:
        'Manage and monitor your analysis tasks, view task status, execution progress and analysis results for efficient investment research workflow management.',
      keywords: [
        'task management',
        'analysis tasks',
        'investment research',
        'workflow management',
        'task monitoring',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/tasks`,
    },
    messages: {
      title: 'Message Query - TradingAgents',
      description:
        'View and manage system messages, analysis reports and agent communication records for comprehensive understanding of analysis processes and decision rationale.',
      keywords: [
        'message query',
        'analysis reports',
        'agent communication',
        'system messages',
        'decision records',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/messages`,
    },
    'create-task': {
      title: 'Create Task - TradingAgents',
      description:
        'Create new analysis tasks, configure stock symbols, analysis parameters and agent settings to start your professional investment analysis.',
      keywords: [
        'create task',
        'analysis configuration',
        'stock analysis',
        'investment research',
        'agent settings',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/create-task`,
    },
  },
};

// 结构化数据模板
export const STRUCTURED_DATA_TEMPLATES = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SEO_CONSTANTS.SITE_NAME,
    description: '多智能体大语言模型金融交易框架',
    url: SEO_CONSTANTS.SITE_URL,
    logo: `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.DEFAULT_OG_IMAGE}`,
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['Chinese', 'English'],
    },
    sameAs: ['https://github.com/tradingagents', 'https://twitter.com/tradingagents'],
  },
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: SEO_CONSTANTS.SITE_NAME,
    url: SEO_CONSTANTS.SITE_URL,
    description: '基于多智能体大语言模型的专业金融交易分析框架',
    potentialAction: {
      '@type': 'SearchAction',
      target: `${SEO_CONSTANTS.SITE_URL}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  },
  softwareApplication: {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: SEO_CONSTANTS.SITE_NAME,
    applicationCategory: 'FinanceApplication',
    operatingSystem: 'Web Browser',
    description: '基于多智能体大语言模型的金融交易分析框架',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '150',
    },
  },
  // 金融服务相关模板
  financialService: {
    '@context': 'https://schema.org',
    '@type': 'FinancialProduct',
    name: 'AI智能交易分析服务',
    description:
      '基于多智能体大语言模型的专业金融分析服务，提供实时市场分析、风险评估和交易决策支持',
    category: '金融科技服务',
    feesAndCommissionsSpecification: '免费使用',
  },
  investmentService: {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: 'AI投资分析服务',
    description: '多智能体协作的投资分析服务，包括基本面分析、技术分析、情绪分析和风险管理',
    serviceType: 'Investment Advisory',
    areaServed: ['China', 'Global'],
    availableChannel: ['Website', 'API', 'Mobile App'],
  },
} as const;

// SEO 最佳实践配置
export const SEO_BEST_PRACTICES = {
  // 标题优化
  titleOptimization: {
    includeKeyword: true,
    includeBrand: true,
    maxLength: SEO_CONSTANTS.TITLE_MAX_LENGTH,
    separator: ' - ',
  },

  // 描述优化
  descriptionOptimization: {
    includeKeyword: true,
    includeCTA: true,
    maxLength: SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH,
  },

  // 关键词优化
  keywordOptimization: {
    maxCount: SEO_CONSTANTS.KEYWORDS_MAX_COUNT,
    includeLongTail: true,
    includeLocalKeywords: true,
  },

  // 图片优化
  imageOptimization: {
    includeAlt: true,
    optimizeSize: true,
    useWebP: true,
    lazyLoad: true,
  },
} as const;
