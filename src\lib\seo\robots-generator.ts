/**
 * Robots.txt 生成器
 * 动态生成 robots.txt 文件，支持环境差异化配置
 */

import { getRuntimeSEOConfig } from './runtime-config';

export interface RobotsRule {
  userAgent: string;
  allow?: string[];
  disallow?: string[];
  crawlDelay?: number;
}

export interface RobotsConfig {
  baseUrl: string;
  environment: 'development' | 'production' | 'staging';
  allowCrawling: boolean;
  sitemapUrl?: string;
  customRules?: RobotsRule[];
}

/**
 * Robots.txt 生成器类
 */
export class RobotsGenerator {
  private config: RobotsConfig;

  constructor() {
    const runtimeConfig = getRuntimeSEOConfig();
    this.config = {
      baseUrl: runtimeConfig.SITE_URL,
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      allowCrawling: process.env.NODE_ENV === 'production',
      sitemapUrl: `${runtimeConfig.SITE_URL}/sitemap.xml`,
    };
  }

  /**
   * 生成 robots.txt 内容
   */
  generateRobotsTxt(): string {
    const header = this.generateHeader();
    const rules = this.generateRules();
    const sitemaps = this.generateSitemaps();
    const footer = this.generateFooter();

    return [header, rules, sitemaps, footer].filter(Boolean).join('\n\n');
  }

  /**
   * 生成文件头部注释
   */
  private generateHeader(): string {
    const timestamp = new Date().toISOString();
    return `# TradingAgents Robots.txt
# 多智能体金融交易框架
# Generated automatically on ${timestamp}
# Environment: ${this.config.environment}`;
  }

  /**
   * 生成爬虫规则
   */
  private generateRules(): string {
    if (!this.config.allowCrawling) {
      return this.generateDevelopmentRules();
    }

    return this.generateProductionRules();
  }

  /**
   * 生成生产环境规则
   */
  private generateProductionRules(): string {
    const rules: string[] = [];

    // 通用爬虫规则
    rules.push(`# 通用爬虫规则
User-agent: *
Allow: /
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Disallow: /login
Disallow: /register
Disallow: /debug/
Disallow: /test/
Disallow: /*.json$
Disallow: /*?*utm_*
Disallow: /*?*ref=*`);

    // Google 爬虫特殊规则
    rules.push(`# Google 爬虫优化
User-agent: Googlebot
Allow: /
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Crawl-delay: 1`);

    // 百度爬虫规则
    rules.push(`# 百度爬虫规则
User-agent: Baiduspider
Allow: /
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Crawl-delay: 2`);

    // 必应爬虫规则
    rules.push(`# 必应爬虫规则
User-agent: Bingbot
Allow: /
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Crawl-delay: 1`);

    // 其他搜索引擎
    rules.push(`# 其他搜索引擎
User-agent: Slurp
User-agent: DuckDuckBot
User-agent: facebookexternalhit
Allow: /
Disallow: /api/
Disallow: /admin/
Crawl-delay: 2`);

    // 恶意爬虫阻止
    rules.push(`# 阻止恶意爬虫
User-agent: SemrushBot
User-agent: AhrefsBot
User-agent: MJ12bot
User-agent: DotBot
Disallow: /`);

    return rules.join('\n\n');
  }

  /**
   * 生成开发环境规则
   */
  private generateDevelopmentRules(): string {
    return `# 开发环境 - 禁止所有爬虫
User-agent: *
Disallow: /

# 仅允许特定测试爬虫
User-agent: GooglebotTesting
Allow: /`;
  }

  /**
   * 生成 sitemap 声明
   */
  private generateSitemaps(): string {
    const sitemaps: string[] = [];

    // 主 sitemap
    if (this.config.sitemapUrl) {
      sitemaps.push(`Sitemap: ${this.config.sitemapUrl}`);
    }

    // 可以添加更多 sitemap
    if (this.config.allowCrawling) {
      sitemaps.push(`Sitemap: ${this.config.baseUrl}/sitemap-index.xml`);
    }

    return sitemaps.length > 0 ? `# Sitemap 位置\n${sitemaps.join('\n')}` : '';
  }

  /**
   * 生成文件尾部信息
   */
  private generateFooter(): string {
    if (!this.config.allowCrawling) {
      return '';
    }

    return `# 网站信息
# 主机: ${this.config.baseUrl}
# 联系方式: 请通过网站联系表单联系我们
# 更新频率: 每日更新`;
  }

  /**
   * 验证 robots.txt 内容
   */
  validateRobotsTxt(content: string): boolean {
    // 基本验证规则
    const hasUserAgent = /User-agent:/i.test(content);
    const hasValidDirectives = /(?:Allow|Disallow):/i.test(content);

    if (!hasUserAgent) {
      console.warn('Robots.txt validation: Missing User-agent directive');
      return false;
    }

    if (!hasValidDirectives) {
      console.warn('Robots.txt validation: Missing Allow/Disallow directives');
      return false;
    }

    return true;
  }

  /**
   * 获取特定爬虫的规则
   */
  getRulesForUserAgent(userAgent: string): string[] {
    const content = this.generateRobotsTxt();
    const lines = content.split('\n');
    const rules: string[] = [];
    let currentUserAgent = '';
    let isTargetUserAgent = false;

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (trimmedLine.startsWith('User-agent:')) {
        currentUserAgent = trimmedLine.replace('User-agent:', '').trim();
        isTargetUserAgent = currentUserAgent === '*' || currentUserAgent === userAgent;
      } else if (
        isTargetUserAgent &&
        (trimmedLine.startsWith('Allow:') || trimmedLine.startsWith('Disallow:'))
      ) {
        rules.push(trimmedLine);
      }
    }

    return rules;
  }
}

/**
 * 创建 robots 生成器实例
 */
export function createRobotsGenerator(): RobotsGenerator {
  return new RobotsGenerator();
}

/**
 * 快速生成 robots.txt
 */
export function generateRobotsTxt(): string {
  const generator = createRobotsGenerator();
  return generator.generateRobotsTxt();
}
