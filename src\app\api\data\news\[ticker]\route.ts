import { akshareAdapter } from '@/lib/akshare/adapter';
import { AKShareNewsItem } from '@/types';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 获取股票新闻数据 API
 * GET /api/data/news/{ticker}?limit=10&days=7
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const { ticker } = await params;
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const limit = parseInt(searchParams.get('limit') || '20');
    const days = parseInt(searchParams.get('days') || '7');

    console.log(`[新闻数据API] 获取股票新闻: ${ticker}, limit: ${limit}, days: ${days}`);

    // 调用AKShare适配器获取股票新闻
    const newsData = await akshareAdapter.invoke<AKShareNewsItem[]>('get_stock_news', {
      symbol: ticker,
      limit: Math.min(limit, 100), // 限制最大数量
    });

    if (!newsData || !Array.isArray(newsData)) {
      return NextResponse.json(
        {
          error: '无法获取新闻数据',
          ticker,
          message: '数据格式错误或数据为空',
        },
        { status: 404 }
      );
    }

    // 过滤指定天数内的新闻
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const filteredNews = newsData
      .filter((item) => {
        const newsDate = new Date(item.发布时间 || item.time || '');
        return newsDate >= cutoffDate;
      })
      .slice(0, limit);

    // 格式化新闻数据
    const formattedNews = filteredNews.map((item, index) => ({
      id: `news-${ticker}-${index}`,
      title: item.标题 || item.title || '',
      summary: item.内容 || item.content || item.摘要 || item.summary || '',
      source: item.来源 || item.source || '未知来源',
      url: item.链接 || item.url || '',
      publishedAt: item.发布时间 || item.time || new Date().toISOString(),
      sentiment: calculateNewsSentiment(
        item.标题 || item.title || '',
        item.内容 || item.content || ''
      ),
      category: categorizeNews(item.标题 || item.title || ''),
      importance: calculateNewsImportance(item),
    }));

    // 计算新闻统计信息
    const stats = calculateNewsStats(formattedNews);

    return NextResponse.json({
      ticker,
      name: getStockName(ticker),
      news: formattedNews,
      stats,
      count: formattedNews.length,
      period: `${days}天`,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('[新闻数据API] 错误:', error);

    return NextResponse.json(
      {
        error: '获取新闻数据失败',
        ticker: (await params).ticker,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 计算新闻情绪分数 (-1 到 1)
 */
function calculateNewsSentiment(title: string, content: string): number {
  const text = (title + ' ' + content).toLowerCase();

  const positiveKeywords = [
    '利好',
    '上涨',
    '增长',
    '盈利',
    '突破',
    '创新',
    '合作',
    '收购',
    '扩张',
    '业绩',
    '分红',
    '回购',
    '增持',
    '看好',
    '推荐',
    '买入',
    '强势',
  ];

  const negativeKeywords = [
    '利空',
    '下跌',
    '亏损',
    '风险',
    '调查',
    '处罚',
    '下降',
    '警告',
    '减持',
    '卖出',
    '看空',
    '暴跌',
    '崩盘',
    '危机',
    '问题',
    '困难',
  ];

  let positiveScore = 0;
  let negativeScore = 0;

  positiveKeywords.forEach((keyword) => {
    const matches = (text.match(new RegExp(keyword, 'g')) || []).length;
    positiveScore += matches;
  });

  negativeKeywords.forEach((keyword) => {
    const matches = (text.match(new RegExp(keyword, 'g')) || []).length;
    negativeScore += matches;
  });

  const totalScore = positiveScore + negativeScore;
  if (totalScore === 0) return 0;

  return (positiveScore - negativeScore) / totalScore;
}

/**
 * 新闻分类
 */
function categorizeNews(title: string): string {
  const categories = {
    财报: ['财报', '业绩', '年报', '季报', '半年报'],
    公告: ['公告', '披露', '通知', '声明'],
    分析: ['分析', '研报', '评级', '目标价', '推荐'],
    市场: ['市场', '行情', '走势', '涨跌'],
    监管: ['监管', '调查', '处罚', '合规'],
    合作: ['合作', '收购', '并购', '投资', '签约'],
    产品: ['产品', '技术', '创新', '研发'],
    人事: ['人事', '任命', '离职', '高管'],
  };

  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some((keyword) => title.includes(keyword))) {
      return category;
    }
  }

  return '其他';
}

/**
 * 计算新闻重要性 (0-1)
 */
function calculateNewsImportance(item: AKShareNewsItem): number {
  const title = item.标题 || item.title || '';
  const source = item.来源 || item.source || '';

  let importance = 0.5; // 基础重要性

  // 根据来源调整重要性
  const authoritiveSources = [
    '新华社',
    '人民日报',
    '证券时报',
    '上海证券报',
    '中国证券报',
    '财经',
    '第一财经',
  ];
  if (authoritiveSources.some((authSource) => source.includes(authSource))) {
    importance += 0.2;
  }

  // 根据标题关键词调整重要性
  const importantKeywords = ['重大', '紧急', '突发', '首次', '创历史', '里程碑', '重组', '并购'];
  if (importantKeywords.some((keyword) => title.includes(keyword))) {
    importance += 0.2;
  }

  // 根据财报相关调整重要性
  if (title.includes('财报') || title.includes('业绩')) {
    importance += 0.1;
  }

  return Math.min(importance, 1.0);
}

/**
 * 计算新闻统计信息
 */
function calculateNewsStats(news: any[]) {
  if (!news || news.length === 0) {
    return null;
  }

  const sentiments = news.map((item) => item.sentiment);
  const avgSentiment =
    sentiments.reduce((sum, sentiment) => sum + sentiment, 0) / sentiments.length;

  const positiveCount = sentiments.filter((s) => s > 0.1).length;
  const negativeCount = sentiments.filter((s) => s < -0.1).length;
  const neutralCount = news.length - positiveCount - negativeCount;

  // 按类别统计
  const categoryStats: { [key: string]: number } = {};
  news.forEach((item) => {
    categoryStats[item.category] = (categoryStats[item.category] || 0) + 1;
  });

  // 按来源统计
  const sourceStats: { [key: string]: number } = {};
  news.forEach((item) => {
    sourceStats[item.source] = (sourceStats[item.source] || 0) + 1;
  });

  return {
    totalCount: news.length,
    sentimentDistribution: {
      positive: positiveCount,
      negative: negativeCount,
      neutral: neutralCount,
    },
    avgSentiment: Math.round(avgSentiment * 1000) / 1000,
    sentimentLabel: avgSentiment > 0.1 ? '偏正面' : avgSentiment < -0.1 ? '偏负面' : '中性',
    categoryStats,
    sourceStats,
    timeRange: {
      latest: news[0]?.publishedAt,
      earliest: news[news.length - 1]?.publishedAt,
    },
  };
}

/**
 * 获取股票名称 (简化版本)
 */
function getStockName(ticker: string): string {
  const stockNames: { [key: string]: string } = {
    '000001': '平安银行',
    '000002': '万科A',
    '600000': '浦发银行',
    '600036': '招商银行',
    '600519': '贵州茅台',
    '000858': '五粮液',
    '002415': '海康威视',
    '300059': '东方财富',
  };

  return stockNames[ticker] || ticker;
}
