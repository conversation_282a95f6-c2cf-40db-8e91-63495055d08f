'use client';

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { extractSummary } from '@/utils/markdownRenderer';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
  DocumentTextIcon,
  EyeIcon,
  HeartIcon,
  InformationCircleIcon,
  NewspaperIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useMemo, useState } from 'react';

// 类型定义
interface AnalystReport {
  id: number;
  report_id: string;
  analyst_type: 'fundamental' | 'technical' | 'sentiment' | 'news';
  summary?: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: string;
}

interface ResearchReport {
  id: number;
  report_id: string;
  researcher_type: 'bull' | 'bear';
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'completed' | 'failed';
  created_at: string;
}

interface EnhancedReportViewerProps {
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
}

// Markdown 渲染函数
const renderMarkdown = (content: string) => {
  if (!content) return '';

  return (
    content
      // 标题处理
      .replace(
        /### (.*?)(?=\n|$)/g,
        '<h3 class="text-lg font-semibold mt-4 mb-2 text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-1">$1</h3>'
      )
      .replace(
        /## (.*?)(?=\n|$)/g,
        '<h2 class="text-xl font-bold mt-6 mb-3 text-slate-900 dark:text-white border-b-2 border-blue-500 pb-2">$1</h2>'
      )
      .replace(
        /# (.*?)(?=\n|$)/g,
        '<h1 class="text-2xl font-bold mt-8 mb-4 text-slate-900 dark:text-white">$1</h1>'
      )
      // 粗体和斜体
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-semibold text-slate-900 dark:text-white">$1</strong>'
      )
      .replace(/\*(.*?)\*/g, '<em class="italic text-slate-700 dark:text-slate-300">$1</em>')
      // 代码
      .replace(
        /`(.*?)`/g,
        '<code class="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded text-sm font-mono text-blue-600 dark:text-blue-400">$1</code>'
      )
      // 列表项
      .replace(
        /^- (.*?)(?=\n|$)/gm,
        '<li class="ml-4 mb-1 text-slate-700 dark:text-slate-300">• $1</li>'
      )
      // 段落
      .replace(/\n\n/g, '</p><p class="mb-3 text-slate-700 dark:text-slate-300 leading-relaxed">')
      .replace(/\n/g, '<br>')
  );
};

// 分析师类型配置
const analystConfig = {
  fundamental: {
    name: '基本面分析师',
    icon: ChartBarIcon,
    color: 'blue',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-800',
    textColor: 'text-blue-700 dark:text-blue-300',
  },
  technical: {
    name: '技术分析师',
    icon: ArrowTrendingUpIcon,
    color: 'green',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    textColor: 'text-green-700 dark:text-green-300',
  },
  sentiment: {
    name: '情绪分析师',
    icon: HeartIcon,
    color: 'pink',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20',
    borderColor: 'border-pink-200 dark:border-pink-800',
    textColor: 'text-pink-700 dark:text-pink-300',
  },
  news: {
    name: '新闻分析师',
    icon: NewspaperIcon,
    color: 'purple',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-800',
    textColor: 'text-purple-700 dark:text-purple-300',
  },
};

// 研究员类型配置
const researcherConfig = {
  bull: {
    name: '多头研究员',
    icon: ArrowTrendingUpIcon,
    color: 'green',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    textColor: 'text-green-700 dark:text-green-300',
  },
  bear: {
    name: '空头研究员',
    icon: ArrowTrendingDownIcon,
    color: 'red',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800',
    textColor: 'text-red-700 dark:text-red-300',
  },
};

export function EnhancedReportViewer({
  analystReports,
  researchReports,
}: EnhancedReportViewerProps) {
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // 合并所有报告
  const allReports = useMemo(() => {
    const analysts = analystReports
      .filter((report) => report.status === 'completed' && report.summary)
      .map((report) => ({
        ...report,
        type: 'analyst' as const,
        config: analystConfig[report.analyst_type],
      }));

    const researchers = researchReports
      .filter((report) => report.status === 'completed' && report.summary)
      .map((report) => ({
        ...report,
        type: 'research' as const,
        config: researcherConfig[report.researcher_type],
      }));

    return [...analysts, ...researchers].sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [analystReports, researchReports]);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const extractSummaryAndDetails = (content: string) => {
    const lines = content.split('\n');
    const summary = lines[0] || '';
    const details = lines.slice(1).join('\n').trim();
    return {
      summary: extractSummary(summary, 150),
      details,
    };
  };

  if (allReports.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-slate-500">
            <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
            <p>暂无分析报告</p>
            <p className="text-sm mt-2">分析完成后报告将在此处显示</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 报告概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DocumentTextIcon className="h-5 w-5" />
              <span>分析报告总览</span>
            </div>
            <span className="text-sm font-normal text-slate-500">
              共 {allReports.length} 份报告
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(analystConfig).map(([type, config]) => {
              const count = analystReports.filter(
                (r) => r.analyst_type === type && r.status === 'completed'
              ).length;
              return (
                <div
                  key={type}
                  className={`p-3 rounded-lg ${config.bgColor} ${config.borderColor} border`}
                >
                  <div className="flex items-center space-x-2">
                    <config.icon className={`h-4 w-4 ${config.textColor}`} />
                    <span className={`text-sm font-medium ${config.textColor}`}>{config.name}</span>
                  </div>
                  <div className="mt-1 text-lg font-bold text-slate-900 dark:text-white">
                    {count}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 详细报告列表 */}
      <div className="space-y-4">
        {allReports.map((report, index) => {
          const { summary, details } = extractSummaryAndDetails(report.summary || '');
          const isExpanded = expandedSections.has(report.report_id);
          const Icon = report.config.icon;

          return (
            <motion.div
              key={report.report_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${report.config.borderColor} border-l-4`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${report.config.bgColor}`}>
                        <Icon className={`h-5 w-5 ${report.config.textColor}`} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-slate-900 dark:text-white">
                          {report.config.name}
                        </h3>
                        <div className="flex items-center space-x-2 text-sm text-slate-500">
                          <ClockIcon className="h-3 w-3" />
                          <span>{new Date(report.created_at).toLocaleString()}</span>
                          {(report as any).execution_time_ms && (
                            <>
                              <span>•</span>
                              <span>
                                耗时 {Math.round((report as any).execution_time_ms / 1000)}秒
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {report.type === 'research' &&
                        'confidence_level' in report &&
                        report.confidence_level && (
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-slate-500">置信度</span>
                            <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full"
                                style={{ width: `${report.confidence_level * 100}%` }}
                              />
                            </div>
                            <span className="text-xs text-slate-600 dark:text-slate-400">
                              {Math.round(report.confidence_level * 100)}%
                            </span>
                          </div>
                        )}
                      <button
                        onClick={() => toggleSection(report.report_id)}
                        className="p-1 hover:bg-slate-100 dark:hover:bg-slate-800 rounded"
                      >
                        {isExpanded ? (
                          <ChevronDownIcon className="h-4 w-4" />
                        ) : (
                          <ChevronRightIcon className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* 报告摘要 */}
                  <div className={`p-3 rounded-lg ${report.config.bgColor} mb-4`}>
                    <div className="flex items-start space-x-2">
                      <InformationCircleIcon
                        className={`h-4 w-4 mt-0.5 flex-shrink-0 ${report.config.textColor}`}
                      />
                      <div>
                        <h4 className={`text-sm font-medium ${report.config.textColor} mb-1`}>
                          分析结论
                        </h4>
                        <p className="text-sm text-slate-700 dark:text-slate-300">{summary}</p>
                      </div>
                    </div>
                  </div>

                  {/* 额外信息 */}
                  {report.type === 'research' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      {'target_price' in report && report.target_price && (
                        <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            目标价格
                          </span>
                          <span className="font-medium">${report.target_price.toFixed(2)}</span>
                        </div>
                      )}
                      {'time_horizon' in report && report.time_horizon && (
                        <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            时间范围
                          </span>
                          <span className="font-medium">{report.time_horizon}</span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 详细内容 */}
                  <AnimatePresence>
                    {isExpanded && details && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="border-t border-slate-200 dark:border-slate-700 pt-4"
                      >
                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                          详细分析
                        </h4>
                        <div
                          className="prose prose-sm max-w-none text-slate-700 dark:text-slate-300"
                          dangerouslySetInnerHTML={{
                            __html: renderMarkdown(details),
                          }}
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* 操作按钮 */}
      <Card>
        <CardContent className="flex items-center justify-center space-x-4 py-6">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
            <DocumentTextIcon className="h-4 w-4" />
            <span>导出所有报告</span>
          </button>
          <button className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors flex items-center space-x-2">
            <EyeIcon className="h-4 w-4" />
            <span>生成综合报告</span>
          </button>
        </CardContent>
      </Card>
    </div>
  );
}
