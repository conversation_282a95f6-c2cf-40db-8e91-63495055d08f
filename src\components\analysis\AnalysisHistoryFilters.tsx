'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useEffect, useState } from 'react';

interface FilterOptions {
  ticker?: string;
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
}

interface AnalysisHistoryFiltersProps {
  filters: FilterOptions;
  onFilter: (filters: FilterOptions) => void;
  loading?: boolean;
}

const STATUS_OPTIONS = [
  { value: 'pending', label: '待处理', color: 'gray' },
  { value: 'running', label: '运行中', color: 'blue' },
  { value: 'completed', label: '已完成', color: 'green' },
  { value: 'failed', label: '失败', color: 'red' },
  { value: 'cancelled', label: '已取消', color: 'yellow' },
] as const;

export function AnalysisHistoryFilters({
  filters,
  onFilter,
  loading = false,
}: AnalysisHistoryFiltersProps) {
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);
  const [isExpanded, setIsExpanded] = useState(false);

  // 同步外部filters到本地状态
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // 检查是否有活跃的筛选条件
  const hasActiveFilters = Object.values(filters).some((value) =>
    Array.isArray(value) ? value.length > 0 : Boolean(value)
  );

  // 应用筛选
  const handleApplyFilters = () => {
    onFilter(localFilters);
    setIsExpanded(false);
  };

  // 清除筛选
  const handleClearFilters = () => {
    const emptyFilters = {};
    setLocalFilters(emptyFilters);
    onFilter(emptyFilters);
    setIsExpanded(false);
  };

  // 处理股票代码输入
  const handleTickerChange = (value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      ticker: value.trim() || undefined,
    }));
  };

  // 处理状态选择
  const handleStatusToggle = (status: string) => {
    setLocalFilters((prev) => {
      const currentStatus = prev.status || [];
      const newStatus = currentStatus.includes(status)
        ? currentStatus.filter((s) => s !== status)
        : [...currentStatus, status];

      return {
        ...prev,
        status: newStatus.length > 0 ? newStatus : undefined,
      };
    });
  };

  // 处理日期范围
  const handleDateFromChange = (value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      dateFrom: value || undefined,
    }));
  };

  const handleDateToChange = (value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      dateTo: value || undefined,
    }));
  };

  // 快速日期筛选
  const handleQuickDateFilter = (days: number) => {
    const now = new Date();
    const from = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    setLocalFilters((prev) => ({
      ...prev,
      dateFrom: from.toISOString().split('T')[0],
      dateTo: now.toISOString().split('T')[0],
    }));
  };

  return (
    <Card className="p-4">
      {/* 简化筛选栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 flex-1">
          {/* 股票代码快速输入 */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">股票代码:</label>
            <input
              type="text"
              placeholder="如: AAPL"
              value={localFilters.ticker || ''}
              onChange={(e) => handleTickerChange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              style={{ width: '120px' }}
            />
          </div>

          {/* 状态快速选择 */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">状态:</span>
            <div className="flex space-x-1">
              {STATUS_OPTIONS.slice(0, 3).map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleStatusToggle(option.value)}
                  className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                    localFilters.status?.includes(option.value)
                      ? 'bg-blue-100 border-blue-300 text-blue-700'
                      : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* 活跃筛选指示器 */}
          {hasActiveFilters && (
            <Badge variant="secondary">
              {
                Object.values(filters).filter((value) =>
                  Array.isArray(value) ? value.length > 0 : Boolean(value)
                ).length
              }{' '}
              个筛选条件
            </Badge>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          <Button variant="secondary" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? '收起' : '更多筛选'}
          </Button>

          <Button size="sm" onClick={handleApplyFilters} disabled={loading}>
            应用筛选
          </Button>

          {hasActiveFilters && (
            <Button variant="secondary" size="sm" onClick={handleClearFilters} disabled={loading}>
              清除
            </Button>
          )}
        </div>
      </div>

      {/* 展开的筛选选项 */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 完整状态选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">分析状态</label>
              <div className="space-y-2">
                {STATUS_OPTIONS.map((option) => (
                  <label key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={localFilters.status?.includes(option.value) || false}
                      onChange={() => handleStatusToggle(option.value)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{option.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 日期范围 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">创建日期范围</label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={localFilters.dateFrom || ''}
                  onChange={(e) => handleDateFromChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="开始日期"
                />
                <input
                  type="date"
                  value={localFilters.dateTo || ''}
                  onChange={(e) => handleDateToChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="结束日期"
                />
              </div>
            </div>

            {/* 快速日期选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">快速日期筛选</label>
              <div className="space-y-2">
                {[
                  { days: 1, label: '今天' },
                  { days: 7, label: '最近7天' },
                  { days: 30, label: '最近30天' },
                  { days: 90, label: '最近90天' },
                ].map(({ days, label }) => (
                  <button
                    key={days}
                    onClick={() => handleQuickDateFilter(days)}
                    className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    {label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 展开状态下的操作按钮 */}
          <div className="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-200">
            <Button variant="secondary" onClick={() => setIsExpanded(false)}>
              取消
            </Button>
            <Button onClick={handleApplyFilters} disabled={loading}>
              应用筛选
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
}
