'use client';

import { useEffect, useState } from 'react';
import { performanceMonitor, WebVitalsMetric } from '@/lib/seo/performance-monitor';

interface PerformanceReport {
  metrics: Record<string, WebVitalsMetric>;
  budget: PerformanceBudget;
  overBudget: string[];
  score: number;
}

interface PerformanceBudget {
  LCP: number;
  INP: number;
  CLS: number;
  FCP: number;
  TTFB: number;
}

export function PerformanceMonitor() {
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [alerts, setAlerts] = useState<WebVitalsMetric[]>([]);

  useEffect(() => {
    // 初始化性能监控
    performanceMonitor.initialize();

    // 监听性能警报
    performanceMonitor.onAlert((metric) => {
      setAlerts((prev) => [...prev.slice(-4), metric]); // 保留最近5个警报
    });

    // 定期更新报告
    const interval = setInterval(() => {
      const newReport = performanceMonitor.getPerformanceReport();
      setReport(newReport);
    }, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  if (!report) {
    return (
      <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-3 rounded-lg shadow-lg">
        <div className="text-sm">初始化性能监控中...</div>
      </div>
    );
  }

  return (
    <>
      {/* 性能监控面板 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-sm">
          <div className="text-sm font-semibold mb-2">性能监控 (分数: {report.score})</div>

          <div className="space-y-1 text-xs">
            {Object.entries(report.metrics).map(([name, metric]) => (
              <div key={name} className="flex justify-between">
                <span>{name}:</span>
                <span
                  className={`font-mono ${
                    metric.rating === 'good'
                      ? 'text-green-400'
                      : metric.rating === 'needs-improvement'
                      ? 'text-yellow-400'
                      : 'text-red-400'
                  }`}
                >
                  {name === 'CLS' ? metric.value.toFixed(3) : Math.round(metric.value)}
                  {name !== 'CLS' && 'ms'}
                </span>
              </div>
            ))}
          </div>

          {report.overBudget.length > 0 && (
            <div className="mt-2 text-xs text-red-400">
              超出预算: {report.overBudget.join(', ')}
            </div>
          )}
        </div>
      )}

      {/* 性能警报 */}
      {alerts.map((alert, index) => (
        <PerformanceAlert
          key={`${alert.name}-${alert.id}-${index}`}
          metric={alert}
          onDismiss={() => {
            setAlerts((prev) => prev.filter((_, i) => i !== index));
          }}
        />
      ))}
    </>
  );
}

interface PerformanceAlertProps {
  metric: WebVitalsMetric;
  onDismiss: () => void;
}

function PerformanceAlert({ metric, onDismiss }: PerformanceAlertProps) {
  useEffect(() => {
    const timer = setTimeout(onDismiss, 5000); // 5秒后自动消失
    return () => clearTimeout(timer);
  }, [onDismiss]);

  return (
    <div className="fixed top-4 right-4 bg-red-600 text-white p-3 rounded-lg shadow-lg max-w-sm animate-slide-in">
      <div className="flex justify-between items-start">
        <div>
          <div className="font-semibold text-sm">性能警报</div>
          <div className="text-xs mt-1">
            {metric.name} 指标超出预算:{' '}
            {metric.name === 'CLS' ? metric.value.toFixed(3) : Math.round(metric.value)}
            {metric.name !== 'CLS' && 'ms'}
          </div>
        </div>
        <button onClick={onDismiss} className="text-white hover:text-gray-200 ml-2">
          ×
        </button>
      </div>
    </div>
  );
}
