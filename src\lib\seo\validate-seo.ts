/**
 * SEO 实现验证脚本
 * 验证 SEO 基础架构是否正确实现
 */

import { SEOUtils } from './utils';
import { MetadataGenerator } from './metadata-generator';
import { SEO_CONSTANTS, DEFAULT_SEO_CONFIG } from './config';

/**
 * 验证 SEO 基础架构
 */
export function validateSEOImplementation(): {
  success: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // 验证常量配置
    if (!SEO_CONSTANTS.SITE_NAME) {
      errors.push('SITE_NAME 未定义');
    }

    if (!SEO_CONSTANTS.SITE_URL) {
      errors.push('SITE_URL 未定义');
    }

    if (SEO_CONSTANTS.TITLE_MAX_LENGTH !== 60) {
      warnings.push('TITLE_MAX_LENGTH 不是推荐值 60');
    }

    // 验证默认配置
    if (!DEFAULT_SEO_CONFIG.title) {
      errors.push('默认标题未定义');
    }

    if (!DEFAULT_SEO_CONFIG.description) {
      errors.push('默认描述未定义');
    }

    if (!Array.isArray(DEFAULT_SEO_CONFIG.keywords) || DEFAULT_SEO_CONFIG.keywords.length === 0) {
      errors.push('默认关键词未正确定义');
    }

    // 验证工具函数
    try {
      const titleValid = SEOUtils.validateTitle('测试标题');
      if (!titleValid) {
        errors.push('标题验证函数异常');
      }
    } catch (e) {
      errors.push(`标题验证函数错误: ${e}`);
    }

    try {
      const descValid = SEOUtils.validateDescription('测试描述');
      if (!descValid) {
        errors.push('描述验证函数异常');
      }
    } catch (e) {
      errors.push(`描述验证函数错误: ${e}`);
    }

    try {
      const keywordsValid = SEOUtils.validateKeywords(['关键词1', '关键词2']);
      if (!keywordsValid) {
        errors.push('关键词验证函数异常');
      }
    } catch (e) {
      errors.push(`关键词验证函数错误: ${e}`);
    }

    // 验证元数据生成器
    try {
      const generator = new MetadataGenerator('zh');

      const metadata = generator.generatePageMetadata({
        page: 'home',
        locale: 'zh',
      });

      if (!metadata.title) {
        errors.push('元数据生成器未生成标题');
      }

      if (!metadata.description) {
        errors.push('元数据生成器未生成描述');
      }

      if (!metadata.openGraph) {
        errors.push('元数据生成器未生成 Open Graph 数据');
      }

      if (!metadata.twitter) {
        errors.push('元数据生成器未生成 Twitter Card 数据');
      }
    } catch (e) {
      errors.push(`元数据生成器错误: ${e}`);
    }

    // 验证结构化数据
    try {
      const generator = new MetadataGenerator('zh');
      const structuredData = generator.generateStructuredData('home');

      if (!structuredData.organization) {
        warnings.push('未生成组织信息结构化数据');
      }

      if (!structuredData.website) {
        warnings.push('未生成网站信息结构化数据');
      }
    } catch (e) {
      errors.push(`结构化数据生成错误: ${e}`);
    }

    return {
      success: errors.length === 0,
      errors,
      warnings,
    };
  } catch (e) {
    return {
      success: false,
      errors: [`验证过程发生错误: ${e}`],
      warnings,
    };
  }
}

/**
 * 运行验证并输出结果
 */
export function runSEOValidation(): void {
  console.log('🔍 开始验证 SEO 基础架构...\n');

  const result = validateSEOImplementation();

  if (result.success) {
    console.log('✅ SEO 基础架构验证通过！');
  } else {
    console.log('❌ SEO 基础架构验证失败！');
  }

  if (result.errors.length > 0) {
    console.log('\n🚨 错误:');
    result.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }

  if (result.warnings.length > 0) {
    console.log('\n⚠️  警告:');
    result.warnings.forEach((warning, index) => {
      console.log(`  ${index + 1}. ${warning}`);
    });
  }

  console.log('\n📊 验证统计:');
  console.log(`  - 错误: ${result.errors.length}`);
  console.log(`  - 警告: ${result.warnings.length}`);
  console.log(`  - 状态: ${result.success ? '通过' : '失败'}`);
}

// 如果直接运行此文件，执行验证
if (require.main === module) {
  runSEOValidation();
}
