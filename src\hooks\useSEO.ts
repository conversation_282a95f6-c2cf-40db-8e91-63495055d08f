/**
 * useSEO Hook
 * 提供 SEO 元数据管理的自定义 Hook
 */

'use client';

import { useMemo, useEffect } from 'react';
import { Metadata } from 'next';
import { PageSEOProps, SupportedLocale, SEOConfig } from '@/types/seo';
import { MetadataGenerator } from '@/lib/seo/metadata-generator';
import { SEOErrorHandler } from '@/lib/seo/error-handler';

interface UseSEOOptions {
  enableAnalytics?: boolean;
  enableWebVitals?: boolean;
  enableStructuredData?: boolean;
}

interface UseSEOReturn {
  metadata: Metadata;
  structuredData: Record<string, any>[];
  jsonLdScripts: string[];
  seoScore: number;
  seoAnalysis: {
    score: number;
    issues: string[];
    recommendations: string[];
  };
  updateSEO: (newProps: Partial<PageSEOProps>) => void;
}

/**
 * useSEO Hook
 * 管理页面的 SEO 元数据和结构化数据
 */
export function useSEO(props: PageSEOProps, options: UseSEOOptions = {}): UseSEOReturn {
  const { enableAnalytics = true, enableWebVitals = true, enableStructuredData = true } = options;

  // 创建元数据生成器实例
  const generator = useMemo(() => {
    return new MetadataGenerator(props.locale || 'zh');
  }, [props.locale]);

  // 生成页面元数据
  const metadata = useMemo(() => {
    try {
      return generator.generatePageMetadata(props);
    } catch (error) {
      console.error('Error generating SEO metadata:', error);
      return SEOErrorHandler.handleMetadataError(error as Error);
    }
  }, [generator, props]);

  // 生成结构化数据
  const structuredData = useMemo(() => {
    if (!enableStructuredData) return [];

    try {
      const data = generator.generateStructuredData(props.page, props.dynamicData);
      return Object.values(data).filter(Boolean);
    } catch (error) {
      console.error('Error generating structured data:', error);
      return [];
    }
  }, [generator, props, enableStructuredData]);

  // 生成 JSON-LD 脚本
  const jsonLdScripts = useMemo(() => {
    if (!enableStructuredData) return [];

    try {
      return generator.generateJsonLdScripts(props.page, props.dynamicData);
    } catch (error) {
      console.error('Error generating JSON-LD scripts:', error);
      return [];
    }
  }, [generator, props, enableStructuredData]);

  // 获取 SEO 分析
  const seoAnalysis = useMemo(() => {
    try {
      return generator.getSEOAnalysis(props);
    } catch (error) {
      console.error('Error analyzing SEO:', error);
      return {
        score: 0,
        issues: ['SEO 分析失败'],
        recommendations: ['请检查页面配置'],
      };
    }
  }, [generator, props]);

  // SEO 分数
  const seoScore = seoAnalysis.score;

  // 更新 SEO 配置的函数
  const updateSEO = useMemo(() => {
    return (newProps: Partial<PageSEOProps>) => {
      // 这个函数主要用于动态更新，在实际使用中会触发重新渲染
      console.log('SEO props updated:', newProps);
    };
  }, []);

  // 设置页面标题（客户端）
  useEffect(() => {
    if (typeof window !== 'undefined' && metadata.title) {
      const title =
        typeof metadata.title === 'string'
          ? metadata.title
          : String(metadata.title) || 'TradingAgents';

      document.title = title;
    }
  }, [metadata.title]);

  // 设置页面描述（客户端）
  useEffect(() => {
    if (typeof window !== 'undefined' && metadata.description) {
      const description = typeof metadata.description === 'string' ? metadata.description : '';

      // 更新或创建 meta description 标签
      let metaDescription = document.querySelector('meta[name="description"]');
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.setAttribute('name', 'description');
        document.head.appendChild(metaDescription);
      }
      metaDescription.setAttribute('content', description);
    }
  }, [metadata.description]);

  // 注入结构化数据（客户端）
  useEffect(() => {
    if (typeof window !== 'undefined' && jsonLdScripts.length > 0) {
      // 清除之前的结构化数据脚本
      const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
      existingScripts.forEach((script) => {
        if (script.getAttribute('data-seo-hook') === 'true') {
          script.remove();
        }
      });

      // 添加新的结构化数据脚本
      jsonLdScripts.forEach((scriptContent, index) => {
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.setAttribute('data-seo-hook', 'true');
        script.setAttribute('data-script-index', index.toString());
        script.textContent = scriptContent;
        document.head.appendChild(script);
      });
    }
  }, [jsonLdScripts]);

  // Web Vitals 监控
  useEffect(() => {
    if (enableWebVitals && typeof window !== 'undefined') {
      // Web vitals monitoring would be implemented here
      // For now, we'll just log that monitoring would happen
      console.log('Web vitals monitoring would be initialized here');

      // Mock web vitals data
      const mockWebVitals = {
        getCLS: (callback: (metric: any) => void) => callback({ value: 0.1 }),
        getFID: (callback: (metric: any) => void) => callback({ value: 50 }),
        getFCP: (callback: (metric: any) => void) => callback({ value: 1200 }),
        getLCP: (callback: (metric: any) => void) => callback({ value: 2000 }),
        getTTFB: (callback: (metric: any) => void) => callback({ value: 300 }),
      };

      Promise.resolve(mockWebVitals)
        .then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          const reportWebVital = (metric: any) => {
            console.log(`[Web Vitals] ${metric.name}:`, metric.value);

            // 可以在这里发送到分析服务
            if (enableAnalytics && window.gtag) {
              window.gtag('event', metric.name, {
                event_category: 'Web Vitals',
                value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
                event_label: props.page,
                non_interaction: true,
              });
            }
          };

          getCLS(reportWebVital);
          getFID(reportWebVital);
          getFCP(reportWebVital);
          getLCP(reportWebVital);
          getTTFB(reportWebVital);
        })
        .catch((error) => {
          console.warn('Failed to load web-vitals:', error);
        });
    }
  }, [enableWebVitals, enableAnalytics, props.page]);

  return {
    metadata,
    structuredData,
    jsonLdScripts,
    seoScore,
    seoAnalysis,
    updateSEO,
  };
}

/**
 * 简化版 SEO Hook，仅返回基本元数据
 */
export function useBasicSEO(props: PageSEOProps): Metadata {
  const { metadata } = useSEO(props, {
    enableAnalytics: false,
    enableWebVitals: false,
    enableStructuredData: false,
  });

  return metadata;
}

/**
 * 用于服务端渲染的 SEO Hook
 */
export function useServerSEO(props: PageSEOProps): {
  metadata: Metadata;
  jsonLdScripts: string[];
} {
  const generator = new MetadataGenerator(props.locale || 'zh');

  try {
    const metadata = generator.generatePageMetadata(props);
    const jsonLdScripts = generator.generateJsonLdScripts(props.page, props.dynamicData);

    return { metadata, jsonLdScripts };
  } catch (error) {
    console.error('Error in server SEO:', error);
    return {
      metadata: SEOErrorHandler.handleMetadataError(error as Error),
      jsonLdScripts: [],
    };
  }
}
