/**
 * 数据API React Hook
 * 提供便捷的数据获取和状态管理
 */

import { useState, useEffect, useCallback } from 'react';
import {
  dataService,
  type StockDataParams,
  type NewsDataParams,
  type TechnicalDataParams,
  type FundamentalDataParams,
} from '@/lib/data-service';

// Hook状态类型
interface DataState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// Hook返回类型
interface DataHookReturn<T> extends DataState<T> {
  refetch: () => Promise<void>;
  reset: () => void;
}

/**
 * 股票数据Hook
 */
export function useStockData(
  params: StockDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.ticker) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getStockData(params);
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取股票数据失败',
        lastUpdated: null,
      });
    }
  }, [params.ticker, params.period, params.startDate, params.endDate, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 新闻数据Hook
 */
export function useNewsData(
  params: NewsDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.ticker) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getNewsData(params);
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取新闻数据失败',
        lastUpdated: null,
      });
    }
  }, [params.ticker, params.limit, params.days, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 技术指标Hook
 */
export function useTechnicalData(
  params: TechnicalDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.ticker) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getTechnicalData(params);
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取技术指标失败',
        lastUpdated: null,
      });
    }
  }, [params.ticker, params.indicators?.join(','), params.period, params.days, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 基本面数据Hook
 */
export function useFundamentalData(
  params: FundamentalDataParams,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !params.ticker) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getFundamentalData(params);
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取基本面数据失败',
        lastUpdated: null,
      });
    }
  }, [params.ticker, params.type, params.period, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 综合数据Hook
 */
export function useComprehensiveData(
  ticker: string,
  options?: {
    enabled?: boolean;
    includeStock?: boolean;
    includeNews?: boolean;
    includeTechnical?: boolean;
    includeFundamental?: boolean;
    stockPeriod?: string;
    newsLimit?: number;
    technicalIndicators?: string[];
    fundamentalType?: string;
    refetchInterval?: number;
  }
) {
  const { enabled = true, refetchInterval, ...dataOptions } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !ticker) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getComprehensiveData(ticker, dataOptions);
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '获取综合数据失败',
        lastUpdated: null,
      });
    }
  }, [ticker, enabled, JSON.stringify(dataOptions)]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 批量股票数据Hook
 */
export function useBatchStockData(
  tickers: string[],
  options?: {
    enabled?: boolean;
    period?: string;
    dataType?: 'stock' | 'news' | 'technical' | 'fundamental';
    refetchInterval?: number;
  }
) {
  const { enabled = true, period = '1m', dataType = 'stock', refetchInterval } = options || {};

  const [state, setState] = useState<DataState<any>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const fetchData = useCallback(async () => {
    if (!enabled || !tickers.length) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const result = await dataService.getBatchStockData(tickers, { period, dataType });
      setState({
        data: result,
        loading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error) {
      setState({
        data: null,
        loading: false,
        error: error instanceof Error ? error.message : '批量获取数据失败',
        lastUpdated: null,
      });
    }
  }, [tickers.join(','), period, dataType, enabled]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 自动刷新
  useEffect(() => {
    if (!refetchInterval || !enabled) return;

    const interval = setInterval(fetchData, refetchInterval);
    return () => clearInterval(interval);
  }, [fetchData, refetchInterval, enabled]);

  return {
    ...state,
    refetch: fetchData,
    reset,
  };
}

/**
 * 数据缓存Hook (用于减少重复请求)
 */
export function useDataCache() {
  const [cache, setCache] = useState<Map<string, { data: any; timestamp: number }>>(new Map());

  const getCachedData = useCallback(
    (key: string, maxAge: number = 60000) => {
      const cached = cache.get(key);
      if (cached && Date.now() - cached.timestamp < maxAge) {
        return cached.data;
      }
      return null;
    },
    [cache]
  );

  const setCachedData = useCallback((key: string, data: any) => {
    setCache((prev) => new Map(prev.set(key, { data, timestamp: Date.now() })));
  }, []);

  const clearCache = useCallback((key?: string) => {
    if (key) {
      setCache((prev) => {
        const newCache = new Map(prev);
        newCache.delete(key);
        return newCache;
      });
    } else {
      setCache(new Map());
    }
  }, []);

  return {
    getCachedData,
    setCachedData,
    clearCache,
    cacheSize: cache.size,
  };
}
