'use client';

import Head from 'next/head';
import { Metadata } from 'next';

interface SEOHeadProps {
  metadata: Metadata;
}

/**
 * SEOHead 组件
 *
 * 用于在客户端动态设置页面的 SEO 元数据
 * 支持完整的 Next.js Metadata API
 */
export function SEOHead({ metadata }: SEOHeadProps) {
  if (!metadata) {
    return null;
  }

  // 安全地获取字符串值
  const getStringValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (value && typeof value === 'object' && 'default' in value) return String(value.default);
    return String(value || '');
  };

  // 安全地获取URL值
  const getUrlValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (value && typeof value === 'object' && 'href' in value) return String(value.href);
    return String(value || '');
  };

  return (
    <Head>
      {/* 基本元数据 */}
      <title>{getStringValue(metadata.title) || 'TradingAgents'}</title>
      <meta name="description" content={getStringValue(metadata.description)} />
      <meta name="keywords" content={getStringValue(metadata.keywords)} />

      {/* 规范链接 */}
      {metadata.alternates?.canonical && (
        <link rel="canonical" href={getUrlValue(metadata.alternates.canonical)} />
      )}

      {/* Open Graph 元标签 */}
      {metadata.openGraph && (
        <>
          <meta property="og:title" content={getStringValue(metadata.openGraph.title)} />
          <meta
            property="og:description"
            content={getStringValue(metadata.openGraph.description)}
          />
          <meta property="og:url" content={getUrlValue(metadata.openGraph.url)} />
          <meta property="og:site_name" content={getStringValue(metadata.openGraph.siteName)} />
          <meta property="og:locale" content={getStringValue(metadata.openGraph.locale)} />

          {metadata.openGraph.images &&
            Array.isArray(metadata.openGraph.images) &&
            metadata.openGraph.images.map((image: any, index: number) => (
              <meta key={index} property="og:image" content={getUrlValue(image)} />
            ))}
        </>
      )}

      {/* Twitter Card 元标签 */}
      {metadata.twitter && (
        <>
          <meta name="twitter:title" content={getStringValue(metadata.twitter.title)} />
          <meta name="twitter:description" content={getStringValue(metadata.twitter.description)} />
          <meta name="twitter:site" content={getStringValue(metadata.twitter.site)} />
          <meta name="twitter:creator" content={getStringValue(metadata.twitter.creator)} />

          {metadata.twitter.images &&
            Array.isArray(metadata.twitter.images) &&
            metadata.twitter.images.map((image: any, index: number) => (
              <meta key={index} name="twitter:image" content={getUrlValue(image)} />
            ))}
        </>
      )}

      {/* 验证标签 */}
      {metadata.verification?.google && (
        <meta
          name="google-site-verification"
          content={getStringValue(metadata.verification.google)}
        />
      )}
      {metadata.verification?.other?.baidu && (
        <meta
          name="baidu-site-verification"
          content={getStringValue(metadata.verification.other.baidu)}
        />
      )}
    </Head>
  );
}

/**
 * 服务端渲染版本的 SEO 组件
 * 用于在服务端生成 SEO 元数据
 */
export function SEOHeadSSR({ metadata }: SEOHeadProps) {
  if (!metadata) {
    return null;
  }

  // 安全地获取字符串值
  const getStringValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (value && typeof value === 'object' && 'default' in value) return String(value.default);
    return String(value || '');
  };

  // 安全地获取URL值
  const getUrlValue = (value: any): string => {
    if (typeof value === 'string') return value;
    if (value && typeof value === 'object' && 'href' in value) return String(value.href);
    return String(value || '');
  };

  return (
    <>
      <title>{getStringValue(metadata.title) || 'TradingAgents'}</title>
      <meta name="description" content={getStringValue(metadata.description)} />

      {/* 规范链接 */}
      {metadata.alternates?.canonical && (
        <link rel="canonical" href={getUrlValue(metadata.alternates.canonical)} />
      )}

      {/* Open Graph 元标签 */}
      {metadata.openGraph && (
        <>
          <meta property="og:title" content={getStringValue(metadata.openGraph.title)} />
          <meta
            property="og:description"
            content={getStringValue(metadata.openGraph.description)}
          />
          <meta property="og:url" content={getUrlValue(metadata.openGraph.url)} />

          {metadata.openGraph.images &&
            Array.isArray(metadata.openGraph.images) &&
            metadata.openGraph.images.map((image: any, index: number) => (
              <meta key={index} property="og:image" content={getUrlValue(image)} />
            ))}
        </>
      )}

      {/* Twitter Card 元标签 */}
      {metadata.twitter && (
        <>
          <meta name="twitter:title" content={getStringValue(metadata.twitter.title)} />
          <meta name="twitter:description" content={getStringValue(metadata.twitter.description)} />

          {metadata.twitter.images &&
            Array.isArray(metadata.twitter.images) &&
            metadata.twitter.images.map((image: any, index: number) => (
              <meta key={index} name="twitter:image" content={getUrlValue(image)} />
            ))}
        </>
      )}
    </>
  );
}
