# SEO 增强功能设计文档

## 概述

本设计文档详细描述了 TradingAgents 多智能体金融交易框架的 SEO 增强功能实现方案。基于 Next.js 15.3 的 App Router 架构，我们将实施全面的搜索引擎优化策略，包括元数据管理、性能优化、内容优化和技术 SEO 等方面。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[用户请求] --> B[Next.js App Router]
    B --> C[SEO中间件]
    C --> D[元数据生成器]
    C --> E[结构化数据生成器]
    C --> F[多语言处理器]

    D --> G[静态元数据]
    D --> H[动态元数据]

    E --> I[JSON-LD结构化数据]
    E --> J[Open Graph数据]
    E --> K[Twitter Card数据]

    F --> L[中文SEO内容]
    F --> M[英文SEO内容]

    G --> N[HTML Head标签]
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N

    N --> O[搜索引擎爬虫]
    N --> P[社交媒体分享]
    N --> Q[用户浏览器]
```

### 技术栈选择

- **Next.js 15.3**: 利用 App Router 的元数据 API 和 SSR 能力
- **TypeScript**: 确保 SEO 配置的类型安全
- **Zod**: 验证 SEO 数据结构
- **next-sitemap**: 生成 XML sitemap
- **@next/bundle-analyzer**: 分析和优化包大小
- **sharp**: 图片优化和 WebP 转换

## 组件设计

### 1. SEO 元数据管理系统

#### 1.1 元数据配置接口

```typescript
// src/types/seo.ts
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  canonical?: string;
  openGraph: {
    title: string;
    description: string;
    image: string;
    type: 'website' | 'article';
    locale: 'zh_CN' | 'en_US';
  };
  twitter: {
    card: 'summary_large_image';
    title: string;
    description: string;
    image: string;
  };
  structuredData?: Record<string, any>;
}

export interface PageSEOProps {
  page: 'home' | 'analysis' | 'tasks' | 'messages' | 'create-task';
  dynamicData?: {
    analysisId?: string;
    stockSymbol?: string;
    taskTitle?: string;
  };
  locale?: 'zh' | 'en';
}
```

#### 1.2 SEO 元数据生成器

```typescript
// src/lib/seo/metadata-generator.ts
export class MetadataGenerator {
  private baseConfig: SEOConfig;
  private locale: 'zh' | 'en';

  constructor(locale: 'zh' | 'en' = 'zh') {
    this.locale = locale;
    this.baseConfig = this.getBaseConfig();
  }

  generatePageMetadata(props: PageSEOProps): Metadata {
    // 根据页面类型和动态数据生成元数据
  }

  generateStructuredData(type: string, data: any): Record<string, any> {
    // 生成JSON-LD结构化数据
  }

  private getBaseConfig(): SEOConfig {
    // 返回基础SEO配置
  }
}
```

#### 1.3 动态元数据 Hook

```typescript
// src/hooks/useSEO.ts
export function useSEO(props: PageSEOProps) {
  const generator = new MetadataGenerator(props.locale);

  return useMemo(() => {
    return generator.generatePageMetadata(props);
  }, [props]);
}
```

### 2. 多语言 SEO 支持

#### 2.1 语言检测中间件

```typescript
// src/middleware/seo-middleware.ts
export function seoMiddleware(request: NextRequest) {
  const locale = detectLocale(request);
  const pathname = request.nextUrl.pathname;

  // 处理hreflang和语言重定向
  if (shouldRedirectForSEO(pathname, locale)) {
    return NextResponse.redirect(new URL(`/${locale}${pathname}`, request.url));
  }

  // 添加SEO相关的响应头
  const response = NextResponse.next();
  response.headers.set('Content-Language', locale);
  response.headers.set('X-Robots-Tag', 'index, follow');

  return response;
}
```

#### 2.2 多语言内容管理

```typescript
// src/lib/seo/i18n-seo.ts
export const seoContent = {
  zh: {
    home: {
      title: 'TradingAgents - 多智能体大语言模型金融交易框架',
      description:
        '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。',
      keywords: ['多智能体', '金融交易', 'AI交易', '量化投资', '大语言模型', '智能分析'],
    },
    analysis: {
      title: '智能分析 - TradingAgents',
      description: '实时多智能体协作分析，包含基本面分析、技术分析、情绪分析和风险评估。',
      keywords: ['股票分析', '技术分析', '基本面分析', '风险评估', '交易决策'],
    },
  },
  en: {
    home: {
      title: 'TradingAgents - Multi-Agent LLM Financial Trading Framework',
      description:
        'Professional financial trading analysis framework based on multi-agent large language models, providing intelligent market analysis, risk assessment and trading decision support.',
      keywords: [
        'multi-agent',
        'financial trading',
        'AI trading',
        'quantitative investment',
        'LLM',
        'intelligent analysis',
      ],
    },
    analysis: {
      title: 'Intelligent Analysis - TradingAgents',
      description:
        'Real-time multi-agent collaborative analysis including fundamental analysis, technical analysis, sentiment analysis and risk assessment.',
      keywords: [
        'stock analysis',
        'technical analysis',
        'fundamental analysis',
        'risk assessment',
        'trading decisions',
      ],
    },
  },
};
```

### 3. 技术 SEO 实现

#### 3.1 Sitemap 生成器

```typescript
// src/lib/seo/sitemap-generator.ts
export class SitemapGenerator {
  async generateSitemap(): Promise<string> {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagents.com';
    const staticPages = [
      { url: '/', priority: 1.0, changefreq: 'daily' },
      { url: '/analysis', priority: 0.8, changefreq: 'hourly' },
      { url: '/tasks', priority: 0.7, changefreq: 'daily' },
      { url: '/create-task', priority: 0.6, changefreq: 'weekly' },
      { url: '/messages', priority: 0.5, changefreq: 'daily' },
    ];

    // 获取动态页面
    const dynamicPages = await this.getDynamicPages();

    return this.buildXMLSitemap([...staticPages, ...dynamicPages]);
  }

  private async getDynamicPages() {
    // 从数据库获取分析页面等动态内容
  }
}
```

#### 3.2 Robots.txt 生成

```typescript
// src/app/robots.ts
import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tradingagents.com';

  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/admin/', '/_next/'],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: ['/api/', '/admin/'],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}
```

### 4. 结构化数据实现

#### 4.1 组织信息结构化数据

```typescript
// src/lib/seo/structured-data.ts
export function generateOrganizationSchema(): Record<string, any> {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'TradingAgents',
    description: '多智能体大语言模型金融交易框架',
    url: 'https://tradingagents.com',
    logo: 'https://tradingagents.com/tradingAgent.png',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['Chinese', 'English'],
    },
    sameAs: ['https://github.com/tradingagents', 'https://twitter.com/tradingagents'],
  };
}
```

#### 4.2 产品信息结构化数据

```typescript
export function generateSoftwareApplicationSchema(): Record<string, any> {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: 'TradingAgents',
    applicationCategory: 'FinanceApplication',
    operatingSystem: 'Web Browser',
    description: '基于多智能体大语言模型的金融交易分析框架',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '150',
    },
  };
}
```

### 5. 性能优化设计

#### 5.1 图片优化组件

```typescript
// src/components/seo/OptimizedImage.tsx
interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className,
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      className={className}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      quality={85}
      format="webp"
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
    />
  );
}
```

#### 5.2 预加载策略

```typescript
// src/lib/seo/preload-strategy.ts
export function setupPreloadStrategy() {
  // 预加载关键资源
  const criticalResources = ['/tradingAgent.png', '/fonts/inter-var.woff2'];

  criticalResources.forEach((resource) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    link.as = resource.includes('.woff') ? 'font' : 'image';
    if (resource.includes('.woff')) {
      link.crossOrigin = 'anonymous';
    }
    document.head.appendChild(link);
  });
}
```

### 6. 分析和监控

#### 6.1 SEO 监控组件

```typescript
// src/components/seo/SEOMonitor.tsx
export function SEOMonitor() {
  useEffect(() => {
    // 监控Core Web Vitals
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(console.log);
      getFID(console.log);
      getFCP(console.log);
      getLCP(console.log);
      getTTFB(console.log);
    });
  }, []);

  return null; // 这是一个监控组件，不渲染任何内容
}
```

#### 6.2 Google Analytics 集成

```typescript
// src/lib/analytics/google-analytics.ts
export function initGoogleAnalytics() {
  const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  if (!GA_MEASUREMENT_ID) return;

  // 加载Google Analytics
  const script = document.createElement('script');
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  script.async = true;
  document.head.appendChild(script);

  window.dataLayer = window.dataLayer || [];
  function gtag(...args: any[]) {
    window.dataLayer.push(args);
  }

  gtag('js', new Date());
  gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });
}
```

## 数据模型

### SEO 配置数据模型

```typescript
// src/types/seo-models.ts
export interface SEOPageConfig {
  id: string;
  path: string;
  title: Record<string, string>; // 多语言标题
  description: Record<string, string>; // 多语言描述
  keywords: Record<string, string[]>; // 多语言关键词
  structuredData?: Record<string, any>;
  lastModified: Date;
  priority: number;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
}

export interface SEOAnalytics {
  pageUrl: string;
  impressions: number;
  clicks: number;
  ctr: number;
  position: number;
  date: Date;
}
```

## 错误处理

### SEO 错误处理策略

```typescript
// src/lib/seo/error-handling.ts
export class SEOErrorHandler {
  static handleMetadataError(error: Error, fallback: Metadata): Metadata {
    console.error('SEO Metadata Error:', error);

    // 返回安全的后备元数据
    return {
      ...fallback,
      title: 'TradingAgents - 金融交易分析框架',
      description: '专业的多智能体金融交易分析平台',
    };
  }

  static handleStructuredDataError(error: Error): Record<string, any> | null {
    console.error('Structured Data Error:', error);
    return null; // 结构化数据错误时返回null，不影响页面渲染
  }
}
```

## 测试策略

### SEO 测试框架

```typescript
// src/lib/seo/testing.ts
export class SEOTester {
  async testPageSEO(url: string): Promise<SEOTestResult> {
    const results = {
      title: await this.testTitle(url),
      description: await this.testDescription(url),
      structuredData: await this.testStructuredData(url),
      performance: await this.testPerformance(url),
      accessibility: await this.testAccessibility(url),
    };

    return results;
  }

  private async testTitle(url: string): Promise<boolean> {
    // 测试标题是否符合SEO最佳实践
  }

  private async testDescription(url: string): Promise<boolean> {
    // 测试描述是否符合SEO最佳实践
  }
}
```

## 部署和配置

### 环境变量配置

```bash
# SEO相关环境变量
NEXT_PUBLIC_BASE_URL=https://tradingagents.com
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GSC_VERIFICATION=your-google-search-console-verification-code
NEXT_PUBLIC_BAIDU_VERIFICATION=your-baidu-verification-code
```

### Next.js 配置优化

```javascript
// next.config.js SEO优化配置
const nextConfig = {
  // 启用图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 启用压缩
  compress: true,

  // 生成sitemap
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap',
      },
    ];
  },

  // 优化构建
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@heroicons/react'],
  },
};
```

这个设计文档提供了完整的 SEO 增强功能实现方案，涵盖了元数据管理、多语言支持、技术 SEO、性能优化和监控分析等各个方面，确保 TradingAgents 网站能够获得最佳的搜索引擎优化效果。
