# 生产环境 - 前端服务 Docker Compose 配置文件
# 仅包含前端服务，用于独立部署前端
# 注意：此配置会连接到已存在的 tradingagents-network 网络以与后端通信
version: '3.8'

services:
  # TradingAgents Frontend - 使用阿里云镜像
  frontend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
    container_name: tradingagents-frontend
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      # 数据库连接配置 - 连接到外部MySQL
      - DB_HOST=*************
      - DB_PORT=13306
      - DB_USER=root
      - DB_PASSWORD=trading123
      - DB_NAME=trading_analysis
      # API配置 - 浏览器端使用外部地址，服务端使用容器名
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_API_BACKEND_BASE_URL=${NEXT_PUBLIC_API_BACKEND_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      # 服务端API调用使用容器名
      - BACK_END_URL=${BACK_END_URL:-http://tradingagents-akshare-backend:5000}
      - AKSHARE_API_URL=${AKSHARE_API_URL:-http://tradingagents-akshare-backend:5000}
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network
