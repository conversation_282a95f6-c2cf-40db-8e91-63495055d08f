/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-26 23:20:40
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 23:26:43
 * @FilePath: \trading-agents-frontend\middleware.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { jwtVerify } from 'jose';
import { NextRequest, NextResponse } from 'next/server';
import { seoMiddleware, shouldApplySEO, getPageLocale, cleanPathname } from '@/lib/seo/middleware';
import { applyPerformanceHeaders } from '@/lib/seo/performance-optimization';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';

// 定义需要保护的路由
const protectedRoutes = ['/create-task', '/tasks', '/analysis', '/messages'];

async function verifyToken(token: string): Promise<boolean> {
  if (!JWT_SECRET) {
    return false;
  }
  try {
    await jwtVerify(token, new TextEncoder().encode(JWT_SECRET));
    return true;
  } catch (error) {
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 1. 首先应用 SEO 中间件（处理语言检测、重定向等）
  if (shouldApplySEO(pathname)) {
    const seoResponse = seoMiddleware(request);

    // 如果 SEO 中间件返回重定向或重写，直接返回
    if (seoResponse.status !== 200) {
      return seoResponse;
    }

    // 如果 SEO 中间件处理了请求（如 sitemap），直接返回
    if (seoResponse.headers.get('x-middleware-rewrite')) {
      return seoResponse;
    }
  }

  // 2. 处理多语言路径的认证逻辑
  const cleanPath = cleanPathname(pathname);
  const locale = getPageLocale(pathname);

  const accessToken = request.cookies.get('access-token')?.value;
  const isAuthenticated = accessToken ? await verifyToken(accessToken) : false;

  // 检查清理后的路径是否为受保护路由
  const isProtectedRoute = protectedRoutes.some((route) => cleanPath.startsWith(route));
  // const isAuthRoute = cleanPath.startsWith('/login') || cleanPath.startsWith('/register');

  console.log(
    'isProtectedRoute:',
    isProtectedRoute,
    'cleanPath:',
    cleanPath,
    'isAuthenticated:',
    isAuthenticated
  );

  if (isProtectedRoute && !isAuthenticated) {
    // 如果是受保护的路由但未认证，重定向到对应语言的登录页
    const url = request.nextUrl.clone();
    url.pathname = locale === 'en' ? '/en/login' : '/login';
    url.searchParams.set('redirect', pathname);
    console.log('Redirecting to login:', url.pathname);
    return NextResponse.redirect(url);
  }

  // 处理已认证用户访问登录页的情况
  // if (isAuthRoute && isAuthenticated) {
  //   const url = request.nextUrl.clone();
  //   url.pathname = locale === 'en' ? '/en/tasks' : '/tasks';
  //   return NextResponse.redirect(url);
  // }

  console.log('Proceeding with request');

  // 3. 如果需要应用 SEO，返回带有 SEO 头的响应
  if (shouldApplySEO(pathname)) {
    const seoResponse = seoMiddleware(request);
    // 应用性能优化头
    return applyPerformanceHeaders(seoResponse, request);
  }

  // 4. 对于其他请求，也应用基本的性能优化头
  const response = NextResponse.next();
  return applyPerformanceHeaders(response, request);
}

// 辅助函数：检查管理员权限
// function checkAdminPermission(token: string | undefined): boolean {
//   if (!token) return false;

//   try {
//     // 这里实现你的权限检查逻辑
//     // 例如：解析 JWT token 或查询数据库
//     return true; // 临时返回 true，根据实际需求修改
//   } catch {
//     return false;
//   }
// }

// 配置中间件匹配的路径
export const config = {
  matcher: [
    // 应用于所有受保护的路由（包括多语言版本）
    '/tasks/:path*',
    '/create-task/:path*',
    '/analysis/:path*',
    '/messages/:path*',
    '/en/tasks/:path*',
    '/en/create-task/:path*',
    '/en/analysis/:path*',
    '/en/messages/:path*',

    // 认证页面（包括多语言版本）
    '/login',
    '/register',
    '/en/login',
    '/en/register',

    // SEO 相关路径
    '/',
    '/en',
    '/en/:path*',
    '/sitemap.xml',
    '/robots.txt',

    // 排除静态资源和 API 路由，但包含所有其他路径用于 SEO 处理
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.[a-zA-Z0-9]+$).*)',
  ],
};
