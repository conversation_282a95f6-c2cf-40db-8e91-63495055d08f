/**
 * 修复数据库字符集冲突问题
 */

const mysql = require('mysql2/promise');

async function fixDatabaseCollation() {
  console.log('🚀 开始修复数据库字符集冲突...\n');

  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 13306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'trading123',
      database: process.env.DB_NAME || 'trading_analysis',
    });

    console.log('✅ 数据库连接成功');

    // 检查当前数据库的字符集
    console.log('\n1. 检查数据库字符集...');
    const [dbInfo] = await connection.execute(`
      SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
      FROM information_schema.SCHEMATA 
      WHERE SCHEMA_NAME = 'trading_analysis'
    `);
    console.log('   数据库字符集:', dbInfo[0]);

    // 检查所有表的字符集
    console.log('\n2. 检查表字符集...');
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_COLLATION 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'trading_analysis' 
      AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    console.log('   表字符集信息:');
    tables.forEach(table => {
      console.log(`   - ${table.TABLE_NAME}: ${table.TABLE_COLLATION}`);
    });

    // 修复数据库字符集
    console.log('\n3. 修复数据库字符集...');
    await connection.query(`
      ALTER DATABASE trading_analysis 
      CHARACTER SET = utf8mb4 
      COLLATE = utf8mb4_unicode_ci
    `);
    console.log('✅ 数据库字符集修复完成');

    // 修复所有表的字符集
    console.log('\n4. 修复表字符集...');
    for (const table of tables) {
      if (table.TABLE_COLLATION !== 'utf8mb4_unicode_ci') {
        console.log(`   修复表: ${table.TABLE_NAME}`);
        await connection.execute(`
          ALTER TABLE ${table.TABLE_NAME} 
          CONVERT TO CHARACTER SET utf8mb4 
          COLLATE utf8mb4_unicode_ci
        `);
      }
    }
    console.log('✅ 所有表字符集修复完成');

    // 验证修复结果
    console.log('\n5. 验证修复结果...');
    const [updatedTables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_COLLATION 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = 'trading_analysis' 
      AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    const inconsistentTables = updatedTables.filter(
      table => table.TABLE_COLLATION !== 'utf8mb4_unicode_ci'
    );
    
    if (inconsistentTables.length === 0) {
      console.log('✅ 所有表字符集一致');
    } else {
      console.log('❌ 仍有表字符集不一致:');
      inconsistentTables.forEach(table => {
        console.log(`   - ${table.TABLE_NAME}: ${table.TABLE_COLLATION}`);
      });
    }

    await connection.end();
    console.log('\n🎉 字符集修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error('   错误详情:', error);
  }
}

// 运行修复
if (require.main === module) {
  fixDatabaseCollation();
}

module.exports = { fixDatabaseCollation };