/**
 * 动态 Sitemap 生成系统
 * 实现完整的 XML sitemap 生成，包括静态和动态页面发现
 */

import { getRuntimeSEOConfig } from './runtime-config';

export interface SitemapPage {
  url: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  alternates?: Array<{ hreflang: string; href: string }>;
}

export interface SitemapConfig {
  baseUrl: string;
  defaultChangefreq: SitemapPage['changefreq'];
  defaultPriority: number;
  includeAlternates: boolean;
  supportedLocales: string[];
}

/**
 * Sitemap 生成器类
 */
export class SitemapGenerator {
  private config: SitemapConfig;
  private currentDate: string;

  constructor() {
    const runtimeConfig = getRuntimeSEOConfig();
    this.config = {
      baseUrl: runtimeConfig.SITE_URL,
      defaultChangefreq: 'weekly',
      defaultPriority: 0.5,
      includeAlternates: true,
      supportedLocales: ['zh', 'en'],
    };
    this.currentDate = new Date().toISOString();
  }

  /**
   * 生成完整的 sitemap
   */
  async generateSitemap(): Promise<string> {
    const staticPages = this.getStaticPages();
    const dynamicPages = await this.getDynamicPages();
    const allPages = [...staticPages, ...dynamicPages];

    return this.buildXMLSitemap(allPages);
  }

  /**
   * 获取静态页面配置
   */
  private getStaticPages(): SitemapPage[] {
    const staticPageConfigs = [
      {
        path: '/',
        priority: 1.0,
        changefreq: 'daily' as const,
        description: '首页 - TradingAgents 多智能体金融交易框架',
      },
      {
        path: '/analysis',
        priority: 0.8,
        changefreq: 'hourly' as const,
        description: '智能分析页面',
      },
      {
        path: '/tasks',
        priority: 0.7,
        changefreq: 'daily' as const,
        description: '任务管理页面',
      },
      {
        path: '/create-task',
        priority: 0.6,
        changefreq: 'weekly' as const,
        description: '创建任务页面',
      },
      {
        path: '/messages',
        priority: 0.5,
        changefreq: 'daily' as const,
        description: '消息查询页面',
      },
      {
        path: '/login',
        priority: 0.3,
        changefreq: 'monthly' as const,
        description: '登录页面',
      },
      {
        path: '/register',
        priority: 0.3,
        changefreq: 'monthly' as const,
        description: '注册页面',
      },
    ];

    const pages: SitemapPage[] = [];

    staticPageConfigs.forEach((pageConfig) => {
      if (this.config.includeAlternates) {
        // 为每个支持的语言生成页面
        this.config.supportedLocales.forEach((locale) => {
          const isDefaultLocale = locale === 'zh';
          const localizedPath = isDefaultLocale ? pageConfig.path : `/en${pageConfig.path}`;

          pages.push({
            url: `${this.config.baseUrl}${localizedPath}`,
            lastmod: this.currentDate,
            changefreq: pageConfig.changefreq,
            priority: pageConfig.priority,
            alternates: this.generateAlternates(pageConfig.path),
          });
        });
      } else {
        pages.push({
          url: `${this.config.baseUrl}${pageConfig.path}`,
          lastmod: this.currentDate,
          changefreq: pageConfig.changefreq,
          priority: pageConfig.priority,
        });
      }
    });

    return pages;
  }

  /**
   * 获取动态页面（从数据库或其他数据源）
   */
  private async getDynamicPages(): Promise<SitemapPage[]> {
    const dynamicPages: SitemapPage[] = [];

    try {
      // 获取分析页面
      const analysisPages = await this.getAnalysisPages();
      dynamicPages.push(...analysisPages);

      // 获取任务详情页面
      const taskPages = await this.getTaskPages();
      dynamicPages.push(...taskPages);

      // 可以添加更多动态页面类型
      // const newsPages = await this.getNewsPages();
      // dynamicPages.push(...newsPages);
    } catch (error) {
      console.error('Error fetching dynamic pages for sitemap:', error);
      // 即使动态页面获取失败，也要返回静态页面
    }

    return dynamicPages;
  }

  /**
   * 获取分析页面
   */
  private async getAnalysisPages(): Promise<SitemapPage[]> {
    // 这里应该从数据库获取分析页面
    // 由于没有具体的数据库连接，我们返回示例数据
    const analysisIds = await this.fetchAnalysisIds();

    return analysisIds.map((id) => ({
      url: `${this.config.baseUrl}/analysis/${id}`,
      lastmod: this.currentDate,
      changefreq: 'daily' as const,
      priority: 0.7,
      alternates: this.config.includeAlternates
        ? this.generateAlternates(`/analysis/${id}`)
        : undefined,
    }));
  }

  /**
   * 获取任务页面
   */
  private async getTaskPages(): Promise<SitemapPage[]> {
    // 这里应该从数据库获取任务页面
    const taskIds = await this.fetchTaskIds();

    return taskIds.map((id) => ({
      url: `${this.config.baseUrl}/tasks/${id}`,
      lastmod: this.currentDate,
      changefreq: 'weekly' as const,
      priority: 0.6,
      alternates: this.config.includeAlternates
        ? this.generateAlternates(`/tasks/${id}`)
        : undefined,
    }));
  }

  /**
   * 从数据库获取分析 ID（模拟实现）
   */
  private async fetchAnalysisIds(): Promise<string[]> {
    // TODO: 实际实现应该从数据库获取
    // 这里返回示例数据
    return [];
  }

  /**
   * 从数据库获取任务 ID（模拟实现）
   */
  private async fetchTaskIds(): Promise<string[]> {
    // TODO: 实际实现应该从数据库获取
    // 这里返回示例数据
    return [];
  }

  /**
   * 生成多语言替代链接
   */
  private generateAlternates(path: string): Array<{ hreflang: string; href: string }> {
    const alternates = [];

    // 中文版本
    alternates.push({
      hreflang: 'zh',
      href: `${this.config.baseUrl}${path}`,
    });

    // 英文版本
    alternates.push({
      hreflang: 'en',
      href: `${this.config.baseUrl}/en${path}`,
    });

    // 默认版本
    alternates.push({
      hreflang: 'x-default',
      href: `${this.config.baseUrl}${path}`,
    });

    return alternates;
  }

  /**
   * 构建 XML sitemap
   */
  private buildXMLSitemap(pages: SitemapPage[]): string {
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${pages.map((page) => this.buildURLEntry(page)).join('\n')}
</urlset>`;

    return xml;
  }

  /**
   * 构建单个 URL 条目
   */
  private buildURLEntry(page: SitemapPage): string {
    const alternatesXML = page.alternates
      ? page.alternates
          .map(
            (alt) =>
              `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
          )
          .join('\n')
      : '';

    return `  <url>
    <loc>${this.escapeXML(page.url)}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority.toFixed(1)}</priority>
${alternatesXML}
  </url>`;
  }

  /**
   * XML 转义
   */
  private escapeXML(str: string): string {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * 验证 sitemap 大小限制
   */
  private validateSitemapSize(xml: string): boolean {
    // Sitemap 文件大小不应超过 50MB
    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    const xmlSize = Buffer.byteLength(xml, 'utf8');

    if (xmlSize > maxSize) {
      console.warn(`Sitemap size (${xmlSize} bytes) exceeds 50MB limit`);
      return false;
    }

    return true;
  }

  /**
   * 生成 sitemap 索引（用于大型网站）
   */
  async generateSitemapIndex(): Promise<string> {
    const sitemaps = [
      {
        loc: `${this.config.baseUrl}/sitemap.xml`,
        lastmod: this.currentDate,
      },
      // 可以添加更多 sitemap 文件
      // {
      //   loc: `${this.config.baseUrl}/sitemap-news.xml`,
      //   lastmod: this.currentDate,
      // },
    ];

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps
  .map(
    (sitemap) => `  <sitemap>
    <loc>${this.escapeXML(sitemap.loc)}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`
  )
  .join('\n')}
</sitemapindex>`;

    return xml;
  }
}

/**
 * 创建 sitemap 生成器实例
 */
export function createSitemapGenerator(): SitemapGenerator {
  return new SitemapGenerator();
}

/**
 * 快速生成 sitemap
 */
export async function generateSitemap(): Promise<string> {
  const generator = createSitemapGenerator();
  return await generator.generateSitemap();
}
