import { renderHook } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { useNavigationHighlight } from '../useNavigationHighlight';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

// Mock usePageTitle hook
jest.mock('../usePageTitle', () => ({
  usePageTitle: jest.fn(() => ({
    title: 'Test Page - TradingAgents',
    description: 'Test description',
  })),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('useNavigationHighlight', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('isAnalysisPage detection', () => {
    it('should detect analysis pages correctly', () => {
      mockUsePathname.mockReturnValue('/analysis/history');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isAnalysisPage).toBe(true);
    });

    it('should detect non-analysis pages correctly', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isAnalysisPage).toBe(false);
    });
  });

  describe('analysisPageType detection', () => {
    it('should detect history page type', () => {
      mockUsePathname.mockReturnValue('/analysis/history');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.analysisPageType).toBe('history');
    });

    it('should detect compare page type', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.analysisPageType).toBe('compare');
    });

    it('should detect detail page type', () => {
      mockUsePathname.mockReturnValue('/analysis/abc123');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.analysisPageType).toBe('detail');
    });

    it('should return null for non-analysis pages', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.analysisPageType).toBe(null);
    });
  });

  describe('isCurrentPage function', () => {
    it('should return true for exact path match', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isCurrentPage('/tasks')).toBe(true);
    });

    it('should return false for non-matching paths', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isCurrentPage('/profile')).toBe(false);
    });

    it('should return true for analysis pages when on any analysis page', () => {
      mockUsePathname.mockReturnValue('/analysis/history');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isCurrentPage('/analysis')).toBe(true);
      expect(result.current.isCurrentPage('/analysis/history')).toBe(true);
    });

    it('should not highlight hash links as active', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.isCurrentPage('/#features')).toBe(false);
    });
  });

  describe('getNavigationItemClasses function', () => {
    it('should return active classes for current page', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      const classes = result.current.getNavigationItemClasses('/tasks');
      expect(classes).toContain('text-blue-600');
      expect(classes).toContain('font-medium');
    });

    it('should return inactive classes for non-current page', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      const classes = result.current.getNavigationItemClasses('/profile');
      expect(classes).toContain('text-slate-600');
      expect(classes).toContain('hover:text-blue-600');
    });

    it('should merge base classes with navigation classes', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      const classes = result.current.getNavigationItemClasses('/tasks', 'flex items-center');
      expect(classes).toContain('flex items-center');
      expect(classes).toContain('text-blue-600');
    });
  });

  describe('generateBreadcrumbs function', () => {
    it('should generate breadcrumbs for home page', () => {
      mockUsePathname.mockReturnValue('/');
      const { result } = renderHook(() => useNavigationHighlight());
      const breadcrumbs = result.current.generateBreadcrumbs();
      expect(breadcrumbs).toHaveLength(1);
      expect(breadcrumbs[0]).toEqual({
        label: '首页',
        href: '/',
        current: false,
      });
    });

    it('should generate breadcrumbs for tasks page', () => {
      mockUsePathname.mockReturnValue('/tasks');
      const { result } = renderHook(() => useNavigationHighlight());
      const breadcrumbs = result.current.generateBreadcrumbs();
      expect(breadcrumbs).toHaveLength(2);
      expect(breadcrumbs[0]).toEqual({
        label: '首页',
        href: '/',
        current: false,
      });
      expect(breadcrumbs[1]).toEqual({
        label: '任务列表',
        href: undefined,
        current: true,
      });
    });

    it('should generate breadcrumbs for analysis history page', () => {
      mockUsePathname.mockReturnValue('/analysis/history');
      const { result } = renderHook(() => useNavigationHighlight());
      const breadcrumbs = result.current.generateBreadcrumbs();
      expect(breadcrumbs).toHaveLength(3);
      expect(breadcrumbs[0]).toEqual({
        label: '首页',
        href: '/',
        current: false,
      });
      expect(breadcrumbs[1]).toEqual({
        label: '分析中心',
        href: '/analysis',
        current: false,
      });
      expect(breadcrumbs[2]).toEqual({
        label: '分析历史',
        href: undefined,
        current: true,
      });
    });

    it('should generate breadcrumbs for analysis detail page', () => {
      mockUsePathname.mockReturnValue('/analysis/abc123');
      const { result } = renderHook(() => useNavigationHighlight());
      const breadcrumbs = result.current.generateBreadcrumbs();
      expect(breadcrumbs).toHaveLength(3);
      expect(breadcrumbs[0]).toEqual({
        label: '首页',
        href: '/',
        current: false,
      });
      expect(breadcrumbs[1]).toEqual({
        label: '分析中心',
        href: '/analysis',
        current: false,
      });
      expect(breadcrumbs[2]).toEqual({
        label: '分析详情',
        href: undefined,
        current: true,
      });
    });
  });

  describe('currentPath property', () => {
    it('should return current pathname', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');
      const { result } = renderHook(() => useNavigationHighlight());
      expect(result.current.currentPath).toBe('/analysis/compare');
    });
  });
});
