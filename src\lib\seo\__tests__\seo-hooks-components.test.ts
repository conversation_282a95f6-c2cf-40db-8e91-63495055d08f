/**
 * SEO Hooks 和组件测试
 * 验证 SEO 相关功能
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { useServerSEO } from '@/hooks/useSEO';
import { PageSEOProps } from '@/types/seo';
import { MetadataGenerator } from '@/lib/seo/metadata-generator';

describe('SEO 元数据生成器测试', () => {
  const mockProps: PageSEOProps = {
    page: 'home',
    locale: 'zh',
  };

  let generator: MetadataGenerator;

  beforeEach(() => {
    generator = new MetadataGenerator('zh');
  });

  describe('MetadataGenerator', () => {
    it('应该生成有效的元数据', () => {
      const metadata = generator.generatePageMetadata(mockProps);

      expect(metadata.title).toBeTruthy();
      expect(metadata.description).toBeTruthy();
      expect(metadata.openGraph).toBeTruthy();
      expect(metadata.twitter).toBeTruthy();
    });

    it('应该生成结构化数据', () => {
      const structuredData = generator.generateStructuredData('home');

      expect(structuredData.organization).toBeTruthy();
      expect(structuredData.website).toBeTruthy();
      expect(structuredData.softwareApplication).toBeTruthy();
    });

    it('应该生成 JSON-LD 脚本', () => {
      const scripts = generator.generateJsonLdScripts('home');

      expect(Array.isArray(scripts)).toBe(true);
      expect(scripts.length).toBeGreaterThan(0);

      // 验证每个脚本都是有效的 JSON
      scripts.forEach((script) => {
        expect(() => JSON.parse(script)).not.toThrow();
      });
    });

    it('应该处理动态数据', () => {
      const propsWithDynamicData: PageSEOProps = {
        page: 'analysis',
        dynamicData: {
          stockSymbol: 'AAPL',
          analysisId: '123',
        },
        locale: 'zh',
      };

      const metadata = generator.generatePageMetadata(propsWithDynamicData);
      expect(metadata.title).toContain('AAPL');
    });

    it('应该支持不同的语言', () => {
      const enGenerator = new MetadataGenerator('en');
      const enProps: PageSEOProps = {
        page: 'home',
        locale: 'en',
      };

      const metadata = enGenerator.generatePageMetadata(enProps);

      expect(metadata.title).toContain('TradingAgents');
      expect(metadata.openGraph?.locale).toBe('en_US');
    });

    it('应该验证生成的元数据', () => {
      const metadata = generator.generatePageMetadata(mockProps);
      const isValid = generator.validateMetadata(metadata);

      expect(isValid).toBe(true);
    });

    it('应该提供 SEO 分析', () => {
      const analysis = generator.getSEOAnalysis(mockProps);

      expect(analysis.score).toBeGreaterThan(0);
      expect(Array.isArray(analysis.issues)).toBe(true);
      expect(Array.isArray(analysis.recommendations)).toBe(true);
    });
  });

  describe('useServerSEO Hook', () => {
    it('应该返回服务端 SEO 数据', () => {
      const result = useServerSEO(mockProps);

      expect(result.metadata).toBeDefined();
      expect(result.jsonLdScripts).toBeDefined();
      expect(Array.isArray(result.jsonLdScripts)).toBe(true);
    });

    it('应该处理错误情况', () => {
      const invalidProps = {
        page: 'invalid-page' as any,
        locale: 'zh' as const,
      };

      const result = useServerSEO(invalidProps);

      // 应该返回默认值而不是抛出错误
      expect(result.metadata).toBeDefined();
      expect(result.jsonLdScripts).toEqual([]);
    });

    it('应该生成有效的 JSON-LD', () => {
      const result = useServerSEO(mockProps);

      result.jsonLdScripts.forEach((script) => {
        expect(() => JSON.parse(script)).not.toThrow();
      });
    });
  });
});

describe('SEO 工具函数测试', () => {
  it('应该正确处理基本功能', () => {
    // 基本测试确保模块可以正确导入
    expect(true).toBe(true);
  });
});
