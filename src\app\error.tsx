'use client'; // 错误组件必须是客户端组件

import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon, HomeIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

interface ErrorPageProps {
  error: Error & { digest?: string }; // Next.js 提供的错误对象
  reset: () => void; // Next.js 提供的重置函数
}

/**
 * Next.js App Router 错误页面
 * 当应用程序在客户端发生错误时会自动显示此页面
 * 
 * @param error - 错误对象，包含错误信息和可选的 digest
 * @param reset - 重置函数，用于重新渲染出错的组件
 */
export default function ErrorPage({ error, reset }: ErrorPageProps) {
  React.useEffect(() => {
    // 在客户端记录错误到控制台
    console.error('应用程序错误:', error);
    
    // 这里可以添加错误报告逻辑，比如发送到错误监控服务
    // reportError(error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-slate-50 dark:bg-slate-900">
      <Card className="max-w-lg w-full shadow-lg">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
            </div>
            <CardTitle className="text-red-900 dark:text-red-100 text-xl">
              应用程序出现错误
            </CardTitle>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-slate-600 dark:text-slate-400">
            <p className="mb-2">
              很抱歉，应用程序遇到了一个意外错误。这可能是临时问题，请尝试以下解决方案：
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>点击"重试"按钮重新加载组件</li>
              <li>刷新整个页面</li>
              <li>返回首页重新开始</li>
            </ul>
          </div>
          
          {/* 开发环境下显示错误详情 */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100">
                🔍 错误详情 (仅开发模式显示)
              </summary>
              <div className="mt-3 p-4 bg-slate-100 dark:bg-slate-800 rounded-lg border">
                <div className="font-semibold text-red-600 dark:text-red-400 mb-2">
                  {error.name}: {error.message}
                </div>
                {error.digest && (
                  <div className="text-xs text-slate-500 dark:text-slate-400 mb-2">
                    错误 ID: {error.digest}
                  </div>
                )}
                <pre className="whitespace-pre-wrap text-xs text-slate-800 dark:text-slate-200 overflow-auto max-h-40 bg-white dark:bg-slate-900 p-2 rounded border">
                  {error.stack}
                </pre>
              </div>
            </details>
          )}
          
          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={reset} 
              className="flex items-center justify-center space-x-2 flex-1"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>重试</span>
            </Button>
            
            <Button 
              variant="secondary" 
              onClick={() => window.location.reload()}
              className="flex items-center justify-center space-x-2 flex-1"
            >
              <ArrowPathIcon className="h-4 w-4" />
              <span>刷新页面</span>
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="flex items-center justify-center space-x-2 flex-1"
            >
              <HomeIcon className="h-4 w-4" />
              <span>返回首页</span>
            </Button>
          </div>
          
          {/* 联系支持信息 */}
          <div className="text-center text-sm text-slate-500 dark:text-slate-400 pt-4 border-t border-slate-200 dark:border-slate-700">
            如果问题持续存在，请联系技术支持或查看
            <a 
              href="/help" 
              className="text-blue-600 dark:text-blue-400 hover:underline ml-1"
            >
              帮助文档
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
