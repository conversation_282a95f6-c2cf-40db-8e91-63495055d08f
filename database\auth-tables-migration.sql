-- ============================================================================
-- 认证系统表结构迁移脚本
-- 版本: 1.1
-- 创建时间: 2025-07-29
-- 说明: 为现有数据库添加完整的认证系统支持。此脚本具有幂等性，可以安全地多次运行。
-- ============================================================================

USE trading_analysis;

-- ============================================================================
-- 工具存储过程
-- ============================================================================

-- 添加列（如果不存在）
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DELIMITER $$
CREATE PROCEDURE AddColumnIfNotExists(
    IN t_name VARCHAR(255),
    IN c_name VARCHAR(255),
    IN c_def TEXT
)
BEGIN
    IF NOT EXISTS (
        SELECT * FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = t_name
        AND column_name = c_name
    )
    THEN
        SET @ddl = CONCAT('ALTER TABLE `', t_name, '` ADD COLUMN `', c_name, '` ', c_def);
        PREPARE stmt FROM @ddl;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 添加索引（如果不存在）
DROP PROCEDURE IF EXISTS AddIndexIfNotExists;
DELIMITER $$
CREATE PROCEDURE AddIndexIfNotExists(
    IN t_name VARCHAR(255),
    IN i_name VARCHAR(255),
    IN i_cols VARCHAR(255)
)
BEGIN
    IF NOT EXISTS (
        SELECT * FROM information_schema.statistics
        WHERE table_schema = DATABASE()
        AND table_name = t_name
        AND index_name = i_name
    )
    THEN
        SET @ddl = CONCAT('ALTER TABLE `', t_name, '` ADD INDEX `', i_name, '` (', i_cols, ')');
        PREPARE stmt FROM @ddl;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- ============================================================================
-- 检查并更新 users 表结构
-- ============================================================================

-- 1. 添加 email_verified 字段
CALL AddColumnIfNotExists('users', 'email_verified', 'BOOLEAN DEFAULT FALSE COMMENT \'邮箱是否已验证\' AFTER `password_hash`');

-- 2. 添加 avatar_url 字段
CALL AddColumnIfNotExists('users', 'avatar_url', 'VARCHAR(500) COMMENT \'用户头像URL\' AFTER `email_verified`');

-- 3. 添加 verification_token 字段
CALL AddColumnIfNotExists('users', 'verification_token', 'VARCHAR(255) COMMENT \'邮箱验证令牌\' AFTER `is_active`');

-- 4. 添加 reset_token 字段
CALL AddColumnIfNotExists('users', 'reset_token', 'VARCHAR(255) COMMENT \'密码重置令牌\' AFTER `verification_token`');

-- 5. 添加 reset_token_expires 字段
CALL AddColumnIfNotExists('users', 'reset_token_expires', 'TIMESTAMP NULL COMMENT \'密码重置令牌过期时间\' AFTER `reset_token`');

-- 6. 添加 last_login_at 字段
CALL AddColumnIfNotExists('users', 'last_login_at', 'TIMESTAMP NULL COMMENT \'最后登录时间\' AFTER `reset_token_expires`');

-- 7. 添加必要的索引
CALL AddIndexIfNotExists('users', 'idx_user_id', '`user_id`');
CALL AddIndexIfNotExists('users', 'idx_verification_token', '`verification_token`');
CALL AddIndexIfNotExists('users', 'idx_reset_token', '`reset_token`');

-- ============================================================================
-- 创建用户会话表
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(36) UNIQUE NOT NULL COMMENT '会话唯一标识',
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    token TEXT NOT NULL COMMENT '访问令牌',
    refresh_token TEXT NOT NULL COMMENT '刷新令牌',
    expires_at TIMESTAMP NOT NULL COMMENT '访问令牌过期时间',
    refresh_expires_at TIMESTAMP NOT NULL COMMENT '刷新令牌过期时间',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '客户端User-Agent',
    is_active BOOLEAN DEFAULT TRUE COMMENT '会话是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话管理表';

-- ============================================================================
-- 创建用户活动日志表
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    activity_type VARCHAR(50) NOT NULL COMMENT '活动类型 (login, logout, register, etc.)',
    ip_address VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '客户端User-Agent',
    details JSON COMMENT '活动详细信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- ============================================================================
-- 数据迁移和清理
-- ============================================================================

-- 为现有用户设置默认值
UPDATE users SET 
    email_verified = FALSE,
    is_active = TRUE
WHERE email_verified IS NULL OR is_active IS NULL;

-- ============================================================================
-- 清理存储过程
-- ============================================================================
DROP PROCEDURE IF EXISTS AddColumnIfNotExists;
DROP PROCEDURE IF EXISTS AddIndexIfNotExists;

-- ============================================================================
-- 验证迁移结果
-- ============================================================================

-- 检查 users 表结构
SELECT 'users 表字段检查:' as status;
DESCRIBE users;

-- 检查 user_sessions 表
SELECT 'user_sessions 表检查:' as status;
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'user_sessions';

-- 检查 user_activity_logs 表
SELECT 'user_activity_logs 表检查:' as status;
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'user_activity_logs';

-- 检查外键约束
SELECT '外键约束检查:' as status;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('user_sessions', 'user_activity_logs');

SELECT '认证系统表结构迁移完成！' as status;
