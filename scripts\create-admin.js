/**
 * 创建管理员账号脚本
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

async function createAdminUser() {
  console.log('🚀 开始创建管理员账号...\n');

  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 13306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'trading123',
      database: process.env.DB_NAME || 'trading_analysis',
    });

    console.log('✅ 数据库连接成功');

    // 管理员账号信息
    const adminData = {
      user_id: uuidv4(),
      email: '<EMAIL>',
      username: 'admin',
      password: 'Admin123!@#', // 强密码
      role: 'admin'
    };

    // 检查是否已存在管理员账号
    const [existingUsers] = await connection.execute(
      'SELECT * FROM users WHERE email = ? OR role = ?',
      [adminData.email, 'admin']
    );

    if (existingUsers.length > 0) {
      console.log('⚠️  管理员账号已存在:');
      existingUsers.forEach(user => {
        console.log(`   - ID: ${user.id}`);
        console.log(`   - 邮箱: ${user.email}`);
        console.log(`   - 用户名: ${user.username}`);
        console.log(`   - 角色: ${user.role}`);
        console.log(`   - 状态: ${user.is_active ? '激活' : '禁用'}`);
      });
      
      // 询问是否要重置密码
      console.log('\n如果需要重置管理员密码，请手动执行以下SQL:');
      const hashedPassword = await bcrypt.hash(adminData.password, 12);
      console.log(`UPDATE users SET password_hash = '${hashedPassword}' WHERE email = '${adminData.email}';`);
      
      await connection.end();
      return;
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(adminData.password, 12);

    // 创建管理员账号
    const [result] = await connection.execute(
      `INSERT INTO users (
        user_id, email, username, password_hash, role, 
        email_verified, is_active, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        adminData.user_id,
        adminData.email,
        adminData.username,
        hashedPassword,
        adminData.role,
        true, // 管理员账号默认已验证
        true  // 管理员账号默认激活
      ]
    );

    if (result.insertId) {
      console.log('✅ 管理员账号创建成功!');
      console.log('\n📋 账号信息:');
      console.log(`   - 用户ID: ${result.insertId}`);
      console.log(`   - UUID: ${adminData.user_id}`);
      console.log(`   - 邮箱: ${adminData.email}`);
      console.log(`   - 用户名: ${adminData.username}`);
      console.log(`   - 密码: ${adminData.password}`);
      console.log(`   - 角色: ${adminData.role}`);
      console.log(`   - 邮箱验证: 已验证`);
      console.log(`   - 账号状态: 激活`);

      // 记录创建活动
      await connection.execute(
        `INSERT INTO user_activity_logs (
          user_id, activity_type, ip_address, user_agent, details
        ) VALUES (?, ?, ?, ?, ?)`,
        [
          result.insertId,
          'admin_created',
          'system',
          'create-admin-script',
          JSON.stringify({
            created_by: 'system',
            created_at: new Date().toISOString(),
            initial_setup: true
          })
        ]
      );

      console.log('\n⚠️  重要提示:');
      console.log('   1. 请立即登录并修改默认密码');
      console.log('   2. 请妥善保管管理员账号信息');
      console.log('   3. 建议启用双因素认证（如果支持）');
      
    } else {
      console.log('❌ 管理员账号创建失败');
    }

    await connection.end();
    console.log('\n🎉 脚本执行完成!');

  } catch (error) {
    console.error('❌ 创建管理员账号失败:', error.message);
    console.log('\n💡 请检查:');
    console.log('   1. 数据库连接参数是否正确');
    console.log('   2. users 表是否存在');
    console.log('   3. 是否有足够的数据库权限');
  }
}

// 运行脚本
if (require.main === module) {
  createAdminUser();
}

module.exports = { createAdminUser };