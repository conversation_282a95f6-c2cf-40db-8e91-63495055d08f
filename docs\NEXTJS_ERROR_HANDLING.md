# Next.js App Router 错误处理指南

## 概述

本项目已从传统的 React ErrorBoundary 迁移到 Next.js App Router 的原生错误处理机制。这种方式更符合 Next.js 的设计理念，提供了更好的用户体验和开发体验。

## 错误处理文件结构

```
src/app/
├── error.tsx          # 页面级别的客户端错误处理
├── global-error.tsx   # 根布局错误处理
├── not-found.tsx      # 404 错误页面
├── loading.tsx        # 加载状态页面
└── test-error/        # 错误测试页面
    └── page.tsx
```

## 错误处理机制

### 1. error.tsx - 页面级错误处理

**用途**: 处理页面组件中发生的客户端错误

**特点**:
- 必须是客户端组件 (`'use client'`)
- 接收 `error` 和 `reset` 参数
- 可以重新渲染出错的组件
- 不会捕获根布局中的错误

**示例**:
```typescript
'use client';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  // 错误处理逻辑
}
```

### 2. global-error.tsx - 全局错误处理

**用途**: 处理根布局 (layout.tsx) 中发生的错误

**特点**:
- 必须是客户端组件 (`'use client'`)
- 会替换整个根布局，需要包含 `<html>` 和 `<body>` 标签
- 处理最严重的应用程序错误
- 包含内联样式，因为外部样式可能无法加载

### 3. not-found.tsx - 404 错误页面

**用途**: 处理页面不存在的情况

**特点**:
- 可以是服务端组件
- 当调用 `notFound()` 函数或访问不存在的路由时显示
- 提供友好的导航选项

### 4. loading.tsx - 加载状态

**用途**: 在页面加载时显示加载状态

**特点**:
- 可以是服务端组件
- 在 Suspense 边界内自动显示
- 提供更好的用户体验

## 错误类型和处理策略

### 1. 组件渲染错误

**触发条件**: 组件在渲染过程中抛出错误

**处理方式**: 由 `error.tsx` 捕获和处理

**示例**:
```typescript
// 这种错误会被 error.tsx 捕获
if (someCondition) {
  throw new Error('组件渲染失败');
}
```

### 2. 异步操作错误

**触发条件**: API 调用、数据获取等异步操作失败

**处理方式**: 需要手动使用 try/catch 处理

**示例**:
```typescript
try {
  const data = await fetchData();
} catch (error) {
  // 手动处理异步错误
  console.error('API 调用失败:', error);
}
```

### 3. 未处理的 Promise 拒绝

**触发条件**: Promise 被拒绝但没有 catch 处理

**处理方式**: 全局监听器处理

**示例**:
```typescript
// 在组件中监听未处理的 Promise 拒绝
useEffect(() => {
  const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    console.error('未处理的 Promise 拒绝:', event.reason);
  };

  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  return () => {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
  };
}, []);
```

### 4. 网络和 API 错误

**触发条件**: 网络请求失败、服务器错误等

**处理方式**: 在 API 层面统一处理

**示例**:
```typescript
// API 中间件处理
export async function apiCall(url: string) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  } catch (error) {
    // 统一的 API 错误处理
    throw new ApiError(error.message);
  }
}
```

## 开发环境 vs 生产环境

### 开发环境
- 显示详细的错误信息和堆栈跟踪
- 包含错误 ID (digest) 用于调试
- 提供错误详情的折叠面板

### 生产环境
- 隐藏敏感的错误信息
- 显示用户友好的错误消息
- 提供恢复操作选项

## 最佳实践

### 1. 错误边界的使用

❌ **不推荐**: 在 Next.js App Router 中使用 React ErrorBoundary
```typescript
// 避免这样做
<ErrorBoundary>
  <MyComponent />
</ErrorBoundary>
```

✅ **推荐**: 使用 Next.js 原生错误处理
```typescript
// 让 Next.js 自动处理错误
// 在 error.tsx 中定义错误 UI
```

### 2. 错误报告

```typescript
// 在错误页面中添加错误报告
useEffect(() => {
  // 发送错误到监控服务
  reportError(error, {
    page: window.location.pathname,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
  });
}, [error]);
```

### 3. 用户体验优化

```typescript
// 提供多种恢复选项
<div className="button-group">
  <Button onClick={reset}>重试</Button>
  <Button onClick={() => window.location.reload()}>刷新页面</Button>
  <Button onClick={() => router.push('/')}>返回首页</Button>
</div>
```

### 4. 错误分类

```typescript
// 根据错误类型提供不同的处理方式
const getErrorMessage = (error: Error) => {
  if (error.name === 'NetworkError') {
    return '网络连接失败，请检查您的网络设置';
  }
  if (error.name === 'ValidationError') {
    return '输入数据有误，请检查后重试';
  }
  return '应用程序遇到了意外错误';
};
```

## 测试错误处理

访问 `/test-error` 页面可以测试各种错误处理机制：

1. **组件错误**: 测试 error.tsx 的错误捕获
2. **异步错误**: 测试手动错误处理
3. **Promise 拒绝**: 测试全局错误监听
4. **全局错误**: 测试定时器中的错误

## 迁移指南

### 从 ErrorBoundary 迁移

1. **移除 ErrorBoundary 组件**:
   ```typescript
   // 删除这些导入和使用
   import { ErrorBoundary } from '@/components/common/ErrorBoundary';
   
   // 移除包装
   <ErrorBoundary>
     <Component />
   </ErrorBoundary>
   ```

2. **创建错误页面**:
   - 创建 `error.tsx` 处理页面错误
   - 创建 `global-error.tsx` 处理根布局错误
   - 创建 `not-found.tsx` 处理 404 错误

3. **更新错误处理逻辑**:
   - 将错误处理逻辑移到相应的错误页面
   - 使用 Next.js 提供的 `error` 和 `reset` 参数

## 故障排除

### 常见问题

1. **错误页面不显示**:
   - 确保错误页面是客户端组件 (`'use client'`)
   - 检查文件命名是否正确 (`error.tsx`, `global-error.tsx`)

2. **样式不生效**:
   - 在 `global-error.tsx` 中使用内联样式
   - 确保样式文件正确加载

3. **错误信息不完整**:
   - 检查是否在生产环境中隐藏了敏感信息
   - 确保错误报告逻辑正确实现

### 调试技巧

1. **查看浏览器控制台**: 错误信息会在控制台中显示
2. **使用 React DevTools**: 查看组件树和错误边界
3. **检查网络面板**: 查看 API 请求的错误状态
4. **使用错误测试页面**: 测试各种错误场景

## 相关资源

- [Next.js 错误处理文档](https://nextjs.org/docs/app/building-your-application/routing/error-handling)
- [React 错误边界文档](https://react.dev/reference/react/Component#catching-rendering-errors-with-an-error-boundary)
- [错误监控最佳实践](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
