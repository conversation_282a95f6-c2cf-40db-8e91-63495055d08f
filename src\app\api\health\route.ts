/**
 * 健康检查API端点
 * 提供系统健康状态和监控信息
 */

import { withApiHandler } from '@/lib/api-middleware';
import { logger } from '@/lib/logger';
import { createHealthCheckHandler, monitoring } from '@/lib/monitoring';
import { NextRequest } from 'next/server';

// 基础健康检查
export const GET = withApiHandler(async (request: NextRequest, context) => {
  const healthCheck = createHealthCheckHandler();
  const health = healthCheck();

  logger.info('健康检查完成', context, {
    status: health.status,
    uptime: health.uptime,
    errorRate: health.errors.errorRate,
  });

  return health;
});

// 详细监控报告
export const POST = withApiHandler(async (request: NextRequest, context) => {
  const body = await request.json();
  const { includeReport = false, reset = false } = body;

  let result: any = {
    health: monitoring.getSystemHealth(),
    timestamp: new Date().toISOString(),
  };

  if (includeReport) {
    result.report = monitoring.generateReport();
  }

  if (reset && process.env.NODE_ENV === 'development') {
    monitoring.reset();
    result.message = '监控数据已重置（仅开发环境）';
    logger.info('监控数据已重置', context);
  }

  logger.info('监控报告生成完成', context, {
    includeReport,
    reset,
    status: result.health.status,
  });

  return result;
});
