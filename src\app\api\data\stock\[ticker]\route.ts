import { akshareAdapter } from '@/lib/akshare/adapter';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 获取股票数据 API
 * GET /api/data/stock/{ticker}?period=1y&start_date=2024-01-01&end_date=2024-12-31
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const { ticker } = await params;
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const period = searchParams.get('period') || '1y';
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const adjust = searchParams.get('adjust') || '';

    console.log(`[股票数据API] 获取股票数据: ${ticker}, period: ${period}`);

    // 构建请求参数
    const params_data = {
      symbol: ticker,
      period,
      ...(startDate && { start_date: startDate }),
      ...(endDate && { end_date: endDate }),
      adjust,
    };

    // 调用AKShare适配器获取股票历史数据
    const stockData = await akshareAdapter.invoke('get_stock_history', params_data);

    if (!stockData || !Array.isArray(stockData)) {
      return NextResponse.json(
        {
          error: '无法获取股票数据',
          ticker,
          message: '数据格式错误或数据为空',
        },
        { status: 404 }
      );
    }

    // 格式化数据
    const formattedData = stockData.map((item: any) => ({
      date: item.日期 || item.date,
      open: parseFloat(item.开盘 || item.open || 0),
      high: parseFloat(item.最高 || item.high || 0),
      low: parseFloat(item.最低 || item.low || 0),
      close: parseFloat(item.收盘 || item.close || 0),
      volume: parseInt(item.成交量 || item.volume || 0),
      amount: parseFloat(item.成交额 || item.amount || 0),
      turnover: parseFloat(item.换手率 || item.turnover || 0),
      pctChange: parseFloat(item.涨跌幅 || item.pct_change || 0),
    }));

    // 计算统计信息
    const stats = calculateStockStats(formattedData);

    return NextResponse.json({
      ticker,
      name: getStockName(ticker),
      period,
      data: formattedData,
      stats,
      count: formattedData.length,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('[股票数据API] 错误:', error);

    return NextResponse.json(
      {
        error: '获取股票数据失败',
        ticker: (await params).ticker,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 计算股票统计信息
 */
function calculateStockStats(data: any[]) {
  if (!data || data.length === 0) {
    return null;
  }

  const prices = data.map((item) => item.close).filter((price) => price > 0);
  const volumes = data.map((item) => item.volume).filter((vol) => vol > 0);

  if (prices.length === 0) {
    return null;
  }

  const currentPrice = prices[prices.length - 1];
  const firstPrice = prices[0];
  const maxPrice = Math.max(...prices);
  const minPrice = Math.min(...prices);
  const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
  const avgVolume =
    volumes.length > 0 ? volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length : 0;

  // 计算波动率 (标准差)
  const variance =
    prices.reduce((sum, price) => sum + Math.pow(price - avgPrice, 2), 0) / prices.length;
  const volatility = Math.sqrt(variance);

  return {
    currentPrice,
    firstPrice,
    maxPrice,
    minPrice,
    avgPrice: Math.round(avgPrice * 100) / 100,
    avgVolume: Math.round(avgVolume),
    totalReturn: (((currentPrice - firstPrice) / firstPrice) * 100).toFixed(2) + '%',
    volatility: Math.round(volatility * 100) / 100,
    priceRange: {
      high: maxPrice,
      low: minPrice,
      range: maxPrice - minPrice,
    },
  };
}

/**
 * 获取股票名称 (简化版本，实际应该从数据库或API获取)
 */
function getStockName(ticker: string): string {
  const stockNames: { [key: string]: string } = {
    '000001': '平安银行',
    '000002': '万科A',
    '600000': '浦发银行',
    '600036': '招商银行',
    '600519': '贵州茅台',
    '000858': '五粮液',
    '002415': '海康威视',
    '300059': '东方财富',
  };

  return stockNames[ticker] || ticker;
}
