# Webpack 模块加载错误修复指南

## 问题描述

遇到的错误：
```
TypeError: Cannot read properties of undefined (reading 'call')
    at options.factory (webpack.js?v=1756018619338:700:31)
    at __webpack_require__ (webpack.js?v=1756018619338:37:33)
    at fn (webpack.js?v=1756018619338:357:21)
```

这是一个 webpack 模块加载错误，通常由以下原因引起：
- 模块导入路径错误
- 循环依赖
- 动态导入问题
- webpack 配置问题
- 缓存问题

## 已实施的修复措施

### 1. 错误处理机制升级

✅ **迁移到 Next.js App Router 原生错误处理**
- 创建了 `src/app/error.tsx` - 处理页面级错误
- 创建了 `src/app/global-error.tsx` - 处理根布局错误
- 创建了 `src/app/not-found.tsx` - 处理 404 错误
- 创建了 `src/app/loading.tsx` - 显示加载状态

✅ **移除传统 ErrorBoundary**
- 删除了 `src/components/common/ErrorBoundary.tsx`
- 从 `layout.tsx` 中移除了 ErrorBoundary 的使用

### 2. Webpack 配置优化

✅ **简化 webpack 配置**
```javascript
// next.config.js 中的修复
webpack: (config, { dev, isServer }) => {
  // 修复模块解析问题
  config.resolve.alias = {
    ...config.resolve.alias,
  };

  // 确保模块正确解析
  config.resolve.extensions = ['.js', '.jsx', '.ts', '.tsx', '.json'];

  // 修复 webpack 模块加载问题
  config.module.rules.push({
    test: /\.m?js$/,
    type: 'javascript/auto',
    resolve: {
      fullySpecified: false,
    },
  });

  return config;
}
```

### 3. 安全包装器

✅ **创建 SafeWrapper 组件**
- 位置：`src/components/common/SafeWrapper.tsx`
- 功能：捕获和处理组件渲染错误
- 特别处理 webpack 模块加载错误

### 4. 布局简化

✅ **简化根布局**
- 临时移除了可能有问题的组件导入
- 保留核心功能：Header、Footer、Providers
- 添加了 SafeWrapper 作为错误边界

## 测试验证

### 访问测试页面
```
http://localhost:3000/test-webpack
```

这个页面会显示：
- 基础组件加载状态
- 错误修复状态
- 修复措施总结
- 进一步的故障排除建议

### 错误处理测试
```
http://localhost:3000/test-error
```

这个页面可以测试各种错误处理场景。

## 如果问题仍然存在

### 1. 清理缓存
```bash
# 删除 Next.js 缓存
rm -rf .next

# 删除 node_modules 缓存
rm -rf node_modules/.cache

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 2. 运行修复脚本
```bash
node scripts/fix-webpack-error.js
```

### 3. 检查具体错误
1. 打开浏览器开发者工具
2. 查看 Console 标签页
3. 查看 Network 标签页是否有加载失败的资源
4. 查看 Sources 标签页检查模块加载情况

### 4. 逐步排查
1. **检查最近的代码更改**
   - 查看最近修改的文件
   - 检查新添加的导入语句
   - 验证导入路径是否正确

2. **检查循环依赖**
   ```bash
   # 安装循环依赖检查工具
   npm install --save-dev madge
   
   # 检查循环依赖
   npx madge --circular src/
   ```

3. **检查动态导入**
   - 查找使用 `import()` 的地方
   - 查找使用 `React.lazy` 的地方
   - 查找使用 `next/dynamic` 的地方

4. **检查第三方库**
   - 确保所有依赖都正确安装
   - 检查是否有版本冲突
   - 尝试更新或降级有问题的包

## 预防措施

### 1. 代码规范
- 使用绝对导入路径（@/ 别名）
- 避免深层嵌套的相对导入
- 确保所有导出都正确命名

### 2. 依赖管理
- 定期更新依赖
- 使用 package-lock.json 锁定版本
- 避免混用不同的包管理器

### 3. 错误监控
- 使用 Next.js 原生错误处理
- 添加错误报告服务（如 Sentry）
- 定期检查错误日志

## 相关文件

- `src/app/error.tsx` - 页面级错误处理
- `src/app/global-error.tsx` - 全局错误处理
- `src/app/not-found.tsx` - 404 错误页面
- `src/app/loading.tsx` - 加载状态页面
- `src/components/common/SafeWrapper.tsx` - 安全包装器
- `next.config.js` - Webpack 配置
- `scripts/fix-webpack-error.js` - 修复脚本

## 联系支持

如果问题仍然无法解决，请：
1. 收集错误日志和堆栈跟踪
2. 记录重现步骤
3. 提供环境信息（Node.js 版本、npm 版本等）
4. 联系开发团队获取进一步支持
