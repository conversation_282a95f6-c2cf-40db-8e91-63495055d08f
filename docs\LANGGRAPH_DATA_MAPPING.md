# LangGraph 结果数据映射与数据库存储方案

## 1. 概述

本文档详细说明了 `langgraph-server.ts` 中 `workflow.invoke` 方法返回的结果对象如何被解析，并将其数据持久化到后端的 MySQL 数据库中。该方案基于 `src/lib/langgraph-state.ts` 中定义的 `TradingAgentAnnotation.State` 类型。

## 2. 数据源

数据源是 `langgraph-server.ts` 中 `this.workflow.invoke(...)` 调用返回的 `result` 对象，其类型为 `TradingAgentAnnotation.State`。关键字段包括：

- `messages`: 对话历史数组。
- `analysis`: 包含各分析师（如基本面、技术面）结果的对象。
- `research`: 包含研究过程（如辩论回合）的对象。
- `risk`: 风险评估结果。
- `decision`: 最终交易决策。

## 3. 数据库表映射关系

| `TradingAgentAnnotation.State` 字段 | 目标数据库表       | 说明                                                              |
| :---------------------------------- | :----------------- | :---------------------------------------------------------------- |
| `messages`                          | `messages`         | 存储完整的对话流。                                                |
| `analysis`                          | `analysis_results` | 将整个 `analysis` 对象作为一条综合分析结果记录。                  |
| `research`                          | `analysis_steps`   | 将 `research.debateRounds` 中的每一轮辩论作为单独的分析步骤记录。 |
| `risk`                              | `analysis_results` | 将风险评估作为一条独立的分析结果记录，类型为 `risk`。             |
| `decision`                          | `analysis_results` | 将最终决策作为一条独立的分析结果记录，类型为 `decision`。         |

## 4. 字段级详细映射

### 4.1 `result.messages` -> `messages` 表

| `message` 对象属性          | `messages` 表列 | 转换逻辑/备注                                   |
| :-------------------------- | :-------------- | :---------------------------------------------- |
| `message.type`              | `message_type`  | 直接映射 (`human`, `ai`, `system`, `tool`)。    |
| `message.content`           | `content`       | 直接映射消息内容。                              |
| `message.additional_kwargs` | `metadata`      | 存储在 JSON 类型的 `metadata` 字段中。          |
| `threadId` (来自会话)       | `task_id`       | 使用会话的 `threadId` 作为 `task_id` 进行关联。 |
| `threadId` (来自会话)       | `thread_id`     | 同样记录 LangGraph 的线程 ID。                  |

### 4.2 `result.analysis` -> `analysis_results` 表

| `analysis` 对象属性   | `analysis_results` 表列 | 转换逻辑/备注                                      |
| :-------------------- | :---------------------- | :------------------------------------------------- |
| `threadId` (来自会话) | `task_id`               | 使用会话的 `threadId` 作为 `task_id`。             |
| (硬编码)              | `result_type`           | 设置为 `'comprehensive_analysis'`。                |
| `analysis` (整个对象) | `result_data`           | 将完整的 `analysis` 对象序列化为 JSON 字符串存入。 |
| (提取或生成)          | `summary`               | 可从 `analysis` 对象中提取或生成一个总体摘要。     |
| (硬编码)              | `is_final`              | 可设置为 `false`，因为它是一个中间分析阶段的结果。 |

### 4.3 `result.risk` -> `analysis_results` 表

| `risk` 对象属性       | `analysis_results` 表列 | 转换逻辑/备注                            |
| :-------------------- | :---------------------- | :--------------------------------------- |
| `threadId` (来自会话) | `task_id`               | 使用会话的 `threadId` 作为 `task_id`。   |
| (硬编码)              | `result_type`           | 设置为 `'risk'`。                        |
| `risk` (整个对象)     | `result_data`           | 将 `risk` 对象序列化为 JSON 字符串存入。 |
| `risk.summary`        | `summary`               | 提取风险摘要。                           |
| (硬编码)              | `is_final`              | 可设置为 `false`，因为它是一个中间步骤。 |

### 4.4 `result.decision` -> `analysis_results` 表

| `decision` 对象属性   | `analysis_results` 表列 | 转换逻辑/备注                                |
| :-------------------- | :---------------------- | :------------------------------------------- |
| `threadId` (来自会话) | `task_id`               | 使用会话的 `threadId` 作为 `task_id`。       |
| (硬编码)              | `result_type`           | 设置为 `'decision'`。                        |
| `decision` (整个对象) | `result_data`           | 将 `decision` 对象序列化为 JSON 字符串存入。 |
| `decision.summary`    | `summary`               | 提取决策摘要。                               |
| (硬编码)              | `is_final`              | 设置为 `true`，因为这是最终决策。            |

### 4.5 `result.research` -> `analysis_steps` 表

`research.debateRounds` 数组中的每个 `round` 对象都将成为 `analysis_steps` 表中的一条记录。

| `round` 对象属性      | `analysis_steps` 表列 | 转换逻辑/备注                                |
| :-------------------- | :-------------------- | :------------------------------------------- |
| `threadId` (来自会话) | `task_id`             | 使用会话的 `threadId` 作为 `task_id`。       |
| (生成)                | `step_name`           | 例如 `辩论回合 #${index + 1}`。              |
| (硬编码)              | `step_type`           | 设置为 `'analysis'` 或 `'validation'`。      |
| `round` (整个对象)    | `output_data`         | 将完整的辩论回合对象存入，以保留所有上下文。 |
| `round.summary`       | `description`         | 提取该回合的摘要作为步骤描述。               |
| `index` (循环索引)    | `sequence_order`      | 记录辩论的顺序。                             |
| (硬编码)              | `status`              | 设置为 `'completed'`。                       |

## 5. 实现策略

将在 `langgraph-server.ts` 中创建一个名为 `_saveGraphResultToDb(threadId: string, result: any)` 的私有方法。此方法将负责执行上述所有的数据映射和数据库写入操作。它将在 `analyzeStock` 和 `sendMessage` 成功从 `workflow.invoke` 获得结果后被调用，从而确保数据能够被完整、原子地保存。
