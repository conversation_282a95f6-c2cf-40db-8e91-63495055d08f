#!/usr/bin/env node

/**
 * Webpack 错误修复脚本
 * 用于解决 "Cannot read properties of undefined (reading 'call')" 错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复 Webpack 模块加载错误...');

// 清理可能的缓存文件
const cacheDirs = [
  '.next',
  'node_modules/.cache',
  '.cache'
];

cacheDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (fs.existsSync(fullPath)) {
    console.log(`🗑️  清理缓存目录: ${dir}`);
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ 已清理: ${dir}`);
    } catch (error) {
      console.warn(`⚠️  清理失败: ${dir} - ${error.message}`);
    }
  }
});

// 检查 package-lock.json 是否存在问题
const packageLockPath = path.join(process.cwd(), 'package-lock.json');
if (fs.existsSync(packageLockPath)) {
  console.log('📦 检查 package-lock.json...');
  try {
    const packageLock = JSON.parse(fs.readFileSync(packageLockPath, 'utf8'));
    console.log(`✅ package-lock.json 格式正确 (版本: ${packageLock.lockfileVersion})`);
  } catch (error) {
    console.error('❌ package-lock.json 格式错误:', error.message);
    console.log('🔄 建议删除 package-lock.json 并重新安装依赖');
  }
}

// 检查 next.config.js
const nextConfigPath = path.join(process.cwd(), 'next.config.js');
if (fs.existsSync(nextConfigPath)) {
  console.log('⚙️  检查 next.config.js...');
  try {
    // 简单的语法检查
    require(nextConfigPath);
    console.log('✅ next.config.js 配置正确');
  } catch (error) {
    console.error('❌ next.config.js 配置错误:', error.message);
  }
}

// 提供修复建议
console.log('\n🛠️  修复建议:');
console.log('1. 运行 npm install 重新安装依赖');
console.log('2. 重启开发服务器 (npm run dev)');
console.log('3. 如果问题持续，尝试删除 node_modules 并重新安装');
console.log('4. 检查是否有循环依赖或导入错误');

console.log('\n📋 常见解决方案:');
console.log('- 确保所有导入路径正确');
console.log('- 检查是否有未正确导出的模块');
console.log('- 验证 TypeScript 配置');
console.log('- 确保没有使用服务端模块在客户端');

console.log('\n✨ 修复脚本执行完成!');
