'use client';

import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface AnalysisPreviewProps {
  status: AnalysisStatus;
  onViewDetails?: () => void;
}

export function AnalysisPreview({ status, onViewDetails }: AnalysisPreviewProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'running':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'cancelled':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '待处理';
    }
  };

  const getStageText = (stage: string) => {
    const stageMap: Record<string, string> = {
      pending: '等待开始',
      starting: '正在启动',
      fundamental_analysis: '基本面分析',
      technical_analysis: '技术分析',
      sentiment_analysis: '情绪分析',
      news_analysis: '新闻分析',
      bull_research: '多头研究',
      bear_research: '空头研究',
      consensus_evaluation: '共识评估',
      risk_assessment: '风险评估',
      final_decision: '最终决策',
      completed: '分析完成',
      failed: '分析失败',
      cancelled: '已取消',
    };
    return stageMap[stage] || stage;
  };

  const completedStages = Object.values(status.stages).filter(Boolean).length;
  const totalStages = Object.keys(status.stages).length;

  return (
    <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-4 space-y-4">
      {/* 状态头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(
              status.status
            )}`}
          >
            {getStatusText(status.status)}
          </span>
          <span className="text-sm text-gray-600">{getStageText(status.currentStage)}</span>
        </div>
        {onViewDetails && (
          <button
            onClick={onViewDetails}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            查看详情 →
          </button>
        )}
      </div>

      {/* 进度条 */}
      {status.status === 'running' && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">分析进度</span>
            <span className="text-gray-900 font-medium">{status.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${status.progress}%` }}
            />
          </div>
          <div className="text-xs text-gray-500">
            已完成 {completedStages}/{totalStages} 个阶段
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 text-sm">
        <div>
          <div className="text-gray-500">消息数</div>
          <div className="font-medium">{status.statistics.totalMessages}</div>
        </div>
        <div>
          <div className="text-gray-500">辩论轮次</div>
          <div className="font-medium">{status.statistics.debateRounds}</div>
        </div>
        <div>
          <div className="text-gray-500">耗时</div>
          <div className="font-medium">
            {status.statistics.duration > 0
              ? `${Math.round(status.statistics.duration / 60)}分钟`
              : '-'}
          </div>
        </div>
      </div>

      {/* 时间信息 */}
      <div className="text-xs text-gray-500 space-y-1">
        {status.startedAt && (
          <div>
            开始时间: {format(new Date(status.startedAt), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
          </div>
        )}
        {status.completedAt && (
          <div>
            完成时间: {format(new Date(status.completedAt), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
          </div>
        )}
      </div>

      {/* 快速预览 - 只在完成时显示 */}
      {status.status === 'completed' && status.latestReports.decision && (
        <div className="border-t pt-3 space-y-2">
          <div className="text-sm font-medium text-gray-900">交易建议</div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <span className="text-gray-500">操作:</span>
              <span
                className={`font-medium ${
                  status.latestReports.decision.action === 'buy'
                    ? 'text-green-600'
                    : status.latestReports.decision.action === 'sell'
                    ? 'text-red-600'
                    : 'text-gray-600'
                }`}
              >
                {status.latestReports.decision.action === 'buy'
                  ? '买入'
                  : status.latestReports.decision.action === 'sell'
                  ? '卖出'
                  : '持有'}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-gray-500">信心度:</span>
              <span className="font-medium">
                {Math.round((status.latestReports.decision.confidence || 0) * 100)}%
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
