'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

/**
 * 错误测试页面
 * 用于测试 Next.js App Router 的错误处理机制
 */
export default function TestErrorPage() {
  const [shouldThrowError, setShouldThrowError] = React.useState(false);

  // 模拟组件错误
  if (shouldThrowError) {
    throw new Error('这是一个测试错误：模拟组件渲染失败');
  }

  const handleThrowError = () => {
    setShouldThrowError(true);
  };

  const handleAsyncError = async () => {
    try {
      // 模拟异步错误
      await new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('这是一个异步错误：模拟 API 调用失败'));
        }, 1000);
      });
    } catch (error) {
      console.error('捕获到异步错误:', error);
      // 在实际应用中，这里可以显示错误通知
      alert(`异步错误: ${(error as Error).message}`);
    }
  };

  const handlePromiseRejection = () => {
    // 模拟未处理的 Promise 拒绝
    Promise.reject(new Error('这是一个未处理的 Promise 拒绝'));
  };

  const handleGlobalError = () => {
    // 模拟全局错误
    setTimeout(() => {
      throw new Error('这是一个全局错误：模拟定时器中的错误');
    }, 1000);
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">
          错误处理测试页面
        </h1>
        <p className="text-slate-600 dark:text-slate-400">
          测试 Next.js App Router 的各种错误处理机制
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 组件错误测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600 dark:text-red-400">
              组件渲染错误
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-slate-600 dark:text-slate-400">
              点击按钮将触发组件渲染错误，这会被 error.tsx 页面捕获。
            </p>
            <Button 
              onClick={handleThrowError}
              variant="destructive"
              className="w-full"
            >
              触发组件错误
            </Button>
          </CardContent>
        </Card>

        {/* 异步错误测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-orange-600 dark:text-orange-400">
              异步错误处理
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-slate-600 dark:text-slate-400">
              测试异步操作中的错误处理，这类错误通常需要手动捕获。
            </p>
            <Button 
              onClick={handleAsyncError}
              variant="secondary"
              className="w-full"
            >
              触发异步错误
            </Button>
          </CardContent>
        </Card>

        {/* Promise 拒绝测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-yellow-600 dark:text-yellow-400">
              未处理的 Promise 拒绝
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-slate-600 dark:text-slate-400">
              测试未处理的 Promise 拒绝，这类错误会在浏览器控制台中显示。
            </p>
            <Button 
              onClick={handlePromiseRejection}
              variant="outline"
              className="w-full"
            >
              触发 Promise 拒绝
            </Button>
          </CardContent>
        </Card>

        {/* 全局错误测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-purple-600 dark:text-purple-400">
              全局错误
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-slate-600 dark:text-slate-400">
              测试在定时器等异步上下文中的全局错误。
            </p>
            <Button 
              onClick={handleGlobalError}
              variant="outline"
              className="w-full"
            >
              触发全局错误
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 错误处理说明 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>错误处理机制说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">
                Next.js App Router 错误页面
              </h3>
              <ul className="text-sm text-slate-600 dark:text-slate-400 space-y-1">
                <li>• <code>error.tsx</code> - 处理页面级别的客户端错误</li>
                <li>• <code>global-error.tsx</code> - 处理根布局的错误</li>
                <li>• <code>not-found.tsx</code> - 处理 404 错误</li>
                <li>• <code>loading.tsx</code> - 显示加载状态</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-2">
                错误类型
              </h3>
              <ul className="text-sm text-slate-600 dark:text-slate-400 space-y-1">
                <li>• 组件渲染错误 - 由 error.tsx 处理</li>
                <li>• 异步错误 - 需要手动 try/catch</li>
                <li>• Promise 拒绝 - 全局监听器处理</li>
                <li>• 网络错误 - API 层面处理</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
