/**
 * 多语言 SEO 内容管理系统
 * 提供中英文 SEO 内容配置、语言检测和内容切换逻辑
 */

import { SupportedLocale, PageType, SEOConfig } from '@/types/seo';
import { SEO_CONSTANTS } from './config';

// 扩展的多语言 SEO 内容配置
export const I18N_SEO_CONTENT: Record<
  SupportedLocale,
  Record<
    PageType,
    Omit<SEOConfig, 'openGraph' | 'twitter'> & {
      hreflang: string;
      alternateTitle?: string;
      socialTitle?: string;
      socialDescription?: string;
    }
  >
> = {
  zh: {
    home: {
      title: 'TradingAgents - 多智能体大语言模型金融交易框架',
      description:
        '基于多智能体大语言模型的专业金融交易分析框架，提供智能化的市场分析、风险评估和交易决策支持。支持实时数据分析、技术指标计算、基本面研究和情绪分析。',
      keywords: [
        '多智能体',
        '金融交易',
        'AI交易',
        '量化投资',
        '大语言模型',
        '智能分析',
        '股票分析',
        '风险管理',
        '交易决策',
        '金融科技',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/`,
      hreflang: 'zh-CN',
      alternateTitle: 'TradingAgents - 专业AI金融分析平台',
      socialTitle: '🤖 TradingAgents - 多智能体金融交易框架',
      socialDescription: '基于AI的专业金融分析工具，多智能体协作提供全方位投资决策支持 📈',
    },
    analysis: {
      title: '智能分析 - TradingAgents',
      description:
        '实时多智能体协作分析，包含基本面分析、技术分析、情绪分析和风险评估，为您的交易决策提供全方位支持。支持多种技术指标、财务数据分析和市场情绪监控。',
      keywords: [
        '股票分析',
        '技术分析',
        '基本面分析',
        '风险评估',
        '交易决策',
        '多智能体协作',
        '实时分析',
        '市场情绪',
        '财务数据',
        '投资研究',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/analysis`,
      hreflang: 'zh-CN',
      alternateTitle: '智能股票分析 - TradingAgents',
      socialTitle: '📊 智能分析 - 多维度股票研究',
      socialDescription: '多智能体协作进行深度股票分析，技术面+基本面+情绪面全覆盖 🎯',
    },
    tasks: {
      title: '任务管理 - TradingAgents',
      description:
        '管理和监控您的分析任务，查看任务状态、执行进度和分析结果，高效管理您的投资研究工作流。支持批量任务处理、定时分析和结果导出。',
      keywords: [
        '任务管理',
        '分析任务',
        '投资研究',
        '工作流管理',
        '任务监控',
        '批量处理',
        '定时分析',
        '结果导出',
        '进度跟踪',
        '效率工具',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/tasks`,
      hreflang: 'zh-CN',
      alternateTitle: '分析任务管理 - TradingAgents',
      socialTitle: '📋 任务管理 - 高效投资研究工作流',
      socialDescription: '智能任务管理系统，让您的投资研究更有序、更高效 ⚡',
    },
    messages: {
      title: '消息查询 - TradingAgents',
      description:
        '查看和管理系统消息、分析报告和智能体通信记录，全面了解分析过程和决策依据。支持消息搜索、分类筛选和历史记录查看。',
      keywords: [
        '消息查询',
        '分析报告',
        '智能体通信',
        '系统消息',
        '决策记录',
        '历史记录',
        '消息搜索',
        '分类筛选',
        '通信日志',
        '过程追踪',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/messages`,
      hreflang: 'zh-CN',
      alternateTitle: '系统消息中心 - TradingAgents',
      socialTitle: '💬 消息查询 - 智能体通信记录',
      socialDescription: '查看分析过程中的所有消息和决策记录，透明化AI决策过程 🔍',
    },
    'create-task': {
      title: '创建任务 - TradingAgents',
      description:
        '创建新的分析任务，配置股票代码、分析参数和智能体设置，开始您的专业投资分析。支持自定义分析策略、参数调优和多股票对比分析。',
      keywords: [
        '创建任务',
        '分析配置',
        '股票分析',
        '投资研究',
        '智能体设置',
        '自定义策略',
        '参数调优',
        '多股票对比',
        '分析参数',
        '任务创建',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/create-task`,
      hreflang: 'zh-CN',
      alternateTitle: '新建分析任务 - TradingAgents',
      socialTitle: '➕ 创建任务 - 开始您的投资分析',
      socialDescription: '快速创建专业的股票分析任务，AI智能体为您提供深度研究 🚀',
    },
  },
  en: {
    home: {
      title: 'TradingAgents - Multi-Agent LLM Financial Trading Framework',
      description:
        'Professional financial trading analysis framework based on multi-agent large language models, providing intelligent market analysis, risk assessment and trading decision support. Features real-time data analysis, technical indicators, fundamental research and sentiment analysis.',
      keywords: [
        'multi-agent',
        'financial trading',
        'AI trading',
        'quantitative investment',
        'LLM',
        'intelligent analysis',
        'stock analysis',
        'risk management',
        'trading decisions',
        'fintech',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en`,
      hreflang: 'en-US',
      alternateTitle: 'TradingAgents - Professional AI Financial Analysis Platform',
      socialTitle: '🤖 TradingAgents - Multi-Agent Financial Trading Framework',
      socialDescription:
        'AI-powered professional financial analysis tool with multi-agent collaboration for comprehensive investment decisions 📈',
    },
    analysis: {
      title: 'Intelligent Analysis - TradingAgents',
      description:
        'Real-time multi-agent collaborative analysis including fundamental analysis, technical analysis, sentiment analysis and risk assessment for comprehensive trading decision support. Features multiple technical indicators, financial data analysis and market sentiment monitoring.',
      keywords: [
        'stock analysis',
        'technical analysis',
        'fundamental analysis',
        'risk assessment',
        'trading decisions',
        'multi-agent collaboration',
        'real-time analysis',
        'market sentiment',
        'financial data',
        'investment research',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/analysis`,
      hreflang: 'en-US',
      alternateTitle: 'Smart Stock Analysis - TradingAgents',
      socialTitle: '📊 Intelligent Analysis - Multi-dimensional Stock Research',
      socialDescription:
        'Multi-agent collaboration for deep stock analysis covering technical, fundamental and sentiment aspects 🎯',
    },
    tasks: {
      title: 'Task Management - TradingAgents',
      description:
        'Manage and monitor your analysis tasks, view task status, execution progress and analysis results for efficient investment research workflow management. Features batch processing, scheduled analysis and result export.',
      keywords: [
        'task management',
        'analysis tasks',
        'investment research',
        'workflow management',
        'task monitoring',
        'batch processing',
        'scheduled analysis',
        'result export',
        'progress tracking',
        'efficiency tools',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/tasks`,
      hreflang: 'en-US',
      alternateTitle: 'Analysis Task Management - TradingAgents',
      socialTitle: '📋 Task Management - Efficient Investment Research Workflow',
      socialDescription:
        'Smart task management system for organized and efficient investment research ⚡',
    },
    messages: {
      title: 'Message Query - TradingAgents',
      description:
        'View and manage system messages, analysis reports and agent communication records for comprehensive understanding of analysis processes and decision rationale. Features message search, category filtering and history viewing.',
      keywords: [
        'message query',
        'analysis reports',
        'agent communication',
        'system messages',
        'decision records',
        'history records',
        'message search',
        'category filtering',
        'communication logs',
        'process tracking',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/messages`,
      hreflang: 'en-US',
      alternateTitle: 'System Message Center - TradingAgents',
      socialTitle: '💬 Message Query - Agent Communication Records',
      socialDescription:
        'View all messages and decision records from the analysis process, transparent AI decision making 🔍',
    },
    'create-task': {
      title: 'Create Task - TradingAgents',
      description:
        'Create new analysis tasks, configure stock symbols, analysis parameters and agent settings to start your professional investment analysis. Features custom analysis strategies, parameter optimization and multi-stock comparison.',
      keywords: [
        'create task',
        'analysis configuration',
        'stock analysis',
        'investment research',
        'agent settings',
        'custom strategies',
        'parameter optimization',
        'multi-stock comparison',
        'analysis parameters',
        'task creation',
      ],
      canonical: `${SEO_CONSTANTS.SITE_URL}/en/create-task`,
      hreflang: 'en-US',
      alternateTitle: 'New Analysis Task - TradingAgents',
      socialTitle: '➕ Create Task - Start Your Investment Analysis',
      socialDescription:
        'Quickly create professional stock analysis tasks with AI agents providing deep research 🚀',
    },
  },
};

// 语言检测配置
export const LANGUAGE_DETECTION_CONFIG = {
  // 支持的语言代码
  supportedLocales: ['zh', 'en'] as SupportedLocale[],

  // 默认语言
  defaultLocale: 'zh' as SupportedLocale,

  // 语言映射
  localeMapping: {
    zh: 'zh-CN',
    'zh-CN': 'zh',
    'zh-Hans': 'zh',
    'zh-Hant': 'zh',
    en: 'en-US',
    'en-US': 'en',
    'en-GB': 'en',
  } as Record<string, string>,

  // 浏览器语言优先级
  browserLanguagePriority: ['zh-CN', 'zh', 'en-US', 'en'],

  // Cookie 配置
  cookieConfig: {
    name: 'tradingagents-locale',
    maxAge: 365 * 24 * 60 * 60, // 1年
    path: '/',
    sameSite: 'lax' as const,
  },
};

// hreflang 标签生成配置
export const HREFLANG_CONFIG = {
  // 语言-地区映射
  localeToHreflang: {
    zh: 'zh-CN',
    en: 'en-US',
  } as Record<SupportedLocale, string>,

  // 默认语言标记
  defaultHreflang: 'x-default',

  // URL 模式
  urlPatterns: {
    zh: '', // 中文为默认，无前缀
    en: '/en', // 英文有前缀
  } as Record<SupportedLocale, string>,
};

/**
 * 语言检测工具类
 */
export class LanguageDetector {
  /**
   * 从请求头检测语言
   */
  static detectFromHeaders(acceptLanguage?: string): SupportedLocale {
    if (!acceptLanguage) {
      return LANGUAGE_DETECTION_CONFIG.defaultLocale;
    }

    // 解析 Accept-Language 头
    const languages = acceptLanguage
      .split(',')
      .map((lang) => {
        const [code, q = '1'] = lang.trim().split(';q=');
        return { code: code.toLowerCase(), quality: parseFloat(q) };
      })
      .sort((a, b) => b.quality - a.quality);

    // 查找支持的语言
    for (const { code } of languages) {
      const mappedLocale = LANGUAGE_DETECTION_CONFIG.localeMapping[code];
      if (
        mappedLocale &&
        LANGUAGE_DETECTION_CONFIG.supportedLocales.includes(mappedLocale as SupportedLocale)
      ) {
        return mappedLocale as SupportedLocale;
      }

      // 检查语言代码前缀
      const prefix = code.split('-')[0];
      const prefixMapped = LANGUAGE_DETECTION_CONFIG.localeMapping[prefix];
      if (
        prefixMapped &&
        LANGUAGE_DETECTION_CONFIG.supportedLocales.includes(prefixMapped as SupportedLocale)
      ) {
        return prefixMapped as SupportedLocale;
      }
    }

    return LANGUAGE_DETECTION_CONFIG.defaultLocale;
  }

  /**
   * 从 URL 路径检测语言
   */
  static detectFromPath(pathname: string): { locale: SupportedLocale; cleanPath: string } {
    // 检查路径是否以语言前缀开始
    for (const [locale, prefix] of Object.entries(HREFLANG_CONFIG.urlPatterns)) {
      if (prefix && pathname.startsWith(prefix + '/')) {
        return {
          locale: locale as SupportedLocale,
          cleanPath: pathname.slice(prefix.length) || '/',
        };
      }
      if (prefix && pathname === prefix) {
        return {
          locale: locale as SupportedLocale,
          cleanPath: '/',
        };
      }
    }

    // 默认为中文
    return {
      locale: 'zh',
      cleanPath: pathname,
    };
  }

  /**
   * 从 Cookie 检测语言
   */
  static detectFromCookie(cookieValue?: string): SupportedLocale | null {
    if (!cookieValue) return null;

    const locale = cookieValue as SupportedLocale;
    return LANGUAGE_DETECTION_CONFIG.supportedLocales.includes(locale) ? locale : null;
  }

  /**
   * 综合检测语言
   */
  static detect(options: { pathname?: string; acceptLanguage?: string; cookieValue?: string }): {
    locale: SupportedLocale;
    cleanPath: string;
  } {
    const { pathname = '/', acceptLanguage, cookieValue } = options;

    // 1. 首先从 URL 路径检测
    const pathResult = this.detectFromPath(pathname);
    if (pathResult.locale !== 'zh' || pathname.startsWith('/en')) {
      return pathResult;
    }

    // 2. 从 Cookie 检测
    const cookieLocale = this.detectFromCookie(cookieValue);
    if (cookieLocale) {
      return {
        locale: cookieLocale,
        cleanPath: pathResult.cleanPath,
      };
    }

    // 3. 从请求头检测
    const headerLocale = this.detectFromHeaders(acceptLanguage);
    return {
      locale: headerLocale,
      cleanPath: pathResult.cleanPath,
    };
  }
}

/**
 * hreflang 标签生成器
 */
export class HreflangGenerator {
  /**
   * 生成 hreflang 链接
   */
  static generateHreflangLinks(
    cleanPath: string,
    currentLocale: SupportedLocale
  ): Array<{ hreflang: string; href: string }> {
    const links: Array<{ hreflang: string; href: string }> = [];

    // 为每种支持的语言生成链接
    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      const prefix = HREFLANG_CONFIG.urlPatterns[locale];
      const href = `${SEO_CONSTANTS.SITE_URL}${prefix}${cleanPath === '/' ? '' : cleanPath}`;
      const hreflang = HREFLANG_CONFIG.localeToHreflang[locale];

      links.push({ hreflang, href });
    });

    // 添加默认语言链接
    const defaultHref = `${SEO_CONSTANTS.SITE_URL}${cleanPath === '/' ? '' : cleanPath}`;
    links.push({
      hreflang: HREFLANG_CONFIG.defaultHreflang,
      href: defaultHref,
    });

    return links;
  }

  /**
   * 生成 Next.js alternates 配置
   */
  static generateAlternates(
    cleanPath: string,
    currentLocale: SupportedLocale
  ): {
    canonical: string;
    languages: Record<string, string>;
  } {
    const languages: Record<string, string> = {};

    // 为每种支持的语言生成链接
    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      const prefix = HREFLANG_CONFIG.urlPatterns[locale];
      const href = `${SEO_CONSTANTS.SITE_URL}${prefix}${cleanPath === '/' ? '' : cleanPath}`;
      languages[locale] = href;
    });

    // 添加默认语言
    const defaultHref = `${SEO_CONSTANTS.SITE_URL}${cleanPath === '/' ? '' : cleanPath}`;
    languages['x-default'] = defaultHref;

    // 当前页面的规范链接
    const currentPrefix = HREFLANG_CONFIG.urlPatterns[currentLocale];
    const canonical = `${SEO_CONSTANTS.SITE_URL}${currentPrefix}${
      cleanPath === '/' ? '' : cleanPath
    }`;

    return { canonical, languages };
  }
}

/**
 * 多语言内容切换器
 */
export class I18nContentSwitcher {
  /**
   * 获取页面的多语言内容
   */
  static getPageContent(
    page: PageType,
    locale: SupportedLocale
  ): (typeof I18N_SEO_CONTENT)[SupportedLocale][PageType] {
    const content = I18N_SEO_CONTENT[locale]?.[page];

    if (!content) {
      console.warn(`No i18n SEO content found for page: ${page}, locale: ${locale}`);
      return I18N_SEO_CONTENT[LANGUAGE_DETECTION_CONFIG.defaultLocale][page];
    }

    return content;
  }

  /**
   * 获取动态内容（支持模板变量替换）
   */
  static getDynamicContent(
    page: PageType,
    locale: SupportedLocale,
    variables: Record<string, string> = {}
  ): (typeof I18N_SEO_CONTENT)[SupportedLocale][PageType] {
    const content = this.getPageContent(page, locale);

    // 替换模板变量
    const processedContent = { ...content };

    if (variables.stockSymbol) {
      processedContent.title = processedContent.title.replace(
        /TradingAgents/g,
        `${variables.stockSymbol} - TradingAgents`
      );
      processedContent.description = processedContent.description.replace(
        /分析/g,
        `${variables.stockSymbol}分析`
      );
    }

    if (variables.taskTitle) {
      processedContent.title = `${variables.taskTitle} - ${processedContent.title}`;
    }

    return processedContent;
  }

  /**
   * 生成语言切换链接
   */
  static generateLanguageSwitchLinks(
    currentPath: string,
    currentLocale: SupportedLocale
  ): Array<{
    locale: SupportedLocale;
    label: string;
    href: string;
    active: boolean;
  }> {
    const { cleanPath } = LanguageDetector.detectFromPath(currentPath);

    return LANGUAGE_DETECTION_CONFIG.supportedLocales.map((locale) => {
      const prefix = HREFLANG_CONFIG.urlPatterns[locale];
      const href = `${prefix}${cleanPath === '/' ? '' : cleanPath}`;

      return {
        locale,
        label: locale === 'zh' ? '中文' : 'English',
        href,
        active: locale === currentLocale,
      };
    });
  }
}

// Note: All exports are already declared above with their respective classes and constants
