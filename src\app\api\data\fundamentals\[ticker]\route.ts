import { akshareAdapter } from '@/lib/akshare/adapter';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 获取基本面数据 API
 * GET /api/data/fundamentals/{ticker}?type=all&period=annual
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const { ticker } = await params;
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const type = searchParams.get('type') || 'all';
    const period = searchParams.get('period') || 'annual';

    console.log(`[基本面数据API] 获取基本面数据: ${ticker}, type: ${type}`);

    // 并行获取各种基本面数据
    const [financialRatios, balanceSheet, incomeStatement, cashFlow] = await Promise.allSettled([
      akshareAdapter.invoke('get_financial_data', {
        symbol: ticker,
        indicator: 'financial_analysis',
      }),
      akshareAdapter.invoke('get_financial_data', {
        symbol: ticker,
        indicator: 'balance_sheet',
      }),
      akshareAdapter.invoke('get_financial_data', {
        symbol: ticker,
        indicator: 'profit_sheet',
      }),
      akshareAdapter.invoke('get_financial_data', {
        symbol: ticker,
        indicator: 'cash_flow_sheet',
      }),
    ]);

    // 处理数据获取结果
    const fundamentalData = {
      financials: extractFinancialData(incomeStatement),
      valuation: extractValuationData(financialRatios),
      growth: extractGrowthData(financialRatios, incomeStatement),
      balanceSheet: extractBalanceSheetData(balanceSheet),
      cashFlow: extractCashFlowData(cashFlow),
      ratios: extractFinancialRatios(financialRatios),
    };

    // 根据type参数返回特定数据
    let responseData;
    switch (type) {
      case 'financials':
        responseData = { financials: fundamentalData.financials };
        break;
      case 'valuation':
        responseData = { valuation: fundamentalData.valuation };
        break;
      case 'growth':
        responseData = { growth: fundamentalData.growth };
        break;
      case 'balance_sheet':
        responseData = { balanceSheet: fundamentalData.balanceSheet };
        break;
      case 'cash_flow':
        responseData = { cashFlow: fundamentalData.cashFlow };
        break;
      case 'ratios':
        responseData = { ratios: fundamentalData.ratios };
        break;
      default:
        responseData = fundamentalData;
    }

    // 生成基本面分析摘要
    const analysis = generateFundamentalAnalysis(fundamentalData);

    return NextResponse.json({
      ticker,
      name: getStockName(ticker),
      period,
      fundamentals: responseData,
      analysis,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('[基本面数据API] 错误:', error);

    return NextResponse.json(
      {
        error: '获取基本面数据失败',
        ticker: (await params).ticker,
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

/**
 * 提取财务数据
 */
function extractFinancialData(incomeStatementResult: PromiseSettledResult<any>) {
  if (incomeStatementResult.status === 'rejected' || !incomeStatementResult.value) {
    return {
      revenue: null,
      netIncome: null,
      grossProfit: null,
      operatingIncome: null,
      eps: null,
      error: '利润表数据获取失败',
    };
  }

  const data = incomeStatementResult.value;
  const latestData = Array.isArray(data) ? data[0] : data;

  return {
    revenue: parseFinancialValue(latestData?.营业收入 || latestData?.revenue),
    netIncome: parseFinancialValue(latestData?.净利润 || latestData?.net_income),
    grossProfit: parseFinancialValue(latestData?.毛利润 || latestData?.gross_profit),
    operatingIncome: parseFinancialValue(latestData?.营业利润 || latestData?.operating_income),
    eps: parseFinancialValue(latestData?.每股收益 || latestData?.eps),
    operatingCosts: parseFinancialValue(latestData?.营业成本 || latestData?.operating_costs),
    totalCosts: parseFinancialValue(latestData?.营业总成本 || latestData?.total_costs),
  };
}

/**
 * 提取估值数据
 */
function extractValuationData(financialRatiosResult: PromiseSettledResult<any>) {
  if (financialRatiosResult.status === 'rejected' || !financialRatiosResult.value) {
    return {
      pe: null,
      pb: null,
      ps: null,
      pcf: null,
      error: '估值数据获取失败',
    };
  }

  const data = financialRatiosResult.value;
  const latestData = Array.isArray(data) ? data[0] : data;

  return {
    pe: parseFinancialValue(latestData?.市盈率 || latestData?.pe_ratio),
    pb: parseFinancialValue(latestData?.市净率 || latestData?.pb_ratio),
    ps: parseFinancialValue(latestData?.市销率 || latestData?.ps_ratio),
    pcf: parseFinancialValue(latestData?.市现率 || latestData?.pcf_ratio),
    ev: parseFinancialValue(latestData?.企业价值 || latestData?.enterprise_value),
    marketCap: parseFinancialValue(latestData?.总市值 || latestData?.market_cap),
  };
}

/**
 * 提取增长数据
 */
function extractGrowthData(
  financialRatiosResult: PromiseSettledResult<any>,
  incomeStatementResult: PromiseSettledResult<any>
) {
  const growth: {
    revenueGrowth: number | null;
    netIncomeGrowth: number | null;
    epsGrowth: number | null;
    operatingIncomeGrowth: number | null;
    error?: string | null;
  } = {
    revenueGrowth: null,
    netIncomeGrowth: null,
    epsGrowth: null,
    operatingIncomeGrowth: null,
    error: null,
  };

  // 尝试从财务比率数据中提取增长率
  if (financialRatiosResult.status === 'fulfilled' && financialRatiosResult.value) {
    const data = financialRatiosResult.value;
    const latestData = Array.isArray(data) ? data[0] : data;

    growth.revenueGrowth = parseFinancialValue(
      latestData?.营业收入增长率 || latestData?.revenue_growth
    );
    growth.netIncomeGrowth = parseFinancialValue(
      latestData?.净利润增长率 || latestData?.net_income_growth
    );
    growth.epsGrowth = parseFinancialValue(latestData?.每股收益增长率 || latestData?.eps_growth);
  }

  // 如果没有直接的增长率数据，尝试从历史数据计算
  if (incomeStatementResult.status === 'fulfilled' && incomeStatementResult.value) {
    const data = incomeStatementResult.value;
    if (Array.isArray(data) && data.length >= 2) {
      const current = data[0];
      const previous = data[1];

      if (!growth.revenueGrowth) {
        growth.revenueGrowth = calculateGrowthRate(
          parseFinancialValue(previous?.营业收入 || previous?.revenue),
          parseFinancialValue(current?.营业收入 || current?.revenue)
        );
      }

      if (!growth.netIncomeGrowth) {
        growth.netIncomeGrowth = calculateGrowthRate(
          parseFinancialValue(previous?.净利润 || previous?.net_income),
          parseFinancialValue(current?.净利润 || current?.net_income)
        );
      }
    }
  }

  return growth;
}

/**
 * 提取资产负债表数据
 */
function extractBalanceSheetData(balanceSheetResult: PromiseSettledResult<any>) {
  if (balanceSheetResult.status === 'rejected' || !balanceSheetResult.value) {
    return {
      totalAssets: null,
      totalLiabilities: null,
      shareholderEquity: null,
      error: '资产负债表数据获取失败',
    };
  }

  const data = balanceSheetResult.value;
  const latestData = Array.isArray(data) ? data[0] : data;

  return {
    totalAssets: parseFinancialValue(latestData?.资产总计 || latestData?.total_assets),
    totalLiabilities: parseFinancialValue(latestData?.负债合计 || latestData?.total_liabilities),
    shareholderEquity: parseFinancialValue(
      latestData?.股东权益合计 || latestData?.shareholder_equity
    ),
    currentAssets: parseFinancialValue(latestData?.流动资产合计 || latestData?.current_assets),
    currentLiabilities: parseFinancialValue(
      latestData?.流动负债合计 || latestData?.current_liabilities
    ),
    longTermDebt: parseFinancialValue(latestData?.长期借款 || latestData?.long_term_debt),
  };
}

/**
 * 提取现金流数据
 */
function extractCashFlowData(cashFlowResult: PromiseSettledResult<any>) {
  if (cashFlowResult.status === 'rejected' || !cashFlowResult.value) {
    return {
      operatingCashFlow: null,
      investingCashFlow: null,
      financingCashFlow: null,
      error: '现金流数据获取失败',
    };
  }

  const data = cashFlowResult.value;
  const latestData = Array.isArray(data) ? data[0] : data;

  return {
    operatingCashFlow: parseFinancialValue(
      latestData?.经营活动现金流量净额 || latestData?.operating_cash_flow
    ),
    investingCashFlow: parseFinancialValue(
      latestData?.投资活动现金流量净额 || latestData?.investing_cash_flow
    ),
    financingCashFlow: parseFinancialValue(
      latestData?.筹资活动现金流量净额 || latestData?.financing_cash_flow
    ),
    freeCashFlow: parseFinancialValue(latestData?.自由现金流 || latestData?.free_cash_flow),
    cashAndEquivalents: parseFinancialValue(
      latestData?.货币资金 || latestData?.cash_and_equivalents
    ),
  };
}

/**
 * 提取财务比率
 */
function extractFinancialRatios(financialRatiosResult: PromiseSettledResult<any>) {
  if (financialRatiosResult.status === 'rejected' || !financialRatiosResult.value) {
    return {
      roe: null,
      roa: null,
      grossMargin: null,
      error: '财务比率数据获取失败',
    };
  }

  const data = financialRatiosResult.value;
  const latestData = Array.isArray(data) ? data[0] : data;

  return {
    roe: parseFinancialValue(latestData?.净资产收益率 || latestData?.roe),
    roa: parseFinancialValue(latestData?.总资产收益率 || latestData?.roa),
    grossMargin: parseFinancialValue(latestData?.毛利率 || latestData?.gross_margin),
    netMargin: parseFinancialValue(latestData?.净利率 || latestData?.net_margin),
    operatingMargin: parseFinancialValue(latestData?.营业利润率 || latestData?.operating_margin),
    currentRatio: parseFinancialValue(latestData?.流动比率 || latestData?.current_ratio),
    quickRatio: parseFinancialValue(latestData?.速动比率 || latestData?.quick_ratio),
    debtToEquity: parseFinancialValue(latestData?.资产负债率 || latestData?.debt_to_equity),
  };
}

/**
 * 解析财务数值
 */
function parseFinancialValue(value: any): number | null {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : Number(value);
  return isNaN(numValue) ? null : numValue;
}

/**
 * 计算增长率
 */
function calculateGrowthRate(
  previousValue: number | null,
  currentValue: number | null
): number | null {
  if (!previousValue || !currentValue || previousValue === 0) {
    return null;
  }

  return ((currentValue - previousValue) / Math.abs(previousValue)) * 100;
}

/**
 * 生成基本面分析摘要
 */
function generateFundamentalAnalysis(fundamentalData: any) {
  const analysis: any = {
    overallRating: 'neutral',
    strengths: [],
    weaknesses: [],
    keyMetrics: {},
    recommendation: '',
  };

  const { financials, valuation, ratios, balanceSheet } = fundamentalData;

  // 盈利能力分析
  if (ratios.roe !== null) {
    analysis.keyMetrics.roe = ratios.roe;
    if (ratios.roe > 15) {
      analysis.strengths.push('净资产收益率较高，盈利能力强');
    } else if (ratios.roe < 8) {
      analysis.weaknesses.push('净资产收益率偏低，盈利能力有待提升');
    }
  }

  // 估值分析
  if (valuation.pe !== null) {
    analysis.keyMetrics.pe = valuation.pe;
    if (valuation.pe < 15) {
      analysis.strengths.push('市盈率较低，估值相对合理');
    } else if (valuation.pe > 30) {
      analysis.weaknesses.push('市盈率较高，估值可能偏贵');
    }
  }

  // 财务健康度分析
  if (ratios.currentRatio !== null) {
    if (ratios.currentRatio > 1.5) {
      analysis.strengths.push('流动比率健康，短期偿债能力强');
    } else if (ratios.currentRatio < 1) {
      analysis.weaknesses.push('流动比率偏低，短期偿债压力较大');
    }
  }

  // 盈利质量分析
  if (ratios.grossMargin !== null) {
    analysis.keyMetrics.grossMargin = ratios.grossMargin;
    if (ratios.grossMargin > 30) {
      analysis.strengths.push('毛利率较高，产品竞争力强');
    } else if (ratios.grossMargin < 15) {
      analysis.weaknesses.push('毛利率偏低，成本控制需要改善');
    }
  }

  // 增长性分析
  if (fundamentalData.growth.revenueGrowth !== null) {
    if (fundamentalData.growth.revenueGrowth > 10) {
      analysis.strengths.push('营收增长强劲，业务发展良好');
    } else if (fundamentalData.growth.revenueGrowth < 0) {
      analysis.weaknesses.push('营收出现负增长，需关注业务发展');
    }
  }

  // 综合评级
  const strengthCount = analysis.strengths.length;
  const weaknessCount = analysis.weaknesses.length;

  if (strengthCount > weaknessCount + 1) {
    analysis.overallRating = 'positive';
    analysis.recommendation = '基本面表现良好，建议关注';
  } else if (weaknessCount > strengthCount + 1) {
    analysis.overallRating = 'negative';
    analysis.recommendation = '基本面存在问题，需谨慎投资';
  } else {
    analysis.overallRating = 'neutral';
    analysis.recommendation = '基本面表现一般，建议进一步分析';
  }

  return analysis;
}

/**
 * 获取股票名称
 */
function getStockName(ticker: string): string {
  const stockNames: { [key: string]: string } = {
    '000001': '平安银行',
    '000002': '万科A',
    '600000': '浦发银行',
    '600036': '招商银行',
    '600519': '贵州茅台',
    '000858': '五粮液',
    '002415': '海康威视',
    '300059': '东方财富',
  };

  return stockNames[ticker] || ticker;
}
