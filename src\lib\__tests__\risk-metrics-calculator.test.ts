/**
 * 风险指标计算引擎测试
 * 验证所有风险指标计算的正确性
 */

import { PriceData, RiskMetricsCalculator } from '../risk-metrics-calculator';

describe('RiskMetricsCalculator', () => {
  let calculator: RiskMetricsCalculator;
  let mockPriceData: PriceData[];
  let mockMarketData: PriceData[];

  beforeEach(() => {
    calculator = RiskMetricsCalculator.getInstance();

    // 生成模拟价格数据（100个交易日）
    mockPriceData = [];
    mockMarketData = [];
    let price = 100;
    let marketPrice = 100;

    for (let i = 0; i < 100; i++) {
      const date = new Date(Date.now() - (100 - i) * 24 * 60 * 60 * 1000).toISOString();
      const dailyReturn = (Math.random() - 0.5) * 0.04; // ±2%随机波动
      const marketReturn = (Math.random() - 0.5) * 0.03; // ±1.5%市场波动

      price *= 1 + dailyReturn;
      marketPrice *= 1 + marketReturn;

      const volume = Math.floor(Math.random() * 1000000) + 100000;

      mockPriceData.push({
        date,
        open: price * 0.999,
        high: price * 1.002,
        low: price * 0.998,
        close: price,
        volume,
      });

      mockMarketData.push({
        date,
        open: marketPrice * 0.999,
        high: marketPrice * 1.002,
        low: marketPrice * 0.998,
        close: marketPrice,
        volume: volume * 10,
      });
    }
  });

  describe('基础风险指标计算', () => {
    test('应该计算完整的风险指标', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      expect(metrics).toHaveProperty('volatility');
      expect(metrics).toHaveProperty('var');
      expect(metrics).toHaveProperty('drawdown');
      expect(metrics).toHaveProperty('ratios');
      expect(metrics).toHaveProperty('liquidity');
      expect(metrics).toHaveProperty('correlation');
    });

    test('波动率计算应该合理', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.volatility.daily_volatility).toBeGreaterThan(0);
      expect(metrics.volatility.weekly_volatility).toBeGreaterThan(
        metrics.volatility.daily_volatility
      );
      expect(metrics.volatility.monthly_volatility).toBeGreaterThan(
        metrics.volatility.weekly_volatility
      );
      expect(metrics.volatility.annualized_volatility).toBeGreaterThan(
        metrics.volatility.monthly_volatility
      );
      expect(metrics.volatility.volatility_percentile).toBeGreaterThanOrEqual(0);
      expect(metrics.volatility.volatility_percentile).toBeLessThanOrEqual(100);
    });

    test('VaR计算应该合理', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.var.var_95_1d).toBeGreaterThan(0);
      expect(metrics.var.var_99_1d).toBeGreaterThan(metrics.var.var_95_1d);
      expect(metrics.var.var_95_10d).toBeGreaterThan(metrics.var.var_95_1d);
      expect(metrics.var.expected_shortfall_95).toBeGreaterThan(metrics.var.var_95_1d);
    });

    test('回撤指标应该合理', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.drawdown.max_drawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.max_drawdown_duration).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.current_drawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.recovery_time_estimate).toBeGreaterThanOrEqual(0);
      expect(metrics.drawdown.drawdown_frequency).toBeGreaterThanOrEqual(0);
    });

    test('风险比率应该合理', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData, mockMarketData);

      expect(typeof metrics.ratios.sharpe_ratio).toBe('number');
      expect(typeof metrics.ratios.sortino_ratio).toBe('number');
      expect(typeof metrics.ratios.calmar_ratio).toBe('number');
      expect(typeof metrics.ratios.beta).toBe('number');
      expect(typeof metrics.ratios.beta).toBe('number');
    });

    test('流动性指标应该合理', () => {
      const metrics = calculator.calculateRiskMetrics(mockPriceData);

      expect(metrics.liquidity.amihud_ratio).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.turnover_rate).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.market_depth_score).toBeGreaterThanOrEqual(0);
      expect(metrics.liquidity.market_depth_score).toBeLessThanOrEqual(100);
    });
  });

  describe('增强的Beta计算', () => {
    test('应该计算增强的Beta指标', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);
      const marketReturns = mockMarketData
        .slice(1)
        .map((data, i) => (data.close - mockMarketData[i].close) / mockMarketData[i].close);

      const betaMetrics = calculator.calculateEnhancedBeta(returns, marketReturns);

      expect(betaMetrics).toHaveProperty('beta');
      expect(betaMetrics).toHaveProperty('alpha');
      expect(betaMetrics).toHaveProperty('r_squared');
      expect(betaMetrics).toHaveProperty('tracking_error');
      expect(betaMetrics).toHaveProperty('up_beta');
      expect(betaMetrics).toHaveProperty('down_beta');
      expect(betaMetrics).toHaveProperty('beta_stability');

      expect(betaMetrics.r_squared).toBeGreaterThanOrEqual(0);
      expect(betaMetrics.r_squared).toBeLessThanOrEqual(1);
      expect(betaMetrics.tracking_error).toBeGreaterThanOrEqual(0);
      expect(betaMetrics.beta_stability).toBeGreaterThanOrEqual(0);
    });
  });

  describe('增强的VaR计算', () => {
    test('应该计算多种方法的VaR', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);

      const varMetrics = calculator.calculateEnhancedVaR(
        returns,
        [0.95, 0.99],
        ['historical', 'parametric', 'monte_carlo']
      );

      expect(varMetrics).toHaveProperty('historical_var');
      expect(varMetrics).toHaveProperty('parametric_var');
      expect(varMetrics).toHaveProperty('monte_carlo_var');
      expect(varMetrics).toHaveProperty('expected_shortfall');
      expect(varMetrics).toHaveProperty('var_backtesting');

      expect(varMetrics.historical_var.var_95).toBeGreaterThan(0);
      expect(varMetrics.historical_var.var_99).toBeGreaterThan(varMetrics.historical_var.var_95);
      expect(varMetrics.parametric_var.var_95).toBeGreaterThan(0);
      expect(varMetrics.monte_carlo_var?.var_95).toBeGreaterThan(0);

      expect(varMetrics.var_backtesting.violations).toBeGreaterThanOrEqual(0);
      expect(varMetrics.var_backtesting.violation_rate).toBeGreaterThanOrEqual(0);
      expect(varMetrics.var_backtesting.violation_rate).toBeLessThanOrEqual(1);
    });
  });

  describe('历史趋势分析', () => {
    test('应该计算风险指标的历史趋势', () => {
      const trends = calculator.calculateHistoricalTrends(mockPriceData, 30);

      expect(trends).toHaveProperty('volatility_trend');
      expect(trends).toHaveProperty('var_trend');
      expect(trends).toHaveProperty('drawdown_trend');
      expect(trends).toHaveProperty('beta_trend');
      expect(trends).toHaveProperty('sharpe_trend');
      expect(trends).toHaveProperty('dates');

      expect(trends.volatility_trend.length).toBeGreaterThan(0);
      expect(trends.var_trend.length).toBe(trends.volatility_trend.length);
      expect(trends.dates.length).toBe(trends.volatility_trend.length);

      // 验证趋势数据的合理性
      trends.volatility_trend.forEach((vol) => {
        expect(vol).toBeGreaterThan(0);
      });

      trends.var_trend.forEach((var_val) => {
        expect(var_val).toBeGreaterThan(0);
      });
    });
  });

  describe('集中度风险计算', () => {
    test('应该计算投资组合的集中度风险', () => {
      const positions = [
        {
          symbol: 'AAPL',
          weight: 0.4,
          returns: Array.from({ length: 50 }, () => (Math.random() - 0.5) * 0.04),
        },
        {
          symbol: 'GOOGL',
          weight: 0.3,
          returns: Array.from({ length: 50 }, () => (Math.random() - 0.5) * 0.05),
        },
        {
          symbol: 'MSFT',
          weight: 0.2,
          returns: Array.from({ length: 50 }, () => (Math.random() - 0.5) * 0.03),
        },
        {
          symbol: 'TSLA',
          weight: 0.1,
          returns: Array.from({ length: 50 }, () => (Math.random() - 0.5) * 0.08),
        },
      ];

      const concentrationRisk = calculator.calculateConcentrationRisk(positions);

      expect(concentrationRisk).toHaveProperty('herfindahl_index');
      expect(concentrationRisk).toHaveProperty('effective_number_of_positions');
      expect(concentrationRisk).toHaveProperty('max_weight');
      expect(concentrationRisk).toHaveProperty('top_5_concentration');
      expect(concentrationRisk).toHaveProperty('diversification_score');

      expect(concentrationRisk.herfindahl_index).toBeGreaterThan(0);
      expect(concentrationRisk.herfindahl_index).toBeLessThanOrEqual(1);
      expect(concentrationRisk.effective_number_of_positions).toBeGreaterThan(0);
      expect(concentrationRisk.max_weight).toBe(0.4);
      expect(concentrationRisk.top_5_concentration).toBeCloseTo(1.0, 10);
      expect(concentrationRisk.diversification_score).toBeGreaterThanOrEqual(0);
      expect(concentrationRisk.diversification_score).toBeLessThanOrEqual(100);
    });
  });

  describe('风险调整收益', () => {
    test('应该计算风险调整收益指标', () => {
      const returns = mockPriceData
        .slice(1)
        .map((data, i) => (data.close - mockPriceData[i].close) / mockPriceData[i].close);
      const benchmarkReturns = mockMarketData
        .slice(1)
        .map((data, i) => (data.close - mockMarketData[i].close) / mockMarketData[i].close);

      const riskAdjustedReturns = calculator.calculateRiskAdjustedReturns(
        returns,
        benchmarkReturns,
        0.03
      );

      expect(riskAdjustedReturns).toHaveProperty('sharpe_ratio');
      expect(riskAdjustedReturns).toHaveProperty('sortino_ratio');
      expect(riskAdjustedReturns).toHaveProperty('calmar_ratio');
      expect(riskAdjustedReturns).toHaveProperty('max_drawdown');
      expect(riskAdjustedReturns).toHaveProperty('volatility');
      expect(riskAdjustedReturns).toHaveProperty('var_95');
      expect(riskAdjustedReturns).toHaveProperty('expected_shortfall');

      expect(typeof riskAdjustedReturns.sharpe_ratio).toBe('number');
      expect(riskAdjustedReturns.volatility).toBeGreaterThan(0);
      expect(riskAdjustedReturns.var_95).toBeGreaterThan(0);
    });
  });

  describe('错误处理', () => {
    test('数据不足时应该抛出错误', () => {
      const insufficientData = mockPriceData.slice(0, 10);

      expect(() => {
        calculator.calculateRiskMetrics(insufficientData);
      }).toThrow('需要至少30个交易日的数据来计算风险指标');
    });

    test('历史趋势分析数据不足时应该抛出错误', () => {
      const insufficientData = mockPriceData.slice(0, 50);

      expect(() => {
        calculator.calculateHistoricalTrends(insufficientData, 30);
      }).toThrow('需要至少60个交易日的数据来计算历史趋势');
    });

    test('Beta计算时收益率长度不匹配应该抛出错误', () => {
      const returns1 = [0.01, 0.02, 0.03];
      const returns2 = [0.01, 0.02];

      expect(() => {
        calculator.calculateEnhancedBeta(returns1, returns2);
      }).toThrow('收益率序列长度必须相同');
    });
  });

  describe('单例模式', () => {
    test('应该返回同一个实例', () => {
      const instance1 = RiskMetricsCalculator.getInstance();
      const instance2 = RiskMetricsCalculator.getInstance();

      expect(instance1).toBe(instance2);
    });
  });
});
