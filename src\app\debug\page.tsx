'use client';

import useUserStore from '@/store/userStore';
import { useEffect, useState } from 'react';

export default function DebugPage() {
  const { user, loading, initialized, fetchUser } = useUserStore();
  const [apiCallCount, setApiCallCount] = useState(0);

  useEffect(() => {
    // 监听网络请求
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const url = args[0];
      if (typeof url === 'string' && url.includes('/api/auth/me')) {
        setApiCallCount(prev => prev + 1);
        console.log(`API call #${apiCallCount + 1} to /api/auth/me`);
      }
      return originalFetch(...args);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, [apiCallCount]);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">调试页面</h1>
      
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">用户状态</h2>
          <div className="space-y-2">
            <p><strong>用户:</strong> {user ? JSON.stringify(user) : '未登录'}</p>
            <p><strong>加载中:</strong> {loading ? '是' : '否'}</p>
            <p><strong>已初始化:</strong> {initialized ? '是' : '否'}</p>
            <p><strong>API调用次数:</strong> {apiCallCount}</p>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">操作</h2>
          <div className="space-x-2">
            <button
              onClick={fetchUser}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              手动获取用户
            </button>
            <button
              onClick={() => setApiCallCount(0)}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              重置计数
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">环境信息</h2>
          <div className="space-y-2">
            <p><strong>NODE_ENV:</strong> {process.env.NODE_ENV}</p>
            <p><strong>BYPASS_AUTH:</strong> {process.env.BYPASS_AUTH || '未设置'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}