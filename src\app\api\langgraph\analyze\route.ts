import { langGraphService } from '@/lib/langgraph-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { ticker, config, task_id: threadId } = await request.json();

    if (!ticker) {
      return NextResponse.json({ error: '股票代码不能为空' }, { status: 400 });
    }

    if (!threadId) {
      return NextResponse.json({ error: '线程ID不能为空' }, { status: 400 });
    }

    // 确保服务已初始化
    await langGraphService.initialize();

    console.log(`[API] Received analysis request for ticker: ${ticker} on thread: ${threadId}`);

    const result = await langGraphService.analyzeStock(threadId, ticker, config || {});

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('[API] Analysis failed:', error);
    return NextResponse.json(
      {
        error: '分析失败，请稍后重试',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
