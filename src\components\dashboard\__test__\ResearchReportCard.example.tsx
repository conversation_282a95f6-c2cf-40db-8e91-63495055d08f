// Example usage of ResearchReportCard component
// This file demonstrates how to use the ResearchReportCard component

import {
  ConsensusEvaluation,
  DebateUtterance,
  ResearchArgument,
  ResearchReport,
} from '@/types/langgraph-database';
import { ResearchReportCard } from '../ResearchReportCard';

// Example data for testing
const exampleResearchReports: ResearchReport[] = [
  {
    id: 1,
    report_id: 'bull-report-1',
    workflow_id: 'workflow-123',
    researcher_type: 'bull',
    summary:
      '基于强劲的财务表现和市场前景，我们认为该股票具有显著的上涨潜力。公司在核心业务领域保持领先地位，预计未来12个月将实现25%的收入增长。',
    confidence_level: 85,
    target_price: 150.0,
    time_horizon: '12个月',
    status: 'completed',
    created_at: new Date('2024-01-15T10:30:00Z'),
  },
  {
    id: 2,
    report_id: 'bear-report-1',
    workflow_id: 'workflow-123',
    researcher_type: 'bear',
    summary:
      '尽管公司基本面良好，但当前估值过高，市场风险增加。考虑到宏观经济不确定性和行业竞争加剧，建议谨慎投资。',
    confidence_level: 75,
    target_price: 95.0,
    time_horizon: '6个月',
    status: 'completed',
    created_at: new Date('2024-01-15T10:45:00Z'),
  },
];

const exampleResearchArguments: ResearchArgument[] = [
  {
    id: 1,
    argument_id: 'arg-1',
    report_id: 'bull-report-1',
    parent_argument_id: undefined,
    argument_type: 'main_argument',
    content:
      '公司Q4财报显示营收同比增长30%，净利润增长45%，远超市场预期。管理层上调了全年业绩指引。',
    source: '公司财报',
    strength_score: 0.9,
    sequence_order: 1,
    created_at: new Date('2024-01-15T10:32:00Z'),
  },
  {
    id: 2,
    argument_id: 'arg-2',
    report_id: 'bull-report-1',
    parent_argument_id: undefined,
    argument_type: 'evidence',
    content:
      '新产品线预计将在未来两年内贡献20%的收入增长，市场需求强劲，竞争对手尚未推出类似产品。',
    source: '行业分析报告',
    strength_score: 0.8,
    sequence_order: 2,
    created_at: new Date('2024-01-15T10:33:00Z'),
  },
  {
    id: 3,
    argument_id: 'arg-3',
    report_id: 'bear-report-1',
    parent_argument_id: undefined,
    argument_type: 'counter_argument',
    content: '当前P/E比率为45倍，远高于行业平均水平的25倍，估值泡沫明显。',
    source: '估值分析',
    strength_score: 0.85,
    sequence_order: 1,
    created_at: new Date('2024-01-15T10:47:00Z'),
  },
];

const exampleDebateUtterances: DebateUtterance[] = [
  {
    id: 1,
    utterance_id: 'utterance-1',
    round_id: 'round-1',
    participant_type: 'bull',
    utterance_type: 'statement',
    content:
      '公司的基本面非常强劲，Q4财报数据证明了管理层的执行能力。30%的营收增长在当前市场环境下是非常出色的表现。',
    rebuttal_to_utterance_id: undefined,
    related_argument_id: 'arg-1',
    sequence_in_round: 1,
    created_at: new Date('2024-01-15T11:00:00Z'),
  },
  {
    id: 2,
    utterance_id: 'utterance-2',
    round_id: 'round-1',
    participant_type: 'bear',
    utterance_type: 'rebuttal',
    content:
      '虽然财报数据亮眼，但我们不能忽视估值风险。45倍的P/E比率意味着市场已经充分反映了未来的增长预期，任何业绩不及预期都可能导致大幅回调。',
    rebuttal_to_utterance_id: 'utterance-1',
    related_argument_id: 'arg-3',
    sequence_in_round: 2,
    created_at: new Date('2024-01-15T11:05:00Z'),
  },
];

const exampleConsensusEvaluation: ConsensusEvaluation = {
  id: 1,
  consensus_id: 'consensus-1',
  workflow_id: 'workflow-123',
  bull_strength: 0.75,
  bear_strength: 0.65,
  consensus_direction: 'bullish',
  consensus_confidence: 70,
  synthesis_summary:
    '综合分析显示，尽管存在估值风险，但公司强劲的基本面和增长前景支撑了看涨观点。建议在合理价位分批建仓，设置止损位以控制风险。',
  key_agreement_points: ['公司基本面表现优秀', '新产品线具有增长潜力', '管理层执行能力强'],
  key_disagreement_points: [
    '当前估值水平的合理性',
    '市场风险对股价的影响程度',
    '竞争环境变化的影响',
  ],
  created_at: new Date('2024-01-15T11:30:00Z'),
};

// Example component usage
export function ResearchReportCardExample() {
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">研究报告展示示例</h1>

        <ResearchReportCard
          researchReports={exampleResearchReports}
          researchArguments={exampleResearchArguments}
          debateUtterances={exampleDebateUtterances}
          consensusEvaluation={exampleConsensusEvaluation}
        />
      </div>
    </div>
  );
}
