/**
 * Risk Comparison Chart Component
 * 风险对比图表组件
 */

'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { useRiskDataManager } from '@/hooks/useRiskDataStorage';
import { BarChart3, PieChart as PieChartIcon, Plus, Radar as RadarIcon, X } from 'lucide-react';
import { useState } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

interface RiskComparisonChartProps {
  className?: string;
}

/**
 * 风险对比图表组件
 * 需求 9.5: 实现风险数据的对比和趋势分析
 */
export function RiskComparisonChart({ className }: RiskComparisonChartProps) {
  const [workflowInput, setWorkflowInput] = useState('');
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'radar'>('bar');

  const {
    selectedWorkflows,
    comparisonResult,
    isComparing,
    addToComparison,
    removeFromComparison,
    clearComparison,
    performComparison,
  } = useRiskDataManager();

  // 添加工作流到对比列表
  const handleAddWorkflow = () => {
    if (workflowInput.trim()) {
      addToComparison(workflowInput.trim());
      setWorkflowInput('');
    }
  };

  // 执行对比分析
  const handleCompare = async () => {
    try {
      await performComparison();
    } catch (error) {
      console.error('Comparison failed:', error);
    }
  };

  // 风险等级颜色
  const RISK_COLORS = {
    low: '#10B981',
    medium: '#F59E0B',
    high: '#EF4444',
  };

  // 准备图表数据
  const chartData =
    comparisonResult?.comparisons?.map((comp: any) => ({
      workflow_id: comp.workflow_id.substring(0, 8) + '...',
      ticker: comp.ticker,
      risk_score: comp.risk_score,
      risk_level: comp.overall_risk_level,
      color: RISK_COLORS[comp.overall_risk_level as keyof typeof RISK_COLORS],
    })) || [];

  // 饼图数据
  const pieData = comparisonResult?.summary?.risk_level_distribution
    ? Object.entries(comparisonResult.summary.risk_level_distribution).map(([level, count]) => ({
        name: level === 'low' ? '低风险' : level === 'medium' ? '中风险' : '高风险',
        value: count,
        color: RISK_COLORS[level as keyof typeof RISK_COLORS],
      }))
    : [];

  // 雷达图数据
  const radarData =
    comparisonResult?.comparisons?.map((comp: any) => {
      const metrics = comp.key_metrics || {};
      return {
        workflow: comp.workflow_id.substring(0, 8),
        volatility: metrics.volatility || 0,
        beta: metrics.beta || 0,
        var_95: metrics.var_95 || 0,
        max_drawdown: Math.abs(metrics.max_drawdown || 0),
        sharpe_ratio: Math.max(0, metrics.sharpe_ratio || 0),
      };
    }) || [];

  return (
    <div className={className}>
      {/* 工作流选择器 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            风险对比分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="输入工作流ID"
              value={workflowInput}
              onChange={(e) => setWorkflowInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddWorkflow()}
            />
            <Button onClick={handleAddWorkflow} disabled={!workflowInput.trim()}>
              <Plus className="w-4 h-4 mr-2" />
              添加
            </Button>
          </div>

          {/* 已选择的工作流 */}
          {selectedWorkflows.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {selectedWorkflows.map((workflowId) => (
                  <Badge key={workflowId} variant="secondary" className="flex items-center gap-1">
                    {workflowId.substring(0, 12)}...
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0"
                      onClick={() => removeFromComparison(workflowId)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button onClick={handleCompare} disabled={selectedWorkflows.length < 2 || isComparing}>
              {isComparing ? '对比中...' : '开始对比'}
            </Button>
            <Button variant="secondary" onClick={clearComparison}>
              清空选择
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 对比结果 */}
      {comparisonResult && (
        <>
          {/* 汇总统计 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>对比汇总</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {comparisonResult.summary.avg_risk_score.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-600">平均风险评分</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {comparisonResult.summary.risk_score_range.min}
                  </div>
                  <div className="text-sm text-gray-600">最低风险评分</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {comparisonResult.summary.risk_score_range.max}
                  </div>
                  <div className="text-sm text-gray-600">最高风险评分</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 图表类型选择器 */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex gap-2">
                <Button
                  variant={chartType === 'bar' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setChartType('bar')}
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  柱状图
                </Button>
                <Button
                  variant={chartType === 'pie' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setChartType('pie')}
                >
                  <PieChartIcon className="w-4 h-4 mr-2" />
                  饼图
                </Button>
                <Button
                  variant={chartType === 'radar' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setChartType('radar')}
                >
                  <RadarIcon className="w-4 h-4 mr-2" />
                  雷达图
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 图表展示 */}
          <Card>
            <CardHeader>
              <CardTitle>
                {chartType === 'bar' && '风险评分对比'}
                {chartType === 'pie' && '风险等级分布'}
                {chartType === 'radar' && '风险指标雷达图'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                {chartType === 'bar' && (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="workflow_id" />
                      <YAxis domain={[0, 10]} />
                      <Tooltip
                        formatter={(value, name, props) => [`${value}/10`, '风险评分']}
                        labelFormatter={(label) => `工作流: ${label}`}
                      />
                      <Bar dataKey="risk_score" fill="#8884d8" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                )}

                {chartType === 'pie' && (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                )}

                {chartType === 'radar' && radarData.length > 0 && (
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={90} domain={[0, 'dataMax']} />
                      <Radar
                        name="风险指标"
                        dataKey="A"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.6}
                      />
                      <Tooltip />
                    </RadarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 详细对比表格 */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>详细对比</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">工作流ID</th>
                      <th className="text-left p-2">股票代码</th>
                      <th className="text-left p-2">风险等级</th>
                      <th className="text-left p-2">风险评分</th>
                      <th className="text-left p-2">创建时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparisonResult.comparisons.map((comp: any) => (
                      <tr key={comp.workflow_id} className="border-b hover:bg-gray-50">
                        <td className="p-2 font-mono text-xs">
                          {comp.workflow_id.substring(0, 12)}...
                        </td>
                        <td className="p-2 font-medium">{comp.ticker}</td>
                        <td className="p-2">
                          <Badge
                            className={
                              comp.overall_risk_level === 'low'
                                ? 'bg-green-100 text-green-800'
                                : comp.overall_risk_level === 'medium'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }
                          >
                            {comp.overall_risk_level === 'low'
                              ? '低风险'
                              : comp.overall_risk_level === 'medium'
                              ? '中风险'
                              : '高风险'}
                          </Badge>
                        </td>
                        <td className="p-2 font-bold">{comp.risk_score}/10</td>
                        <td className="p-2 text-gray-600">
                          {new Date(comp.created_at).toLocaleDateString('zh-CN')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
