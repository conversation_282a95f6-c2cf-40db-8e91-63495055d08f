import { query } from './db';

export interface User {
  id: number;
  email: string;
  username: string;
  password_hash: string;
  email_verified: boolean;
  avatar_url?: string;
  role: 'user' | 'admin';
  is_active: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserData {
  email: string;
  username: string;
  password_hash: string;
  verification_token?: string;
}

// 根据邮箱获取用户
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const rows = await query('SELECT * FROM users WHERE email = ? AND is_active = TRUE', [email]);
    const userRows = rows as User[];
    return userRows.length > 0 ? userRows[0] : null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

// 根据ID获取用户
export async function getUserById(id: number): Promise<User | null> {
  try {
    const rows = await query('SELECT * FROM users WHERE id = ? AND is_active = TRUE', [id]);
    const userRows = rows as User[];
    return userRows.length > 0 ? userRows[0] : null;
  } catch (error) {
    console.error('Error getting user by id:', error);
    return null;
  }
}

// 创建新用户
export async function createUser(userData: CreateUserData): Promise<number | null> {
  try {
    // 生成UUID作为user_id
    const { v4: uuidv4 } = require('uuid');
    const userId = uuidv4();

    const result = await query(
      `INSERT INTO users (user_id, email, username, password_hash, verification_token) 
       VALUES (?, ?, ?, ?, ?)`,
      [
        userId,
        userData.email,
        userData.username,
        userData.password_hash,
        userData.verification_token || null,
      ]
    );

    // 获取插入的ID
    const insertResult = result as any;
    return insertResult.insertId;
  } catch (error: any) {
    if (error.code === 'ER_DUP_ENTRY') {
      throw new Error('邮箱已被注册');
    }
    console.error('Error creating user:', error);
    throw new Error('创建用户失败');
  }
}

// 更新用户最后登录时间
export async function updateLastLogin(userId: number): Promise<void> {
  try {
    await query('UPDATE users SET last_login_at = NOW() WHERE id = ?', [userId]);
  } catch (error) {
    console.error('Error updating last login:', error);
  }
}

// 创建用户会话
export async function createUserSession(
  userId: number,
  sessionId: string,
  token: string,
  refreshToken: string,
  expiresAt: Date,
  refreshExpiresAt: Date,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await query(
      `INSERT INTO user_sessions 
       (session_id, user_id, token, refresh_token, expires_at, refresh_expires_at, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        sessionId,
        userId,
        token,
        refreshToken,
        expiresAt,
        refreshExpiresAt,
        ipAddress || null,
        userAgent || null,
      ]
    );
  } catch (error) {
    console.error('Error creating user session:', error);
    throw new Error('创建会话失败');
  }
}

// 获取有效会话
export async function getValidSession(sessionId: string): Promise<any | null> {
  try {
    const rows = await query(
      'SELECT * FROM user_sessions WHERE session_id = ? AND is_active = TRUE AND expires_at > NOW()',
      [sessionId]
    );
    const sessionRows = rows as any[];
    return sessionRows.length > 0 ? sessionRows[0] : null;
  } catch (error) {
    console.error('Error getting valid session:', error);
    return null;
  }
}

// 使会话失效
export async function invalidateSession(sessionId: string): Promise<void> {
  try {
    await query('UPDATE user_sessions SET is_active = FALSE WHERE session_id = ?', [sessionId]);
  } catch (error) {
    console.error('Error invalidating session:', error);
  }
}

// 使所有用户会话失效（登出所有设备）
export async function invalidateAllUserSessions(userId: number): Promise<void> {
  try {
    await query('UPDATE user_sessions SET is_active = FALSE WHERE user_id = ?', [userId]);
  } catch (error) {
    console.error('Error invalidating all user sessions:', error);
  }
}

// 记录用户活动
export async function logUserActivity(
  userId: number,
  activityType: string,
  ipAddress?: string,
  userAgent?: string,
  details?: any
): Promise<void> {
  try {
    await query(
      `INSERT INTO user_activity_logs 
       (user_id, activity_type, ip_address, user_agent, details) 
       VALUES (?, ?, ?, ?, ?)`,
      [
        userId,
        activityType,
        ipAddress || null,
        userAgent || null,
        details ? JSON.stringify(details) : null,
      ]
    );
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}

// 获取用户活动日志
export async function getUserActivityLogs(userId: number, limit: number = 10): Promise<any[]> {
  try {
    const rows = await query(
      `SELECT * FROM user_activity_logs 
       WHERE user_id = ? 
       ORDER BY created_at DESC 
       LIMIT ?`,
      [userId, limit]
    );
    return rows as any[];
  } catch (error) {
    console.error('Error getting user activity logs:', error);
    return [];
  }
}

// 更新用户邮箱验证状态
export async function verifyUserEmail(userId: number): Promise<void> {
  try {
    await query('UPDATE users SET email_verified = TRUE, verification_token = NULL WHERE id = ?', [
      userId,
    ]);
  } catch (error) {
    console.error('Error verifying user email:', error);
    throw new Error('邮箱验证失败');
  }
}

// 设置密码重置token
export async function setPasswordResetToken(
  userId: number,
  resetToken: string,
  expiresAt: Date
): Promise<void> {
  try {
    await query('UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?', [
      resetToken,
      expiresAt,
      userId,
    ]);
  } catch (error) {
    console.error('Error setting password reset token:', error);
    throw new Error('设置重置token失败');
  }
}

// 根据重置token获取用户
export async function getUserByResetToken(resetToken: string): Promise<User | null> {
  try {
    const rows = await query(
      'SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW() AND is_active = TRUE',
      [resetToken]
    );
    const userRows = rows as User[];
    return userRows.length > 0 ? userRows[0] : null;
  } catch (error) {
    console.error('Error getting user by reset token:', error);
    return null;
  }
}

// 更新用户密码
export async function updateUserPassword(userId: number, newPasswordHash: string): Promise<void> {
  try {
    await query(
      'UPDATE users SET password_hash = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
      [newPasswordHash, userId]
    );
  } catch (error) {
    console.error('Error updating user password:', error);
    throw new Error('更新密码失败');
  }
}
