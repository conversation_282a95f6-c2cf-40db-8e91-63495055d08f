{"mcpServers": {"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "mysql": {"command": "node", "args": ["d:/project/mysql-mcp/dist/index.js"], "env": {"MYSQL_HOST": "localhost", "MYSQL_PORT": "13306", "MYSQL_USER": "root", "MYSQL_PASS": "trading123", "MYSQL_DB": "trading_analysis"}, "disabled": false}, "mcp-deepwiki": {"type": "stdio", "command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}}}}