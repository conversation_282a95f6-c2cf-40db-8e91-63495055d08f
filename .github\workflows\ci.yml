# TradingAgents Frontend 统一 CI/CD Pipeline
# 集成代码质量检查、Docker构建、安全扫描和自动部署
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    tags: ['v*']
  pull_request:
    branches: [main, develop]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: '强制部署（跳过检查）'
        required: false
        default: false
        type: boolean
      push_to_registry:
        description: '是否推送到镜像仓库'
        required: false
        default: true
        type: boolean
      deploy_services:
        description: '部署的服务'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - frontend
          - backend

permissions:
  contents: read
  security-events: write
  packages: write
  pull-requests: write

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  # 阿里云容器镜像服务
  ALIYUN_REGISTRY: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com
  ALIYUN_NAMESPACE: ez_trading
  FRONTEND_IMAGE_NAME: frontend
  BACKEND_IMAGE_NAME: akshare-backend
  DOCKER_BUILDKIT: 1
  # 部署环境URL
  DEPLOY_URL: ${{ secrets.DEPLOY_URL }}
  API_URL: ${{ secrets.API_URL }}

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest

    outputs:
      cache-hit: ${{ steps.cache.outputs.cache-hit }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整历史用于分析

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 缓存依赖
        id: cache
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: 安装依赖
        run: npm ci

      - name: ESLint 代码检查
        run: npm run lint
        continue-on-error: false

      - name: TypeScript 类型检查
        run: npm run type-check

  # 后端代码质量检查
  backend-quality:
    name: 后端代码质量检查
    runs-on: ubuntu-latest

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: 安装后端依赖
        working-directory: ./backend/akshare-service
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install flake8 black isort mypy

      - name: Python 代码格式检查 (Black)
        working-directory: ./backend/akshare-service
        run: black --check --diff app/
        continue-on-error: true

      - name: Python 导入排序检查 (isort)
        working-directory: ./backend/akshare-service
        run: isort --check-only --diff app/
        continue-on-error: true

      - name: Python 代码风格检查 (Flake8)
        working-directory: ./backend/akshare-service
        run: flake8 app/ --max-line-length=88 --extend-ignore=E203,W503
        continue-on-error: true

      - name: Python 类型检查 (MyPy)
        working-directory: ./backend/akshare-service
        run: mypy app/ --ignore-missing-imports
        continue-on-error: true

  # 单元测试（预留）
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code-quality
    if: false # 暂时禁用，等添加测试后启用

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 运行测试
        run: npm test -- --coverage

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        if: always()
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # 前端 Docker 构建和推送
  build-frontend:
    name: 构建前端镜像
    needs: [code-quality]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    outputs:
      image-tag: ${{ steps.set-image-tag.outputs.image-tag }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: 登录到阿里云容器镜像服务
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 提取前端镜像元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.FRONTEND_IMAGE_NAME }}
          tags: |
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=staging,enable=${{ github.ref == 'refs/heads/develop' }}
            type=ref,event=branch,enable=${{ github.ref != 'refs/heads/main' && github.ref != 'refs/heads/develop' }}
          labels: |
            org.opencontainers.image.title=TradingAgents Frontend
            org.opencontainers.image.description=多智能体大语言模型金融交易框架前端
            org.opencontainers.image.vendor=TradingAgents
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.documentation=https://github.com/${{ github.repository }}/blob/main/README.md

      - name: 构建并推送前端 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          platforms: 'linux/amd64'
          push: ${{ github.event_name != 'pull_request' && (github.event.inputs.push_to_registry != 'false') }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=frontend
          cache-to: type=gha,mode=max,scope=frontend
          build-args: |
            NODE_ENV=production
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VCS_REF=${{ github.sha }}

      - name: 设置镜像标签输出
        id: set-image-tag
        run: |
          if [ "${{ github.ref }}" == "refs/heads/main" ]; then
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.FRONTEND_IMAGE_NAME }}:latest"
          elif [ "${{ github.ref }}" == "refs/heads/develop" ]; then
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.FRONTEND_IMAGE_NAME }}:staging"
          else
            BRANCH_NAME=$(echo "${{ github.ref }}" | sed 's/refs\/heads\///')
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.FRONTEND_IMAGE_NAME }}:${BRANCH_NAME}"
          fi
          echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "前端镜像标签: $IMAGE_TAG"

      - name: 前端镜像安全扫描
        if: github.event_name != 'pull_request'
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.set-image-tag.outputs.image-tag }}
          format: 'sarif'
          output: 'frontend-security-results.sarif'

  # 后端 Docker 构建和推送
  build-backend:
    name: 构建后端镜像
    needs: [backend-quality]
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    outputs:
      image-tag: ${{ steps.set-image-tag.outputs.image-tag }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host

      - name: 登录到阿里云容器镜像服务
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 提取后端镜像元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.BACKEND_IMAGE_NAME }}
          tags: |
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=staging,enable=${{ github.ref == 'refs/heads/develop' }}
            type=ref,event=branch,enable=${{ github.ref != 'refs/heads/main' && github.ref != 'refs/heads/develop' }}
          labels: |
            org.opencontainers.image.title=TradingAgents AKShare Backend
            org.opencontainers.image.description=多智能体大语言模型金融交易框架后端数据服务
            org.opencontainers.image.vendor=TradingAgents
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.documentation=https://github.com/${{ github.repository }}/blob/main/backend/akshare-service/README.md

      - name: 构建并推送后端 Docker 镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./backend/akshare-service
          file: ./backend/akshare-service/Dockerfile
          platforms: 'linux/amd64'
          push: ${{ github.event_name != 'pull_request' && (github.event.inputs.push_to_registry != 'false') }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=backend
          cache-to: type=gha,mode=max,scope=backend
          build-args: |
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VCS_REF=${{ github.sha }}

      - name: 设置镜像标签输出
        id: set-image-tag
        run: |
          if [ "${{ github.ref }}" == "refs/heads/main" ]; then
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.BACKEND_IMAGE_NAME }}:latest"
          elif [ "${{ github.ref }}" == "refs/heads/develop" ]; then
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.BACKEND_IMAGE_NAME }}:staging"
          else
            BRANCH_NAME=$(echo "${{ github.ref }}" | sed 's/refs\/heads\///')
            IMAGE_TAG="${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.BACKEND_IMAGE_NAME }}:${BRANCH_NAME}"
          fi
          echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "后端镜像标签: $IMAGE_TAG"

      - name: 后端镜像安全扫描
        if: github.event_name != 'pull_request'
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ steps.set-image-tag.outputs.image-tag }}
          format: 'sarif'
          output: 'backend-security-results.sarif'

      - name: 生成镜像清单
        if: github.event_name != 'pull_request'
        run: |
          echo "## Docker 镜像信息" >> $GITHUB_STEP_SUMMARY
          echo "### 前端镜像" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像仓库**: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.FRONTEND_IMAGE_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo "### 后端镜像" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像仓库**: ${{ env.ALIYUN_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ env.BACKEND_IMAGE_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像标签**: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像摘要**: ${{ steps.build.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: $(date -u +"%Y-%m-%dT%H:%M:%SZ")" >> $GITHUB_STEP_SUMMARY
          echo "- **Git SHA**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY

  # 镜像测试
  test-images:
    name: 测试 Docker 镜像
    runs-on: ubuntu-latest
    needs: [build-frontend, build-backend]
    if: github.event_name != 'pull_request'

    steps:
      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到阿里云注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ALIYUN_REGISTRY }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}

      - name: 测试前端镜像启动
        run: |
          # 使用构建步骤输出的镜像标签
          FRONTEND_IMAGE_TAG="${{ needs.build-frontend.outputs.image-tag }}"
          echo "测试前端镜像: $FRONTEND_IMAGE_TAG"

          # 运行前端容器并检查健康状态
          docker run --rm -d --name test-frontend-container \
            -p 3000:3000 \
            -e NODE_ENV=production \
            -e NEXT_PUBLIC_API_BASE_URL=http://localhost:5000 \
            "$FRONTEND_IMAGE_TAG"

          # 等待容器启动
          echo "等待前端容器启动..."
          sleep 45

          # 检查容器是否正在运行
          if docker ps --filter "name=test-frontend-container" --format "table {{.Names}}" | grep -q test-frontend-container; then
            echo "✅ 前端容器启动成功"
            
            # 显示容器日志以便调试
            echo "容器日志:"
            docker logs test-frontend-container --tail 20
            
            # 测试前端服务 (增加重试机制)
            echo "测试前端服务..."
            for i in {1..3}; do
              if curl -f -s --connect-timeout 15 --max-time 30 http://localhost:3000/ > /dev/null 2>&1; then
                echo "✅ 前端服务响应正常 (第${i}次尝试)"
                break
              else
                echo "⚠️ 前端服务测试失败 (第${i}次尝试)，等待15秒后重试..."
                sleep 15
              fi
              
              if [ $i -eq 3 ]; then
                echo "⚠️ 前端服务最终测试失败，但容器正在运行"
                echo "完整容器日志:"
                docker logs test-frontend-container
              fi
            done
          else
            echo "❌ 前端容器启动失败"
            echo "容器状态:"
            docker ps -a --filter "name=test-frontend-container"
            echo "容器日志:"
            docker logs test-frontend-container 2>&1 || echo "无法获取容器日志"
            exit 1
          fi

          # 清理
          docker stop test-frontend-container 2>/dev/null || echo "容器已停止"

      - name: 测试后端镜像启动
        run: |
          # 使用构建步骤输出的镜像标签
          BACKEND_IMAGE_TAG="${{ needs.build-backend.outputs.image-tag }}"
          echo "测试后端镜像: $BACKEND_IMAGE_TAG"

          # 运行后端容器并检查健康状态
          docker run --rm -d --name test-backend-container \
            -p 5000:5000 \
            -e DB_HOST=localhost \
            -e DB_PORT=3306 \
            -e DB_USER=root \
            -e DB_PASSWORD=test_password \
            -e DB_NAME=trading_analysis \
            "$BACKEND_IMAGE_TAG"

          # 等待容器启动 (增加等待时间)
          echo "等待后端容器启动..."
          sleep 60

          # 检查容器是否正在运行
          if docker ps --filter "name=test-backend-container" --format "table {{.Names}}" | grep -q test-backend-container; then
            echo "✅ 后端容器启动成功"
            
            # 显示容器日志以便调试
            echo "容器日志:"
            docker logs test-backend-container --tail 20
            
            # 测试健康检查端点 (增加重试机制)
            echo "测试健康检查端点..."
            for i in {1..5}; do
              if curl -f -s --connect-timeout 10 --max-time 30 http://localhost:5000/health > /dev/null 2>&1; then
                echo "✅ 后端健康检查通过 (第${i}次尝试)"
                break
              else
                echo "⚠️ 后端健康检查失败 (第${i}次尝试)，等待10秒后重试..."
                sleep 10
              fi
              
              if [ $i -eq 5 ]; then
                echo "⚠️ 后端健康检查最终失败，但容器正在运行"
                # 显示更多日志用于调试
                echo "完整容器日志:"
                docker logs test-backend-container
              fi
            done
          else
            echo "❌ 后端容器启动失败"
            echo "容器状态:"
            docker ps -a --filter "name=test-backend-container"
            echo "容器日志:"
            docker logs test-backend-container 2>&1 || echo "无法获取容器日志"
            exit 1
          fi

          # 清理
          docker stop test-backend-container 2>/dev/null || echo "容器已停止"

  # 部署到生产环境
  deploy:
    name: 部署应用
    needs: [build-frontend, build-backend, test-images]
    runs-on: ubuntu-latest
    if: |
      (github.ref == 'refs/heads/main') ||
      (github.ref == 'refs/heads/develop') ||
      (github.event_name == 'workflow_dispatch')
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
      url: ${{ env.DEPLOY_URL }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置部署环境变量
        run: |
          if [ "${{ github.ref }}" == "refs/heads/main" ]; then
            echo "DEPLOY_ENV=production" >> $GITHUB_ENV
            echo "IMAGE_TAG=latest" >> $GITHUB_ENV
          else
            echo "DEPLOY_ENV=staging" >> $GITHUB_ENV
            echo "IMAGE_TAG=staging" >> $GITHUB_ENV
          fi

          # 根据部署服务选择配置文件
          DEPLOY_SERVICES="${{ github.event.inputs.deploy_services || 'all' }}"
          case "$DEPLOY_SERVICES" in
            "frontend")
              echo "COMPOSE_FILE=docker/docker-compose.prod.frontend.yml" >> $GITHUB_ENV
              echo "DEPLOY_SERVICES=frontend" >> $GITHUB_ENV
              ;;
            "backend")
              echo "COMPOSE_FILE=docker/docker-compose.prod.backend.yml" >> $GITHUB_ENV
              echo "DEPLOY_SERVICES=backend" >> $GITHUB_ENV
              ;;
            *)
              echo "COMPOSE_FILE=docker/docker-compose.prod.yml" >> $GITHUB_ENV
              echo "DEPLOY_SERVICES=all" >> $GITHUB_ENV
              ;;
          esac

      - name: 准备部署配置
        run: |
          # 创建环境配置文件
          cat > .env.production << 'ENVEOF'
          NODE_ENV=production
          NEXT_PUBLIC_API_BASE_URL=${{ env.API_URL }}
          NEXT_PUBLIC_API_BACKEND_BASE_URL=${{ env.API_URL }}
          NEXT_PUBLIC_WS_URL=${{ secrets.NEXT_PUBLIC_WS_URL }}
          NEXT_PUBLIC_OPENAI_API_KEY=${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}
          NEXT_PUBLIC_FINNHUB_API_KEY=${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}
          BACK_END_URL=${{ env.API_URL }}
          AKSHARE_API_URL=${{ env.API_URL }}
          MYSQL_ROOT_PASSWORD=${{ secrets.MYSQL_ROOT_PASSWORD }}
          MYSQL_DATABASE=trading_analysis
          MYSQL_USER=trading_user
          MYSQL_PASSWORD=${{ secrets.MYSQL_PASSWORD }}
          ENVEOF

          echo "✅ .env.production 文件已创建"
          echo "文件大小: $(wc -c < .env.production) bytes"

      - name: 设置SSH密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 部署应用到服务器
        run: |
          echo "🚀 开始部署到 ${{ env.DEPLOY_ENV }} 环境..."

          # 验证本地文件存在
          if [ ! -f .env.production ]; then
            echo "❌ 本地 .env.production 文件不存在"
            exit 1
          fi

          if [ ! -f ${{ env.COMPOSE_FILE }} ]; then
            echo "❌ 本地 ${{ env.COMPOSE_FILE }} 文件不存在"
            exit 1
          fi

          # 设置部署路径
          DEPLOY_PATH="${{ secrets.DEPLOY_PATH }}"
          if [ -z "$DEPLOY_PATH" ]; then
            DEPLOY_PATH="/root"
            echo "⚠️ DEPLOY_PATH 未设置，使用默认路径: $DEPLOY_PATH"
          fi

          # 上传配置文件到服务器
          echo "📤 上传配置文件到服务器..."
          scp -i ~/.ssh/deploy_key .env.production ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/
          scp -i ~/.ssh/deploy_key ${{ env.COMPOSE_FILE }} ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:$DEPLOY_PATH/

          # SSH连接到服务器执行部署
          ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} << 'EOF'
            cd ${{ secrets.DEPLOY_PATH }}

            echo "1. 登录阿里云镜像服务..."
            echo "${{ secrets.ALIYUN_REGISTRY_PASSWORD }}" | docker login --username ${{ secrets.ALIYUN_REGISTRY_USERNAME }} --password-stdin ${{ env.ALIYUN_REGISTRY }}

            echo "2. 确保Docker网络存在..."
            docker network create tradingagents-network 2>/dev/null || echo "网络已存在或创建失败，继续部署..."

            echo "3. 拉取最新镜像..."
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production pull

            echo "4. 停止旧服务..."
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production stop || true
            docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production rm -f || true

            echo "5. 清理相关资源..."
            docker container ls -a --filter "name=tradingagents-" --format "{{.ID}}" | xargs -r docker rm -f || true

            echo "6. 启动服务..."
            if ! docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production up -d; then
              echo "❌ 容器启动失败，尝试强制重建..."
              docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) --env-file .env.production up -d --force-recreate
            fi

            echo "7. 验证容器网络连接..."
            sleep 10
            if docker ps | grep -q "tradingagents-frontend"; then
              echo "检查前端容器网络连接..."
              docker exec tradingagents-frontend ping -c 2 tradingagents-akshare-backend || echo "⚠️ 前端无法ping通后端"
            fi
            
            if docker ps | grep -q "tradingagents-akshare-backend"; then
              echo "检查后端容器网络连接..."
              docker exec tradingagents-akshare-backend ping -c 2 tradingagents-frontend || echo "⚠️ 后端无法ping通前端"
            fi

            echo "8. 清理旧镜像..."
            docker image prune -f

            echo "✅ 部署完成"
          EOF

      - name: 健康检查
        run: |
          echo "🔍 执行健康检查..."
          sleep 60  # 给应用充分的启动时间

          # 检查容器状态
          echo "检查容器运行状态..."
          FRONTEND_STATUS=$(ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
            "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) ps frontend | grep -c 'Up'" 2>/dev/null || echo "0")

          BACKEND_STATUS=$(ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
            "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) ps akshare-backend | grep -c 'Up'" 2>/dev/null || echo "0")

          if [ "${FRONTEND_STATUS}" -gt 0 ] && [ "${BACKEND_STATUS}" -gt 0 ]; then
            echo "✅ 前端和后端容器运行正常"
            
            # 检查前端HTTP服务
            echo "检查前端HTTP服务..."
            if curl -f -s --connect-timeout 15 --max-time 45 ${{ env.DEPLOY_URL }}/ > /dev/null 2>&1; then
              echo "✅ 前端HTTP服务正常"
            else
              echo "⚠️ 前端HTTP服务检查失败，但容器正在运行"
            fi
            
            # 检查后端API服务
            echo "检查后端API服务..."
            if curl -f -s --connect-timeout 15 --max-time 45 ${{ env.API_URL }}/health > /dev/null 2>&1; then
              echo "✅ 后端API服务正常"
            else
              echo "⚠️ 后端API服务检查失败，但容器正在运行"
            fi
            
            echo "✅ 部署验证完成"
          else
            echo "❌ 部分容器未正常运行"
            echo "前端状态: ${FRONTEND_STATUS}, 后端状态: ${BACKEND_STATUS}"
            echo "查看服务日志:"
            ssh -i ~/.ssh/deploy_key ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} \
              "cd ${{ secrets.DEPLOY_PATH }} && docker-compose -f $(basename ${{ env.COMPOSE_FILE }}) logs --tail=50"
          fi

      - name: 通知部署结果
        if: always()
        run: |
          echo "## 部署结果 📋" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ **应用部署成功**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "- **环境**: ${{ env.DEPLOY_ENV }}" >> $GITHUB_STEP_SUMMARY
            echo "- **部署服务**: ${{ env.DEPLOY_SERVICES }}" >> $GITHUB_STEP_SUMMARY
            echo "- **前端地址**: [${{ env.DEPLOY_URL }}](${{ env.DEPLOY_URL }})" >> $GITHUB_STEP_SUMMARY
            echo "- **后端API**: [${{ env.API_URL }}](${{ env.API_URL }})" >> $GITHUB_STEP_SUMMARY
            echo "- **镜像标签**: ${{ env.IMAGE_TAG }}" >> $GITHUB_STEP_SUMMARY
            echo "- **部署时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 快速链接 🔗" >> $GITHUB_STEP_SUMMARY
            echo "- [前端应用](${{ env.DEPLOY_URL }})" >> $GITHUB_STEP_SUMMARY
            echo "- [后端API健康检查](${{ env.API_URL }}/health)" >> $GITHUB_STEP_SUMMARY
            echo "- [后端API文档](${{ env.API_URL }}/docs)" >> $GITHUB_STEP_SUMMARY
            
            echo "✅ 应用部署成功到 ${{ env.DEPLOY_ENV }} 环境"
            echo "🔗 访问地址: ${{ env.DEPLOY_URL }}"
          else
            echo "❌ **应用部署失败**" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "请检查上述日志获取详细错误信息。" >> $GITHUB_STEP_SUMMARY
            
            echo "❌ 应用部署失败"
            echo "请检查 GitHub Actions 日志获取详细信息"
          fi

  # 性能测试
  performance-test:
    needs: build-frontend
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
