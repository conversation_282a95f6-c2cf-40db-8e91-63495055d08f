/**
 * 风险报告生成器测试
 * Risk Report Generator Tests
 */

import { AlertLevel, RiskLevel, RiskType } from '@/types/risk-report';
import { riskReportGenerator } from '../risk-report-generator';

// Mock dependencies
jest.mock('../risk-metrics-calculator', () => ({
  riskMetricsCalculator: {
    calculateMarketRisk: jest.fn().mockResolvedValue({
      volatility: 0.25,
      beta: 1.2,
      var: -0.08,
      maxDrawdown: -0.15,
    }),
    calculateLiquidityRisk: jest.fn().mockResolvedValue({
      volume: 500000,
      spread: 0.005,
      amihudRatio: 0.0005,
    }),
    calculateCreditRisk: jest.fn().mockResolvedValue({
      debtRatio: 0.4,
      currentRatio: 1.8,
      interestCoverage: 3.5,
    }),
  },
}));

jest.mock('../risk-scenario-analyzer', () => ({
  riskScenarioAnalyzer: {
    analyzeScenarios: jest.fn().mockResolvedValue([
      {
        scenario: '市场下跌10%',
        probability: 0.2,
        potentialLoss: -1000,
        potentialLossPercentage: -10,
        timeHorizon: '1个月',
        description: '市场整体下跌10%的情况下预期损失',
        mitigationStrategies: ['设置止损位', '分散投资'],
      },
    ]),
  },
}));

jest.mock('../risk-control-recommender', () => ({
  riskControlRecommender: {
    generateRecommendations: jest.fn().mockResolvedValue([
      {
        category: 'position',
        priority: 'medium',
        action: '适度控制仓位',
        description: '当前风险水平适中，建议适度控制仓位',
        expectedImpact: '降低投资风险',
        implementation: ['控制仓位在70%以下', '设置止损位'],
      },
    ]),
  },
}));

describe('RiskReportGenerator', () => {
  const mockMarketData = {
    symbol: 'AAPL',
    prices: [150, 152, 148, 155, 153],
    volumes: [1000000, 1200000, 800000, 1500000, 1100000],
    timestamps: ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateRiskReport', () => {
    it('应该生成完整的风险报告', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report).toBeDefined();
      expect(report.symbol).toBe('AAPL');
      expect(report.analysisId).toBe('analysis_123');
      expect(report.id).toMatch(/^risk_report_/);
      expect(report.timestamp).toBeInstanceOf(Date);
    });

    it('应该包含综合风险评估', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.overallRisk).toBeDefined();
      expect(Object.values(RiskLevel)).toContain(report.overallRisk.level);
      expect(report.overallRisk.score).toBeGreaterThanOrEqual(0);
      expect(report.overallRisk.score).toBeLessThanOrEqual(100);
      expect(report.overallRisk.confidence).toBeGreaterThanOrEqual(0);
      expect(report.overallRisk.confidence).toBeLessThanOrEqual(100);
      expect(report.overallRisk.summary).toBeTruthy();
    });

    it('应该包含各类风险评估', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.riskAssessments).toBeInstanceOf(Array);
      expect(report.riskAssessments.length).toBeGreaterThan(0);

      const marketRisk = report.riskAssessments.find((r) => r.type === RiskType.MARKET);
      expect(marketRisk).toBeDefined();
      expect(Object.values(RiskLevel)).toContain(marketRisk?.level);
      expect(marketRisk?.score).toBeGreaterThanOrEqual(0);
      expect(marketRisk?.score).toBeLessThanOrEqual(100);
      expect(marketRisk?.description).toBeTruthy();
      expect(marketRisk?.factors).toBeInstanceOf(Array);
      expect(marketRisk?.recommendations).toBeInstanceOf(Array);
    });

    it('应该包含关键风险指标', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.keyMetrics).toBeInstanceOf(Array);
      expect(report.keyMetrics.length).toBeGreaterThan(0);

      const metric = report.keyMetrics[0];
      expect(metric.name).toBeTruthy();
      expect(typeof metric.value).toBe('number');
      expect(metric.description).toBeTruthy();
    });

    it('应该生成风险预警', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.alerts).toBeInstanceOf(Array);

      if (report.alerts.length > 0) {
        const alert = report.alerts[0];
        expect(alert.id).toBeTruthy();
        expect(Object.values(AlertLevel)).toContain(alert.level);
        expect(Object.values(RiskType)).toContain(alert.type);
        expect(alert.title).toBeTruthy();
        expect(alert.message).toBeTruthy();
        expect(alert.timestamp).toBeInstanceOf(Date);
        expect(alert.isActive).toBe(true);
        expect(alert.recommendations).toBeInstanceOf(Array);
      }
    });

    it('应该包含风险控制建议', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.recommendations).toBeInstanceOf(Array);
      expect(report.recommendations.length).toBeGreaterThan(0);

      const recommendation = report.recommendations[0];
      expect(['position', 'timing', 'hedging', 'monitoring']).toContain(recommendation.category);
      expect(['high', 'medium', 'low']).toContain(recommendation.priority);
      expect(recommendation.action).toBeTruthy();
      expect(recommendation.description).toBeTruthy();
      expect(recommendation.expectedImpact).toBeTruthy();
      expect(recommendation.implementation).toBeInstanceOf(Array);
    });

    it('应该包含场景分析结果', async () => {
      const config = {
        includeScenarioAnalysis: true,
        includeHistoricalComparison: false,
        alertThresholds: {
          [RiskType.MARKET]: 70,
          [RiskType.LIQUIDITY]: 60,
          [RiskType.CREDIT]: 75,
          [RiskType.OPERATIONAL]: 80,
          [RiskType.SYSTEMATIC]: 70,
        },
        confidenceThreshold: 70,
        timeHorizon: '1m' as const,
      };

      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData,
        config
      );

      expect(report.scenarioAnalysis).toBeInstanceOf(Array);
      expect(report.scenarioAnalysis.length).toBeGreaterThan(0);

      const scenario = report.scenarioAnalysis[0];
      expect(scenario.scenario).toBeTruthy();
      expect(scenario.probability).toBeGreaterThanOrEqual(0);
      expect(scenario.probability).toBeLessThanOrEqual(1);
      expect(typeof scenario.potentialLoss).toBe('number');
      expect(typeof scenario.potentialLossPercentage).toBe('number');
      expect(scenario.timeHorizon).toBeTruthy();
      expect(scenario.description).toBeTruthy();
      expect(scenario.mitigationStrategies).toBeInstanceOf(Array);
    });

    it('应该包含历史对比数据', async () => {
      const config = {
        includeScenarioAnalysis: false,
        includeHistoricalComparison: true,
        alertThresholds: {
          [RiskType.MARKET]: 70,
          [RiskType.LIQUIDITY]: 60,
          [RiskType.CREDIT]: 75,
          [RiskType.OPERATIONAL]: 80,
          [RiskType.SYSTEMATIC]: 70,
        },
        confidenceThreshold: 70,
        timeHorizon: '1m' as const,
      };

      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData,
        config
      );

      expect(report.historicalComparison).toBeDefined();
      expect(typeof report.historicalComparison?.previousScore).toBe('number');
      expect(['improving', 'deteriorating', 'stable']).toContain(
        report.historicalComparison?.trend
      );
      expect(typeof report.historicalComparison?.changePercentage).toBe('number');
      expect(report.historicalComparison?.comparisonPeriod).toBeTruthy();
    });

    it('应该包含元数据', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      expect(report.metadata).toBeDefined();
      expect(report.metadata.version).toBeTruthy();
      expect(report.metadata.generatedBy).toBe('RiskReportGenerator');
      expect(typeof report.metadata.processingTime).toBe('number');
      expect(report.metadata.processingTime).toBeGreaterThanOrEqual(0);
      expect(report.metadata.dataQuality).toBeGreaterThanOrEqual(0);
      expect(report.metadata.dataQuality).toBeLessThanOrEqual(100);
      expect(report.metadata.lastUpdated).toBeInstanceOf(Date);
    });

    it('应该处理数据不足的情况', async () => {
      const limitedData = {
        symbol: 'TEST',
        prices: [100],
        volumes: [1000],
        timestamps: ['2024-01-01'],
      };

      const report = await riskReportGenerator.generateRiskReport(
        'TEST',
        'analysis_456',
        limitedData
      );

      expect(report).toBeDefined();
      expect(report.symbol).toBe('TEST');
      expect(report.overallRisk.level).toBeDefined();
      expect(report.metadata.dataQuality).toBeLessThanOrEqual(100);
    });

    it('应该处理错误情况', async () => {
      // Mock error in risk metrics calculation
      const { riskMetricsCalculator } = require('../risk-metrics-calculator');
      riskMetricsCalculator.calculateMarketRisk.mockRejectedValueOnce(new Error('计算失败'));
      riskMetricsCalculator.calculateLiquidityRisk.mockRejectedValueOnce(new Error('计算失败'));
      riskMetricsCalculator.calculateCreditRisk.mockRejectedValueOnce(new Error('计算失败'));

      // The generator should handle errors gracefully and still produce a report
      const report = await riskReportGenerator.generateRiskReport(
        'ERROR',
        'analysis_error',
        mockMarketData
      );

      expect(report).toBeDefined();
      expect(report.symbol).toBe('ERROR');
      expect(report.metadata.dataQuality).toBe(0); // Should be 0 due to failed calculations
    });
  });

  describe('报告质量验证', () => {
    it('应该生成高质量的风险报告', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      // 验证报告完整性
      expect(report.riskAssessments.length).toBeGreaterThanOrEqual(1);
      expect(report.keyMetrics.length).toBeGreaterThanOrEqual(5);
      expect(report.recommendations.length).toBeGreaterThan(0);

      // 验证数据质量
      expect(report.metadata.dataQuality).toBeGreaterThan(50);
      expect(report.overallRisk.confidence).toBeGreaterThan(0);

      // 验证逻辑一致性
      const highRiskAssessments = report.riskAssessments.filter(
        (r) => r.level === RiskLevel.HIGH || r.level === RiskLevel.CRITICAL
      );

      if (highRiskAssessments.length > 0) {
        expect([RiskLevel.MEDIUM, RiskLevel.HIGH, RiskLevel.CRITICAL]).toContain(
          report.overallRisk.level
        );
      }
    });

    it('应该根据风险等级生成相应的建议', async () => {
      const report = await riskReportGenerator.generateRiskReport(
        'AAPL',
        'analysis_123',
        mockMarketData
      );

      const highPriorityRecommendations = report.recommendations.filter(
        (r) => r.priority === 'high'
      );

      // Only check if we actually have critical risk level
      if (
        report.overallRisk.level === RiskLevel.CRITICAL &&
        highPriorityRecommendations.length === 0
      ) {
        // This is acceptable as the mock data might not trigger critical recommendations
        console.log('Critical risk level detected but no high priority recommendations generated');
      }

      // 验证建议的实用性
      report.recommendations.forEach((rec) => {
        expect(rec.action).toBeTruthy();
        expect(rec.description).toBeTruthy();
        expect(rec.implementation.length).toBeGreaterThan(0);
      });
    });
  });
});
