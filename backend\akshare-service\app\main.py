"""
AKShare 后端服务主文件
提供金融数据获取的RESTful API接口
支持数据库缓存以提高性能和减少API调用

这个服务将akshare的数据获取功能封装为API，
供前端通过HTTP请求调用，而不是在前端直接运行Python代码。
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import akshare as ak
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
from pydantic import BaseModel
import uvicorn
import os
import mysql.connector
from mysql.connector import Error
import hashlib
import re

# --- 配置 ---
# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置 (从环境变量获取)
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", 3306)
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
DB_NAME = os.getenv("DB_NAME", "trading_analysis")


# --- 数据库连接 ---
def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=DB_HOST,
            port=DB_PORT,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
        )
        if connection.is_connected():
            return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None


# --- FastAPI 应用实例 ---
app = FastAPI(
    title="AKShare 数据服务",
    description="提供中国金融市场数据的API服务，并支持数据库缓存。",
    version="1.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- Pydantic 请求模型 ---
class StockHistoryRequest(BaseModel):
    """股票历史数据请求模型"""

    symbol: str
    period: str = "daily"
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    adjust: str = ""
    use_cache: bool = True
    time_period: Optional[str] = (
        None  # 时间周期：数字+单位(d/w/m/y)，如 '3d', '2w', '6m', '1y' (从指定时间前到现在)
    )


class StockNewsRequest(BaseModel):
    """股票新闻请求模型"""

    symbol: str
    limit: int = 20
    use_cache: bool = True  # 新增：是否使用缓存


# --- 工具函数 ---
def safe_json_convert(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """安全地将DataFrame转换为JSON格式"""
    try:
        df = df.copy()
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].dt.strftime("%Y-%m-%d %H:%M:%S")

        if isinstance(df.index, pd.DatetimeIndex):
            df.index = df.index.strftime("%Y-%m-%d")

        return df.fillna("").to_dict("records")
    except Exception as e:
        logger.error(f"JSON转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据转换失败: {str(e)}")


def parse_time_period(time_period: str) -> int:
    """
    解析时间周期字符串，返回对应的天数

    支持的格式：
    - 数字 + d (天): 1d, 3d, 30d
    - 数字 + w (周): 1w, 2w, 4w
    - 数字 + m (月): 1m, 3m, 6m, 12m
    - 数字 + y (年): 1y, 2y, 5y

    Args:
        time_period: 时间周期字符串，如 "3d", "2w", "6m", "1y"

    Returns:
        int: 对应的天数

    Raises:
        ValueError: 如果格式不正确
    """
    if not time_period:
        raise ValueError("时间周期不能为空")

    # 使用正则表达式解析格式：数字 + 单位
    pattern = r"^(\d+)([dwmy])$"
    match = re.match(pattern, time_period.lower().strip())

    if not match:
        raise ValueError(
            f"无效的时间周期格式: {time_period}，支持格式: 数字+d/w/m/y (如: 3d, 2w, 6m, 1y)"
        )

    number = int(match.group(1))
    unit = match.group(2)

    # 验证数字范围
    if number <= 0:
        raise ValueError(f"时间周期数字必须大于0: {number}")

    if number > 9999:  # 设置合理的上限
        raise ValueError(f"时间周期数字过大: {number}，最大支持9999")

    # 计算天数
    if unit == "d":  # 天
        days = number
    elif unit == "w":  # 周
        days = number * 7
    elif unit == "m":  # 月 (按30天计算)
        days = number * 30
    elif unit == "y":  # 年 (按365天计算)
        days = number * 365
    else:
        raise ValueError(f"不支持的时间单位: {unit}")

    # 验证计算结果的合理性 (最多支持20年的数据)
    if days > 365 * 20:  # 最多20年
        raise ValueError(f"时间周期过长: {days}天，最多支持20年")

    return days


def generate_news_id(url: str) -> str:
    """根据URL生成唯一的新闻ID"""
    return hashlib.sha256(url.encode("utf-8")).hexdigest()


# --- API 路由 ---
@app.get("/")
async def root():
    return {"service": "AKShare 数据服务", "version": "1.1.0", "status": "运行中"}


@app.post("/api/stock/history")
async def get_stock_history(request: StockHistoryRequest):
    """获取股票历史数据，支持缓存和时间周期"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)  # 默认一年

    if request.time_period:
        try:
            # 使用动态解析函数计算天数
            days = parse_time_period(request.time_period)
            start_date = end_date - timedelta(days=days)

            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
            logger.info(
                f"使用时间周期 {request.time_period} ({days}天): {start_date_str} 到 {end_date_str}"
            )
        except ValueError as e:
            # 如果解析失败，使用默认值并记录警告
            logger.warning(f"时间周期解析失败: {str(e)}，使用默认值1年")
            start_date = end_date - timedelta(days=365)
            start_date_str = start_date.strftime("%Y%m%d")
            end_date_str = end_date.strftime("%Y%m%d")
    else:
        start_date_str = request.start_date or start_date.strftime("%Y%m%d")
        end_date_str = request.end_date or end_date.strftime("%Y%m%d")

    if request.use_cache:
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor(dictionary=True)
                query = """
                    SELECT * FROM stock_daily_data
                    WHERE ticker = %s AND trade_date BETWEEN %s AND %s
                    ORDER BY trade_date ASC
                """
                start_date_sql = datetime.strptime(start_date_str, "%Y%m%d").strftime(
                    "%Y-%m-%d"
                )
                end_date_sql = datetime.strptime(end_date_str, "%Y%m%d").strftime(
                    "%Y-%m-%d"
                )

                cursor.execute(query, (request.symbol, start_date_sql, end_date_sql))
                result = cursor.fetchall()

                if (
                    result
                    and len(result)
                    >= (
                        datetime.strptime(end_date_str, "%Y%m%d")
                        - datetime.strptime(start_date_str, "%Y%m%d")
                    ).days
                    * 0.8
                ):
                    logger.info(f"从缓存获取股票历史数据: {request.symbol}")
                    return {
                        "success": True,
                        "data": result,
                        "count": len(result),
                        "source": "cache",
                    }
            except Error as e:
                logger.error(f"查询缓存失败: {e}")
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

    try:
        logger.info(f"从API获取股票历史数据: {request.symbol}")

        # 确保参数格式正确
        params = {
            "symbol": request.symbol,
            "period": request.period,
            "start_date": start_date_str,
            "end_date": end_date_str,
        }

        # 只有当adjust不为空时才添加adjust参数
        if request.adjust and request.adjust.strip():
            params["adjust"] = request.adjust

        logger.info(f"调用参数: {params}")
        df = ak.stock_zh_a_hist(**params)
        if df.empty:
            raise HTTPException(status_code=404, detail="未找到股票数据")

        # 重命名字段以匹配数据库
        df.rename(
            columns={
                "日期": "trade_date",
                "开盘": "open",
                "最高": "high",
                "最低": "low",
                "收盘": "close",
                "成交量": "volume",
                "成交额": "turnover",
                "振幅": "amplitude",
                "涨跌幅": "change_pct",
                "涨跌额": "change_amount",
                "换手率": "turnover_rate",
            },
            inplace=True,
        )
        df["ticker"] = request.symbol

        # 存入数据库
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor()
                insert_query = """
                    INSERT INTO stock_daily_data (ticker, trade_date, open, high, low, close, volume, turnover, amplitude, change_pct, change_amount, turnover_rate)
                    VALUES (%(ticker)s, %(trade_date)s, %(open)s, %(high)s, %(low)s, %(close)s, %(volume)s, %(turnover)s, %(amplitude)s, %(change_pct)s, %(change_amount)s, %(turnover_rate)s)
                    ON DUPLICATE KEY UPDATE
                    open=VALUES(open), high=VALUES(high), low=VALUES(low), close=VALUES(close), volume=VALUES(volume), turnover=VALUES(turnover),
                    amplitude=VALUES(amplitude), change_pct=VALUES(change_pct), change_amount=VALUES(change_amount), turnover_rate=VALUES(turnover_rate);
                """
                data_tuples = [row.to_dict() for index, row in df.iterrows()]
                cursor.executemany(insert_query, data_tuples)
                db_conn.commit()
                logger.info(
                    f"成功缓存 {cursor.rowcount} 条股票历史数据: {request.symbol}"
                )
            except Error as e:
                logger.error(f"缓存股票历史数据失败: {e}")
                db_conn.rollback()
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

        data = safe_json_convert(df)
        return {"success": True, "data": data, "count": len(data), "source": "api"}

    except Exception as e:
        logger.error(f"获取股票历史数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")


@app.post("/api/stock/news")
async def get_stock_news(request: StockNewsRequest):
    """获取股票相关新闻，支持缓存"""
    # 注意：akshare的stock_news_em不按股票代码筛选，这里我们获取通用新闻并缓存
    if request.use_cache:
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor(dictionary=True)
                # 假设我们只取最近的新闻
                query = (
                    "SELECT * FROM financial_news ORDER BY publish_time DESC LIMIT %s"
                )
                cursor.execute(query, (request.limit,))
                result = cursor.fetchall()
                if result:
                    logger.info(f"从缓存获取新闻数据")
                    return {
                        "success": True,
                        "data": result,
                        "count": len(result),
                        "source": "cache",
                    }
            except Error as e:
                logger.error(f"查询新闻缓存失败: {e}")
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

    try:
        logger.info(f"从API获取新闻数据")

        # 尝试多种新闻获取方式
        df = None
        error_messages = []

        # 方法1: 尝试使用 stock_news_em (需要传递symbol参数)
        try:
            df = ak.stock_news_em(symbol=request.symbol)
            logger.info("使用 stock_news_em 成功获取数据")
        except Exception as e:
            error_messages.append(f"stock_news_em 失败: {str(e)}")
            logger.warning(f"stock_news_em 失败: {str(e)}")

        # 方法2: 如果方法1失败，尝试使用其他新闻接口
        if df is None or df.empty:
            try:
                # 尝试使用央视新闻接口 (需要date参数)
                current_date = datetime.now().strftime("%Y%m%d")
                df = ak.news_cctv(date=current_date)
                logger.info("使用 news_cctv 成功获取数据")
            except Exception as e:
                error_messages.append(f"news_cctv 失败: {str(e)}")
                logger.warning(f"news_cctv 失败: {str(e)}")

        # 方法3: 尝试使用财新网新闻
        if df is None or df.empty:
            try:
                df = ak.stock_news_main_cx()
                logger.info("使用 stock_news_main_cx 成功获取数据")
            except Exception as e:
                error_messages.append(f"stock_news_main_cx 失败: {str(e)}")
                logger.warning(f"stock_news_main_cx 失败: {str(e)}")

        # 方法4: 尝试使用经济新闻
        if df is None or df.empty:
            try:
                current_date = datetime.now().strftime("%Y%m%d")
                df = ak.news_economic_baidu(date=current_date)
                logger.info("使用 news_economic_baidu 成功获取数据")
            except Exception as e:
                error_messages.append(f"news_economic_baidu 失败: {str(e)}")
                logger.warning(f"news_economic_baidu 失败: {str(e)}")

        # 如果所有方法都失败，抛出异常
        if df is None or df.empty:
            error_detail = "所有新闻接口都失败: " + "; ".join(error_messages)
            logger.error(error_detail)
            raise HTTPException(status_code=503, detail=error_detail)

        df = df.head(request.limit)

        # 处理不同的数据结构
        processed_data = []
        for index, row in df.iterrows():
            # 生成唯一ID
            unique_content = str(row.get("标题", row.get("title", ""))) + str(index)
            news_id = hashlib.sha256(unique_content.encode("utf-8")).hexdigest()[:16]

            # 提取数据，处理不同的字段名
            news_item = {
                "news_id": news_id,
                "title": row.get(
                    "标题", row.get("title", row.get("新闻标题", f"新闻 {index}"))
                ),
                "content": row.get("内容", row.get("content", row.get("新闻内容", ""))),
                "source": row.get("来源", row.get("source", "东方财富")),
                "publish_time": str(
                    row.get(
                        "发布时间",
                        row.get(
                            "时间",
                            row.get(
                                "time", datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            ),
                        ),
                    )
                ),
                "url": row.get(
                    "链接", row.get("url", f"https://example.com/news/{news_id}")
                ),
                "related_tickers": json.dumps([request.symbol]),
            }
            processed_data.append(news_item)

        df = pd.DataFrame(processed_data)
        df["source"] = "东方财富"

        # 存入数据库
        db_conn = get_db_connection()
        if db_conn:
            try:
                cursor = db_conn.cursor()
                insert_query = """
                    INSERT INTO financial_news (news_id, source, title, content, publish_time, url, related_tickers)
                    VALUES (%(news_id)s, %(source)s, %(title)s, %(content)s, %(publish_time)s, %(url)s, %(related_tickers)s)
                    ON DUPLICATE KEY UPDATE
                    title=VALUES(title), content=VALUES(content), publish_time=VALUES(publish_time);
                """
                data_tuples = [row.to_dict() for index, row in df.iterrows()]
                cursor.executemany(insert_query, data_tuples)
                db_conn.commit()
                logger.info(f"成功缓存 {cursor.rowcount} 条新闻数据")
            except Error as e:
                logger.error(f"缓存新闻数据失败: {e}")
                db_conn.rollback()
            finally:
                if db_conn.is_connected():
                    cursor.close()
                    db_conn.close()

        data = safe_json_convert(df)
        return {"success": True, "data": data, "count": len(data), "source": "api"}

    except Exception as e:
        logger.error(f"获取新闻失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取新闻失败: {str(e)}")


# --- 其他路由和异常处理 (保持不变) ---


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "akshare-service"}


@app.post("/api/stock/realtime")
async def get_stock_realtime(symbol: str):
    try:
        # 获取所有A股实时数据
        df = ak.stock_zh_a_spot_em()

        # 根据股票代码筛选数据
        # 注意：akshare返回的代码可能包含市场前缀，需要灵活匹配
        stock_data = df[df["代码"].str.contains(symbol, na=False)]

        if stock_data.empty:
            # 如果直接匹配失败，尝试去掉前缀匹配
            stock_data = df[df["代码"].str.endswith(symbol, na=False)]

        if stock_data.empty:
            raise HTTPException(
                status_code=404, detail=f"未找到股票 {symbol} 的实时数据"
            )

        data = safe_json_convert(stock_data)
        return {"success": True, "data": data[0] if data else {}, "symbol": symbol}
    except Exception as e:
        logger.error(f"获取实时数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取实时数据失败: {str(e)}")


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code, content={"success": False, "error": exc.detail}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500, content={"success": False, "error": "内部服务器错误"}
    )


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=5000, reload=True)
