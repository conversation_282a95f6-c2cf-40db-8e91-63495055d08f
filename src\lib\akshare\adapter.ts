/**
 * AKShare API 适配器
 *
 * 这个适配器现在通过HTTP API调用后端的AKShare服务，
 * 而不是直接在前端运行Python代码。
 *
 * 主要改进：
 * 1. 更好的性能 - 后端服务可以复用连接和缓存数据
 * 2. 更好的安全性 - Python代码在隔离的容器中运行
 * 3. 更好的可维护性 - 前后端职责分离
 */

// API响应接口定义
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  count?: number;
  symbol?: string;
  timestamp?: string;
}

// 股票历史数据请求参数
interface StockHistoryParams {
  symbol: string;
  period?: string;
  start_date?: string;
  end_date?: string;
  adjust?: string;
}

// 股票新闻请求参数
interface StockNewsParams {
  symbol: string;
  limit?: number;
}

class AKShareAdapter {
  private baseUrl: string;
  private timeout: number;

  constructor() {
    // 从环境变量获取后端API地址，默认为localhost:5000
    this.baseUrl = process.env.BACK_END_URL || 'http://localhost:5000';
    this.timeout = 30000; // 30秒超时
  }

  /**
   * 检查后端服务健康状态
   * 替代原来的start()方法
   */
  public async start(): Promise<void> {
    // 在构建时跳过连接检查
    if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
      console.log('构建模式：跳过AKShare后端服务连接检查');
      return;
    }

    try {
      console.log(`检查AKShare后端服务连接: ${this.baseUrl}`);
      const response = await this.makeRequest('/health', 'GET');

      if (response.status === 'healthy') {
        console.log('AKShare后端服务连接成功');
      } else {
        throw new Error('AKShare后端服务不健康');
      }
    } catch (error) {
      console.error('AKShare后端服务连接失败:', error);
      // 在构建时不抛出错误，只记录日志
      if (typeof window === 'undefined') {
        console.warn('构建时无法连接AKShare后端服务，这是正常的');
        return;
      }
      throw error;
    }
  }

  /**
   * 停止服务（现在只是清理资源）
   * 替代原来的stop()方法
   */
  public stop(): void {
    console.log('AKShare适配器已停止（后端服务继续运行）');
    // 在API模式下，我们不需要停止任何进程
    // 后端服务独立运行
  }

  /**
   * 通用的HTTP请求方法
   */
  private async makeRequest(
    endpoint: string,
    method: 'GET' | 'POST' = 'GET',
    data?: any
  ): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;

    // 创建AbortController来处理超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, this.timeout);

    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    };

    if (data && method === 'POST') {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);

      // 清除超时定时器
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      // 清除超时定时器
      clearTimeout(timeoutId);

      // 处理不同类型的错误
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`请求超时 (${this.timeout / 1000}秒)`);
        }
        console.error(`API请求失败 ${method} ${url}:`, error.message);
        throw error;
      } else {
        console.error(`API请求失败 ${method} ${url}:`, error);
        throw new Error('未知错误');
      }
    }
  }

  /**
   * 通用的调用方法，兼容原有的invoke接口
   * 根据command参数路由到相应的API端点
   */
  public async invoke<T>(command: string, params: any = {}): Promise<T> {
    try {
      console.log(`调用AKShare API: ${command}`, params);

      switch (command) {
        case 'get_stock_history':
        case 'stock_zh_a_hist':
          return await this.getStockHistory(params as StockHistoryParams);

        case 'get_stock_news':
        case 'stock_news':
          return await this.getStockNews(params as StockNewsParams);

        case 'get_financial_data':
        case 'financial_data':
          return await this.getStockHistory(params as StockHistoryParams);

        case 'get_technical_indicators':
        case 'technical_indicators':
          return await this.getStockHistory(params as StockHistoryParams);

        case 'get_stock_realtime':
        case 'stock_realtime':
          return await this.getStockRealtime(params.symbol);

        default:
          throw new Error(`不支持的命令: ${command}`);
      }
    } catch (error) {
      console.error(`调用失败 ${command}:`, error);
      throw error;
    }
  }

  /**
   * 获取股票历史数据
   */
  public async getStockHistory(params: StockHistoryParams): Promise<any> {
    const response = await this.makeRequest('/api/stock/history', 'POST', params);

    if (!response.success) {
      throw new Error(response.error || '获取股票历史数据失败');
    }

    return response.data;
  }

  /**
   * 获取股票新闻
   */
  public async getStockNews(params: StockNewsParams): Promise<any> {
    try {
      const response = await this.makeRequest('/api/stock/news', 'POST', params);

      if (!response.success) {
        throw new Error(response.error || '获取股票新闻失败');
      }

      return response.data;
    } catch (error) {
      console.warn('主要新闻服务失败，使用备用模拟数据:', error);

      // 返回备用模拟数据
      const mockNews = this.generateMockNews(params.symbol, params.limit || 20);
      return mockNews;
    }
  }

  /**
   * 生成模拟新闻数据（备用方案）
   */
  private generateMockNews(symbol: string, limit: number): any[] {
    const newsTemplates = [
      `${symbol} 公司发布最新财报，业绩超预期`,
      `${symbol} 股价异动，市场关注度提升`,
      `分析师上调 ${symbol} 目标价，看好后市表现`,
      `${symbol} 获得重要合同，业务拓展顺利`,
      `机构调研 ${symbol}，关注公司发展战略`,
      `${symbol} 技术创新获得突破，竞争优势明显`,
      `监管政策利好 ${symbol} 所在行业发展`,
      `${symbol} 管理层变动，新团队上任`,
      `市场传言 ${symbol} 将有重大并购动作`,
      `${symbol} 分红方案公布，股东回报丰厚`,
    ];

    const mockNews = [];
    const now = new Date();

    for (let i = 0; i < Math.min(limit, newsTemplates.length); i++) {
      const publishTime = new Date(now.getTime() - i * 2 * 60 * 60 * 1000); // 每条新闻间隔2小时

      mockNews.push({
        news_id: `mock_${symbol}_${i}_${Date.now()}`,
        title: newsTemplates[i] || `${symbol} 相关新闻 ${i + 1}`,
        content: `这是关于 ${symbol} 的详细新闻内容。${
          newsTemplates[i] || `新闻内容 ${i + 1}`
        }。更多详情请关注后续报道。`,
        source: '财经新闻网',
        publish_time: publishTime.toISOString().slice(0, 19).replace('T', ' '),
        url: `https://finance.example.com/news/mock_${symbol}_${i}`,
        related_tickers: [symbol],
      });
    }

    console.log(`生成了 ${mockNews.length} 条模拟新闻数据`);
    return mockNews;
  }

  /**
   * 获取股票实时数据
   */
  public async getStockRealtime(symbol: string): Promise<any> {
    const response = await this.makeRequest(`/api/stock/realtime?symbol=${symbol}`, 'POST');

    if (!response.success) {
      throw new Error(response.error || '获取股票实时数据失败');
    }

    return response.data;
  }

  /**
   * 获取市场概览
   */
  public async getMarketOverview(): Promise<any> {
    const response = await this.makeRequest('/api/market/overview', 'GET');

    if (!response.success) {
      throw new Error(response.error || '获取市场概览失败');
    }

    return response.data;
  }

  /**
   * 获取技术指标数据
   */
  public async getTechnicalIndicators(params: {
    symbol: string;
    indicator?: string;
    period?: string;
  }): Promise<any> {
    const response = await this.makeRequest('/api/stock/technical', 'POST', params);

    if (!response.success) {
      throw new Error(response.error || '获取技术指标失败');
    }

    return response.data;
  }

  /**
   * 批量获取数据
   */
  public async getBatchData(requests: Array<{ command: string; params: any }>): Promise<any[]> {
    const promises = requests.map((req) => this.invoke(req.command, req.params));

    try {
      const results = await Promise.allSettled(promises);
      return results.map((result, index) => ({
        command: requests[index].command,
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason.message : null,
      }));
    } catch (error) {
      console.error('批量获取数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取历史数据（兼容方法）
   */
  public async getHistoricalData(symbol: string, days: number = 252): Promise<any[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const params = {
      symbol,
      period: 'daily',
      start_date: startDate.toISOString().slice(0, 10).replace(/-/g, ''),
      end_date: endDate.toISOString().slice(0, 10).replace(/-/g, ''),
    };

    const data = await this.getStockHistory(params);
    return data || [];
  }

  /**
   * 获取当前价格
   */
  public async getCurrentPrice(symbol: string): Promise<number> {
    try {
      const data = await this.getStockRealtime(symbol);
      // 从实时数据中提取当前价格
      return data?.现价 || data?.close || data?.current_price || 0;
    } catch (error) {
      console.warn(`获取 ${symbol} 当前价格失败，使用默认值:`, error);
      return 0;
    }
  }

  /**
   * 获取市场指数数据
   */
  public async getMarketIndexData(
    indexCode: string = '000300',
    days: number = 252
  ): Promise<any[]> {
    try {
      // 沪深300指数的代码转换
      let symbol = indexCode;
      if (indexCode === '000300') {
        symbol = 'sh000300'; // 沪深300指数
      }

      return await this.getHistoricalData(symbol, days);
    } catch (error) {
      console.warn(`获取市场指数 ${indexCode} 数据失败:`, error);
      return [];
    }
  }

  /**
   * 获取成交量数据
   */
  public async getVolumeData(symbol: string, days: number = 30): Promise<any[]> {
    try {
      const historicalData = await this.getHistoricalData(symbol, days);
      return historicalData.map((d) => ({
        date: d.trade_date || d.date,
        volume: d.volume || 0,
        turnover: d.turnover || 0,
        turnover_rate: d.turnover_rate || 0,
      }));
    } catch (error) {
      console.warn(`获取 ${symbol} 成交量数据失败:`, error);
      return [];
    }
  }

  /**
   * 获取买卖价差数据
   */
  public async getSpreadData(symbol: string): Promise<any> {
    try {
      const realtimeData = await this.getStockRealtime(symbol);

      // 从实时数据中提取买卖价差信息
      const bid = realtimeData?.买一 || realtimeData?.bid1 || 0;
      const ask = realtimeData?.卖一 || realtimeData?.ask1 || 0;
      const currentPrice = realtimeData?.现价 || realtimeData?.close || 0;

      const spread = ask - bid;
      const spreadPercentage = currentPrice > 0 ? (spread / currentPrice) * 100 : 0;

      return {
        spread: spread,
        spread_percentage: spreadPercentage,
        market_depth: 1000000, // 默认市场深度
        bid_price: bid,
        ask_price: ask,
        current_price: currentPrice,
      };
    } catch (error) {
      console.warn(`获取 ${symbol} 价差数据失败，使用默认值:`, error);
      return {
        spread: 0.01,
        spread_percentage: 0.1,
        market_depth: 1000000,
        bid_price: 0,
        ask_price: 0,
        current_price: 0,
      };
    }
  }

  /**
   * 获取财务数据（增强版）
   */
  public async getFinancialData(symbol: string): Promise<any> {
    try {
      const params = { symbol, indicator: 'all' };
      const data = await this.makeRequest('/api/stock/financial', 'POST', params);

      if (!data.success) {
        throw new Error(data.error || '获取财务数据失败');
      }

      return data.data;
    } catch (error) {
      console.warn(`获取 ${symbol} 财务数据失败，使用默认值:`, error);
      return {
        total_debt: 0,
        debt_to_equity: 0,
        debt_to_assets: 0,
        interest_coverage_ratio: 0,
        current_ratio: 1,
        quick_ratio: 1,
        roe: 0,
        roa: 0,
        gross_margin: 0,
        operating_margin: 0,
        net_margin: 0,
        operating_cash_flow: 0,
        free_cash_flow: 0,
        cash_flow_to_debt: 0,
        cash_and_equivalents: 0,
      };
    }
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{ status: string; latency: number }> {
    const startTime = Date.now();

    try {
      const response = await this.makeRequest('/health', 'GET');
      const latency = Date.now() - startTime;

      return {
        status: response.status === 'healthy' ? 'healthy' : 'unhealthy',
        latency,
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        status: 'unhealthy',
        latency,
      };
    }
  }
}

// 创建并导出适配器实例
export const akshareAdapter = new AKShareAdapter();

// 导出类型定义，供其他模块使用
export type { ApiResponse, StockHistoryParams, StockNewsParams };
