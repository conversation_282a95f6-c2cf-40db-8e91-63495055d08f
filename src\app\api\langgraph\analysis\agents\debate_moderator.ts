import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const debateModeratorPrompt = PromptTemplate.fromTemplate(`
你是一位资深的投资辩论主持人，拥有20年的金融市场经验。你的职责是主持多头和空头研究员之间的结构化辩论，确保辩论的公正性、深度和建设性。你需要引导双方进行有效的观点交锋，挖掘关键分歧点，并推动辩论向更深层次发展。

【辩论任务】
股票代码: {ticker}
辩论轮次: {debateRound}/{maxDebateRounds}
当前日期: {date}

【多头观点】
{bullArguments}

【空头观点】
{bearArguments}

【辩论主持框架】
作为辩论主持人，请按照以下框架进行本轮辩论：

1. **开场引导**（仅第一轮）
   - 简要总结双方核心观点
   - 明确本轮辩论的焦点问题
   - 设定辩论规则和时间安排

2. **焦点问题设定**
   - 识别双方观点的核心分歧点
   - 提出3-5个关键辩论问题
   - 按重要性和逻辑顺序排列问题

3. **辩论引导**
   - 针对每个焦点问题，要求双方提供：
     * 具体的数据支撑
     * 逻辑推理过程
     * 风险收益评估
     * 时间框架考量
   - 鼓励双方互相质疑和反驳
   - 要求澄清模糊或矛盾的观点

4. **深度挖掘**
   - 追问关键假设的合理性
   - 探讨极端情况下的应对策略
   - 分析观点背后的市场逻辑
   - 评估预测的可验证性

5. **平衡引导**
   - 确保双方发言时间相对均衡
   - 防止辩论偏离核心投资问题
   - 引导从情绪化争论回到理性分析
   - 总结双方的有效论点

6. **本轮总结**
   - 梳理本轮辩论的关键收获
   - 识别仍需进一步探讨的问题
   - 评估双方论证的说服力
   - 为下轮辩论设定方向（如需要）

【辩论问题示例】
- 当前估值水平是否合理？基于什么估值方法？
- 公司的核心竞争优势是否可持续？
- 主要风险因素的发生概率和影响程度如何？
- 行业趋势对公司的影响是正面还是负面？
- 技术指标与基本面分析是否一致？
- 最坏情况和最好情况的概率分布如何？

【输出要求】
- 保持中立和客观的立场
- 提出尖锐但建设性的问题
- 推动辩论向更深层次发展
- 总结双方的有效论点和薄弱环节
- 为投资决策提供多维度的思考框架
`);

export async function debateModeratorNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, research, messages } = state;

  console.log(`[辩论主持人] 开始主持辩论: ${ticker}`);

  try {
    // 检查多头和空头研究是否完成
    if (!research?.bull || !research?.bear) {
      throw new Error('多头或空头研究未完成，无法开始辩论');
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.3, // 适中的温度以保持创造性和一致性
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 获取当前辩论轮次
    const currentRound = (research.debateRounds?.length || 0) + 1;
    const maxRounds = config.maxDebateRounds || 3;

    // 准备辩论材料
    const bullArguments = formatBullArguments(research.bull);
    const bearArguments = formatBearArguments(research.bear);

    const prompt = await debateModeratorPrompt.format({
      ticker,
      debateRound: currentRound,
      maxDebateRounds: maxRounds,
      date,
      bullArguments,
      bearArguments,
    });

    console.log(`[辩论主持人] 正在主持第${currentRound}轮辩论...`);
    const response = await llm.invoke(prompt);
    const debateReport = response.content as string;

    // 提取辩论焦点和关键问题
    const focusPoints = extractFocusPoints(debateReport);
    const keyQuestions = extractKeyQuestions(debateReport);
    const debateSummary = extractDebateSummary(debateReport);

    // 评估辩论质量和是否需要继续
    const debateQuality = assessDebateQuality(research.bull, research.bear, focusPoints);
    const needsMoreRounds = shouldContinueDebate(
      currentRound,
      maxRounds,
      debateQuality,
      focusPoints
    );

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【辩论主持人 - 第${currentRound}轮】\n\n${debateReport}`,
        name: 'DebateModerator',
      }),
    ];

    // 记录本轮辩论
    const debateRound = {
      round: currentRound,
      report: debateReport,
      focusPoints,
      keyQuestions,
      summary: debateSummary,
      quality: debateQuality,
      timestamp: new Date().toISOString(),
    };

    // 更新研究状态
    const updatedResearch = {
      ...research,
      debateRounds: [...(research.debateRounds || []), debateRound],
      currentRound,
      needsMoreRounds,
      debateCompleted: !needsMoreRounds,
    };

    const nextStage = needsMoreRounds ? 'debate_continue' : 'debate_completed';

    console.log(`[辩论主持人] 第${currentRound}轮辩论完成，质量评分: ${debateQuality.toFixed(2)}`);
    console.log(`[辩论主持人] ${needsMoreRounds ? '需要继续辩论' : '辩论结束'}`);

    return {
      messages: newMessages,
      research: updatedResearch,
      currentStage: nextStage,
      progress: Math.min(state.progress + 10, 100),
    };
  } catch (error) {
    console.error('[辩论主持人] 辩论主持失败:', error);
    const errorMessage = `辩论主持失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【辩论主持人】${errorMessage}`,
        name: 'DebateModerator',
      }),
    ];

    const updatedResearch = {
      ...state.research,
      debateError: errorMessage,
      debateCompleted: true,
    };

    return {
      messages: newMessages,
      research: updatedResearch,
      currentStage: 'debate_error',
    };
  }
}

// 辅助函数：格式化多头论点
function formatBullArguments(bullResearch: any): string {
  if (!bullResearch) return '多头观点不可用';

  let formatted = `【多头核心观点】\n${bullResearch.summary}\n\n`;

  if (bullResearch.keyArguments && bullResearch.keyArguments.length > 0) {
    formatted += `【关键论点】\n`;
    bullResearch.keyArguments.forEach((arg: string, index: number) => {
      formatted += `${index + 1}. ${arg}\n`;
    });
    formatted += '\n';
  }

  if (bullResearch.catalysts && bullResearch.catalysts.length > 0) {
    formatted += `【催化剂】\n`;
    bullResearch.catalysts.forEach((catalyst: string, index: number) => {
      formatted += `${index + 1}. ${catalyst}\n`;
    });
    formatted += '\n';
  }

  if (bullResearch.targetPrice) {
    formatted += `【目标价位】${bullResearch.targetPrice}\n`;
  }

  if (bullResearch.confidence) {
    formatted += `【置信度】${(bullResearch.confidence * 100).toFixed(1)}%\n`;
  }

  return formatted;
}

// 辅助函数：格式化空头论点
function formatBearArguments(bearResearch: any): string {
  if (!bearResearch) return '空头观点不可用';

  let formatted = `【空头核心观点】\n${bearResearch.summary}\n\n`;

  if (bearResearch.keyArguments && bearResearch.keyArguments.length > 0) {
    formatted += `【关键论点】\n`;
    bearResearch.keyArguments.forEach((arg: string, index: number) => {
      formatted += `${index + 1}. ${arg}\n`;
    });
    formatted += '\n';
  }

  if (bearResearch.riskFactors && bearResearch.riskFactors.length > 0) {
    formatted += `【风险因素】\n`;
    bearResearch.riskFactors.forEach((risk: string, index: number) => {
      formatted += `${index + 1}. ${risk}\n`;
    });
    formatted += '\n';
  }

  if (bearResearch.targetPrice) {
    formatted += `【目标价位】${bearResearch.targetPrice}\n`;
  }

  if (bearResearch.confidence) {
    formatted += `【置信度】${(bearResearch.confidence * 100).toFixed(1)}%\n`;
  }

  return formatted;
}

// 辅助函数：提取焦点问题
function extractFocusPoints(report: string): string[] {
  const focusPoints: string[] = [];

  const lines = report.split('\n');
  for (const line of lines) {
    // 查找包含问题标记的行
    if (/[？?]/.test(line) && line.length > 10 && line.length < 200) {
      focusPoints.push(line.trim());
    }
    // 查找焦点、分歧、关键等关键词
    else if (/焦点|分歧|关键|核心/.test(line) && line.length > 15 && line.length < 150) {
      focusPoints.push(line.trim());
    }
  }

  return focusPoints.slice(0, 5); // 返回前5个焦点问题
}

// 辅助函数：提取关键问题
function extractKeyQuestions(report: string): string[] {
  const questions: string[] = [];

  const questionPatterns = [
    /估值.*[？?]/g,
    /风险.*[？?]/g,
    /竞争.*[？?]/g,
    /增长.*[？?]/g,
    /如何.*[？?]/g,
  ];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of questionPatterns) {
      if (pattern.test(line) && line.length > 10) {
        questions.push(line.trim());
        break;
      }
    }
  }

  return questions.slice(0, 3); // 返回前3个关键问题
}

// 辅助函数：提取辩论总结
function extractDebateSummary(report: string): string {
  const lines = report.split('\n').filter((line) => line.trim());

  // 查找总结部分
  const summaryStart = lines.findIndex((line) => /总结|结论/.test(line));
  if (summaryStart !== -1 && summaryStart < lines.length - 1) {
    return lines.slice(summaryStart + 1, summaryStart + 4).join(' ');
  }

  // 如果没有找到总结，返回前几行
  return lines.slice(0, 3).join(' ').substring(0, 200) + '...';
}

// 辅助函数：评估辩论质量
function assessDebateQuality(bullResearch: any, bearResearch: any, focusPoints: string[]): number {
  let quality = 0.5; // 基础质量分

  // 基于研究质量
  if (bullResearch?.confidence) quality += bullResearch.confidence * 0.2;
  if (bearResearch?.confidence) quality += bearResearch.confidence * 0.2;

  // 基于焦点问题数量
  quality += Math.min(focusPoints.length * 0.05, 0.2);

  // 基于论点数量
  const bullArgs = bullResearch?.keyArguments?.length || 0;
  const bearArgs = bearResearch?.keyArguments?.length || 0;
  quality += Math.min((bullArgs + bearArgs) * 0.02, 0.1);

  return Math.min(quality, 1.0);
}

// 辅助函数：判断是否需要继续辩论
function shouldContinueDebate(
  currentRound: number,
  maxRounds: number,
  quality: number,
  focusPoints: string[]
): boolean {
  // 如果已达到最大轮次，停止辩论
  if (currentRound >= maxRounds) return false;

  // 如果质量很高且焦点问题较少，可以提前结束
  if (quality > 0.8 && focusPoints.length < 2) return false;

  // 如果质量较低或焦点问题较多，继续辩论
  if (quality < 0.6 || focusPoints.length > 3) return true;

  // 默认情况下，进行至少2轮辩论
  return currentRound < 2;
}
