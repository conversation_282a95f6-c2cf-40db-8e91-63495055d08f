/**
 * SEO 工具类和辅助函数
 * 提供元数据生成、验证、优化等核心功能
 */

import { Metadata } from 'next';
import { SEOConfig, PageSEOProps, SupportedLocale, CoreWebVitals } from '@/types/seo';
import { SEO_CONSTANTS, SEO_CONTENT, STRUCTURED_DATA_TEMPLATES } from './config';

/**
 * SEO 工具类
 */
export class SEOUtils {
  /**
   * 验证标题长度和格式
   */
  static validateTitle(title: string): boolean {
    return title.length > 0 && title.length <= SEO_CONSTANTS.TITLE_MAX_LENGTH;
  }

  /**
   * 验证描述长度和格式
   */
  static validateDescription(description: string): boolean {
    return description.length > 0 && description.length <= SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH;
  }

  /**
   * 验证关键词数量
   */
  static validateKeywords(keywords: string[]): boolean {
    return keywords.length > 0 && keywords.length <= SEO_CONSTANTS.KEYWORDS_MAX_COUNT;
  }

  /**
   * 优化标题 - 确保包含关键词和品牌名
   */
  static optimizeTitle(title: string, keyword?: string, includeBrand: boolean = true): string {
    let optimizedTitle = title;

    // 如果提供了关键词且标题中不包含，尝试添加
    if (keyword && !title.toLowerCase().includes(keyword.toLowerCase())) {
      optimizedTitle = `${keyword} - ${title}`;
    }

    // 添加品牌名（如果还没有且需要）
    if (includeBrand && !optimizedTitle.includes(SEO_CONSTANTS.SITE_NAME)) {
      optimizedTitle = `${optimizedTitle} - ${SEO_CONSTANTS.SITE_NAME}`;
    }

    // 确保不超过最大长度
    if (optimizedTitle.length > SEO_CONSTANTS.TITLE_MAX_LENGTH) {
      const maxTitleLength = SEO_CONSTANTS.TITLE_MAX_LENGTH - SEO_CONSTANTS.SITE_NAME.length - 3;
      optimizedTitle = `${title.substring(0, maxTitleLength)} - ${SEO_CONSTANTS.SITE_NAME}`;
    }

    return optimizedTitle;
  }

  /**
   * 优化描述 - 确保包含关键词和行动号召
   */
  static optimizeDescription(description: string, keywords?: string[]): string {
    let optimizedDescription = description;

    // 确保不超过最大长度
    if (optimizedDescription.length > SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH) {
      optimizedDescription =
        optimizedDescription.substring(0, SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH - 3) + '...';
    }

    return optimizedDescription;
  }

  /**
   * 生成规范化 URL
   */
  static generateCanonicalUrl(path: string, locale?: SupportedLocale): string {
    const baseUrl = SEO_CONSTANTS.SITE_URL;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    const localePrefix = locale && locale !== 'zh' ? `/${locale}` : '';

    return `${baseUrl}${localePrefix}${cleanPath}`;
  }

  /**
   * 生成 hreflang 标签
   */
  static generateHreflangTags(path: string): Record<string, string> {
    const baseUrl = SEO_CONSTANTS.SITE_URL;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;

    return {
      zh: `${baseUrl}${cleanPath}`,
      en: `${baseUrl}/en${cleanPath}`,
      'x-default': `${baseUrl}${cleanPath}`,
    };
  }

  /**
   * 生成面包屑导航结构化数据
   */
  static generateBreadcrumbSchema(path: string, locale: SupportedLocale = 'zh'): any {
    const pathSegments = path.split('/').filter(Boolean);
    const baseUrl = SEO_CONSTANTS.SITE_URL;
    const localePrefix = locale !== 'zh' ? `/${locale}` : '';

    const breadcrumbItems = [
      {
        '@type': 'ListItem',
        position: 1,
        name: locale === 'zh' ? '首页' : 'Home',
        item: `${baseUrl}${localePrefix}/`,
      },
    ];

    let currentPath = localePrefix;
    pathSegments.forEach((segment, index) => {
      if (segment === 'en') return; // 跳过语言前缀

      currentPath += `/${segment}`;
      const segmentName = this.getSegmentName(segment, locale);

      breadcrumbItems.push({
        '@type': 'ListItem',
        position: index + 2,
        name: segmentName,
        item: `${baseUrl}${currentPath}`,
      });
    });

    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbItems,
    };
  }

  /**
   * 获取路径段的显示名称
   */
  private static getSegmentName(segment: string, locale: SupportedLocale): string {
    const segmentNames: Record<SupportedLocale, Record<string, string>> = {
      zh: {
        analysis: '智能分析',
        tasks: '任务管理',
        messages: '消息查询',
        'create-task': '创建任务',
      },
      en: {
        analysis: 'Analysis',
        tasks: 'Tasks',
        messages: 'Messages',
        'create-task': 'Create Task',
      },
    };

    return segmentNames[locale][segment] || segment;
  }

  /**
   * 检测用户语言偏好
   */
  static detectLocale(acceptLanguage?: string): SupportedLocale {
    if (!acceptLanguage) return 'zh';

    const languages = acceptLanguage.split(',').map((lang) => lang.split(';')[0].trim());

    for (const lang of languages) {
      if (lang.startsWith('en')) return 'en';
      if (lang.startsWith('zh')) return 'zh';
    }

    return 'zh'; // 默认中文
  }

  /**
   * 生成动态标题（用于分析页面等）
   */
  static generateDynamicTitle(
    baseTitle: string,
    dynamicData?: { stockSymbol?: string; analysisId?: string; taskTitle?: string },
    locale: SupportedLocale = 'zh'
  ): string {
    if (!dynamicData) return baseTitle;

    const { stockSymbol, analysisId, taskTitle } = dynamicData;

    if (stockSymbol) {
      const stockPrefix =
        locale === 'zh' ? `${stockSymbol} 股票分析` : `${stockSymbol} Stock Analysis`;
      return `${stockPrefix} - ${baseTitle}`;
    }

    if (taskTitle) {
      return `${taskTitle} - ${baseTitle}`;
    }

    if (analysisId) {
      const analysisPrefix = locale === 'zh' ? '分析报告' : 'Analysis Report';
      return `${analysisPrefix} #${analysisId} - ${baseTitle}`;
    }

    return baseTitle;
  }

  /**
   * 生成动态描述
   */
  static generateDynamicDescription(
    baseDescription: string,
    dynamicData?: { stockSymbol?: string; analysisId?: string; taskTitle?: string },
    locale: SupportedLocale = 'zh'
  ): string {
    if (!dynamicData) return baseDescription;

    const { stockSymbol, analysisId, taskTitle } = dynamicData;

    if (stockSymbol) {
      const stockDesc =
        locale === 'zh'
          ? `查看 ${stockSymbol} 的详细分析报告，包含基本面分析、技术分析、情绪分析和风险评估。`
          : `View detailed analysis report for ${stockSymbol}, including fundamental analysis, technical analysis, sentiment analysis and risk assessment.`;
      return stockDesc;
    }

    if (taskTitle) {
      const taskDesc =
        locale === 'zh'
          ? `${taskTitle} - 查看任务详情、执行状态和分析结果。`
          : `${taskTitle} - View task details, execution status and analysis results.`;
      return taskDesc;
    }

    return baseDescription;
  }

  /**
   * 验证结构化数据
   */
  static validateStructuredData(data: any): boolean {
    try {
      // 基本验证：必须有 @context 和 @type
      if (!data['@context'] || !data['@type']) {
        return false;
      }

      // 验证 JSON 格式
      JSON.stringify(data);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 合并多个结构化数据对象
   */
  static mergeStructuredData(...dataObjects: any[]): any[] {
    return dataObjects.filter((data) => data && this.validateStructuredData(data));
  }

  /**
   * 生成 JSON-LD 脚本标签内容
   */
  static generateJsonLdScript(data: any): string {
    if (!this.validateStructuredData(data)) {
      console.warn('Invalid structured data provided');
      return '';
    }

    return JSON.stringify(data, null, 2);
  }

  /**
   * 计算 SEO 分数
   */
  static calculateSEOScore(config: SEOConfig): number {
    let score = 0;
    const maxScore = 100;

    // 标题评分 (25分)
    if (this.validateTitle(config.title)) {
      score += 25;
      // 额外分数：包含关键词
      if (
        config.keywords.some((keyword) =>
          config.title.toLowerCase().includes(keyword.toLowerCase())
        )
      ) {
        score += 5;
      }
    }

    // 描述评分 (25分)
    if (this.validateDescription(config.description)) {
      score += 25;
      // 额外分数：包含关键词
      if (
        config.keywords.some((keyword) =>
          config.description.toLowerCase().includes(keyword.toLowerCase())
        )
      ) {
        score += 5;
      }
    }

    // 关键词评分 (15分)
    if (this.validateKeywords(config.keywords)) {
      score += 15;
    }

    // Open Graph 评分 (15分)
    if (config.openGraph && config.openGraph.title && config.openGraph.description) {
      score += 15;
    }

    // Twitter Card 评分 (10分)
    if (config.twitter && config.twitter.title && config.twitter.description) {
      score += 10;
    }

    // 结构化数据评分 (10分)
    if (config.structuredData && this.validateStructuredData(config.structuredData)) {
      score += 10;
    }

    return Math.min(score, maxScore);
  }
}

/**
 * Core Web Vitals 监控工具
 */
export class WebVitalsUtils {
  /**
   * 评估 LCP 性能
   */
  static evaluateLCP(lcp: number): 'good' | 'needs-improvement' | 'poor' {
    if (lcp <= 2500) return 'good';
    if (lcp <= 4000) return 'needs-improvement';
    return 'poor';
  }

  /**
   * 评估 FID 性能
   */
  static evaluateFID(fid: number): 'good' | 'needs-improvement' | 'poor' {
    if (fid <= 100) return 'good';
    if (fid <= 300) return 'needs-improvement';
    return 'poor';
  }

  /**
   * 评估 CLS 性能
   */
  static evaluateCLS(cls: number): 'good' | 'needs-improvement' | 'poor' {
    if (cls <= 0.1) return 'good';
    if (cls <= 0.25) return 'needs-improvement';
    return 'poor';
  }

  /**
   * 计算整体性能分数
   */
  static calculatePerformanceScore(vitals: CoreWebVitals): number {
    const lcpScore =
      this.evaluateLCP(vitals.lcp) === 'good'
        ? 40
        : this.evaluateLCP(vitals.lcp) === 'needs-improvement'
        ? 20
        : 0;

    const fidScore =
      this.evaluateFID(vitals.fid) === 'good'
        ? 30
        : this.evaluateFID(vitals.fid) === 'needs-improvement'
        ? 15
        : 0;

    const clsScore =
      this.evaluateCLS(vitals.cls) === 'good'
        ? 30
        : this.evaluateCLS(vitals.cls) === 'needs-improvement'
        ? 15
        : 0;

    return lcpScore + fidScore + clsScore;
  }
}
