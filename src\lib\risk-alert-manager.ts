/**
 * 风险预警管理器
 * Risk Alert Manager
 */

import {
  AlertLevel,
  RiskAlert,
  RiskAssessment,
  RiskLevel,
  RiskMetric,
  RiskType,
} from '@/types/risk-report';

// 预警规则配置
export interface AlertRule {
  id: string;
  name: string;
  riskType: RiskType;
  condition: (value: number, threshold: number) => boolean;
  threshold: number;
  level: AlertLevel;
  message: string;
  isActive: boolean;
}

// 预警历史记录
export interface AlertHistory {
  alertId: string;
  timestamp: Date;
  action: 'created' | 'acknowledged' | 'resolved' | 'escalated';
  userId?: string;
  notes?: string;
}

/**
 * 风险预警管理器类
 */
export class RiskAlertManager {
  private alertRules: AlertRule[] = [];
  private activeAlerts: Map<string, RiskAlert> = new Map();
  private alertHistory: AlertHistory[] = [];
  private subscribers: ((alert: RiskAlert) => void)[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * 初始化默认预警规则
   */
  private initializeDefaultRules(): void {
    this.alertRules = [
      // 市场风险预警规则
      {
        id: 'market_volatility_high',
        name: '高波动率预警',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => value > threshold,
        threshold: 0.3,
        level: AlertLevel.WARNING,
        message: '股票波动率超过30%，市场风险较高',
        isActive: true,
      },
      {
        id: 'market_volatility_critical',
        name: '极高波动率预警',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => value > threshold,
        threshold: 0.5,
        level: AlertLevel.CRITICAL,
        message: '股票波动率超过50%，市场风险极高',
        isActive: true,
      },
      {
        id: 'market_beta_high',
        name: '高Beta系数预警',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => Math.abs(value) > threshold,
        threshold: 1.8,
        level: AlertLevel.WARNING,
        message: 'Beta系数过高，系统性风险较大',
        isActive: true,
      },
      {
        id: 'market_var_high',
        name: '高VaR预警',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => Math.abs(value) > threshold,
        threshold: 0.15,
        level: AlertLevel.DANGER,
        message: 'VaR值过高，潜在损失风险较大',
        isActive: true,
      },
      {
        id: 'market_drawdown_high',
        name: '大幅回撤预警',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => Math.abs(value) > threshold,
        threshold: 0.25,
        level: AlertLevel.DANGER,
        message: '最大回撤超过25%，投资风险较高',
        isActive: true,
      },

      // 流动性风险预警规则
      {
        id: 'liquidity_volume_low',
        name: '低交易量预警',
        riskType: RiskType.LIQUIDITY,
        condition: (value, threshold) => value < threshold,
        threshold: 100000,
        level: AlertLevel.WARNING,
        message: '日均交易量过低，流动性风险较高',
        isActive: true,
      },
      {
        id: 'liquidity_spread_high',
        name: '高价差预警',
        riskType: RiskType.LIQUIDITY,
        condition: (value, threshold) => value > threshold,
        threshold: 0.02,
        level: AlertLevel.WARNING,
        message: '买卖价差过大，交易成本较高',
        isActive: true,
      },
      {
        id: 'liquidity_amihud_high',
        name: '高Amihud比率预警',
        riskType: RiskType.LIQUIDITY,
        condition: (value, threshold) => value > threshold,
        threshold: 0.001,
        level: AlertLevel.DANGER,
        message: 'Amihud比率过高，价格冲击风险较大',
        isActive: true,
      },

      // 信用风险预警规则
      {
        id: 'credit_debt_ratio_high',
        name: '高债务比率预警',
        riskType: RiskType.CREDIT,
        condition: (value, threshold) => value > threshold,
        threshold: 0.7,
        level: AlertLevel.WARNING,
        message: '债务比率过高，财务风险较大',
        isActive: true,
      },
      {
        id: 'credit_current_ratio_low',
        name: '低流动比率预警',
        riskType: RiskType.CREDIT,
        condition: (value, threshold) => value < threshold,
        threshold: 1.2,
        level: AlertLevel.WARNING,
        message: '流动比率过低，短期偿债能力不足',
        isActive: true,
      },
      {
        id: 'credit_interest_coverage_low',
        name: '低利息覆盖率预警',
        riskType: RiskType.CREDIT,
        condition: (value, threshold) => value < threshold,
        threshold: 2.0,
        level: AlertLevel.DANGER,
        message: '利息覆盖率过低，偿债能力堪忧',
        isActive: true,
      },
    ];
  }

  /**
   * 检查并生成预警
   */
  checkAlerts(
    symbol: string,
    metrics: Record<string, RiskMetric>,
    assessments: RiskAssessment[]
  ): RiskAlert[] {
    const newAlerts: RiskAlert[] = [];

    // 基于指标检查预警
    for (const [metricName, metric] of Object.entries(metrics)) {
      const applicableRules = this.getApplicableRules(metricName, metric);

      for (const rule of applicableRules) {
        if (rule.isActive && rule.condition(metric.value, rule.threshold)) {
          const alert = this.createAlert(rule, symbol, metric);
          newAlerts.push(alert);
        }
      }
    }

    // 基于风险评估检查预警
    for (const assessment of assessments) {
      if (assessment.level === RiskLevel.HIGH || assessment.level === RiskLevel.CRITICAL) {
        const alert = this.createAssessmentAlert(assessment, symbol);
        newAlerts.push(alert);
      }
    }

    // 更新活跃预警列表
    this.updateActiveAlerts(newAlerts);

    // 通知订阅者
    newAlerts.forEach((alert) => this.notifySubscribers(alert));

    return newAlerts;
  }

  /**
   * 创建基于规则的预警
   */
  private createAlert(rule: AlertRule, symbol: string, metric: RiskMetric): RiskAlert {
    const alertId = this.generateAlertId();

    const alert: RiskAlert = {
      id: alertId,
      level: rule.level,
      type: rule.riskType,
      title: `${symbol} - ${rule.name}`,
      message: `${rule.message}。当前值：${metric.value}${metric.unit || ''}，阈值：${
        rule.threshold
      }${metric.unit || ''}`,
      timestamp: new Date(),
      isActive: true,
      threshold: rule.threshold,
      currentValue: metric.value,
      recommendations: this.generateAlertRecommendations(rule, metric),
    };

    // 记录预警历史
    this.recordAlertHistory(alertId, 'created');

    return alert;
  }

  /**
   * 创建基于评估的预警
   */
  private createAssessmentAlert(assessment: RiskAssessment, symbol: string): RiskAlert {
    const alertId = this.generateAlertId();

    const levelMapping: Record<RiskLevel, AlertLevel> = {
      [RiskLevel.LOW]: AlertLevel.INFO,
      [RiskLevel.MEDIUM]: AlertLevel.WARNING,
      [RiskLevel.HIGH]: AlertLevel.DANGER,
      [RiskLevel.CRITICAL]: AlertLevel.CRITICAL,
    };

    const alert: RiskAlert = {
      id: alertId,
      level: levelMapping[assessment.level] || AlertLevel.WARNING,
      type: assessment.type,
      title: `${symbol} - ${this.getRiskTypeName(assessment.type)}风险${
        assessment.level === RiskLevel.CRITICAL ? '极高' : '偏高'
      }`,
      message: assessment.description,
      timestamp: new Date(),
      isActive: true,
      currentValue: assessment.score,
      recommendations: assessment.recommendations.slice(0, 3),
    };

    // 记录预警历史
    this.recordAlertHistory(alertId, 'created');

    return alert;
  }

  /**
   * 获取适用的预警规则
   */
  private getApplicableRules(metricName: string, metric: RiskMetric): AlertRule[] {
    const metricRuleMapping: Record<string, string[]> = {
      volatility: ['market_volatility_high', 'market_volatility_critical'],
      beta: ['market_beta_high'],
      var: ['market_var_high'],
      maxDrawdown: ['market_drawdown_high'],
      volume: ['liquidity_volume_low'],
      spread: ['liquidity_spread_high'],
      amihudRatio: ['liquidity_amihud_high'],
      debtRatio: ['credit_debt_ratio_high'],
      currentRatio: ['credit_current_ratio_low'],
      interestCoverage: ['credit_interest_coverage_low'],
    };

    const ruleIds = metricRuleMapping[metricName] || [];
    return this.alertRules.filter((rule) => ruleIds.includes(rule.id));
  }

  /**
   * 生成预警建议
   */
  private generateAlertRecommendations(rule: AlertRule, metric: RiskMetric): string[] {
    const recommendations: string[] = [];

    switch (rule.riskType) {
      case RiskType.MARKET:
        if (rule.level === AlertLevel.CRITICAL) {
          recommendations.push('立即考虑减仓或止损');
          recommendations.push('暂停新增投资');
        } else {
          recommendations.push('密切监控市场变化');
          recommendations.push('考虑设置止损位');
        }
        break;

      case RiskType.LIQUIDITY:
        recommendations.push('避免大额交易');
        recommendations.push('选择流动性更好的时段交易');
        if (rule.level >= AlertLevel.DANGER) {
          recommendations.push('考虑分批交易');
        }
        break;

      case RiskType.CREDIT:
        recommendations.push('关注公司财务状况变化');
        recommendations.push('评估信用风险敞口');
        if (rule.level >= AlertLevel.DANGER) {
          recommendations.push('考虑降低仓位');
        }
        break;

      default:
        recommendations.push('密切监控相关指标');
        recommendations.push('评估风险影响');
    }

    return recommendations;
  }

  /**
   * 更新活跃预警列表
   */
  private updateActiveAlerts(newAlerts: RiskAlert[]): void {
    // 添加新预警
    newAlerts.forEach((alert) => {
      this.activeAlerts.set(alert.id, alert);
    });

    // 检查并移除过期预警
    const currentTime = new Date();
    for (const [alertId, alert] of Array.from(this.activeAlerts.entries())) {
      const alertAge = currentTime.getTime() - alert.timestamp.getTime();
      const maxAge = this.getMaxAlertAge(alert.level);

      if (alertAge > maxAge) {
        this.resolveAlert(alertId, '自动过期');
      }
    }
  }

  /**
   * 获取预警最大存活时间
   */
  private getMaxAlertAge(level: AlertLevel): number {
    const maxAges = {
      [AlertLevel.CRITICAL]: 24 * 60 * 60 * 1000, // 24小时
      [AlertLevel.DANGER]: 48 * 60 * 60 * 1000, // 48小时
      [AlertLevel.WARNING]: 72 * 60 * 60 * 1000, // 72小时
      [AlertLevel.INFO]: 168 * 60 * 60 * 1000, // 7天
    };
    return maxAges[level] || 72 * 60 * 60 * 1000;
  }

  /**
   * 确认预警
   */
  acknowledgeAlert(alertId: string, userId?: string, notes?: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    this.recordAlertHistory(alertId, 'acknowledged', userId, notes);
    return true;
  }

  /**
   * 解决预警
   */
  resolveAlert(alertId: string, notes?: string, userId?: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.isActive = false;
    this.activeAlerts.delete(alertId);
    this.recordAlertHistory(alertId, 'resolved', userId, notes);

    return true;
  }

  /**
   * 升级预警
   */
  escalateAlert(alertId: string, newLevel: AlertLevel, userId?: string, notes?: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return false;
    }

    alert.level = newLevel;
    this.recordAlertHistory(alertId, 'escalated', userId, notes);
    this.notifySubscribers(alert);

    return true;
  }

  /**
   * 获取活跃预警
   */
  getActiveAlerts(riskType?: RiskType, level?: AlertLevel): RiskAlert[] {
    let alerts = Array.from(this.activeAlerts.values());

    if (riskType) {
      alerts = alerts.filter((alert) => alert.type === riskType);
    }

    if (level) {
      alerts = alerts.filter((alert) => alert.level === level);
    }

    return alerts.sort((a, b) => {
      // 按级别和时间排序
      const levelPriority = this.getAlertPriority(b.level) - this.getAlertPriority(a.level);
      if (levelPriority !== 0) return levelPriority;
      return b.timestamp.getTime() - a.timestamp.getTime();
    });
  }

  /**
   * 获取预警历史
   */
  getAlertHistory(alertId?: string): AlertHistory[] {
    if (alertId) {
      return this.alertHistory.filter((h) => h.alertId === alertId);
    }
    return [...this.alertHistory];
  }

  /**
   * 订阅预警通知
   */
  subscribe(callback: (alert: RiskAlert) => void): () => void {
    this.subscribers.push(callback);

    // 返回取消订阅函数
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * 通知订阅者
   */
  private notifySubscribers(alert: RiskAlert): void {
    this.subscribers.forEach((callback) => {
      try {
        callback(alert);
      } catch (error) {
        console.error('预警通知失败:', error);
      }
    });
  }

  /**
   * 记录预警历史
   */
  private recordAlertHistory(
    alertId: string,
    action: AlertHistory['action'],
    userId?: string,
    notes?: string
  ): void {
    this.alertHistory.push({
      alertId,
      timestamp: new Date(),
      action,
      userId,
      notes,
    });

    // 限制历史记录数量
    if (this.alertHistory.length > 10000) {
      this.alertHistory = this.alertHistory.slice(-5000);
    }
  }

  /**
   * 添加自定义预警规则
   */
  addAlertRule(rule: Omit<AlertRule, 'id'>): string {
    const ruleId = this.generateRuleId();
    const newRule: AlertRule = {
      ...rule,
      id: ruleId,
    };

    this.alertRules.push(newRule);
    return ruleId;
  }

  /**
   * 更新预警规则
   */
  updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const ruleIndex = this.alertRules.findIndex((rule) => rule.id === ruleId);
    if (ruleIndex === -1) {
      return false;
    }

    this.alertRules[ruleIndex] = {
      ...this.alertRules[ruleIndex],
      ...updates,
    };

    return true;
  }

  /**
   * 删除预警规则
   */
  removeAlertRule(ruleId: string): boolean {
    const ruleIndex = this.alertRules.findIndex((rule) => rule.id === ruleId);
    if (ruleIndex === -1) {
      return false;
    }

    this.alertRules.splice(ruleIndex, 1);
    return true;
  }

  /**
   * 获取所有预警规则
   */
  getAlertRules(): AlertRule[] {
    return [...this.alertRules];
  }

  // 辅助方法
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private getRiskTypeName(riskType: RiskType): string {
    const names = {
      [RiskType.MARKET]: '市场',
      [RiskType.LIQUIDITY]: '流动性',
      [RiskType.CREDIT]: '信用',
      [RiskType.OPERATIONAL]: '操作',
      [RiskType.SYSTEMATIC]: '系统性',
    };
    return names[riskType] || '未知';
  }

  private getAlertPriority(level: AlertLevel): number {
    const priorities = {
      [AlertLevel.CRITICAL]: 4,
      [AlertLevel.DANGER]: 3,
      [AlertLevel.WARNING]: 2,
      [AlertLevel.INFO]: 1,
    };
    return priorities[level] || 0;
  }
}

// 导出单例实例
export const riskAlertManager = new RiskAlertManager();
