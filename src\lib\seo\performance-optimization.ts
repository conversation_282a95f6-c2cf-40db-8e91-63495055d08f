/**
 * 性能优化工具
 * 优化首屏内容渲染性能和 Core Web Vitals
 */

import { NextRequest, NextResponse } from 'next/server';

export interface PerformanceConfig {
  enableResourceHints: boolean;
  enableCriticalResourcePreload: boolean;
  enableImageOptimization: boolean;
  enableScriptOptimization: boolean;
  enableStyleOptimization: boolean;
}

export interface CoreWebVitalsThresholds {
  LCP: number; // Largest Contentful Paint (ms)
  FID: number; // First Input Delay (ms)
  CLS: number; // Cumulative Layout Shift
  FCP: number; // First Contentful Paint (ms)
  TTFB: number; // Time to First Byte (ms)
}

/**
 * 性能优化管理器
 */
export class PerformanceOptimizer {
  private config: PerformanceConfig;
  private thresholds: CoreWebVitalsThresholds;

  constructor(config?: Partial<PerformanceConfig>) {
    this.config = {
      enableResourceHints: true,
      enableCriticalResourcePreload: true,
      enableImageOptimization: true,
      enableScriptOptimization: true,
      enableStyleOptimization: true,
      ...config,
    };

    this.thresholds = {
      LCP: 2500, // 2.5秒
      FID: 100, // 100毫秒
      CLS: 0.1, // 0.1
      FCP: 1800, // 1.8秒
      TTFB: 800, // 800毫秒
    };
  }

  /**
   * 生成性能优化的响应头
   */
  generatePerformanceHeaders(request: NextRequest): Record<string, string> {
    const headers: Record<string, string> = {};
    const pathname = request.nextUrl.pathname;

    // 启用 Early Hints (HTTP 103)
    if (this.config.enableResourceHints) {
      const earlyHints = this.generateEarlyHints(pathname);
      if (earlyHints.length > 0) {
        headers['Link'] = earlyHints.join(', ');
      }
    }

    // 服务端推送提示
    headers['X-Accel-Buffering'] = 'no'; // 禁用 Nginx 缓冲以加快 TTFB

    // 压缩提示
    headers['Vary'] = 'Accept-Encoding, Accept, User-Agent';

    return headers;
  }

  /**
   * 生成 Early Hints
   */
  private generateEarlyHints(pathname: string): string[] {
    const hints: string[] = [];

    // 关键字体预加载
    hints.push('</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin');

    // 关键 CSS 预加载
    hints.push('</styles/critical.css>; rel=preload; as=style');

    // 页面特定的预加载
    if (pathname === '/' || pathname === '/en') {
      hints.push('</tradingAgent.png>; rel=preload; as=image');
      hints.push('</api/data/market-overview>; rel=preload; as=fetch; crossorigin');
    }

    if (pathname.startsWith('/analysis')) {
      hints.push('</scripts/charts.js>; rel=preload; as=script');
    }

    return hints;
  }

  /**
   * 优化图片加载策略
   */
  getImageOptimizationConfig(
    src: string,
    priority = false
  ): {
    loading: 'lazy' | 'eager';
    decoding: 'async' | 'sync';
    fetchPriority: 'high' | 'low' | 'auto';
    sizes: string;
  } {
    return {
      loading: priority ? 'eager' : 'lazy',
      decoding: 'async',
      fetchPriority: priority ? 'high' : 'auto',
      sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    };
  }

  /**
   * 生成关键资源预加载标签
   */
  generateCriticalResourcePreloads(page: string): string[] {
    if (!this.config.enableCriticalResourcePreload) {
      return [];
    }

    const preloads: string[] = [];

    // 通用关键资源
    preloads.push(
      '<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>'
    );

    // 页面特定资源
    switch (page) {
      case 'home':
        preloads.push(
          '<link rel="preload" href="/tradingAgent.png" as="image">',
          '<link rel="preload" href="/api/data/market-overview" as="fetch" crossorigin>'
        );
        break;
      case 'analysis':
        preloads.push(
          '<link rel="modulepreload" href="/_next/static/chunks/recharts.js">',
          '<link rel="preload" href="/api/analysis/latest" as="fetch" crossorigin>'
        );
        break;
      case 'tasks':
        preloads.push('<link rel="preload" href="/api/tasks" as="fetch" crossorigin>');
        break;
    }

    return preloads;
  }

  /**
   * 生成脚本优化配置
   */
  getScriptOptimizationConfig(
    src: string,
    critical = false
  ): {
    strategy: 'beforeInteractive' | 'afterInteractive' | 'lazyOnload';
    defer?: boolean;
    async?: boolean;
  } {
    if (!this.config.enableScriptOptimization) {
      return { strategy: 'afterInteractive' };
    }

    if (critical) {
      return { strategy: 'beforeInteractive' };
    }

    // 第三方脚本延迟加载
    if (src.includes('google-analytics') || src.includes('gtag')) {
      return { strategy: 'lazyOnload' };
    }

    return { strategy: 'afterInteractive', defer: true };
  }

  /**
   * 生成样式优化配置
   */
  getStyleOptimizationConfig(
    href: string,
    critical = false
  ): {
    media?: string;
    onLoad?: string;
  } {
    if (!this.config.enableStyleOptimization) {
      return {};
    }

    if (critical) {
      return {}; // 关键样式直接加载
    }

    // 非关键样式异步加载
    return {
      media: 'print',
      onLoad: "this.media='all'",
    };
  }

  /**
   * 检查 Core Web Vitals 阈值
   */
  checkCoreWebVitals(metrics: Partial<CoreWebVitalsThresholds>): {
    passed: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (metrics.LCP && metrics.LCP > this.thresholds.LCP) {
      issues.push(`LCP (${metrics.LCP}ms) exceeds threshold (${this.thresholds.LCP}ms)`);
      recommendations.push('优化图片加载、减少渲染阻塞资源、使用 CDN');
    }

    if (metrics.FID && metrics.FID > this.thresholds.FID) {
      issues.push(`FID (${metrics.FID}ms) exceeds threshold (${this.thresholds.FID}ms)`);
      recommendations.push('减少 JavaScript 执行时间、使用 Web Workers、优化事件处理');
    }

    if (metrics.CLS && metrics.CLS > this.thresholds.CLS) {
      issues.push(`CLS (${metrics.CLS}) exceeds threshold (${this.thresholds.CLS})`);
      recommendations.push('为图片和广告预留空间、避免动态插入内容、使用 transform 动画');
    }

    if (metrics.FCP && metrics.FCP > this.thresholds.FCP) {
      issues.push(`FCP (${metrics.FCP}ms) exceeds threshold (${this.thresholds.FCP}ms)`);
      recommendations.push('优化关键渲染路径、内联关键 CSS、减少服务器响应时间');
    }

    if (metrics.TTFB && metrics.TTFB > this.thresholds.TTFB) {
      issues.push(`TTFB (${metrics.TTFB}ms) exceeds threshold (${this.thresholds.TTFB}ms)`);
      recommendations.push('优化服务器性能、使用 CDN、启用缓存');
    }

    return {
      passed: issues.length === 0,
      issues,
      recommendations,
    };
  }

  /**
   * 生成性能监控脚本
   */
  generatePerformanceMonitoringScript(): string {
    return `
      <script>
        // Core Web Vitals 监控
        function sendToAnalytics(metric) {
          // 发送到分析服务
          if (typeof gtag !== 'undefined') {
            gtag('event', metric.name, {
              event_category: 'Web Vitals',
              event_label: metric.id,
              value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
              non_interaction: true,
            });
          }
        }

        // 动态导入 web-vitals
        import('https://unpkg.com/web-vitals@3/dist/web-vitals.js').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          getCLS(sendToAnalytics);
          getFID(sendToAnalytics);
          getFCP(sendToAnalytics);
          getLCP(sendToAnalytics);
          getTTFB(sendToAnalytics);
        });

        // 资源加载时间监控
        window.addEventListener('load', () => {
          const navigation = performance.getEntriesByType('navigation')[0];
          const paintEntries = performance.getEntriesByType('paint');
          
          console.log('Navigation timing:', {
            TTFB: navigation.responseStart - navigation.requestStart,
            DOMContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
            Load: navigation.loadEventEnd - navigation.navigationStart,
          });

          paintEntries.forEach(entry => {
            console.log(\`\${entry.name}: \${entry.startTime}ms\`);
          });
        });
      </script>
    `;
  }

  /**
   * 生成资源提示标签
   */
  generateResourceHints(): string[] {
    const hints: string[] = [];

    // DNS 预取
    const domains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://www.google-analytics.com',
      'https://api.openai.com',
    ];

    domains.forEach((domain) => {
      hints.push(`<link rel="dns-prefetch" href="${domain}">`);
    });

    // 预连接
    hints.push('<link rel="preconnect" href="https://fonts.googleapis.com">');
    hints.push('<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>');

    return hints;
  }
}

/**
 * 创建性能优化器实例
 */
export function createPerformanceOptimizer(
  config?: Partial<PerformanceConfig>
): PerformanceOptimizer {
  return new PerformanceOptimizer(config);
}

/**
 * 应用性能优化头到响应
 */
export function applyPerformanceHeaders(
  response: NextResponse,
  request: NextRequest,
  config?: Partial<PerformanceConfig>
): NextResponse {
  const optimizer = createPerformanceOptimizer(config);
  const headers = optimizer.generatePerformanceHeaders(request);

  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}
