/**
 * 风险管理师 WebSocket 实时通信模块
 * 提供风险管理师状态的实时推送功能
 */

import { Server as HTTPServer } from 'http';
import {
  RiskManagerProgressUpdate,
  RiskManagerState,
  riskManagerStateManager,
} from './risk-manager-state';

// 条件导入，避免在前端构建中包含
let SocketIOServer: any;
if (typeof window === 'undefined') {
  try {
    SocketIOServer = require('socket.io').Server;
  } catch (e) {
    // socket.io 不可用时的fallback
    SocketIOServer = null;
  }
}

export interface RiskManagerWebSocketServer {
  io: any; // 使用 any 类型避免构建时的类型错误
  initialize: () => void;
  cleanup: () => void;
}

let riskManagerWebSocketServer: RiskManagerWebSocketServer | null = null;

/**
 * 初始化风险管理师 WebSocket 服务器
 */
export function initializeRiskManagerWebSocket(httpServer: HTTPServer): RiskManagerWebSocketServer {
  if (riskManagerWebSocketServer) {
    return riskManagerWebSocketServer;
  }

  const io = new SocketIOServer(httpServer, {
    path: '/api/risk-manager/ws',
    cors: {
      origin: process.env.NODE_ENV === 'development' ? '*' : false,
      methods: ['GET', 'POST'],
    },
  });

  const server: RiskManagerWebSocketServer = {
    io,
    initialize: () => {
      console.log('[Risk Manager WebSocket] Initializing WebSocket server...');

      // 处理客户端连接
      io.on('connection', (socket: any) => {
        console.log(`[Risk Manager WebSocket] Client connected: ${socket.id}`);

        // 处理订阅特定工作流的风险管理师状态
        socket.on('subscribe_risk_manager', (data: { workflowId: string }) => {
          const { workflowId } = data;

          if (!workflowId) {
            socket.emit('error', { message: 'workflowId is required' });
            return;
          }

          const room = `risk_manager_${workflowId}`;
          socket.join(room);

          console.log(`[Risk Manager WebSocket] Client ${socket.id} subscribed to ${room}`);

          // 发送当前状态
          const currentState = riskManagerStateManager.getState(workflowId);
          if (currentState) {
            socket.emit('risk_manager_state_update', {
              workflowId,
              state: currentState,
            });
          }

          socket.emit('subscription_confirmed', { workflowId, room });
        });

        // 处理取消订阅
        socket.on('unsubscribe_risk_manager', (data: { workflowId: string }) => {
          const { workflowId } = data;

          if (!workflowId) {
            socket.emit('error', { message: 'workflowId is required' });
            return;
          }

          const room = `risk_manager_${workflowId}`;
          socket.leave(room);

          console.log(`[Risk Manager WebSocket] Client ${socket.id} unsubscribed from ${room}`);
          socket.emit('unsubscription_confirmed', { workflowId, room });
        });

        // 处理获取所有状态
        socket.on('get_all_risk_manager_states', () => {
          const allStates = riskManagerStateManager.getAllStates();
          const statistics = riskManagerStateManager.getStatistics();

          socket.emit('all_risk_manager_states', {
            states: allStates,
            statistics,
          });
        });

        // 处理客户端断开连接
        socket.on('disconnect', (reason: any) => {
          console.log(
            `[Risk Manager WebSocket] Client disconnected: ${socket.id}, reason: ${reason}`
          );
        });

        // 处理错误
        socket.on('error', (error: any) => {
          console.error(`[Risk Manager WebSocket] Socket error for client ${socket.id}:`, error);
        });
      });

      // 监听风险管理师状态管理器的事件
      setupStateManagerListeners();
    },

    cleanup: () => {
      console.log('[Risk Manager WebSocket] Cleaning up WebSocket server...');

      // 移除状态管理器监听器
      riskManagerStateManager.removeAllListeners('stateInitialized');
      riskManagerStateManager.removeAllListeners('stateUpdated');
      riskManagerStateManager.removeAllListeners('progressUpdate');

      // 关闭 WebSocket 服务器
      io.close();
    },
  };

  /**
   * 设置状态管理器事件监听器
   */
  function setupStateManagerListeners() {
    // 监听状态初始化事件
    riskManagerStateManager.on('stateInitialized', (state: RiskManagerState) => {
      const room = `risk_manager_${state.workflowId}`;
      io.to(room).emit('risk_manager_state_initialized', {
        workflowId: state.workflowId,
        state,
      });

      console.log(`[Risk Manager WebSocket] Broadcasted state initialization to room ${room}`);
    });

    // 监听状态更新事件
    riskManagerStateManager.on('stateUpdated', (state: RiskManagerState) => {
      const room = `risk_manager_${state.workflowId}`;
      io.to(room).emit('risk_manager_state_update', {
        workflowId: state.workflowId,
        state,
      });

      console.log(`[Risk Manager WebSocket] Broadcasted state update to room ${room}`);
    });

    // 监听进度更新事件
    riskManagerStateManager.on('progressUpdate', (update: RiskManagerProgressUpdate) => {
      const room = `risk_manager_${update.workflowId}`;
      io.to(room).emit('risk_manager_progress_update', update);

      console.log(
        `[Risk Manager WebSocket] Broadcasted progress update to room ${room}: ${update.progress}%`
      );
    });
  }

  riskManagerWebSocketServer = server;
  return server;
}

/**
 * 获取风险管理师 WebSocket 服务器实例
 */
export function getRiskManagerWebSocketServer(): RiskManagerWebSocketServer | null {
  return riskManagerWebSocketServer;
}

/**
 * 广播风险管理师状态更新到特定工作流
 */
export function broadcastRiskManagerStateUpdate(workflowId: string, state: RiskManagerState) {
  if (!riskManagerWebSocketServer) {
    console.warn('[Risk Manager WebSocket] WebSocket server not initialized');
    return;
  }

  const room = `risk_manager_${workflowId}`;
  riskManagerWebSocketServer.io.to(room).emit('risk_manager_state_update', {
    workflowId,
    state,
  });
}

/**
 * 广播风险管理师进度更新到特定工作流
 */
export function broadcastRiskManagerProgressUpdate(update: RiskManagerProgressUpdate) {
  if (!riskManagerWebSocketServer) {
    console.warn('[Risk Manager WebSocket] WebSocket server not initialized');
    return;
  }

  const room = `risk_manager_${update.workflowId}`;
  riskManagerWebSocketServer.io.to(room).emit('risk_manager_progress_update', update);
}

/**
 * 广播风险管理师错误到特定工作流
 */
export function broadcastRiskManagerError(workflowId: string, error: string) {
  if (!riskManagerWebSocketServer) {
    console.warn('[Risk Manager WebSocket] WebSocket server not initialized');
    return;
  }

  const room = `risk_manager_${workflowId}`;
  riskManagerWebSocketServer.io.to(room).emit('risk_manager_error', {
    workflowId,
    error,
    timestamp: Date.now(),
  });
}

/**
 * 获取特定工作流的连接客户端数量
 */
export function getRiskManagerRoomClientCount(workflowId: string): number {
  if (!riskManagerWebSocketServer) {
    return 0;
  }

  const room = `risk_manager_${workflowId}`;
  const roomSockets = riskManagerWebSocketServer.io.sockets.adapter.rooms.get(room);
  return roomSockets ? roomSockets.size : 0;
}

/**
 * 获取所有风险管理师房间的统计信息
 */
export function getRiskManagerRoomStatistics(): Array<{
  workflowId: string;
  room: string;
  clientCount: number;
}> {
  if (!riskManagerWebSocketServer) {
    return [];
  }

  const statistics: Array<{
    workflowId: string;
    room: string;
    clientCount: number;
  }> = [];

  const rooms = riskManagerWebSocketServer.io.sockets.adapter.rooms;

  rooms.forEach((sockets: any, roomName: any) => {
    if (roomName.startsWith('risk_manager_')) {
      const workflowId = roomName.replace('risk_manager_', '');
      statistics.push({
        workflowId,
        room: roomName,
        clientCount: sockets.size,
      });
    }
  });

  return statistics;
}
