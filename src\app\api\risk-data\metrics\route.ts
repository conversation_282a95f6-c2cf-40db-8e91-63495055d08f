/**
 * Risk Metrics API Routes
 * 风险指标和趋势分析API接口
 */

import { getRiskMetricsTrend, saveRiskMetricsHistory } from '@/lib/risk-data-storage';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/risk-data/metrics - 保存风险指标历史数据
 * 需求 9.2: 系统应保存风险指标的历史数据和趋势
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workflow_id, ticker, metrics } = body;

    // 验证必需字段
    if (!workflow_id || !ticker || !metrics) {
      return NextResponse.json(
        { error: 'Missing required fields: workflow_id, ticker, metrics' },
        { status: 400 }
      );
    }

    await saveRiskMetricsHistory(workflow_id, ticker, metrics);

    return NextResponse.json({
      success: true,
      message: 'Risk metrics saved successfully',
    });
  } catch (error) {
    console.error('Error saving risk metrics:', error);
    return NextResponse.json({ error: 'Failed to save risk metrics' }, { status: 500 });
  }
}

/**
 * GET /api/risk-data/metrics - 获取风险指标趋势数据
 * 查询参数:
 * - ticker: 股票代码
 * - metric: 指标名称
 * - days: 天数（默认30天）
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const ticker = searchParams.get('ticker');
    const metricName = searchParams.get('metric');
    const days = parseInt(searchParams.get('days') || '30');

    if (!ticker || !metricName) {
      return NextResponse.json(
        { error: 'Missing required parameters: ticker, metric' },
        { status: 400 }
      );
    }

    const trendData = await getRiskMetricsTrend(ticker, metricName, days);

    return NextResponse.json({
      success: true,
      data: {
        ticker,
        metric_name: metricName,
        days,
        trend_data: trendData,
      },
    });
  } catch (error) {
    console.error('Error retrieving risk metrics trend:', error);
    return NextResponse.json({ error: 'Failed to retrieve risk metrics trend' }, { status: 500 });
  }
}
