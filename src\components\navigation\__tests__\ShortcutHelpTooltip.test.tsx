import React from 'react';
import { KeyboardShortcut } from '@/types/navigation';
import { fireEvent, render, screen } from '@testing-library/react';
import { ShortcutHelpTooltip } from '../ShortcutHelpTooltip';

const mockShortcuts: KeyboardShortcut[] = [
  {
    key: 'Alt+H',
    description: '打开分析历史页面',
    action: jest.fn(),
    enabled: true,
    category: 'navigation',
  },
  {
    key: 'Alt+C',
    description: '打开分析对比页面',
    action: jest.fn(),
    enabled: true,
    category: 'navigation',
  },
  {
    key: 'Alt+T',
    description: '返回任务列表',
    action: jest.fn(),
    enabled: false,
    category: 'navigation',
  },
  {
    key: 'Ctrl+S',
    description: '保存分析',
    action: jest.fn(),
    enabled: true,
    category: 'analysis',
  },
];

describe('ShortcutHelpTooltip', () => {
  it('should render help button', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    expect(helpButton).toBeInTheDocument();
  });

  it('should show tooltip on hover', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Initially tooltip should not be visible
    expect(screen.queryByText('键盘快捷键')).not.toBeInTheDocument();

    // Hover over button
    fireEvent.mouseEnter(helpButton);

    // Tooltip should be visible
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();
  });

  it('should hide tooltip on mouse leave', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Show tooltip
    fireEvent.mouseEnter(helpButton);
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();

    // Hide tooltip
    fireEvent.mouseLeave(helpButton);
    expect(screen.queryByText('键盘快捷键')).not.toBeInTheDocument();
  });

  it('should show tooltip on focus', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Focus button
    fireEvent.focus(helpButton);

    // Tooltip should be visible
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();
  });

  it('should hide tooltip on blur', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });

    // Show tooltip
    fireEvent.focus(helpButton);
    expect(screen.getByText('键盘快捷键')).toBeInTheDocument();

    // Hide tooltip
    fireEvent.blur(helpButton);
    expect(screen.queryByText('键盘快捷键')).not.toBeInTheDocument();
  });

  it('should display shortcuts grouped by category', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Check category headers
    expect(screen.getByText('导航')).toBeInTheDocument();
    expect(screen.getByText('分析')).toBeInTheDocument();

    // Check shortcuts in navigation category
    expect(screen.getByText('打开分析历史页面')).toBeInTheDocument();
    expect(screen.getByText('打开分析对比页面')).toBeInTheDocument();

    // Check shortcuts in analysis category
    expect(screen.getByText('保存分析')).toBeInTheDocument();
  });

  it('should only show enabled shortcuts', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Enabled shortcuts should be visible
    expect(screen.getByText('打开分析历史页面')).toBeInTheDocument();
    expect(screen.getByText('打开分析对比页面')).toBeInTheDocument();

    // Disabled shortcut should not be visible
    expect(screen.queryByText('返回任务列表')).not.toBeInTheDocument();
  });

  it('should format shortcut keys correctly', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Check formatted keys
    expect(screen.getByText('⌥ H')).toBeInTheDocument(); // Alt+H
    expect(screen.getByText('⌥ C')).toBeInTheDocument(); // Alt+C
    expect(screen.getByText('⌃ S')).toBeInTheDocument(); // Ctrl+S
  });

  it('should show empty state when no shortcuts provided', () => {
    render(<ShortcutHelpTooltip shortcuts={[]} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    expect(screen.getByText('暂无可用的键盘快捷键')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(
      <ShortcutHelpTooltip shortcuts={mockShortcuts} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('should have proper accessibility attributes', () => {
    render(<ShortcutHelpTooltip shortcuts={mockShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    const tooltip = screen.getByRole('tooltip');
    expect(tooltip).toBeInTheDocument();
  });

  it('should handle shortcuts without category', () => {
    const shortcutsWithoutCategory: KeyboardShortcut[] = [
      {
        key: 'F1',
        description: '帮助',
        action: jest.fn(),
        enabled: true,
      },
    ];

    render(<ShortcutHelpTooltip shortcuts={shortcutsWithoutCategory} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    // Should show under 'general' category
    expect(screen.getByText('通用')).toBeInTheDocument();
    expect(screen.getByText('帮助')).toBeInTheDocument();
  });

  it('should format complex shortcut keys', () => {
    const complexShortcuts: KeyboardShortcut[] = [
      {
        key: 'Cmd+Shift+A',
        description: '复杂快捷键',
        action: jest.fn(),
        enabled: true,
      },
    ];

    render(<ShortcutHelpTooltip shortcuts={complexShortcuts} />);

    const helpButton = screen.getByRole('button', { name: '显示键盘快捷键帮助' });
    fireEvent.mouseEnter(helpButton);

    expect(screen.getByText('⌘ ⇧ A')).toBeInTheDocument();
  });
});
