import { getCurrentUser } from '@/lib/api';
import { create } from 'zustand';

interface User {
  id: number;
  username: string;
  email: string;
}

interface UserState {
  user: User | null;
  sessionId: string | null;
  loading: boolean;
  initialized: boolean;
  fetchUser: () => Promise<void>;
  setUser: (user: User, sessionId?: string) => void;
  setSession: (sessionId: string) => void;
  clearUser: () => void;
  isAuthenticated: () => boolean;
  getAuthHeaders: () => Record<string, string>;
}

const useUserStore = create<UserState>((set, get) => ({
  user: null,
  sessionId: null,
  loading: false,
  initialized: false,

  fetchUser: async () => {
    const state = get();

    // 如果正在加载或已经初始化过，直接返回
    if (state.loading || state.initialized) {
      return;
    }

    set({ loading: true });
    try {
      const response = await getCurrentUser();
      if (response.success) {
        set({ user: response.data, loading: false, initialized: true });
      } else {
        console.error('Failed to fetch user:', response.message);
        set({ user: null, sessionId: null, loading: false, initialized: true });
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      set({ user: null, sessionId: null, loading: false, initialized: true });

      // 如果是401错误，不要继续重试
      if (error instanceof Error && error.message.includes('401')) {
        console.log('User not authenticated, stopping retry attempts');
        return;
      }
    }
  },

  setUser: (user: User, sessionId?: string) => {
    set({ user, sessionId: sessionId || get().sessionId });
  },

  setSession: (sessionId: string) => {
    set({ sessionId });
  },

  clearUser: () => {
    set({ user: null, sessionId: null, initialized: false });
  },

  isAuthenticated: () => {
    const state = get();
    return state.sessionId !== null;
  },

  getAuthHeaders: () => {
    const state = get();
    const headers: Record<string, string> = {};

    if (state.sessionId) {
      headers['X-Session-ID'] = state.sessionId;
    }

    return headers;
  },
}));

export default useUserStore;
