/**
 * 数据服务工具类
 * 提供统一的数据获取接口，封装各种数据API调用
 */

import { serverApi } from './server-api';

// 数据类型定义
export interface StockData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  amount: number;
  turnover: number;
  pctChange: number;
}

export interface NewsData {
  id: string;
  title: string;
  summary: string;
  source: string;
  url: string;
  publishedAt: string;
  sentiment: number;
  category: string;
  importance: number;
}

export interface TechnicalIndicators {
  ma?: Array<{
    date: string;
    close: number;
    ma5?: number;
    ma10?: number;
    ma20?: number;
    ma60?: number;
  }>;
  macd?: Array<{
    date: string;
    macd: number;
    signal: number;
    histogram: number;
  }>;
  rsi?: Array<{
    date: string;
    rsi: number | null;
  }>;
  kdj?: Array<{
    date: string;
    k: number | null;
    d: number | null;
    j: number | null;
  }>;
  bollinger?: Array<{
    date: string;
    upper: number | null;
    middle: number | null;
    lower: number | null;
  }>;
  volume?: Array<{
    date: string;
    volume: number;
    volumeMA5: number | null;
    volumeMA10: number | null;
    volumeRatio: number | null;
  }>;
}

export interface FundamentalData {
  financials: {
    revenue: number | null;
    netIncome: number | null;
    grossProfit: number | null;
    operatingIncome: number | null;
    eps: number | null;
    operatingCosts: number | null;
    totalCosts: number | null;
  };
  valuation: {
    pe: number | null;
    pb: number | null;
    ps: number | null;
    pcf: number | null;
    ev: number | null;
    marketCap: number | null;
  };
  growth: {
    revenueGrowth: number | null;
    netIncomeGrowth: number | null;
    epsGrowth: number | null;
    operatingIncomeGrowth: number | null;
  };
  balanceSheet: {
    totalAssets: number | null;
    totalLiabilities: number | null;
    shareholderEquity: number | null;
    currentAssets: number | null;
    currentLiabilities: number | null;
    longTermDebt: number | null;
  };
  cashFlow: {
    operatingCashFlow: number | null;
    investingCashFlow: number | null;
    financingCashFlow: number | null;
    freeCashFlow: number | null;
    cashAndEquivalents: number | null;
  };
  ratios: {
    roe: number | null;
    roa: number | null;
    grossMargin: number | null;
    netMargin: number | null;
    operatingMargin: number | null;
    currentRatio: number | null;
    quickRatio: number | null;
    debtToEquity: number | null;
  };
}

// API响应类型
export interface ApiResponse<T> {
  ticker: string;
  name: string;
  data?: T;
  news?: NewsData[];
  indicators?: TechnicalIndicators;
  fundamentals?: FundamentalData;
  stats?: any;
  analysis?: any;
  count?: number;
  period?: string;
  updatedAt: string;
}

// 请求参数类型
export interface StockDataParams {
  ticker: string;
  period?: string;
  startDate?: string;
  endDate?: string;
}

export interface NewsDataParams {
  ticker: string;
  limit?: number;
  days?: number;
}

export interface TechnicalDataParams {
  ticker: string;
  indicators?: string[];
  period?: string;
  days?: number;
}

export interface FundamentalDataParams {
  ticker: string;
  type?: 'all' | 'financials' | 'valuation' | 'growth' | 'balance_sheet' | 'cash_flow' | 'ratios';
  period?: 'annual' | 'quarterly';
}

/**
 * 数据服务类
 */
export class DataService {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || '/api/data';
  }

  /**
   * 获取股票数据
   */
  async getStockData(params: StockDataParams): Promise<ApiResponse<StockData[]>> {
    try {
      const { ticker, period, startDate, endDate } = params;

      const queryParams = new URLSearchParams();
      if (period) queryParams.append('period', period);
      if (startDate) queryParams.append('start_date', startDate);
      if (endDate) queryParams.append('end_date', endDate);

      const url = `${this.baseUrl}/stock/${ticker}?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取股票数据失败:', error);
      throw this.handleError(error, '获取股票数据失败');
    }
  }

  /**
   * 获取新闻数据
   */
  async getNewsData(params: NewsDataParams): Promise<ApiResponse<NewsData[]>> {
    try {
      const { ticker, limit, days } = params;

      const queryParams = new URLSearchParams();
      if (limit) queryParams.append('limit', limit.toString());
      if (days) queryParams.append('days', days.toString());

      const url = `${this.baseUrl}/news/${ticker}?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取新闻数据失败:', error);
      throw this.handleError(error, '获取新闻数据失败');
    }
  }

  /**
   * 获取技术指标数据
   */
  async getTechnicalData(params: TechnicalDataParams): Promise<ApiResponse<TechnicalIndicators>> {
    try {
      const { ticker, indicators, period, days } = params;

      const queryParams = new URLSearchParams();
      if (indicators && indicators.length > 0) {
        queryParams.append('indicators', indicators.join(','));
      }
      if (period) queryParams.append('period', period);
      if (days) queryParams.append('days', days.toString());

      const url = `${this.baseUrl}/technical/${ticker}?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取技术指标数据失败:', error);
      throw this.handleError(error, '获取技术指标数据失败');
    }
  }

  /**
   * 获取基本面数据
   */
  async getFundamentalData(params: FundamentalDataParams): Promise<ApiResponse<FundamentalData>> {
    try {
      const { ticker, type, period } = params;

      const queryParams = new URLSearchParams();
      if (type) queryParams.append('type', type);
      if (period) queryParams.append('period', period);

      const url = `${this.baseUrl}/fundamentals/${ticker}?${queryParams.toString()}`;
      const response = await serverApi.get(url);

      return response.data;
    } catch (error) {
      console.error('获取基本面数据失败:', error);
      throw this.handleError(error, '获取基本面数据失败');
    }
  }

  /**
   * 获取综合数据 (并行获取多种数据)
   */
  async getComprehensiveData(
    ticker: string,
    options?: {
      includeStock?: boolean;
      includeNews?: boolean;
      includeTechnical?: boolean;
      includeFundamental?: boolean;
      stockPeriod?: string;
      newsLimit?: number;
      technicalIndicators?: string[];
      fundamentalType?: string;
    }
  ) {
    const {
      includeStock = true,
      includeNews = true,
      includeTechnical = true,
      includeFundamental = true,
      stockPeriod = '1m',
      newsLimit = 10,
      technicalIndicators = ['ma', 'macd', 'rsi'],
      fundamentalType = 'all',
    } = options || {};

    const promises: Promise<any>[] = [];
    const dataTypes: string[] = [];

    if (includeStock) {
      promises.push(this.getStockData({ ticker, period: stockPeriod }));
      dataTypes.push('stock');
    }

    if (includeNews) {
      promises.push(this.getNewsData({ ticker, limit: newsLimit }));
      dataTypes.push('news');
    }

    if (includeTechnical) {
      promises.push(this.getTechnicalData({ ticker, indicators: technicalIndicators }));
      dataTypes.push('technical');
    }

    if (includeFundamental) {
      promises.push(this.getFundamentalData({ ticker, type: fundamentalType as any }));
      dataTypes.push('fundamental');
    }

    try {
      const results = await Promise.allSettled(promises);

      const data: any = {
        ticker,
        timestamp: new Date().toISOString(),
      };

      results.forEach((result, index) => {
        const dataType = dataTypes[index];
        if (result.status === 'fulfilled') {
          data[dataType] = result.value;
        } else {
          console.error(`获取${dataType}数据失败:`, result.reason);
          data[dataType] = { error: result.reason.message };
        }
      });

      return data;
    } catch (error) {
      console.error('获取综合数据失败:', error);
      throw this.handleError(error, '获取综合数据失败');
    }
  }

  /**
   * 批量获取多只股票的数据
   */
  async getBatchStockData(
    tickers: string[],
    options?: {
      period?: string;
      dataType?: 'stock' | 'news' | 'technical' | 'fundamental';
    }
  ): Promise<{ [ticker: string]: any }> {
    const { period = '1m', dataType = 'stock' } = options || {};

    const promises = tickers.map((ticker) => {
      switch (dataType) {
        case 'stock':
          return this.getStockData({ ticker, period });
        case 'news':
          return this.getNewsData({ ticker, limit: 5 });
        case 'technical':
          return this.getTechnicalData({ ticker, indicators: ['ma', 'rsi'] });
        case 'fundamental':
          return this.getFundamentalData({ ticker, type: 'valuation' });
        default:
          return this.getStockData({ ticker, period });
      }
    });

    try {
      const results = await Promise.allSettled(promises);

      const batchData: { [ticker: string]: any } = {};

      results.forEach((result, index) => {
        const ticker = tickers[index];
        if (result.status === 'fulfilled') {
          batchData[ticker] = result.value;
        } else {
          console.error(`获取${ticker}数据失败:`, result.reason);
          batchData[ticker] = { error: result.reason.message };
        }
      });

      return batchData;
    } catch (error) {
      console.error('批量获取数据失败:', error);
      throw this.handleError(error, '批量获取数据失败');
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: any, defaultMessage: string): Error {
    if (error.response) {
      // API返回的错误
      const { status, data } = error.response;
      const message = data?.message || data?.error || `HTTP ${status} 错误`;
      return new Error(message);
    } else if (error.request) {
      // 网络错误
      return new Error('网络连接失败，请检查网络连接');
    } else {
      // 其他错误
      return new Error(error.message || defaultMessage);
    }
  }
}

// 创建默认实例
export const dataService = new DataService();

// 导出工具函数
export const dataUtils = {
  /**
   * 格式化数值
   */
  formatNumber: (value: number | null, decimals: number = 2): string => {
    if (value === null || value === undefined) return '--';
    return value.toFixed(decimals);
  },

  /**
   * 格式化百分比
   */
  formatPercentage: (value: number | null, decimals: number = 2): string => {
    if (value === null || value === undefined) return '--';
    return `${value.toFixed(decimals)}%`;
  },

  /**
   * 格式化大数值 (万、亿)
   */
  formatLargeNumber: (value: number | null): string => {
    if (value === null || value === undefined) return '--';

    if (value >= 100000000) {
      return `${(value / 100000000).toFixed(2)}亿`;
    } else if (value >= 10000) {
      return `${(value / 10000).toFixed(2)}万`;
    } else {
      return value.toFixed(2);
    }
  },

  /**
   * 计算涨跌颜色
   */
  getPriceColor: (change: number | null): string => {
    if (change === null || change === undefined) return 'text-gray-500';
    if (change > 0) return 'text-red-500';
    if (change < 0) return 'text-green-500';
    return 'text-gray-500';
  },

  /**
   * 获取情绪颜色
   */
  getSentimentColor: (sentiment: number | null): string => {
    if (sentiment === null || sentiment === undefined) return 'text-gray-500';
    if (sentiment > 0.1) return 'text-red-500';
    if (sentiment < -0.1) return 'text-green-500';
    return 'text-gray-500';
  },

  /**
   * 验证股票代码格式
   */
  validateTicker: (ticker: string): boolean => {
    // 简单的股票代码验证 (6位数字)
    return /^\d{6}$/.test(ticker);
  },

  /**
   * 获取股票市场
   */
  getMarket: (ticker: string): string => {
    if (!ticker || ticker.length !== 6) return 'unknown';

    const code = ticker.substring(0, 3);
    if (code >= '000' && code <= '002') return 'SZSE'; // 深交所
    if (code >= '300' && code <= '399') return 'SZSE'; // 创业板
    if (code >= '600' && code <= '699') return 'SSE'; // 上交所
    if (code >= '688' && code <= '689') return 'SSE'; // 科创板

    return 'unknown';
  },
};
