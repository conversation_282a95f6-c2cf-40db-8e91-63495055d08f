/**
 * SEO 优化的图片组件
 * 提供自动 alt 属性生成、懒加载、WebP 格式支持等功能
 */

'use client';

import Image, { ImageProps } from 'next/image';
import { useEffect, useState } from 'react';

interface OptimizedImageProps extends Omit<ImageProps, 'alt'> {
  // 图片描述 - 如果不提供会自动生成
  alt?: string;
  // 图片类型，用于自动生成 alt
  imageType?: 'logo' | 'chart' | 'screenshot' | 'avatar' | 'icon' | 'decorative' | 'content';
  // 图片上下文，用于生成更准确的 alt
  context?: {
    stockSymbol?: string;
    analysisType?: string;
    userName?: string;
    chartType?: string;
    feature?: string;
  };
  // 是否为装饰性图片
  decorative?: boolean;
  // 图片标题
  title?: string;
  // 图片说明文字
  caption?: string;
  // 是否显示加载状态
  showLoading?: boolean;
  // 加载失败时的回退图片
  fallbackSrc?: string;
  // 是否启用 WebP 格式
  enableWebP?: boolean;
  // 图片质量 (1-100)
  quality?: number;
  // 自定义类名
  className?: string;
}

/**
 * 自动生成 alt 属性
 */
function generateAltText(
  src: string,
  imageType: OptimizedImageProps['imageType'] = 'content',
  context?: OptimizedImageProps['context']
): string {
  const filename = src.split('/').pop()?.split('.')[0] || '';

  switch (imageType) {
    case 'logo':
      return context?.feature ? `${context.feature} 标志` : 'TradingAgents 标志';

    case 'chart':
      if (context?.stockSymbol && context?.chartType) {
        return `${context.stockSymbol} ${context.chartType}图表`;
      }
      if (context?.stockSymbol) {
        return `${context.stockSymbol} 股票图表`;
      }
      return '股票分析图表';

    case 'screenshot':
      if (context?.feature) {
        return `${context.feature} 功能截图`;
      }
      return '应用功能截图';

    case 'avatar':
      if (context?.userName) {
        return `${context.userName} 的头像`;
      }
      return '用户头像';

    case 'icon':
      if (context?.feature) {
        return `${context.feature} 图标`;
      }
      return filename ? `${filename} 图标` : '功能图标';

    case 'decorative':
      return ''; // 装饰性图片不需要 alt

    case 'content':
    default:
      // 尝试从文件名生成描述
      if (filename) {
        return filename.replace(/[-_]/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
      }
      return '内容图片';
  }
}

/**
 * 获取优化的图片格式
 */
function getOptimizedSrc(src: string, enableWebP: boolean = true): string {
  if (!enableWebP || src.startsWith('data:') || src.startsWith('blob:')) {
    return src;
  }

  // 如果是外部链接，不进行格式转换
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }

  // 对于本地图片，Next.js 会自动处理格式优化
  return src;
}

/**
 * 生成响应式尺寸
 */
function generateSizes(width?: number, height?: number): string {
  if (!width) {
    return '100vw';
  }

  // 根据图片宽度生成响应式尺寸
  if (width <= 400) {
    return '(max-width: 768px) 100vw, 400px';
  } else if (width <= 800) {
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px';
  } else {
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
  }
}

export default function OptimizedImage({
  alt,
  imageType = 'content',
  context,
  decorative = false,
  title,
  caption,
  showLoading = true,
  fallbackSrc,
  enableWebP = true,
  quality = 85,
  className = '',
  sizes,
  ...props
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(props.src);

  // 生成 alt 文本
  const altText = decorative
    ? ''
    : alt || generateAltText(props.src.toString(), imageType, context);

  // 优化图片源
  const optimizedSrc = getOptimizedSrc(currentSrc.toString(), enableWebP);

  // 生成响应式尺寸
  const responsiveSizes =
    sizes ||
    generateSizes(
      typeof props.width === 'number' ? props.width : undefined,
      typeof props.height === 'number' ? props.height : undefined
    );

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  // 处理图片加载错误
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);

    // 如果有回退图片，使用回退图片
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    }
  };

  // 重置状态当 src 改变时
  useEffect(() => {
    setCurrentSrc(props.src);
    setIsLoading(true);
    setHasError(false);
  }, [props.src]);

  const imageProps: ImageProps = {
    ...props,
    src: optimizedSrc,
    alt: altText,
    sizes: responsiveSizes,
    quality,
    onLoad: handleLoad,
    onError: handleError,
    className: `optimized-image ${className} ${isLoading ? 'loading' : ''} ${
      hasError ? 'error' : ''
    }`,
    ...(title && { title }),
    ...(decorative && { 'aria-hidden': true }),
  };

  return (
    <figure className={`optimized-image-container ${caption ? 'with-caption' : ''}`}>
      {/* 加载状态 */}
      {showLoading && isLoading && (
        <div
          className="optimized-image-loading absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"
          aria-label="图片加载中"
        />
      )}

      {/* 错误状态 */}
      {hasError && !fallbackSrc && (
        <div
          className="optimized-image-error flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 rounded"
          style={{
            width: typeof props.width === 'number' ? props.width : '100%',
            height: typeof props.height === 'number' ? props.height : 200,
          }}
          role="img"
          aria-label="图片加载失败"
        >
          <div className="text-center">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm">图片加载失败</p>
          </div>
        </div>
      )}

      {/* 图片 */}
      {!hasError && <Image {...imageProps} />}

      {/* 图片说明 */}
      {caption && (
        <figcaption className="optimized-image-caption mt-2 text-sm text-gray-600 dark:text-gray-400 text-center">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}

/**
 * 预设的图片组件变体
 */

// Logo 图片
export function LogoImage(props: Omit<OptimizedImageProps, 'imageType'>) {
  return <OptimizedImage {...props} imageType="logo" />;
}

// 图表图片
export function ChartImage(props: Omit<OptimizedImageProps, 'imageType'>) {
  return <OptimizedImage {...props} imageType="chart" />;
}

// 截图图片
export function ScreenshotImage(props: Omit<OptimizedImageProps, 'imageType'>) {
  return <OptimizedImage {...props} imageType="screenshot" />;
}

// 头像图片
export function AvatarImage(props: Omit<OptimizedImageProps, 'imageType'>) {
  return <OptimizedImage {...props} imageType="avatar" />;
}

// 图标图片
export function IconImage(props: Omit<OptimizedImageProps, 'imageType'>) {
  return <OptimizedImage {...props} imageType="icon" />;
}

// 装饰性图片
export function DecorativeImage(props: Omit<OptimizedImageProps, 'imageType' | 'decorative'>) {
  return <OptimizedImage {...props} imageType="decorative" decorative={true} />;
}

/**
 * 图片 SEO 优化工具函数
 */
export const imageOptimizationUtils = {
  /**
   * 验证图片 alt 属性
   */
  validateAlt: (
    alt: string,
    imageType: OptimizedImageProps['imageType'] = 'content'
  ): {
    isValid: boolean;
    suggestions: string[];
  } => {
    const suggestions: string[] = [];

    if (!alt || alt.trim() === '') {
      return {
        isValid: imageType === 'decorative',
        suggestions: ['图片缺少 alt 属性描述'],
      };
    }

    if (alt.length > 125) {
      suggestions.push('alt 文本过长，建议控制在 125 字符以内');
    }

    if (alt.length < 3) {
      suggestions.push('alt 文本过短，建议使用更具描述性的文本');
    }

    // 检查是否包含无用词汇
    const uselessWords = ['图片', '图像', 'image', 'picture', 'photo'];
    const hasUselessWords = uselessWords.some((word) =>
      alt.toLowerCase().includes(word.toLowerCase())
    );

    if (hasUselessWords) {
      suggestions.push('避免在 alt 文本中使用"图片"、"图像"等冗余词汇');
    }

    return {
      isValid: suggestions.length === 0,
      suggestions,
    };
  },

  /**
   * 生成图片的结构化数据
   */
  generateImageStructuredData: (
    src: string,
    alt: string,
    context?: {
      caption?: string;
      author?: string;
      datePublished?: string;
      license?: string;
    }
  ) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'ImageObject',
      url: src,
      description: alt,
      ...(context?.caption && { caption: context.caption }),
      ...(context?.author && { author: { '@type': 'Person', name: context.author } }),
      ...(context?.datePublished && { datePublished: context.datePublished }),
      ...(context?.license && { license: context.license }),
    };
  },

  /**
   * 获取图片的性能优化建议
   */
  getPerformanceOptimizations: (width?: number, height?: number, fileSize?: number): string[] => {
    const suggestions: string[] = [];

    if (width && width > 1920) {
      suggestions.push('图片宽度过大，建议压缩到合适尺寸');
    }

    if (height && height > 1080) {
      suggestions.push('图片高度过大，建议压缩到合适尺寸');
    }

    if (fileSize && fileSize > 500 * 1024) {
      // 500KB
      suggestions.push('图片文件过大，建议压缩或使用 WebP 格式');
    }

    return suggestions;
  },
};
