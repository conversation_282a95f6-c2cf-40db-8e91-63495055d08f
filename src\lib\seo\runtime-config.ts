/**
 * SEO 运行时配置
 * 处理环境变量和动态配置
 */

import { SEO_CONSTANTS } from './config';

// 运行时 SEO 配置接口
export interface RuntimeSEOConfig {
  SITE_URL: string;
  SITE_NAME: string;
  GOOGLE_VERIFICATION: string;
  BAIDU_VERIFICATION: string;
  GA_MEASUREMENT_ID: string;
  GSC_PROPERTY_ID: string;
  ENABLE_ANALYTICS: boolean;
  ENABLE_SEARCH_CONSOLE: boolean;
  ENVIRONMENT: 'development' | 'production' | 'test';
}

// 缓存的配置
let cachedConfig: RuntimeSEOConfig | null = null;

/**
 * 获取运行时 SEO 配置
 */
export function getRuntimeSEOConfig(): RuntimeSEOConfig {
  if (cachedConfig) {
    return cachedConfig;
  }

  const config: RuntimeSEOConfig = {
    SITE_URL:
      process.env.NEXT_PUBLIC_SITE_URL ||
      process.env.NEXT_PUBLIC_BASE_URL ||
      SEO_CONSTANTS.SITE_URL,
    SITE_NAME: process.env.NEXT_PUBLIC_SITE_NAME || SEO_CONSTANTS.SITE_NAME,
    GOOGLE_VERIFICATION: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || '',
    BAIDU_VERIFICATION: process.env.NEXT_PUBLIC_BAIDU_VERIFICATION || '',
    GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '',
    GSC_PROPERTY_ID: process.env.NEXT_PUBLIC_GSC_PROPERTY_ID || '',
    ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    ENABLE_SEARCH_CONSOLE: process.env.NEXT_PUBLIC_ENABLE_SEARCH_CONSOLE === 'true',
    ENVIRONMENT: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
  };

  // 验证必需的配置
  if (!config.SITE_URL) {
    console.warn('NEXT_PUBLIC_SITE_URL is not set, using default value');
  }

  // 确保 URL 格式正确
  if (config.SITE_URL && !config.SITE_URL.startsWith('http')) {
    config.SITE_URL = `https://${config.SITE_URL}`;
  }

  // 移除尾部斜杠
  if (config.SITE_URL.endsWith('/')) {
    config.SITE_URL = config.SITE_URL.slice(0, -1);
  }

  cachedConfig = config;
  return config;
}

/**
 * 重置配置缓存（主要用于测试）
 */
export function resetRuntimeSEOConfig(): void {
  cachedConfig = null;
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  return getRuntimeSEOConfig().ENVIRONMENT === 'production';
}

/**
 * 检查是否启用了分析功能
 */
export function isAnalyticsEnabled(): boolean {
  const config = getRuntimeSEOConfig();
  return config.ENABLE_ANALYTICS && !!config.GA_MEASUREMENT_ID;
}

/**
 * 检查是否启用了 Search Console
 */
export function isSearchConsoleEnabled(): boolean {
  const config = getRuntimeSEOConfig();
  return config.ENABLE_SEARCH_CONSOLE && !!config.GSC_PROPERTY_ID;
}

/**
 * 获取完整的 URL
 */
export function getFullUrl(path: string): string {
  const config = getRuntimeSEOConfig();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${config.SITE_URL}${cleanPath}`;
}

/**
 * 获取 CDN URL（如果配置了 CDN）
 */
export function getCDNUrl(path: string): string {
  const cdnUrl = process.env.NEXT_PUBLIC_CDN_URL;
  if (cdnUrl) {
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${cdnUrl}${cleanPath}`;
  }
  return getFullUrl(path);
}

/**
 * 获取环境特定的配置
 */
export function getEnvironmentConfig() {
  const config = getRuntimeSEOConfig();

  return {
    isDevelopment: config.ENVIRONMENT === 'development',
    isProduction: config.ENVIRONMENT === 'production',
    isTest: config.ENVIRONMENT === 'test',
    enableDebugHeaders: config.ENVIRONMENT === 'development',
    enableCaching: config.ENVIRONMENT === 'production',
    enableMinification: config.ENVIRONMENT === 'production',
  };
}
