import { api } from '@/lib/api';
import { useCallback, useEffect, useState } from 'react';

export interface AnalysisStatus {
  workflowId?: string;
  taskId?: string;
  ticker?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentStage: string;
  stages: {
    fundamental: boolean;
    technical: boolean;
    sentiment: boolean;
    news: boolean;
    bull: boolean;
    bear: boolean;
    consensus: boolean;
    risk: boolean;
    decision: boolean;
  };
  statistics: {
    totalMessages: number;
    debateRounds: number;
    duration: number;
  };
  createdAt?: string;
  startedAt?: string;
  completedAt?: string;
  latestReports: {
    analysts: any;
    researchers: any;
    consensus?: any;
    risk?: any;
    decision?: any;
  };
}

export interface UseAnalysisStatusOptions {
  workflowId?: string;
  taskId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onStatusChange?: (status: AnalysisStatus) => void;
}

export function useAnalysisStatus(options: UseAnalysisStatusOptions) {
  const {
    workflowId,
    taskId,
    autoRefresh = false,
    refreshInterval = 5000,
    onStatusChange,
  } = options;

  const [status, setStatus] = useState<AnalysisStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    if (!workflowId && !taskId) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (workflowId) params.append('workflow_id', workflowId);
      if (taskId) params.append('task_id', taskId);

      const response = await api.get(`/api/analysis/status?${params.toString()}`);

      if ((response as any).success) {
        const newStatus = (response as any).data as AnalysisStatus;
        setStatus(newStatus);

        if (onStatusChange) {
          onStatusChange(newStatus);
        }
      } else {
        setError((response as any).message || '获取分析状态失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取分析状态失败';
      setError(errorMessage);
      console.error('获取分析状态失败:', err);
    } finally {
      setLoading(false);
    }
  }, [workflowId, taskId, onStatusChange]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh || (!workflowId && !taskId)) return;

    // 立即获取一次状态
    fetchStatus();

    // 设置定时刷新
    const interval = setInterval(() => {
      // 只有在分析运行中时才继续刷新
      if (status?.status === 'running') {
        fetchStatus();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchStatus, status?.status]);

  // 手动刷新
  const refresh = useCallback(() => {
    fetchStatus();
  }, [fetchStatus]);

  // 启动分析
  const startAnalysis = useCallback(
    async (taskId: string, config?: any) => {
      setLoading(true);
      setError(null);

      try {
        const response = await api.post('/api/analysis/start', {
          taskId,
          config,
        });

        if ((response as any).success) {
          // 启动成功后立即刷新状态
          setTimeout(() => {
            fetchStatus();
          }, 1000);

          return response;
        } else {
          setError((response as any).message || '启动分析失败');
          return response;
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '启动分析失败';
        setError(errorMessage);
        console.error('启动分析失败:', err);
        return { success: false, message: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [fetchStatus]
  );

  return {
    status,
    loading,
    error,
    refresh,
    startAnalysis,

    // 便捷的状态检查
    isRunning: status?.status === 'running',
    isCompleted: status?.status === 'completed',
    isFailed: status?.status === 'failed',
    isPending: status?.status === 'pending',

    // 进度信息
    progress: status?.progress || 0,
    currentStage: status?.currentStage || 'pending',

    // 阶段完成状态
    completedStages: status ? Object.values(status.stages).filter(Boolean).length : 0,
    totalStages: 9, // 总共9个阶段
  };
}
