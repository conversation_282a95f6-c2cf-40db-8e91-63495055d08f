/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-04 21:56:55
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-08-13 21:19:49
 * @FilePath: \trading-agents-frontend\src\app\layout.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { SafeWrapper } from '@/components/common/SafeWrapper';
import { Footer } from '@/components/layout/Footer';
import { Header } from '@/components/layout/Header';
import { Providers } from '@/components/providers';

import { createPerformanceOptimizer } from '@/lib/seo/performance-optimization';
import { createSSROptimizer } from '@/lib/seo/ssr-optimization';
import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

// 在服务端启动时打印环境变量
if (typeof window === 'undefined') {
  console.log('🚀 TradingAgents Frontend 启动中...');
}

const inter = Inter({ subsets: ['latin'] });

// 生成根布局的 SEO 元数据
async function generateRootMetadata(): Promise<Metadata> {
  const ssrOptimizer = createSSROptimizer();

  const optimizedMetadata = await ssrOptimizer.generateOptimizedMetadata({
    page: 'home',
    locale: 'zh',
  });

  // 添加根布局特有的配置
  return {
    ...optimizedMetadata,
    icons: {
      icon: '/tradingAgent.ico',
      shortcut: '/tradingAgent.ico',
      apple: '/tradingAgent.png',
      other: [
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '32x32',
          url: '/tradingAgent.png',
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          url: '/tradingAgent.ico',
        },
      ],
    },
    manifest: '/manifest.json',
    other: {
      'msapplication-TileColor': '#000000',
      'theme-color': '#000000',
    },
  };
}

export async function generateMetadata(): Promise<Metadata> {
  return await generateRootMetadata();
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const performanceOptimizer = createPerformanceOptimizer();
  const ssrOptimizer = createSSROptimizer();

  // 生成关键资源预加载
  const criticalPreloads = ssrOptimizer.generateCriticalCSS('home');
  const resourceHints = performanceOptimizer.generateResourceHints();

  return (
    <html lang="zh-CN">
      <head>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebApplication',
              name: 'TradingAgents',
              description: '多智能体大语言模型金融交易框架',
              url: 'https://tradingagents.com',
            }),
          }}
        />

        {/* 关键 CSS 内联 */}
        {criticalPreloads && <style dangerouslySetInnerHTML={{ __html: criticalPreloads }} />}

        {/* 预加载关键资源 */}
        {/* <link rel="preload" href="/tradingAgent.png" as="image" type="image/png" /> */}
        {/* <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        /> */}

        {/* 资源提示 */}
        {resourceHints.map((hint, index) => (
          <link
            key={index}
            rel="dns-prefetch"
            href={hint.replace('<link rel="dns-prefetch" href="', '').replace('">', '')}
          />
        ))}

        {/* 预连接关键域名 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* PWA 支持 */}
        <meta name="application-name" content="TradingAgents" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="TradingAgents" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="theme-color" content="#000000" />

        {/* 性能监控脚本 */}
        {/* <script
          dangerouslySetInnerHTML={{
            __html: performanceOptimizer.generatePerformanceMonitoringScript(),
          }}
        /> */}
      </head>
      <body className={inter.className}>
        <SafeWrapper
          fallback={
            <div className="min-h-screen flex items-center justify-center bg-slate-900 text-white">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                <p>正在加载应用程序...</p>
              </div>
            </div>
          }
          onError={(error) => {
            console.error('Layout error caught by SafeWrapper:', error);
          }}
        >
          <Providers>
            <div className="min-h-screen flex flex-col">
              {/* 语义化头部导航 */}
              <header role="banner">
                <Header />
              </header>

              {/* 主要内容区域 */}
              <main id="main-content" role="main" className="flex-1 pt-16" aria-label="主要内容">
                {children}
              </main>

              {/* 语义化页脚 */}
              <footer role="contentinfo">
                <Footer />
              </footer>
            </div>
          </Providers>
        </SafeWrapper>
      </body>
    </html>
  );
}
