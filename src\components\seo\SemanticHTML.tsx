/**
 * 语义化 HTML 组件
 * 提供符合 SEO 和可访问性标准的语义化 HTML 结构
 */

import {
  createSemanticOptimizer,
  SemanticStructureConfig,
  ariaHelpers,
} from '@/lib/seo/semantic-structure';
import React from 'react';

interface SemanticPageProps {
  config: SemanticStructureConfig;
  children: React.ReactNode;
  className?: string;
}

/**
 * 语义化页面容器
 */
export function SemanticPage({ config, children, className = '' }: SemanticPageProps) {
  const optimizer = createSemanticOptimizer(config);
  const { landmarks, skipLinks } = optimizer.generatePageStructure();

  return (
    <div className={`semantic-page ${className}`}>
      {/* 跳转链接 - 仅对屏幕阅读器可见 */}
      <nav className="sr-only focus-within:not-sr-only">
        <ul className="flex space-x-4 p-4 bg-blue-600 text-white">
          {skipLinks.map((link, index) => (
            <li key={index}>
              <a
                href={link.href}
                className="underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-white"
              >
                {link.text}
              </a>
            </li>
          ))}
        </ul>
      </nav>

      {/* 主要内容 */}
      <main id="main-content" {...landmarks.main} className="flex-1">
        {children}
      </main>
    </div>
  );
}

interface SemanticSectionProps {
  as?: 'section' | 'article' | 'aside' | 'div';
  title?: string;
  titleLevel?: 1 | 2 | 3 | 4 | 5 | 6;
  id?: string;
  ariaLabel?: string;
  ariaLabelledBy?: string;
  role?: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * 语义化区域组件
 */
export function SemanticSection({
  as: Component = 'section',
  title,
  titleLevel = 2,
  id,
  ariaLabel,
  ariaLabelledBy,
  role,
  children,
  className = '',
}: SemanticSectionProps) {
  const TitleTag = `h${titleLevel}` as keyof JSX.IntrinsicElements;
  const titleId = title ? `${id || 'section'}-title` : undefined;

  const sectionProps: Record<string, any> = {
    id,
    className,
  };

  // 设置 ARIA 属性
  if (role) sectionProps.role = role;
  if (ariaLabel) sectionProps['aria-label'] = ariaLabel;
  if (ariaLabelledBy) sectionProps['aria-labelledby'] = ariaLabelledBy;
  if (titleId && !ariaLabelledBy) sectionProps['aria-labelledby'] = titleId;

  return (
    <Component {...sectionProps}>
      {title && (
        <TitleTag id={titleId} className="section-title">
          {title}
        </TitleTag>
      )}
      {children}
    </Component>
  );
}

interface SemanticHeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  id?: string;
  children: React.ReactNode;
  className?: string;
  ariaLabel?: string;
}

/**
 * 语义化标题组件
 */
export function SemanticHeading({
  level,
  id,
  children,
  className = '',
  ariaLabel,
}: SemanticHeadingProps) {
  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;

  const headingProps: Record<string, any> = {
    id,
    className: `semantic-heading semantic-heading-${level} ${className}`,
  };

  if (ariaLabel) headingProps['aria-label'] = ariaLabel;

  return <HeadingTag {...headingProps}>{children}</HeadingTag>;
}

interface SemanticListProps {
  as?: 'ul' | 'ol';
  ariaLabel?: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * 语义化列表组件
 */
export function SemanticList({
  as: ListComponent = 'ul',
  ariaLabel,
  children,
  className = '',
}: SemanticListProps) {
  const listProps = {
    role: 'list',
    className: `semantic-list ${className}`,
    ...(ariaLabel && { 'aria-label': ariaLabel }),
  };

  return <ListComponent {...listProps}>{children}</ListComponent>;
}

interface SemanticListItemProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * 语义化列表项组件
 */
export function SemanticListItem({ children, className = '' }: SemanticListItemProps) {
  return (
    <li role="listitem" className={`semantic-list-item ${className}`}>
      {children}
    </li>
  );
}

interface SemanticButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  ariaLabel?: string;
  ariaExpanded?: boolean;
  ariaControls?: string;
  ariaDescribedBy?: string;
  ariaPressed?: boolean;
  children: React.ReactNode;
}

/**
 * 语义化按钮组件
 */
export function SemanticButton({
  variant = 'primary',
  size = 'md',
  ariaLabel,
  ariaExpanded,
  ariaControls,
  ariaDescribedBy,
  ariaPressed,
  className = '',
  children,
  ...props
}: SemanticButtonProps) {
  const buttonProps = {
    ...props,
    className: `semantic-button semantic-button-${variant} semantic-button-${size} ${className}`,
    ...ariaHelpers.button({
      label: ariaLabel,
      expanded: ariaExpanded,
      controls: ariaControls,
      describedBy: ariaDescribedBy,
      pressed: ariaPressed,
    }),
  };

  return <button {...buttonProps}>{children}</button>;
}

interface SemanticFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  ariaLabel?: string;
  ariaLabelledBy?: string;
  children: React.ReactNode;
}

/**
 * 语义化表单组件
 */
export function SemanticForm({
  ariaLabel,
  ariaLabelledBy,
  className = '',
  children,
  ...props
}: SemanticFormProps) {
  const formProps = {
    ...props,
    role: 'form',
    className: `semantic-form ${className}`,
    ...(ariaLabel && { 'aria-label': ariaLabel }),
    ...(ariaLabelledBy && { 'aria-labelledby': ariaLabelledBy }),
  };

  return <form {...formProps}>{children}</form>;
}

interface SemanticInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  description?: string;
  showLabel?: boolean;
}

/**
 * 语义化输入框组件
 */
export function SemanticInput({
  label,
  error,
  description,
  showLabel = true,
  id,
  className = '',
  ...props
}: SemanticInputProps) {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = error ? `${inputId}-error` : undefined;
  const descriptionId = description ? `${inputId}-description` : undefined;

  const describedBy = [descriptionId, errorId].filter(Boolean).join(' ');

  return (
    <div className={`semantic-input-group ${className}`}>
      <label htmlFor={inputId} className={`semantic-label ${showLabel ? '' : 'sr-only'}`}>
        {label}
        {props.required && (
          <span className="text-red-500 ml-1" aria-label="必填">
            *
          </span>
        )}
      </label>

      {description && (
        <p id={descriptionId} className="semantic-input-description text-sm text-gray-600 mt-1">
          {description}
        </p>
      )}

      <input
        {...props}
        id={inputId}
        className={`semantic-input ${error ? 'semantic-input-error' : ''}`}
        {...ariaHelpers.input({
          describedBy: describedBy || undefined,
          required: props.required,
          invalid: !!error,
        })}
      />

      {error && (
        <p
          id={errorId}
          role="alert"
          className="semantic-input-error-message text-sm text-red-600 mt-1"
        >
          {error}
        </p>
      )}
    </div>
  );
}

interface SemanticStatusProps {
  status: 'success' | 'warning' | 'error' | 'info';
  message: string;
  live?: 'polite' | 'assertive';
  className?: string;
}

/**
 * 语义化状态指示器组件
 */
export function SemanticStatus({
  status,
  message,
  live = 'polite',
  className = '',
}: SemanticStatusProps) {
  const statusProps = ariaHelpers.status({
    live,
    atomic: true,
  });

  const statusClasses = {
    success: 'bg-green-50 text-green-800 border-green-200',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200',
    error: 'bg-red-50 text-red-800 border-red-200',
    info: 'bg-blue-50 text-blue-800 border-blue-200',
  };

  return (
    <div
      {...statusProps}
      className={`semantic-status p-3 border rounded-md ${statusClasses[status]} ${className}`}
    >
      {message}
    </div>
  );
}

interface SemanticTableProps {
  caption?: string;
  headers: string[];
  data: Array<Record<string, any>>;
  sortable?: boolean;
  className?: string;
}

/**
 * 语义化表格组件
 */
export function SemanticTable({
  caption,
  headers,
  data,
  sortable = false,
  className = '',
}: SemanticTableProps) {
  return (
    <div className={`semantic-table-container ${className}`}>
      <table role="table" className="semantic-table w-full">
        {caption && <caption className="semantic-table-caption">{caption}</caption>}

        <thead>
          <tr role="row">
            {headers.map((header, index) => (
              <th
                key={index}
                role="columnheader"
                scope="col"
                className="semantic-table-header"
                {...(sortable && { 'aria-sort': 'none' })}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {data.map((row, rowIndex) => (
            <tr key={rowIndex} role="row">
              {headers.map((header, cellIndex) => (
                <td key={cellIndex} role="cell" className="semantic-table-cell">
                  {row[header.toLowerCase().replace(/\s+/g, '_')]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

interface SemanticNavigationProps {
  ariaLabel: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * 语义化导航组件
 */
export function SemanticNavigation({
  ariaLabel,
  children,
  className = '',
}: SemanticNavigationProps) {
  return (
    <nav role="navigation" aria-label={ariaLabel} className={`semantic-navigation ${className}`}>
      {children}
    </nav>
  );
}

interface SemanticBreadcrumbProps {
  items: Array<{
    label: string;
    href?: string;
    current?: boolean;
  }>;
  className?: string;
}

/**
 * 语义化面包屑导航组件
 */
export function SemanticBreadcrumb({ items, className = '' }: SemanticBreadcrumbProps) {
  return (
    <nav role="navigation" aria-label="面包屑导航" className={`semantic-breadcrumb ${className}`}>
      <ol role="list" className="flex items-center space-x-2">
        {items.map((item, index) => (
          <li key={index} role="listitem" className="flex items-center">
            {index > 0 && (
              <span className="mx-2 text-gray-400" aria-hidden="true">
                /
              </span>
            )}
            {item.href && !item.current ? (
              <a href={item.href} className="text-blue-600 hover:text-blue-800 hover:underline">
                {item.label}
              </a>
            ) : (
              <span
                className={item.current ? 'text-gray-900 font-medium' : 'text-gray-600'}
                {...(item.current && { 'aria-current': 'page' })}
              >
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// All components are already exported individually above
