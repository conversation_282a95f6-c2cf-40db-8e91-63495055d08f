/**
 * 环境变量日志工具
 * 在应用启动时打印环境变量配置信息
 */

interface EnvConfig {
  name: string;
  value: string | undefined;
  isPublic: boolean;
  category: string;
  description?: string;
}

/**
 * 环境变量配置定义
 */
const ENV_CONFIGS: EnvConfig[] = [
  // 基础配置
  {
    name: 'NODE_ENV',
    value: process.env.NODE_ENV,
    isPublic: true,
    category: '基础配置',
    description: '运行环境',
  },
  {
    name: 'NEXT_TELEMETRY_DISABLED',
    value: process.env.NEXT_TELEMETRY_DISABLED,
    isPublic: true,
    category: '基础配置',
    description: 'Next.js 遥测禁用',
  },

  // API 配置
  {
    name: 'NEXT_PUBLIC_API_BASE_URL',
    value: process.env.NEXT_PUBLIC_API_BASE_URL,
    isPublic: true,
    category: 'API配置',
    description: '前端API基础URL',
  },
  {
    name: 'NEXT_PUBLIC_API_BACKEND_BASE_URL',
    value: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL,
    isPublic: true,
    category: 'API配置',
    description: '后端API基础URL',
  },
  {
    name: 'NEXT_PUBLIC_WS_URL',
    value: process.env.NEXT_PUBLIC_WS_URL,
    isPublic: true,
    category: 'API配置',
    description: 'WebSocket连接URL',
  },
  {
    name: 'BACK_END_URL',
    value: process.env.BACK_END_URL,
    isPublic: false,
    category: 'API配置',
    description: '后端服务URL',
  },
  {
    name: 'FRONTEND_URL',
    value: process.env.FRONTEND_URL,
    isPublic: false,
    category: 'API配置',
    description: '前端服务URL',
  },

  // OpenAI 配置
  {
    name: 'NEXT_PUBLIC_OPENAI_API_KEY',
    value: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
    isPublic: true,
    category: 'AI配置',
    description: 'OpenAI API密钥',
  },
  {
    name: 'OPENAI_BASE_URL',
    value: process.env.OPENAI_BASE_URL,
    isPublic: false,
    category: 'AI配置',
    description: 'OpenAI API基础URL',
  },
  {
    name: 'OPENAI_API_BASE',
    value: process.env.OPENAI_API_BASE,
    isPublic: false,
    category: 'AI配置',
    description: 'OpenAI API基础地址',
  },

  // 第三方API
  {
    name: 'NEXT_PUBLIC_FINNHUB_API_KEY',
    value: process.env.NEXT_PUBLIC_FINNHUB_API_KEY,
    isPublic: true,
    category: '第三方API',
    description: 'FinnHub API密钥',
  },

  // 数据库配置
  {
    name: 'DB_HOST',
    value: process.env.DB_HOST,
    isPublic: false,
    category: '数据库配置',
    description: '数据库主机地址',
  },
  {
    name: 'DB_PORT',
    value: process.env.DB_PORT,
    isPublic: false,
    category: '数据库配置',
    description: '数据库端口',
  },
  {
    name: 'DB_NAME',
    value: process.env.DB_NAME,
    isPublic: false,
    category: '数据库配置',
    description: '数据库名称',
  },
  {
    name: 'DB_USER',
    value: process.env.DB_USER,
    isPublic: false,
    category: '数据库配置',
    description: '数据库用户名',
  },
  {
    name: 'DB_PASSWORD',
    value: process.env.DB_PASSWORD,
    isPublic: false,
    category: '数据库配置',
    description: '数据库密码',
  },
  {
    name: 'MYSQL_ROOT_PASSWORD',
    value: process.env.MYSQL_ROOT_PASSWORD,
    isPublic: false,
    category: '数据库配置',
    description: 'MySQL root密码',
  },
  {
    name: 'MYSQL_DATABASE',
    value: process.env.MYSQL_DATABASE,
    isPublic: false,
    category: '数据库配置',
    description: 'MySQL数据库名',
  },
  {
    name: 'MYSQL_USER',
    value: process.env.MYSQL_USER,
    isPublic: false,
    category: '数据库配置',
    description: 'MySQL用户名',
  },
  {
    name: 'MYSQL_PASSWORD',
    value: process.env.MYSQL_PASSWORD,
    isPublic: false,
    category: '数据库配置',
    description: 'MySQL密码',
  },

  // LangSmith 监控配置
  {
    name: 'LANGSMITH_TRACING',
    value: process.env.LANGSMITH_TRACING,
    isPublic: false,
    category: '监控配置',
    description: 'LangSmith追踪开关',
  },
  {
    name: 'LANGSMITH_ENDPOINT',
    value: process.env.LANGSMITH_ENDPOINT,
    isPublic: false,
    category: '监控配置',
    description: 'LangSmith端点',
  },
  {
    name: 'LANGSMITH_API_KEY',
    value: process.env.LANGSMITH_API_KEY,
    isPublic: false,
    category: '监控配置',
    description: 'LangSmith API密钥',
  },
  {
    name: 'LANGSMITH_PROJECT',
    value: process.env.LANGSMITH_PROJECT,
    isPublic: false,
    category: '监控配置',
    description: 'LangSmith项目名',
  },
];

/**
 * 格式化环境变量值用于显示
 */
function formatEnvValue(value: string | undefined, isSecret: boolean = false): string {
  if (!value) {
    return '❌ 未设置';
  }

  // 对于敏感信息，只显示前几位和后几位
  if (isSecret && value.length > 8) {
    return `${value.substring(0, 4)}****${value.substring(value.length - 4)}`;
  }

  return value;
}

/**
 * 判断是否为敏感信息
 */
function isSensitiveEnv(name: string): boolean {
  const sensitiveKeywords = ['password', 'key', 'secret', 'token'];
  return sensitiveKeywords.some((keyword) => name.toLowerCase().includes(keyword));
}

/**
 * 获取环境变量状态图标
 */
function getStatusIcon(value: string | undefined): string {
  return value ? '✅' : '❌';
}

/**
 * 打印环境变量配置信息
 */
export function logEnvironmentVariables(): void {
  console.log('\n' + '='.repeat(80));
  console.log('🚀 TradingAgents Frontend - 环境变量配置');
  console.log('='.repeat(80));

  // 按类别分组
  const groupedConfigs = ENV_CONFIGS.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, EnvConfig[]>);

  // 打印每个类别的配置
  Object.entries(groupedConfigs).forEach(([category, configs]) => {
    console.log(`\n📂 ${category}`);
    console.log('-'.repeat(50));

    configs.forEach((config) => {
      const status = getStatusIcon(config.value);
      const isSecret = isSensitiveEnv(config.name);
      const displayValue = formatEnvValue(config.value, isSecret);
      const scope = config.isPublic ? '🌐 Public' : '🔒 Server';

      console.log(`${status} ${config.name}`);
      console.log(`   ${scope} | ${displayValue}`);
      if (config.description) {
        console.log(`   📝 ${config.description}`);
      }
      console.log('');
    });
  });

  // 统计信息
  const totalConfigs = ENV_CONFIGS.length;
  const setConfigs = ENV_CONFIGS.filter((config) => config.value).length;
  const missingConfigs = ENV_CONFIGS.filter((config) => !config.value);

  console.log('📊 配置统计');
  console.log('-'.repeat(50));
  console.log(`总配置项: ${totalConfigs}`);
  console.log(`已配置: ${setConfigs} ✅`);
  console.log(`未配置: ${totalConfigs - setConfigs} ❌`);

  if (missingConfigs.length > 0) {
    console.log('\n⚠️  未配置的环境变量:');
    missingConfigs.forEach((config) => {
      console.log(`   • ${config.name} (${config.description || '无描述'})`);
    });
  }

  // 环境检查
  console.log('\n🔍 环境检查');
  console.log('-'.repeat(50));

  const currentEnv = process.env.NODE_ENV || 'development';
  console.log(`当前环境: ${currentEnv}`);

  // 检查关键配置
  const criticalConfigs = ['NEXT_PUBLIC_API_BASE_URL', 'NEXT_PUBLIC_OPENAI_API_KEY', 'DB_HOST'];

  const missingCritical = criticalConfigs.filter(
    (name) => !ENV_CONFIGS.find((config) => config.name === name)?.value
  );

  if (missingCritical.length > 0) {
    console.log('🚨 缺少关键配置:');
    missingCritical.forEach((name) => {
      console.log(`   • ${name}`);
    });
  } else {
    console.log('✅ 关键配置完整');
  }

  console.log('\n' + '='.repeat(80));
  console.log('🎯 应用启动完成 - 环境配置检查完毕');
  console.log('='.repeat(80) + '\n');
}

/**
 * 获取环境变量摘要信息
 */
export function getEnvironmentSummary(): {
  environment: string;
  totalConfigs: number;
  configuredCount: number;
  missingCount: number;
  criticalMissing: string[];
} {
  const totalConfigs = ENV_CONFIGS.length;
  const configuredCount = ENV_CONFIGS.filter((config) => config.value).length;
  const missingCount = totalConfigs - configuredCount;

  const criticalConfigs = ['NEXT_PUBLIC_API_BASE_URL', 'NEXT_PUBLIC_OPENAI_API_KEY', 'DB_HOST'];

  const criticalMissing = criticalConfigs.filter(
    (name) => !ENV_CONFIGS.find((config) => config.name === name)?.value
  );

  return {
    environment: process.env.NODE_ENV || 'development',
    totalConfigs,
    configuredCount,
    missingCount,
    criticalMissing,
  };
}
