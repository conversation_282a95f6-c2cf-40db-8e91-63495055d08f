/**
 * 移动端 SEO 测试工具
 * 自动化测试移动端 SEO 的各个方面
 */

export interface MobileSEOTestResult {
  score: number; // 0-100
  passed: boolean;
  tests: {
    viewport: TestResult;
    touchTargets: TestResult;
    textReadability: TestResult;
    imageOptimization: TestResult;
    performanceMetrics: TestResult;
    responsiveDesign: TestResult;
    accessibility: TestResult;
  };
  recommendations: string[];
}

export interface TestResult {
  passed: boolean;
  score: number;
  message: string;
  details?: any;
}

export class MobileSEOTester {
  /**
   * 运行完整的移动端 SEO 测试
   */
  async runFullTest(): Promise<MobileSEOTestResult> {
    const tests = {
      viewport: await this.testViewport(),
      touchTargets: await this.testTouchTargets(),
      textReadability: await this.testTextReadability(),
      imageOptimization: await this.testImageOptimization(),
      performanceMetrics: await this.testPerformanceMetrics(),
      responsiveDesign: await this.testResponsiveDesign(),
      accessibility: await this.testAccessibility(),
    };

    // 计算总分
    const totalScore = Object.values(tests).reduce((sum, test) => sum + test.score, 0);
    const averageScore = Math.round(totalScore / Object.keys(tests).length);
    const passed = averageScore >= 80;

    // 生成建议
    const recommendations = this.generateRecommendations(tests);

    return {
      score: averageScore,
      passed,
      tests,
      recommendations,
    };
  }

  /**
   * 测试视口配置
   */
  private async testViewport(): Promise<TestResult> {
    const viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;

    if (!viewportMeta) {
      return {
        passed: false,
        score: 0,
        message: '缺少视口元标签',
      };
    }

    const content = viewportMeta.content.toLowerCase();
    const hasWidth = content.includes('width=device-width');
    const hasInitialScale = content.includes('initial-scale=1');

    let score = 0;
    if (hasWidth) score += 50;
    if (hasInitialScale) score += 50;

    return {
      passed: score >= 80,
      score,
      message: score >= 80 ? '视口配置正确' : '视口配置需要改进',
      details: { content, hasWidth, hasInitialScale },
    };
  }

  /**
   * 测试触摸目标大小
   */
  private async testTouchTargets(): Promise<TestResult> {
    const touchTargets = document.querySelectorAll(
      'button, a, input[type="button"], input[type="submit"], [role="button"]'
    );
    let validTargets = 0;
    let totalTargets = touchTargets.length;

    const smallTargets: Array<{ element: Element; size: { width: number; height: number } }> = [];

    touchTargets.forEach((target) => {
      const rect = target.getBoundingClientRect();
      const isValid = rect.width >= 44 && rect.height >= 44;

      if (isValid) {
        validTargets++;
      } else {
        smallTargets.push({
          element: target,
          size: { width: Math.round(rect.width), height: Math.round(rect.height) },
        });
      }
    });

    const score = totalTargets > 0 ? Math.round((validTargets / totalTargets) * 100) : 100;

    return {
      passed: score >= 80,
      score,
      message: `${validTargets}/${totalTargets} 个触摸目标符合最小尺寸要求`,
      details: { smallTargets: smallTargets.slice(0, 5) }, // 只返回前5个问题
    };
  }

  /**
   * 测试文本可读性
   */
  private async testTextReadability(): Promise<TestResult> {
    const textElements = document.querySelectorAll(
      'p, span, div, h1, h2, h3, h4, h5, h6, li, td, th'
    );
    let readableText = 0;
    let totalText = 0;

    const smallTextElements: Array<{ element: Element; fontSize: number }> = [];

    textElements.forEach((element) => {
      const style = window.getComputedStyle(element);
      const fontSize = parseInt(style.fontSize);

      // 只计算有实际文本内容的元素
      if (element.textContent && element.textContent.trim().length > 0) {
        totalText++;

        if (fontSize >= 16) {
          readableText++;
        } else {
          smallTextElements.push({
            element,
            fontSize,
          });
        }
      }
    });

    const score = totalText > 0 ? Math.round((readableText / totalText) * 100) : 100;

    return {
      passed: score >= 70,
      score,
      message: `${readableText}/${totalText} 个文本元素具有良好的可读性`,
      details: { smallTextElements: smallTextElements.slice(0, 5) },
    };
  }

  /**
   * 测试图片优化
   */
  private async testImageOptimization(): Promise<TestResult> {
    const images = document.querySelectorAll('img');
    let optimizedImages = 0;
    let totalImages = images.length;

    const unoptimizedImages: Array<{ src: string; issues: string[] }> = [];

    images.forEach((img) => {
      const issues: string[] = [];
      let isOptimized = true;

      // 检查 alt 属性
      if (!img.alt) {
        issues.push('缺少 alt 属性');
        isOptimized = false;
      }

      // 检查 loading 属性
      if (!img.hasAttribute('loading')) {
        issues.push('缺少 loading 属性');
        isOptimized = false;
      }

      // 检查 decoding 属性
      if (!img.hasAttribute('decoding')) {
        issues.push('缺少 decoding 属性');
        isOptimized = false;
      }

      // 检查尺寸属性
      if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
        issues.push('缺少尺寸属性');
        isOptimized = false;
      }

      if (isOptimized) {
        optimizedImages++;
      } else {
        unoptimizedImages.push({
          src: img.src,
          issues,
        });
      }
    });

    const score = totalImages > 0 ? Math.round((optimizedImages / totalImages) * 100) : 100;

    return {
      passed: score >= 80,
      score,
      message: `${optimizedImages}/${totalImages} 张图片已优化`,
      details: { unoptimizedImages: unoptimizedImages.slice(0, 5) },
    };
  }

  /**
   * 测试性能指标
   */
  private async testPerformanceMetrics(): Promise<TestResult> {
    try {
      const { onCLS, onINP, onFCP, onLCP } = await import('web-vitals');

      return new Promise((resolve) => {
        const metrics: any = {};
        let metricsReceived = 0;
        const totalMetrics = 4;

        const checkComplete = () => {
          metricsReceived++;
          if (metricsReceived === totalMetrics) {
            let score = 0;

            // LCP 评分
            if (metrics.lcp <= 2500) score += 25;
            else if (metrics.lcp <= 4000) score += 15;

            // FID 评分
            if (metrics.fid <= 100) score += 25;
            else if (metrics.fid <= 300) score += 15;

            // CLS 评分
            if (metrics.cls <= 0.1) score += 25;
            else if (metrics.cls <= 0.25) score += 15;

            // FCP 评分
            if (metrics.fcp <= 1800) score += 25;
            else if (metrics.fcp <= 3000) score += 15;

            resolve({
              passed: score >= 80,
              score,
              message: `性能评分: ${score}/100`,
              details: metrics,
            });
          }
        };

        onCLS((metric: any) => {
          metrics.cls = metric.value;
          checkComplete();
        });

        onINP((metric: any) => {
          metrics.fid = metric.value;
          checkComplete();
        });

        onFCP((metric: any) => {
          metrics.fcp = metric.value;
          checkComplete();
        });

        onLCP((metric: any) => {
          metrics.lcp = metric.value;
          checkComplete();
        });

        // 超时处理
        setTimeout(() => {
          resolve({
            passed: false,
            score: 0,
            message: '性能指标获取超时',
          });
        }, 5000);
      });
    } catch (error) {
      return {
        passed: false,
        score: 0,
        message: '无法获取性能指标',
      };
    }
  }

  /**
   * 测试响应式设计
   */
  private async testResponsiveDesign(): Promise<TestResult> {
    const issues: string[] = [];
    let score = 100;

    // 检查水平滚动
    if (document.body.scrollWidth > window.innerWidth) {
      issues.push('页面存在水平滚动');
      score -= 30;
    }

    // 检查媒体查询
    const stylesheets = Array.from(document.styleSheets);
    let hasMediaQueries = false;

    try {
      stylesheets.forEach((stylesheet) => {
        if (stylesheet.cssRules) {
          Array.from(stylesheet.cssRules).forEach((rule) => {
            if (rule.type === CSSRule.MEDIA_RULE) {
              hasMediaQueries = true;
            }
          });
        }
      });
    } catch (error) {
      // 跨域样式表无法访问
    }

    if (!hasMediaQueries) {
      issues.push('未检测到媒体查询');
      score -= 20;
    }

    // 检查固定宽度元素
    const fixedWidthElements = document.querySelectorAll('[style*="width:"][style*="px"]');
    if (fixedWidthElements.length > 0) {
      issues.push(`发现 ${fixedWidthElements.length} 个固定宽度元素`);
      score -= 10;
    }

    return {
      passed: score >= 80,
      score: Math.max(0, score),
      message: issues.length === 0 ? '响应式设计良好' : `发现 ${issues.length} 个问题`,
      details: { issues },
    };
  }

  /**
   * 测试可访问性
   */
  private async testAccessibility(): Promise<TestResult> {
    const issues: string[] = [];
    let score = 100;

    // 检查图片 alt 属性
    const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
    if (imagesWithoutAlt.length > 0) {
      issues.push(`${imagesWithoutAlt.length} 张图片缺少 alt 属性`);
      score -= 20;
    }

    // 检查表单标签
    const inputsWithoutLabels = document.querySelectorAll(
      'input:not([aria-label]):not([aria-labelledby])'
    );
    let unlabeledInputs = 0;
    inputsWithoutLabels.forEach((input) => {
      const id = input.getAttribute('id');
      if (!id || !document.querySelector(`label[for="${id}"]`)) {
        unlabeledInputs++;
      }
    });

    if (unlabeledInputs > 0) {
      issues.push(`${unlabeledInputs} 个输入框缺少标签`);
      score -= 15;
    }

    // 检查标题层级
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) {
      issues.push('页面缺少标题');
      score -= 25;
    } else {
      const h1Count = document.querySelectorAll('h1').length;
      if (h1Count === 0) {
        issues.push('页面缺少 H1 标题');
        score -= 15;
      } else if (h1Count > 1) {
        issues.push('页面有多个 H1 标题');
        score -= 10;
      }
    }

    // 检查链接文本
    const emptyLinks = document.querySelectorAll('a:not([aria-label]):not([aria-labelledby])');
    let badLinks = 0;
    emptyLinks.forEach((link) => {
      const text = link.textContent?.trim();
      if (!text || text.length < 3) {
        badLinks++;
      }
    });

    if (badLinks > 0) {
      issues.push(`${badLinks} 个链接缺少描述性文本`);
      score -= 10;
    }

    return {
      passed: score >= 80,
      score: Math.max(0, score),
      message: issues.length === 0 ? '可访问性良好' : `发现 ${issues.length} 个问题`,
      details: { issues },
    };
  }

  /**
   * 生成改进建议
   */
  private generateRecommendations(tests: MobileSEOTestResult['tests']): string[] {
    const recommendations: string[] = [];

    if (!tests.viewport.passed) {
      recommendations.push(
        '添加正确的视口元标签: <meta name="viewport" content="width=device-width, initial-scale=1">'
      );
    }

    if (!tests.touchTargets.passed) {
      recommendations.push('确保所有触摸目标至少为 44x44 像素');
    }

    if (!tests.textReadability.passed) {
      recommendations.push('确保主要文本至少为 16px，提高可读性');
    }

    if (!tests.imageOptimization.passed) {
      recommendations.push('为所有图片添加 alt、loading 和 decoding 属性');
    }

    if (!tests.performanceMetrics.passed) {
      recommendations.push('优化页面性能，改善 Core Web Vitals 指标');
    }

    if (!tests.responsiveDesign.passed) {
      recommendations.push('实现完全响应式设计，避免水平滚动');
    }

    if (!tests.accessibility.passed) {
      recommendations.push('改善可访问性，添加必要的 ARIA 标签和语义化标记');
    }

    return recommendations;
  }
}

// 导出单例实例
export const mobileSEOTester = new MobileSEOTester();
