import { MetadataGenerator } from '@/lib/seo/metadata-generator';
import { Metadata } from 'next';
import CreateTaskPage from './CreateTaskPage';

// 生成创建任务页面元数据
export async function generateMetadata(): Promise<Metadata> {
  const generator = new MetadataGenerator('zh');
  return generator.generatePageMetadata({
    page: 'create-task',
    locale: 'zh',
  });
}

// 导出默认组件
export default CreateTaskPage;
