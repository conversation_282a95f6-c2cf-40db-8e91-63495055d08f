// Centralized database configuration to ensure consistent collation settings
// This prevents collation mismatch errors across different database connections

export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '13306'),
  user: process.env.DB_USER || 'trading_user',
  password: process.env.DB_PASSWORD || 'trading123',
  database: process.env.DB_NAME || 'trading_analysis',
  charset: 'utf8mb4',
  timezone: '+00:00',
  // Additional options to handle collation issues
  typeCast: function (field: any, next: any) {
    if (field.type === 'VAR_STRING' || field.type === 'STRING') {
      return field.string();
    }
    return next();
  },
};

export default dbConfig;
