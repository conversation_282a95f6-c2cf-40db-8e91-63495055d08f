import { NextRequest, NextResponse } from 'next/server';

/**
 * 数据 API 总览
 * GET /api/data
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const format = searchParams.get('format') || 'json';

  const apiDocumentation = {
    title: 'TradingAgents 数据 API',
    version: '1.0.0',
    description: '提供股票数据、新闻数据、技术指标和基本面数据的综合API接口',
    baseUrl: '/api/data',
    endpoints: {
      stock: {
        path: '/stock/{ticker}',
        method: 'GET',
        description: '获取股票历史价格数据',
        parameters: {
          ticker: {
            type: 'string',
            required: true,
            description: '股票代码，如 600519',
            example: '600519',
          },
          period: {
            type: 'string',
            required: false,
            default: '1y',
            description: '数据周期',
            options: ['1d', '5d', '1m', '3m', '6m', '1y', '2y', '5y', 'max'],
          },
          start_date: {
            type: 'string',
            required: false,
            description: '开始日期 (YYYY-MM-DD)',
            example: '2024-01-01',
          },
          end_date: {
            type: 'string',
            required: false,
            description: '结束日期 (YYYY-MM-DD)',
            example: '2024-12-31',
          },
        },
        response: {
          ticker: 'string',
          name: 'string',
          period: 'string',
          data: 'array',
          stats: 'object',
          count: 'number',
          updatedAt: 'string',
        },
        example: '/api/data/stock/600519?period=1m',
      },
      news: {
        path: '/news/{ticker}',
        method: 'GET',
        description: '获取股票相关新闻数据',
        parameters: {
          ticker: {
            type: 'string',
            required: true,
            description: '股票代码',
            example: '600519',
          },
          limit: {
            type: 'number',
            required: false,
            default: 20,
            description: '返回新闻数量限制',
            range: '1-100',
          },
          days: {
            type: 'number',
            required: false,
            default: 7,
            description: '获取最近几天的新闻',
            range: '1-30',
          },
        },
        response: {
          ticker: 'string',
          name: 'string',
          news: 'array',
          stats: 'object',
          count: 'number',
          period: 'string',
          updatedAt: 'string',
        },
        example: '/api/data/news/600519?limit=10&days=7',
      },
      technical: {
        path: '/technical/{ticker}',
        method: 'GET',
        description: '获取技术指标数据',
        parameters: {
          ticker: {
            type: 'string',
            required: true,
            description: '股票代码',
            example: '600519',
          },
          indicators: {
            type: 'string',
            required: false,
            default: 'ma,macd,rsi,kdj',
            description: '技术指标列表，用逗号分隔',
            options: ['ma', 'macd', 'rsi', 'kdj', 'bollinger', 'volume'],
          },
          period: {
            type: 'string',
            required: false,
            default: 'daily',
            description: '数据周期',
            options: ['daily', 'weekly', 'monthly'],
          },
          days: {
            type: 'number',
            required: false,
            default: 60,
            description: '数据天数',
            range: '30-250',
          },
        },
        response: {
          ticker: 'string',
          name: 'string',
          period: 'string',
          indicators: 'object',
          analysis: 'object',
          dataPoints: 'number',
          updatedAt: 'string',
        },
        example: '/api/data/technical/600519?indicators=ma,macd,rsi',
      },
      fundamentals: {
        path: '/fundamentals/{ticker}',
        method: 'GET',
        description: '获取基本面数据',
        parameters: {
          ticker: {
            type: 'string',
            required: true,
            description: '股票代码',
            example: '600519',
          },
          type: {
            type: 'string',
            required: false,
            default: 'all',
            description: '数据类型',
            options: [
              'all',
              'financials',
              'valuation',
              'growth',
              'balance_sheet',
              'cash_flow',
              'ratios',
            ],
          },
          period: {
            type: 'string',
            required: false,
            default: 'annual',
            description: '报告周期',
            options: ['annual', 'quarterly'],
          },
        },
        response: {
          ticker: 'string',
          name: 'string',
          period: 'string',
          fundamentals: 'object',
          analysis: 'object',
          updatedAt: 'string',
        },
        example: '/api/data/fundamentals/600519?type=valuation',
      },
    },
    dataTypes: {
      stock: {
        description: '股票价格数据',
        fields: {
          date: '日期',
          open: '开盘价',
          high: '最高价',
          low: '最低价',
          close: '收盘价',
          volume: '成交量',
          amount: '成交额',
          turnover: '换手率',
          pctChange: '涨跌幅',
        },
      },
      news: {
        description: '新闻数据',
        fields: {
          id: '新闻ID',
          title: '标题',
          summary: '摘要',
          source: '来源',
          url: '链接',
          publishedAt: '发布时间',
          sentiment: '情绪分数',
          category: '分类',
          importance: '重要性',
        },
      },
      technical: {
        description: '技术指标数据',
        indicators: {
          ma: '移动平均线 (MA5, MA10, MA20, MA60)',
          macd: 'MACD指标 (MACD线, 信号线, 柱状图)',
          rsi: 'RSI相对强弱指标',
          kdj: 'KDJ随机指标',
          bollinger: '布林带',
          volume: '成交量指标',
        },
      },
      fundamentals: {
        description: '基本面数据',
        categories: {
          financials: '财务数据 (营收, 净利润, 每股收益等)',
          valuation: '估值数据 (PE, PB, PS等)',
          growth: '增长数据 (营收增长率, 净利润增长率等)',
          balance_sheet: '资产负债表',
          cash_flow: '现金流数据',
          ratios: '财务比率 (ROE, ROA, 毛利率等)',
        },
      },
    },
    errorCodes: {
      400: '请求参数错误',
      404: '数据不存在或股票代码无效',
      500: '服务器内部错误',
      503: '数据源服务不可用',
    },
    rateLimit: {
      description: '请求频率限制',
      limit: '每分钟最多100次请求',
      headers: {
        'X-RateLimit-Limit': '请求限制',
        'X-RateLimit-Remaining': '剩余请求次数',
        'X-RateLimit-Reset': '重置时间',
      },
    },
    examples: {
      stockData: {
        url: '/api/data/stock/600519?period=1m',
        description: '获取贵州茅台最近1个月的股价数据',
      },
      newsData: {
        url: '/api/data/news/600519?limit=5&days=3',
        description: '获取贵州茅台最近3天的5条新闻',
      },
      technicalData: {
        url: '/api/data/technical/600519?indicators=ma,rsi',
        description: '获取贵州茅台的移动平均线和RSI指标',
      },
      fundamentalData: {
        url: '/api/data/fundamentals/600519?type=valuation',
        description: '获取贵州茅台的估值数据',
      },
    },
  };

  if (format === 'html') {
    // 返回HTML格式的API文档
    const html = generateHtmlDocumentation(apiDocumentation);
    return new NextResponse(html, {
      headers: { 'Content-Type': 'text/html; charset=utf-8' },
    });
  }

  return NextResponse.json(apiDocumentation);
}

/**
 * 生成HTML格式的API文档
 */
function generateHtmlDocumentation(doc: any): string {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${doc.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        h3 { color: #7f8c8d; }
        .endpoint { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 6px; border-left: 4px solid #3498db; }
        .method { background: #27ae60; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .path { font-family: 'Courier New', monospace; background: #ecf0f1; padding: 4px 8px; border-radius: 4px; }
        .param { margin: 10px 0; }
        .param-name { font-weight: bold; color: #e74c3c; }
        .param-type { color: #9b59b6; font-size: 12px; }
        .example { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; margin: 10px 0; }
        .note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>${doc.title}</h1>
        <p><strong>版本:</strong> ${doc.version}</p>
        <p>${doc.description}</p>
        
        <h2>API 端点</h2>
        ${Object.entries(doc.endpoints)
          .map(
            ([key, endpoint]: [string, any]) => `
            <div class="endpoint">
                <h3><span class="method">${endpoint.method}</span> <span class="path">${
              endpoint.path
            }</span></h3>
                <p>${endpoint.description}</p>
                
                <h4>参数:</h4>
                ${Object.entries(endpoint.parameters)
                  .map(
                    ([paramName, param]: [string, any]) => `
                    <div class="param">
                        <span class="param-name">${paramName}</span>
                        <span class="param-type">(${param.type}${
                      param.required ? ', 必需' : ', 可选'
                    })</span>
                        <p>${param.description}</p>
                        ${param.example ? `<p><strong>示例:</strong> ${param.example}</p>` : ''}
                        ${
                          param.options
                            ? `<p><strong>选项:</strong> ${param.options.join(', ')}</p>`
                            : ''
                        }
                        ${param.default ? `<p><strong>默认值:</strong> ${param.default}</p>` : ''}
                    </div>
                `
                  )
                  .join('')}
                
                <h4>示例请求:</h4>
                <div class="example">${doc.baseUrl}${endpoint.example}</div>
            </div>
        `
          )
          .join('')}
        
        <h2>数据类型说明</h2>
        ${Object.entries(doc.dataTypes)
          .map(
            ([key, dataType]: [string, any]) => `
            <div class="endpoint">
                <h3>${dataType.description}</h3>
                ${
                  dataType.fields
                    ? Object.entries(dataType.fields)
                        .map(
                          ([field, desc]) => `
                    <div class="param">
                        <span class="param-name">${field}</span>: ${desc}
                    </div>
                `
                        )
                        .join('')
                    : ''
                }
                ${
                  dataType.indicators
                    ? Object.entries(dataType.indicators)
                        .map(
                          ([indicator, desc]) => `
                    <div class="param">
                        <span class="param-name">${indicator}</span>: ${desc}
                    </div>
                `
                        )
                        .join('')
                    : ''
                }
                ${
                  dataType.categories
                    ? Object.entries(dataType.categories)
                        .map(
                          ([category, desc]) => `
                    <div class="param">
                        <span class="param-name">${category}</span>: ${desc}
                    </div>
                `
                        )
                        .join('')
                    : ''
                }
            </div>
        `
          )
          .join('')}
        
        <div class="note">
            <h3>使用说明</h3>
            <ul>
                <li>所有API返回JSON格式数据</li>
                <li>时间格式统一使用ISO 8601标准</li>
                <li>数值类型的数据可能为null，表示数据不可用</li>
                <li>请求频率限制：每分钟最多100次请求</li>
                <li>建议在生产环境中实现适当的缓存机制</li>
            </ul>
        </div>
    </div>
</body>
</html>
  `;
}
