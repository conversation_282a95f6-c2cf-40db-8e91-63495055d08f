import { AnalysisStatus } from '@/hooks/useAnalysisStatus';
import { Task } from '@/types/database';
import { fireEvent, render, screen } from '@testing-library/react';
import { TaskCard } from '../TaskCard';

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => '01-15 10:30'),
}));

jest.mock('date-fns/locale', () => ({
  zhCN: {},
}));

// Mock utils
jest.mock('@/utils/enums', () => ({
  getAnalysisPeriodLabel: jest.fn((period) => {
    const labels: Record<string, string> = {
      '1d': '1天',
      '1w': '1周',
      '1m': '1个月',
    };
    return labels[period] || period;
  }),
  getResearchDepthLabel: jest.fn((depth) => {
    const labels: Record<string, string> = {
      shallow: '浅度',
      medium: '中度',
      deep: '深度',
    };
    return labels[depth] || depth;
  }),
}));

const mockTask: Task = {
  id: 1,
  task_id: 'task-123456789',
  workflow_id: 'task-123456789',
  thread_id: 'thread-123',
  ticker: 'AAPL',
  title: 'Apple Inc. 股票分析',
  description: '对苹果公司进行全面的投资分析',
  status: 'pending',
  current_stage: 'pending',
  progress: 0,
  config: {},
  created_by: 'user1',
  created_at: new Date('2024-01-15T10:30:00Z'),
  updated_at: new Date('2024-01-15T10:30:00Z'),
  research_depth: 'medium',
  analysis_period: '1d',
  priority: 1,
};

const mockAnalysisStatus: AnalysisStatus = {
  taskId: 'task-123456789',
  ticker: 'AAPL',
  status: 'running',
  progress: 45,
  currentStage: 'technical_analysis',
  stages: {
    fundamental: true,
    technical: false,
    sentiment: false,
    news: false,
    bull: false,
    bear: false,
    consensus: false,
    risk: false,
    decision: false,
  },
  statistics: {
    totalMessages: 25,
    debateRounds: 2,
    duration: 300,
  },
  startedAt: '2024-01-15T10:30:00Z',
  latestReports: {
    analysts: {},
    researchers: {},
  },
};

describe('TaskCard', () => {
  it('renders task information correctly', () => {
    render(<TaskCard task={mockTask} />);

    expect(screen.getByText('AAPL')).toBeInTheDocument();
    expect(screen.getByText('Apple Inc. 股票分析')).toBeInTheDocument();
    expect(screen.getByText('待处理')).toBeInTheDocument();
    expect(screen.getByText(/ID: task-123/)).toBeInTheDocument();
    expect(screen.getByText(/创建于/)).toBeInTheDocument();
    expect(screen.getByText(/创建者: user1/)).toBeInTheDocument();
  });

  it('displays research depth and analysis period', () => {
    render(<TaskCard task={mockTask} />);

    expect(screen.getByText('中度')).toBeInTheDocument();
    expect(screen.getByText('1天')).toBeInTheDocument();
  });

  it('shows start analysis button for pending tasks', () => {
    render(<TaskCard task={mockTask} />);

    const startButton = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(startButton).toBeInTheDocument();
    expect(startButton).toHaveTextContent('开始分析');
  });

  it('shows view analysis button for running tasks', () => {
    const runningTask = { ...mockTask, status: 'running' as const };
    render(<TaskCard task={runningTask} />);

    const viewButton = screen.getByRole('button', { name: /查看任务 AAPL 的分析结果/ });
    expect(viewButton).toBeInTheDocument();
    expect(viewButton).toHaveTextContent('实时查看');
  });

  it('shows view analysis button for completed tasks', () => {
    const completedTask = { ...mockTask, status: 'completed' as const };
    render(<TaskCard task={completedTask} />);

    const viewButton = screen.getByRole('button', { name: /查看任务 AAPL 的分析结果/ });
    expect(viewButton).toBeInTheDocument();
    expect(viewButton).toHaveTextContent('查看分析');
  });

  it('displays analysis status when provided', () => {
    render(<TaskCard task={mockTask} analysisStatus={mockAnalysisStatus} />);

    expect(screen.getByText('分析中')).toBeInTheDocument();
    expect(screen.getByText('45%')).toBeInTheDocument();
    expect(screen.getByText(/当前阶段: technical_analysis/)).toBeInTheDocument();
  });

  it('shows progress bar for running analysis', () => {
    render(<TaskCard task={mockTask} analysisStatus={mockAnalysisStatus} />);

    const progressBar = document.querySelector('.bg-blue-600');
    expect(progressBar).toHaveStyle('width: 45%');
  });

  it('displays error message when task has error', () => {
    const errorTask = { ...mockTask, error_message: '分析过程中发生错误' };
    render(<TaskCard task={errorTask} />);

    expect(screen.getByText('分析过程中发生错误')).toBeInTheDocument();
  });

  it('calls onStartAnalysis when start button is clicked', () => {
    const onStartAnalysis = jest.fn();
    render(<TaskCard task={mockTask} onStartAnalysis={onStartAnalysis} />);

    const startButton = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    fireEvent.click(startButton);

    expect(onStartAnalysis).toHaveBeenCalledWith(mockTask);
  });

  it('calls onViewAnalysis when view analysis button is clicked', () => {
    const onViewAnalysis = jest.fn();
    const runningTask = { ...mockTask, status: 'running' as const };
    render(<TaskCard task={runningTask} onViewAnalysis={onViewAnalysis} />);

    const viewButton = screen.getByRole('button', { name: /查看任务 AAPL 的分析结果/ });
    fireEvent.click(viewButton);

    expect(onViewAnalysis).toHaveBeenCalledWith(runningTask);
  });

  it('calls onViewDetails when details button is clicked', () => {
    const onViewDetails = jest.fn();
    render(<TaskCard task={mockTask} onViewDetails={onViewDetails} />);

    const detailsButton = screen.getByRole('button', { name: /查看任务 AAPL 的详情/ });
    fireEvent.click(detailsButton);

    expect(onViewDetails).toHaveBeenCalledWith(mockTask);
  });

  it('calls onViewMessages when messages button is clicked', () => {
    const onViewMessages = jest.fn();
    render(<TaskCard task={mockTask} onViewMessages={onViewMessages} />);

    const messagesButton = screen.getByRole('button', { name: /查看任务 AAPL 的消息/ });
    fireEvent.click(messagesButton);

    expect(onViewMessages).toHaveBeenCalledWith('task-123456789');
  });

  it('shows loading state for start button', () => {
    render(<TaskCard task={mockTask} startingTask="task-123456789" />);

    const startButton = screen.getByRole('button', { name: /开始分析任务 AAPL/ });
    expect(startButton).toBeDisabled();
    expect(screen.getByText('启动中...')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<TaskCard task={mockTask} className="custom-class" />);

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles different task statuses correctly', () => {
    const statuses: Array<{ status: Task['status']; expectedText: string; expectedClass: string }> =
      [
        { status: 'pending', expectedText: '待处理', expectedClass: 'bg-yellow-100' },
        { status: 'running', expectedText: '运行中', expectedClass: 'bg-blue-100' },
        { status: 'completed', expectedText: '已完成', expectedClass: 'bg-green-100' },
        { status: 'failed', expectedText: '失败', expectedClass: 'bg-red-100' },
        { status: 'cancelled', expectedText: '已取消', expectedClass: 'bg-gray-100' },
      ];

    statuses.forEach(({ status, expectedText, expectedClass }) => {
      const taskWithStatus = { ...mockTask, status };
      const { rerender } = render(<TaskCard task={taskWithStatus} />);

      expect(screen.getByText(expectedText)).toBeInTheDocument();
      expect(screen.getByText(expectedText)).toHaveClass(expectedClass);

      rerender(<div />); // Clear for next iteration
    });
  });

  it('handles different analysis statuses correctly', () => {
    const statuses: Array<{ status: AnalysisStatus['status']; expectedText: string }> = [
      { status: 'pending', expectedText: '未开始' },
      { status: 'running', expectedText: '分析中' },
      { status: 'completed', expectedText: '分析完成' },
      { status: 'failed', expectedText: '分析失败' },
      { status: 'cancelled', expectedText: '未开始' },
    ];

    statuses.forEach(({ status, expectedText }) => {
      const analysisStatusWithStatus = { ...mockAnalysisStatus, status };
      const { rerender } = render(
        <TaskCard task={mockTask} analysisStatus={analysisStatusWithStatus} />
      );

      expect(screen.getByText(expectedText)).toBeInTheDocument();

      rerender(<div />); // Clear for next iteration
    });
  });

  it('truncates long task IDs correctly', () => {
    const longIdTask = { ...mockTask, task_id: 'very-long-task-id-123456789' };
    render(<TaskCard task={longIdTask} />);

    expect(screen.getByText(/ID: very-lon/)).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    const minimalTask: Task = {
      ...mockTask,
      research_depth: undefined,
      analysis_period: undefined,
      created_by: undefined,
    };

    render(<TaskCard task={minimalTask} />);

    // research_depth, analysis_period should show '-', created_by should be hidden
    expect(screen.getAllByText('-')).toHaveLength(2);
    expect(screen.queryByText(/创建者:/)).not.toBeInTheDocument();
  });
});
