'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useEffect, useState } from 'react';

interface AnalysisItem {
  workflow_id: string;
  ticker: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  completed_at?: string;
  report_count: number;
}

interface AnalysisComparisonSelectorProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  maxSelection?: number;
}

const STATUS_CONFIG = {
  pending: { label: '待处理', color: 'default' },
  running: { label: '运行中', color: 'secondary' },
  completed: { label: '已完成', color: 'success' },
  failed: { label: '失败', color: 'destructive' },
  cancelled: { label: '已取消', color: 'outline' },
} as const;

export function AnalysisComparisonSelector({
  selectedIds,
  onSelectionChange,
  maxSelection = 5,
}: AnalysisComparisonSelectorProps) {
  const [availableAnalyses, setAvailableAnalyses] = useState<AnalysisItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>(['completed']);

  // 加载可用的分析列表
  const loadAvailableAnalyses = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: '50', // 限制数量以提高性能
        status: statusFilter.join(','),
      });

      const response = await fetch(`/api/analysis/history?${params}`);

      if (!response.ok) {
        throw new Error('获取分析列表失败');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '获取分析列表失败');
      }

      setAvailableAnalyses(result.data.workflows || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadAvailableAnalyses();
  }, [statusFilter]);

  // 处理选择
  const handleToggleSelection = (workflowId: string) => {
    if (selectedIds.includes(workflowId)) {
      // 取消选择
      onSelectionChange(selectedIds.filter((id) => id !== workflowId));
    } else {
      // 添加选择
      if (selectedIds.length >= maxSelection) {
        alert(`最多只能选择 ${maxSelection} 个分析进行对比`);
        return;
      }
      onSelectionChange([...selectedIds, workflowId]);
    }
  };

  // 清除所有选择
  const handleClearSelection = () => {
    onSelectionChange([]);
  };

  // 筛选分析列表
  const filteredAnalyses = availableAnalyses.filter((analysis) => {
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        analysis.ticker.toLowerCase().includes(term) || analysis.title.toLowerCase().includes(term)
      );
    }
    return true;
  });

  // 处理状态筛选
  const handleStatusFilterChange = (status: string) => {
    if (statusFilter.includes(status)) {
      setStatusFilter((prev) => prev.filter((s) => s !== status));
    } else {
      setStatusFilter((prev) => [...prev, status]);
    }
  };

  return (
    <Card className="p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">选择要对比的分析</h3>
        <p className="text-sm text-gray-600">
          最多可选择 {maxSelection} 个分析进行对比。已选择: {selectedIds.length}/{maxSelection}
        </p>
      </div>

      {/* 筛选控件 */}
      <div className="mb-4 space-y-3">
        {/* 搜索框 */}
        <div>
          <input
            type="text"
            placeholder="搜索股票代码或标题..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* 状态筛选 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">状态筛选:</span>
          {Object.entries(STATUS_CONFIG).map(([status, config]) => (
            <button
              key={status}
              onClick={() => handleStatusFilterChange(status)}
              className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                statusFilter.includes(status)
                  ? 'bg-blue-100 border-blue-300 text-blue-700'
                  : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
              }`}
            >
              {config.label}
            </button>
          ))}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {selectedIds.length > 0 && (
              <Button variant="secondary" size="sm" onClick={handleClearSelection}>
                清除选择
              </Button>
            )}
            <Button
              variant="secondary"
              size="sm"
              onClick={loadAvailableAnalyses}
              disabled={loading}
            >
              刷新列表
            </Button>
          </div>

          {selectedIds.length >= 2 && <Badge variant="secondary">可以开始对比</Badge>}
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="md" />
          <span className="ml-2 text-gray-600">加载分析列表...</span>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <div className="text-red-500 text-lg mr-2">⚠️</div>
            <div>
              <h4 className="text-red-800 font-medium">加载失败</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 分析列表 */}
      {!loading && !error && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredAnalyses.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>没有找到符合条件的分析</p>
              <p className="text-sm mt-1">请尝试调整筛选条件</p>
            </div>
          ) : (
            filteredAnalyses.map((analysis) => {
              const isSelected = selectedIds.includes(analysis.workflow_id);
              const statusConfig = STATUS_CONFIG[analysis.status];

              return (
                <div
                  key={analysis.workflow_id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => handleToggleSelection(analysis.workflow_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      {/* 选择框 */}
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}} // 由父级div的onClick处理
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />

                      {/* 分析信息 */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-semibold text-blue-600">{analysis.ticker}</span>
                          <Badge variant={statusConfig.color}>{statusConfig.label}</Badge>
                        </div>
                        <p className="text-sm text-gray-700 line-clamp-1">{analysis.title}</p>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <span>
                            创建:{' '}
                            {format(new Date(analysis.created_at), 'MM-dd HH:mm', { locale: zhCN })}
                          </span>
                          {analysis.completed_at && (
                            <span>
                              完成:{' '}
                              {format(new Date(analysis.completed_at), 'MM-dd HH:mm', {
                                locale: zhCN,
                              })}
                            </span>
                          )}
                          <span>{analysis.report_count} 个报告</span>
                        </div>
                      </div>
                    </div>

                    {/* 选择指示器 */}
                    {isSelected && <div className="text-blue-600 text-lg">✓</div>}
                  </div>
                </div>
              );
            })
          )}
        </div>
      )}

      {/* 选择摘要 */}
      {selectedIds.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">已选择 {selectedIds.length} 个分析</span>
            {selectedIds.length >= 2 && (
              <span className="text-sm text-green-600 font-medium">✓ 可以开始对比</span>
            )}
          </div>
        </div>
      )}
    </Card>
  );
}
