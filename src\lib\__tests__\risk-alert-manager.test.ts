/**
 * 风险预警管理器测试
 * Risk Alert Manager Tests
 */

import { AlertLevel, RiskLevel, RiskType } from '@/types/risk-report';
import { RiskAlertManager } from '../risk-alert-manager';

describe('RiskAlertManager', () => {
  let alertManager: RiskAlertManager;

  beforeEach(() => {
    alertManager = new RiskAlertManager();
  });

  describe('checkAlerts', () => {
    it('应该基于指标生成预警', () => {
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.4, // 高波动率
          unit: '%',
          description: '价格波动的标准差',
          threshold: { low: 0.15, medium: 0.25, high: 0.4 },
        },
        volume: {
          name: '平均交易量',
          value: 50000, // 低交易量
          unit: '股',
          description: '日均交易量',
          threshold: { low: 100000, medium: 500000, high: 1000000 },
        },
      };

      const assessments = [
        {
          type: RiskType.MARKET,
          level: RiskLevel.HIGH,
          score: 75,
          description: '市场风险较高',
          metrics: { volatility: 0.4 },
          factors: ['高波动率'],
          recommendations: ['考虑减仓'],
        },
      ];

      const alerts = alertManager.checkAlerts('AAPL', metrics, assessments);

      expect(alerts.length).toBeGreaterThan(0);

      // 检查高波动率预警
      const volatilityAlert = alerts.find(
        (alert) => alert.title.includes('高波动率预警') || alert.title.includes('极高波动率预警')
      );
      expect(volatilityAlert).toBeDefined();
      expect(volatilityAlert?.level).toBeOneOf([AlertLevel.WARNING, AlertLevel.CRITICAL]);

      // 检查低交易量预警
      const volumeAlert = alerts.find((alert) => alert.title.includes('低交易量预警'));
      expect(volumeAlert).toBeDefined();
      expect(volumeAlert?.level).toBe(AlertLevel.WARNING);

      // 检查基于评估的预警
      const assessmentAlert = alerts.find((alert) => alert.title.includes('市场风险偏高'));
      expect(assessmentAlert).toBeDefined();
      expect(assessmentAlert?.level).toBe(AlertLevel.DANGER);
    });

    it('应该正确设置预警属性', () => {
      const metrics = {
        debtRatio: {
          name: '债务比率',
          value: 0.8, // 高债务比率
          unit: '%',
          description: '总债务占总资产的比例',
          threshold: { low: 0.3, medium: 0.5, high: 0.7 },
        },
      };

      const alerts = alertManager.checkAlerts('TEST', metrics, []);

      expect(alerts.length).toBeGreaterThan(0);

      const alert = alerts[0];
      expect(alert.id).toBeTruthy();
      expect(alert.level).toBeOneOf(Object.values(AlertLevel));
      expect(alert.type).toBeOneOf(Object.values(RiskType));
      expect(alert.title).toContain('TEST');
      expect(alert.message).toBeTruthy();
      expect(alert.timestamp).toBeInstanceOf(Date);
      expect(alert.isActive).toBe(true);
      expect(alert.threshold).toBeDefined();
      expect(alert.currentValue).toBe(0.8);
      expect(alert.recommendations).toBeInstanceOf(Array);
      expect(alert.recommendations.length).toBeGreaterThan(0);
    });

    it('应该按优先级排序预警', () => {
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.6, // 极高波动率 -> CRITICAL
          unit: '%',
          description: '价格波动的标准差',
        },
        spread: {
          name: '买卖价差',
          value: 0.025, // 高价差 -> WARNING
          unit: '%',
          description: '买卖价差',
        },
      };

      const assessments = [
        {
          type: RiskType.MARKET,
          level: RiskLevel.CRITICAL,
          score: 95,
          description: '市场风险极高',
          metrics: { volatility: 0.6 },
          factors: [],
          recommendations: [],
        },
      ];

      const alerts = alertManager.checkAlerts('TEST', metrics, assessments);

      expect(alerts.length).toBeGreaterThan(1);

      // 第一个应该是最高优先级的预警
      expect(alerts[0].level).toBe(AlertLevel.CRITICAL);

      // 验证排序正确性
      for (let i = 1; i < alerts.length; i++) {
        const currentPriority = getAlertPriority(alerts[i].level);
        const previousPriority = getAlertPriority(alerts[i - 1].level);
        expect(currentPriority).toBeLessThanOrEqual(previousPriority);
      }
    });

    it('应该处理没有预警的情况', () => {
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.1, // 低波动率
          unit: '%',
          description: '价格波动的标准差',
        },
      };

      const assessments = [
        {
          type: RiskType.MARKET,
          level: RiskLevel.LOW,
          score: 20,
          description: '市场风险较低',
          metrics: { volatility: 0.1 },
          factors: [],
          recommendations: [],
        },
      ];

      const alerts = alertManager.checkAlerts('TEST', metrics, assessments);

      expect(alerts).toBeInstanceOf(Array);
      expect(alerts.length).toBe(0);
    });
  });

  describe('getActiveAlerts', () => {
    beforeEach(() => {
      // 添加一些测试预警
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.4,
          unit: '%',
          description: '价格波动的标准差',
        },
        volume: {
          name: '平均交易量',
          value: 50000,
          unit: '股',
          description: '日均交易量',
        },
      };

      alertManager.checkAlerts('TEST', metrics, []);
    });

    it('应该返回所有活跃预警', () => {
      const alerts = alertManager.getActiveAlerts();

      expect(alerts).toBeInstanceOf(Array);
      expect(alerts.length).toBeGreaterThan(0);

      alerts.forEach((alert) => {
        expect(alert.isActive).toBe(true);
      });
    });

    it('应该按风险类型过滤预警', () => {
      const marketAlerts = alertManager.getActiveAlerts(RiskType.MARKET);
      const liquidityAlerts = alertManager.getActiveAlerts(RiskType.LIQUIDITY);

      marketAlerts.forEach((alert) => {
        expect(alert.type).toBe(RiskType.MARKET);
      });

      liquidityAlerts.forEach((alert) => {
        expect(alert.type).toBe(RiskType.LIQUIDITY);
      });
    });

    it('应该按预警级别过滤预警', () => {
      const warningAlerts = alertManager.getActiveAlerts(undefined, AlertLevel.WARNING);

      warningAlerts.forEach((alert) => {
        expect(alert.level).toBe(AlertLevel.WARNING);
      });
    });

    it('应该按优先级和时间排序', () => {
      const alerts = alertManager.getActiveAlerts();

      for (let i = 1; i < alerts.length; i++) {
        const currentPriority = getAlertPriority(alerts[i].level);
        const previousPriority = getAlertPriority(alerts[i - 1].level);

        if (currentPriority === previousPriority) {
          // 相同优先级按时间排序（新的在前）
          expect(alerts[i].timestamp.getTime()).toBeLessThanOrEqual(
            alerts[i - 1].timestamp.getTime()
          );
        } else {
          // 不同优先级按优先级排序
          expect(currentPriority).toBeLessThanOrEqual(previousPriority);
        }
      }
    });
  });

  describe('acknowledgeAlert', () => {
    let alertId: string;

    beforeEach(() => {
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.4,
          unit: '%',
          description: '价格波动的标准差',
        },
      };

      const alerts = alertManager.checkAlerts('TEST', metrics, []);
      alertId = alerts[0]?.id;
    });

    it('应该成功确认预警', () => {
      const result = alertManager.acknowledgeAlert(alertId, 'user123', '已查看');

      expect(result).toBe(true);

      const history = alertManager.getAlertHistory(alertId);
      const acknowledgeRecord = history.find((h) => h.action === 'acknowledged');

      expect(acknowledgeRecord).toBeDefined();
      expect(acknowledgeRecord?.userId).toBe('user123');
      expect(acknowledgeRecord?.notes).toBe('已查看');
    });

    it('应该处理不存在的预警ID', () => {
      const result = alertManager.acknowledgeAlert('nonexistent', 'user123');

      expect(result).toBe(false);
    });
  });

  describe('resolveAlert', () => {
    let alertId: string;

    beforeEach(() => {
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.4,
          unit: '%',
          description: '价格波动的标准差',
        },
      };

      const alerts = alertManager.checkAlerts('TEST', metrics, []);
      alertId = alerts[0]?.id;
    });

    it('应该成功解决预警', () => {
      const result = alertManager.resolveAlert(alertId, '问题已解决', 'user123');

      expect(result).toBe(true);

      const activeAlerts = alertManager.getActiveAlerts();
      const resolvedAlert = activeAlerts.find((alert) => alert.id === alertId);

      expect(resolvedAlert).toBeUndefined();

      const history = alertManager.getAlertHistory(alertId);
      const resolveRecord = history.find((h) => h.action === 'resolved');

      expect(resolveRecord).toBeDefined();
      expect(resolveRecord?.notes).toBe('问题已解决');
      expect(resolveRecord?.userId).toBe('user123');
    });

    it('应该处理不存在的预警ID', () => {
      const result = alertManager.resolveAlert('nonexistent');

      expect(result).toBe(false);
    });
  });

  describe('escalateAlert', () => {
    let alertId: string;

    beforeEach(() => {
      const metrics = {
        spread: {
          name: '买卖价差',
          value: 0.025, // 这会生成WARNING级别的预警
          unit: '%',
          description: '买卖价差',
        },
      };

      const alerts = alertManager.checkAlerts('TEST', metrics, []);
      alertId = alerts[0]?.id;
    });

    it('应该成功升级预警', () => {
      const result = alertManager.escalateAlert(alertId, AlertLevel.CRITICAL, 'admin', '情况恶化');

      expect(result).toBe(true);

      const activeAlerts = alertManager.getActiveAlerts();
      const escalatedAlert = activeAlerts.find((alert) => alert.id === alertId);

      expect(escalatedAlert?.level).toBe(AlertLevel.CRITICAL);

      const history = alertManager.getAlertHistory(alertId);
      const escalateRecord = history.find((h) => h.action === 'escalated');

      expect(escalateRecord).toBeDefined();
      expect(escalateRecord?.notes).toBe('情况恶化');
      expect(escalateRecord?.userId).toBe('admin');
    });
  });

  describe('alertRules management', () => {
    it('应该能够添加自定义预警规则', () => {
      const ruleId = alertManager.addAlertRule({
        name: '自定义测试规则',
        riskType: RiskType.MARKET,
        condition: (value, threshold) => value > threshold,
        threshold: 0.35,
        level: AlertLevel.WARNING,
        message: '自定义预警消息',
        isActive: true,
      });

      expect(ruleId).toBeTruthy();

      const rules = alertManager.getAlertRules();
      const addedRule = rules.find((rule) => rule.id === ruleId);

      expect(addedRule).toBeDefined();
      expect(addedRule?.name).toBe('自定义测试规则');
    });

    it('应该能够更新预警规则', () => {
      const rules = alertManager.getAlertRules();
      const ruleId = rules[0].id;

      const result = alertManager.updateAlertRule(ruleId, {
        threshold: 0.5,
        isActive: false,
      });

      expect(result).toBe(true);

      const updatedRules = alertManager.getAlertRules();
      const updatedRule = updatedRules.find((rule) => rule.id === ruleId);

      expect(updatedRule?.threshold).toBe(0.5);
      expect(updatedRule?.isActive).toBe(false);
    });

    it('应该能够删除预警规则', () => {
      const rules = alertManager.getAlertRules();
      const initialCount = rules.length;
      const ruleId = rules[0].id;

      const result = alertManager.removeAlertRule(ruleId);

      expect(result).toBe(true);

      const updatedRules = alertManager.getAlertRules();
      expect(updatedRules.length).toBe(initialCount - 1);
      expect(updatedRules.find((rule) => rule.id === ruleId)).toBeUndefined();
    });
  });

  describe('subscription', () => {
    it('应该能够订阅预警通知', (done) => {
      const unsubscribe = alertManager.subscribe((alert) => {
        expect(alert).toBeDefined();
        expect(alert.id).toBeTruthy();
        unsubscribe();
        done();
      });

      // 触发预警
      const metrics = {
        volatility: {
          name: '历史波动率',
          value: 0.4,
          unit: '%',
          description: '价格波动的标准差',
        },
      };

      alertManager.checkAlerts('TEST', metrics, []);
    });

    it('应该能够取消订阅', () => {
      let notificationCount = 0;

      const unsubscribe = alertManager.subscribe(() => {
        notificationCount++;
      });

      // 触发第一次预警
      const metrics1 = {
        volatility: {
          name: '历史波动率',
          value: 0.4,
          unit: '%',
          description: '价格波动的标准差',
        },
      };
      alertManager.checkAlerts('TEST1', metrics1, []);

      expect(notificationCount).toBe(1);

      // 取消订阅
      unsubscribe();

      // 触发第二次预警
      const metrics2 = {
        volatility: {
          name: '历史波动率',
          value: 0.5,
          unit: '%',
          description: '价格波动的标准差',
        },
      };
      alertManager.checkAlerts('TEST2', metrics2, []);

      // 通知数量应该保持不变
      expect(notificationCount).toBe(1);
    });
  });
});

// 辅助方法
function getAlertPriority(level: AlertLevel): number {
  const priorities = {
    [AlertLevel.CRITICAL]: 4,
    [AlertLevel.DANGER]: 3,
    [AlertLevel.WARNING]: 2,
    [AlertLevel.INFO]: 1,
  };
  return priorities[level] || 0;
}
