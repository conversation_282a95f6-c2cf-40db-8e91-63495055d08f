/**
 * 搜索引擎验证文件支持
 * 支持 Google Search Console、百度站长工具等验证文件
 */

import { NextResponse } from 'next/server';
import { getRuntimeSEOConfig } from './runtime-config';

export interface VerificationConfig {
  google?: string;
  baidu?: string;
  bing?: string;
  yandex?: string;
  custom?: Record<string, string>;
}

/**
 * 搜索引擎验证文件管理器
 */
export class VerificationFileManager {
  private config: VerificationConfig;

  constructor() {
    const runtimeConfig = getRuntimeSEOConfig();
    this.config = {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      baidu: process.env.BAIDU_SITE_VERIFICATION,
      bing: process.env.BING_SITE_VERIFICATION,
      yandex: process.env.YANDEX_SITE_VERIFICATION,
    };
  }

  /**
   * 处理验证文件请求
   */
  handleVerificationRequest(filename: string): NextResponse | null {
    // Google Search Console 验证
    if (filename.startsWith('google') && filename.endsWith('.html')) {
      return this.handleGoogleVerification(filename);
    }

    // 百度站长工具验证
    if (filename.startsWith('baidu_verify_') && filename.endsWith('.html')) {
      return this.handleBaiduVerification(filename);
    }

    // 必应网站管理员工具验证
    if (filename.startsWith('BingSiteAuth.xml')) {
      return this.handleBingVerification();
    }

    // Yandex 验证
    if (filename.startsWith('yandex_') && filename.endsWith('.html')) {
      return this.handleYandexVerification(filename);
    }

    return null;
  }

  /**
   * 处理 Google 验证文件
   */
  private handleGoogleVerification(filename: string): NextResponse {
    if (!this.config.google) {
      return new NextResponse('Google verification not configured', { status: 404 });
    }

    const content = `google-site-verification: ${filename}`;

    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=86400',
      },
    });
  }

  /**
   * 处理百度验证文件
   */
  private handleBaiduVerification(filename: string): NextResponse {
    if (!this.config.baidu) {
      return new NextResponse('Baidu verification not configured', { status: 404 });
    }

    const content = this.config.baidu;

    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=86400',
      },
    });
  }

  /**
   * 处理必应验证文件
   */
  private handleBingVerification(): NextResponse {
    if (!this.config.bing) {
      return new NextResponse('Bing verification not configured', { status: 404 });
    }

    const content = `<?xml version="1.0"?>
<users>
  <user>${this.config.bing}</user>
</users>`;

    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=86400',
      },
    });
  }

  /**
   * 处理 Yandex 验证文件
   */
  private handleYandexVerification(filename: string): NextResponse {
    if (!this.config.yandex) {
      return new NextResponse('Yandex verification not configured', { status: 404 });
    }

    const content = `<html>
<head>
  <meta name="yandex-verification" content="${this.config.yandex}" />
</head>
<body>Yandex verification</body>
</html>`;

    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=86400',
      },
    });
  }

  /**
   * 生成所有验证文件的列表
   */
  getVerificationFiles(): string[] {
    const files: string[] = [];

    if (this.config.google) {
      files.push(`google${this.config.google}.html`);
    }

    if (this.config.baidu) {
      files.push(`baidu_verify_${this.config.baidu}.html`);
    }

    if (this.config.bing) {
      files.push('BingSiteAuth.xml');
    }

    if (this.config.yandex) {
      files.push(`yandex_${this.config.yandex}.html`);
    }

    return files;
  }

  /**
   * 验证配置是否完整
   */
  validateConfiguration(): { isValid: boolean; missing: string[] } {
    const missing: string[] = [];

    if (!this.config.google) missing.push('Google Search Console');
    if (!this.config.baidu) missing.push('Baidu Webmaster Tools');
    if (!this.config.bing) missing.push('Bing Webmaster Tools');

    return {
      isValid: missing.length === 0,
      missing,
    };
  }
}

/**
 * 创建验证文件管理器
 */
export function createVerificationFileManager(): VerificationFileManager {
  return new VerificationFileManager();
}

/**
 * 处理验证文件请求的辅助函数
 */
export function handleVerificationFile(filename: string): NextResponse | null {
  const manager = createVerificationFileManager();
  return manager.handleVerificationRequest(filename);
}
