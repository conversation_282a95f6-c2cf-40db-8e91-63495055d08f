/**
 * 风险控制建议生成器
 * 基于风险评估提供具体的风险控制建议
 * 需求 6: 风险控制建议生成
 */

import { PriceData, RiskMetrics } from './risk-metrics-calculator';
import { StressTestResult } from './risk-scenario-analyzer';

export interface PositionRecommendation {
  recommended_position_size: number; // 建议仓位大小 (0-1)
  position_adjustment: 'increase' | 'decrease' | 'maintain' | 'close';
  adjustment_reason: string;
  risk_budget_utilization: number; // 风险预算利用率
  max_position_size: number; // 最大允许仓位
  confidence_level: number; // 建议置信度 (0-1)
}

export interface StopLossRecommendation {
  stop_loss_price: number; // 止损价格
  stop_loss_percentage: number; // 止损百分比
  stop_loss_type: 'fixed' | 'trailing' | 'volatility_based' | 'technical';
  risk_reward_ratio: number; // 风险收益比
  max_acceptable_loss: number; // 最大可接受损失
  adjustment_frequency: 'daily' | 'weekly' | 'monthly';
}

export interface TimeHorizonRecommendation {
  recommended_holding_period: number; // 建议持有期（天）
  min_holding_period: number; // 最短持有期
  max_holding_period: number; // 最长持有期
  optimal_entry_timing: 'immediate' | 'wait' | 'gradual';
  exit_strategy: 'profit_target' | 'time_based' | 'risk_based' | 'technical';
  market_timing_score: number; // 市场时机评分 (0-100)
}

export interface HedgingRecommendation {
  hedging_required: boolean;
  hedging_strategies: Array<{
    strategy_type: 'options' | 'futures' | 'correlation' | 'sector_rotation';
    strategy_name: string;
    hedge_ratio: number; // 对冲比例
    cost_estimate: number; // 成本估算
    effectiveness: number; // 有效性评分 (0-1)
    implementation_complexity: 'low' | 'medium' | 'high';
  }>;
  portfolio_hedge_ratio: number; // 组合对冲比例
  hedging_cost_benefit: number; // 对冲成本效益比
}

export interface RiskAdjustedReturn {
  expected_return: number; // 预期收益率
  risk_adjusted_return: number; // 风险调整后收益率
  sharpe_ratio: number; // 夏普比率
  risk_premium: number; // 风险溢价
  probability_of_profit: number; // 盈利概率
  expected_maximum_loss: number; // 预期最大损失
}

export interface DiversificationRecommendation {
  current_diversification_score: number; // 当前分散化评分
  recommended_diversification_score: number; // 建议分散化评分
  concentration_risk_level: 'low' | 'medium' | 'high' | 'extreme';
  diversification_suggestions: Array<{
    suggestion_type:
      | 'reduce_concentration'
      | 'add_sectors'
      | 'geographic_spread'
      | 'asset_class_mix';
    description: string;
    priority: 'high' | 'medium' | 'low';
    expected_benefit: number;
  }>;
  correlation_warnings: string[];
}

export interface RiskMonitoringIndicators {
  key_indicators: Array<{
    indicator_name: string;
    current_value: number;
    warning_threshold: number;
    critical_threshold: number;
    monitoring_frequency: 'real_time' | 'daily' | 'weekly';
    alert_condition: string;
  }>;
  risk_dashboard_metrics: string[];
  automated_alerts: Array<{
    alert_type: 'position_size' | 'volatility' | 'correlation' | 'drawdown';
    trigger_condition: string;
    action_required: string;
  }>;
}

export interface ComprehensiveRiskControlRecommendation {
  overall_risk_level: 'low' | 'medium' | 'high' | 'extreme';
  risk_score: number; // 综合风险评分 (0-100)
  investment_recommendation: 'buy' | 'hold' | 'reduce' | 'sell' | 'avoid';

  position_recommendation: PositionRecommendation;
  stop_loss_recommendation: StopLossRecommendation;
  time_horizon_recommendation: TimeHorizonRecommendation;
  hedging_recommendation: HedgingRecommendation;
  risk_adjusted_return: RiskAdjustedReturn;
  diversification_recommendation: DiversificationRecommendation;
  monitoring_indicators: RiskMonitoringIndicators;

  summary_recommendations: string[];
  risk_warnings: string[];
  implementation_priority: Array<{
    action: string;
    priority: 'immediate' | 'high' | 'medium' | 'low';
    timeline: string;
  }>;
}

/**
 * 风险控制建议生成器类
 */
export class RiskControlRecommender {
  private static instance: RiskControlRecommender;

  public static getInstance(): RiskControlRecommender {
    if (!RiskControlRecommender.instance) {
      RiskControlRecommender.instance = new RiskControlRecommender();
    }
    return RiskControlRecommender.instance;
  }

  /**
   * 需求 6.1: 实现基于风险评估的仓位建议算法
   */
  public generatePositionRecommendation(
    riskMetrics: RiskMetrics,
    currentPosition: number,
    riskTolerance: number = 0.02, // 2% VaR限制
    portfolioValue: number = 1000000
  ): PositionRecommendation {
    console.log('[RiskControlRecommender] Generating position recommendation');

    // 基于VaR的仓位计算
    const var95 = riskMetrics.var.var_95_1d;
    const maxPositionByVaR = var95 > 0 ? riskTolerance / var95 : 0.1;

    // 基于波动率的仓位计算
    const volatility = riskMetrics.volatility.annualized_volatility;
    const targetVolatility = 0.15; // 15%目标波动率
    const maxPositionByVol = volatility > 0 ? targetVolatility / volatility : 0.1;

    // 基于最大回撤的仓位计算
    const maxDrawdown = riskMetrics.drawdown.max_drawdown;
    const maxAcceptableDrawdown = 0.1; // 10%最大可接受回撤
    const maxPositionByDrawdown = maxDrawdown > 0 ? maxAcceptableDrawdown / maxDrawdown : 0.1;

    // 基于夏普比率的仓位调整
    const sharpeRatio = riskMetrics.ratios.sharpe_ratio;
    const sharpeAdjustment = Math.max(0.5, Math.min(1.5, 1 + sharpeRatio * 0.2));

    // 综合计算建议仓位
    const basePosition = Math.min(maxPositionByVaR, maxPositionByVol, maxPositionByDrawdown);
    const recommendedPosition = Math.min(1.0, Math.max(0, basePosition * sharpeAdjustment));

    // 确定仓位调整方向
    let positionAdjustment: 'increase' | 'decrease' | 'maintain' | 'close';
    let adjustmentReason: string;

    // 更严格的清仓条件
    if (recommendedPosition < 0.1 || var95 > 0.1 || maxDrawdown > 0.35) {
      positionAdjustment = 'close';
      adjustmentReason = '风险过高，建议清仓';
      // 强制设置为接近0的仓位
      const finalRecommendedPosition = 0.01;
    } else if (recommendedPosition < currentPosition * 0.8) {
      positionAdjustment = 'decrease';
      adjustmentReason = '风险上升，建议减仓';
    } else if (recommendedPosition > currentPosition * 1.2) {
      positionAdjustment = 'increase';
      adjustmentReason = '风险可控，可以增仓';
    } else {
      positionAdjustment = 'maintain';
      adjustmentReason = '当前仓位合理';
    }

    // 风险预算利用率
    const riskBudgetUtilization = (var95 * recommendedPosition) / riskTolerance;

    // 置信度评估
    const confidenceLevel = this.calculateConfidenceLevel(riskMetrics, recommendedPosition);

    // 如果是清仓建议，强制设置极小仓位
    const finalRecommendedPosition = positionAdjustment === 'close' ? 0.01 : recommendedPosition;
    const finalRiskBudgetUtilization = (var95 * finalRecommendedPosition) / riskTolerance;

    return {
      recommended_position_size: finalRecommendedPosition,
      position_adjustment: positionAdjustment,
      adjustment_reason: adjustmentReason,
      risk_budget_utilization: finalRiskBudgetUtilization,
      max_position_size: Math.min(1.0, basePosition * 1.5),
      confidence_level: confidenceLevel,
    };
  }

  /**
   * 需求 6.2: 实现止损位计算和推荐逻辑
   */
  public generateStopLossRecommendation(
    priceData: PriceData[],
    riskMetrics: RiskMetrics,
    currentPrice: number,
    positionSize: number,
    maxLossPercentage: number = 0.05 // 5%最大损失
  ): StopLossRecommendation {
    console.log('[RiskControlRecommender] Generating stop loss recommendation');

    const volatility = riskMetrics.volatility.daily_volatility;
    const var95 = riskMetrics.var.var_95_1d;

    // 基于波动率的止损位
    const volatilityStopLoss = currentPrice * (1 - volatility * 2);

    // 基于VaR的止损位
    const varStopLoss = currentPrice * (1 - var95);

    // 基于最大可接受损失的止损位
    const maxLossStopLoss = currentPrice * (1 - maxLossPercentage);

    // 技术分析止损位（简化版）
    const technicalStopLoss = this.calculateTechnicalStopLoss(priceData, currentPrice);

    // 选择最保守的止损位
    const stopLossPrice = Math.max(
      volatilityStopLoss,
      varStopLoss,
      maxLossStopLoss,
      technicalStopLoss
    );

    const stopLossPercentage = (currentPrice - stopLossPrice) / currentPrice;

    // 确定止损类型
    let stopLossType: 'fixed' | 'trailing' | 'volatility_based' | 'technical';
    if (volatility > 0.03) {
      stopLossType = 'volatility_based';
    } else if (this.hasTechnicalSupport(priceData, stopLossPrice)) {
      stopLossType = 'technical';
    } else if (riskMetrics.volatility.volatility_trend > 0.1) {
      stopLossType = 'trailing';
    } else {
      stopLossType = 'fixed';
    }

    // 风险收益比计算
    const expectedReturn = riskMetrics.ratios.sharpe_ratio * volatility * Math.sqrt(252);
    const riskRewardRatio = expectedReturn > 0 ? expectedReturn / stopLossPercentage : 0;

    // 调整频率
    const adjustmentFrequency =
      volatility > 0.025 ? 'daily' : volatility > 0.015 ? 'weekly' : 'monthly';

    return {
      stop_loss_price: stopLossPrice,
      stop_loss_percentage: stopLossPercentage,
      stop_loss_type: stopLossType,
      risk_reward_ratio: riskRewardRatio,
      max_acceptable_loss: maxLossPercentage,
      adjustment_frequency: adjustmentFrequency,
    };
  }

  /**
   * 需求 6.3: 实现投资时间窗口评估
   */
  public generateTimeHorizonRecommendation(
    riskMetrics: RiskMetrics,
    marketConditions: {
      trend: 'bullish' | 'bearish' | 'sideways';
      volatility_regime: 'low' | 'medium' | 'high';
      market_cycle: 'early' | 'mid' | 'late';
    }
  ): TimeHorizonRecommendation {
    console.log('[RiskControlRecommender] Generating time horizon recommendation');

    const volatility = riskMetrics.volatility.annualized_volatility;
    const sharpeRatio = riskMetrics.ratios.sharpe_ratio;
    const maxDrawdown = riskMetrics.drawdown.max_drawdown;

    // 基础持有期计算（基于波动率）
    let baseHoldingPeriod: number;
    if (volatility < 0.15) {
      baseHoldingPeriod = 180; // 6个月
    } else if (volatility < 0.25) {
      baseHoldingPeriod = 90; // 3个月
    } else {
      baseHoldingPeriod = 30; // 1个月
    }

    // 基于夏普比率调整
    const sharpeAdjustment = sharpeRatio > 1 ? 1.5 : sharpeRatio > 0.5 ? 1.2 : 0.8;
    const adjustedHoldingPeriod = baseHoldingPeriod * sharpeAdjustment;

    // 市场条件调整
    let marketAdjustment = 1.0;
    if (marketConditions.trend === 'bullish' && marketConditions.volatility_regime === 'low') {
      marketAdjustment = 1.3;
    } else if (
      marketConditions.trend === 'bearish' ||
      marketConditions.volatility_regime === 'high'
    ) {
      marketAdjustment = 0.7;
    }

    const recommendedHoldingPeriod = Math.round(adjustedHoldingPeriod * marketAdjustment);
    const minHoldingPeriod = Math.round(recommendedHoldingPeriod * 0.5);
    const maxHoldingPeriod = Math.round(recommendedHoldingPeriod * 2);

    // 入场时机建议
    let optimalEntryTiming: 'immediate' | 'wait' | 'gradual';
    if (volatility > 0.3 || maxDrawdown > 0.15) {
      optimalEntryTiming = 'wait';
    } else if (volatility > 0.2) {
      optimalEntryTiming = 'gradual';
    } else {
      optimalEntryTiming = 'immediate';
    }

    // 退出策略
    let exitStrategy: 'profit_target' | 'time_based' | 'risk_based' | 'technical';
    if (sharpeRatio > 1) {
      exitStrategy = 'profit_target';
    } else if (volatility > 0.25) {
      exitStrategy = 'risk_based';
    } else if (marketConditions.trend === 'sideways') {
      exitStrategy = 'technical';
    } else {
      exitStrategy = 'time_based';
    }

    // 市场时机评分
    const marketTimingScore = this.calculateMarketTimingScore(riskMetrics, marketConditions);

    return {
      recommended_holding_period: recommendedHoldingPeriod,
      min_holding_period: minHoldingPeriod,
      max_holding_period: maxHoldingPeriod,
      optimal_entry_timing: optimalEntryTiming,
      exit_strategy: exitStrategy,
      market_timing_score: marketTimingScore,
    };
  }

  /**
   * 需求 6.4: 实现对冲策略建议生成
   */
  public generateHedgingRecommendation(
    riskMetrics: RiskMetrics,
    portfolioValue: number,
    correlationWithMarket: number = 0.7
  ): HedgingRecommendation {
    console.log('[RiskControlRecommender] Generating hedging recommendation');

    const volatility = riskMetrics.volatility.annualized_volatility;
    const beta = riskMetrics.ratios.beta;
    const var95 = riskMetrics.var.var_95_1d;

    // 判断是否需要对冲
    const hedgingRequired = volatility > 0.25 || Math.abs(beta) > 1.2 || var95 > 0.03;

    const hedgingStrategies: Array<{
      strategy_type: 'options' | 'futures' | 'correlation' | 'sector_rotation';
      strategy_name: string;
      hedge_ratio: number;
      cost_estimate: number;
      effectiveness: number;
      implementation_complexity: 'low' | 'medium' | 'high';
    }> = [];

    if (hedgingRequired) {
      // 期权对冲策略
      if (volatility > 0.2) {
        hedgingStrategies.push({
          strategy_type: 'options',
          strategy_name: '保护性看跌期权',
          hedge_ratio: Math.min(1.0, volatility * 2),
          cost_estimate: portfolioValue * 0.02, // 2%成本估算
          effectiveness: 0.85,
          implementation_complexity: 'medium',
        });
      }

      // 期货对冲策略
      if (Math.abs(beta) > 1.0) {
        hedgingStrategies.push({
          strategy_type: 'futures',
          strategy_name: '股指期货对冲',
          hedge_ratio: Math.abs(beta) * 0.8,
          cost_estimate: portfolioValue * 0.005, // 0.5%成本估算
          effectiveness: 0.75,
          implementation_complexity: 'low',
        });
      }

      // 相关性对冲策略
      if (correlationWithMarket > 0.8) {
        hedgingStrategies.push({
          strategy_type: 'correlation',
          strategy_name: '反向ETF对冲',
          hedge_ratio: correlationWithMarket * 0.5,
          cost_estimate: portfolioValue * 0.01, // 1%成本估算
          effectiveness: 0.6,
          implementation_complexity: 'low',
        });
      }

      // 行业轮动策略
      if (riskMetrics.correlation.sector_correlation > 0.7) {
        hedgingStrategies.push({
          strategy_type: 'sector_rotation',
          strategy_name: '行业分散化',
          hedge_ratio: 0.3,
          cost_estimate: portfolioValue * 0.003, // 0.3%成本估算
          effectiveness: 0.5,
          implementation_complexity: 'medium',
        });
      }
    }

    // 组合对冲比例
    const portfolioHedgeRatio = hedgingRequired ? Math.min(0.5, volatility) : 0;

    // 对冲成本效益比
    const totalCost = hedgingStrategies.reduce((sum, strategy) => sum + strategy.cost_estimate, 0);
    const avgEffectiveness =
      hedgingStrategies.length > 0
        ? hedgingStrategies.reduce((sum, strategy) => sum + strategy.effectiveness, 0) /
          hedgingStrategies.length
        : 0;
    const hedgingCostBenefit = totalCost > 0 ? avgEffectiveness / (totalCost / portfolioValue) : 0;

    return {
      hedging_required: hedgingRequired,
      hedging_strategies: hedgingStrategies,
      portfolio_hedge_ratio: portfolioHedgeRatio,
      hedging_cost_benefit: hedgingCostBenefit,
    };
  }

  /**
   * 需求 6.5: 评估风险调整后的预期收益
   */
  public calculateRiskAdjustedReturn(
    riskMetrics: RiskMetrics,
    expectedReturn: number,
    riskFreeRate: number = 0.03
  ): RiskAdjustedReturn {
    console.log('[RiskControlRecommender] Calculating risk adjusted return');

    const volatility = riskMetrics.volatility.annualized_volatility;
    const sharpeRatio = riskMetrics.ratios.sharpe_ratio;
    const var95 = riskMetrics.var.var_95_1d;
    const maxDrawdown = riskMetrics.drawdown.max_drawdown;

    // 风险调整后收益率
    const riskAdjustment = Math.max(0.5, 1 - volatility * 0.5 - maxDrawdown);
    const riskAdjustedReturn = expectedReturn * riskAdjustment;

    // 风险溢价
    const riskPremium = expectedReturn - riskFreeRate;

    // 盈利概率估算（基于正态分布假设）
    const zScore = expectedReturn / volatility;
    const probabilityOfProfit = this.normalCDF(zScore);

    // 预期最大损失（基于VaR和最大回撤）
    const expectedMaximumLoss = Math.max(var95 * Math.sqrt(252), maxDrawdown);

    return {
      expected_return: expectedReturn,
      risk_adjusted_return: riskAdjustedReturn,
      sharpe_ratio: sharpeRatio,
      risk_premium: riskPremium,
      probability_of_profit: probabilityOfProfit,
      expected_maximum_loss: expectedMaximumLoss,
    };
  }

  /**
   * 生成综合风险控制建议
   */
  public generateComprehensiveRecommendation(
    riskMetrics: RiskMetrics,
    priceData: PriceData[],
    stressTestResult?: StressTestResult,
    currentPosition: number = 0,
    portfolioValue: number = 1000000,
    riskTolerance: number = 0.02
  ): ComprehensiveRiskControlRecommendation {
    console.log('[RiskControlRecommender] Generating comprehensive risk control recommendation');

    // 计算综合风险评分
    const riskScore = this.calculateOverallRiskScore(riskMetrics, stressTestResult);
    const overallRiskLevel = this.determineRiskLevel(riskScore);

    // 生成各项建议
    const positionRecommendation = this.generatePositionRecommendation(
      riskMetrics,
      currentPosition,
      riskTolerance,
      portfolioValue
    );

    const stopLossRecommendation = this.generateStopLossRecommendation(
      priceData,
      riskMetrics,
      priceData[priceData.length - 1].close,
      positionRecommendation.recommended_position_size
    );

    const timeHorizonRecommendation = this.generateTimeHorizonRecommendation(riskMetrics, {
      trend: 'sideways',
      volatility_regime: riskMetrics.volatility.annualized_volatility > 0.25 ? 'high' : 'medium',
      market_cycle: 'mid',
    });

    const hedgingRecommendation = this.generateHedgingRecommendation(
      riskMetrics,
      portfolioValue,
      riskMetrics.correlation.market_correlation
    );

    const expectedReturn =
      riskMetrics.ratios.sharpe_ratio * riskMetrics.volatility.annualized_volatility + 0.03;
    const riskAdjustedReturn = this.calculateRiskAdjustedReturn(riskMetrics, expectedReturn);

    const diversificationRecommendation = this.generateDiversificationRecommendation(riskMetrics);
    const monitoringIndicators = this.generateMonitoringIndicators(riskMetrics);

    // 投资建议
    const investmentRecommendation = this.determineInvestmentRecommendation(
      riskScore,
      positionRecommendation,
      riskAdjustedReturn
    );

    // 生成总结建议和警告
    const summaryRecommendations = this.generateSummaryRecommendations(
      overallRiskLevel,
      positionRecommendation,
      stopLossRecommendation,
      hedgingRecommendation
    );

    const riskWarnings = this.generateRiskWarnings(riskMetrics, stressTestResult);

    const implementationPriority = this.generateImplementationPriority(
      overallRiskLevel,
      positionRecommendation,
      hedgingRecommendation
    );

    return {
      overall_risk_level: overallRiskLevel,
      risk_score: riskScore,
      investment_recommendation: investmentRecommendation,
      position_recommendation: positionRecommendation,
      stop_loss_recommendation: stopLossRecommendation,
      time_horizon_recommendation: timeHorizonRecommendation,
      hedging_recommendation: hedgingRecommendation,
      risk_adjusted_return: riskAdjustedReturn,
      diversification_recommendation: diversificationRecommendation,
      monitoring_indicators: monitoringIndicators,
      summary_recommendations: summaryRecommendations,
      risk_warnings: riskWarnings,
      implementation_priority: implementationPriority,
    };
  }

  // 私有辅助方法

  private calculateConfidenceLevel(riskMetrics: RiskMetrics, position: number): number {
    const volatility = riskMetrics.volatility.annualized_volatility;
    const sharpeRatio = riskMetrics.ratios.sharpe_ratio;
    const correlationStability = riskMetrics.correlation.correlation_stability;

    // 基于多个因素计算置信度
    let confidence = 0.5; // 基础置信度

    // 夏普比率贡献
    if (sharpeRatio > 1) confidence += 0.2;
    else if (sharpeRatio > 0.5) confidence += 0.1;
    else if (sharpeRatio < 0) confidence -= 0.2;

    // 波动率贡献
    if (volatility < 0.15) confidence += 0.15;
    else if (volatility > 0.3) confidence -= 0.15;

    // 相关性稳定性贡献
    confidence += correlationStability * 0.15;

    // 仓位大小调整
    if (position < 0.3) confidence += 0.1;
    else if (position > 0.7) confidence -= 0.1;

    return Math.max(0.1, Math.min(0.95, confidence));
  }

  private calculateTechnicalStopLoss(priceData: PriceData[], currentPrice: number): number {
    // 简化的技术分析止损位计算
    const recentLows = priceData.slice(-20).map((d) => d.low);
    const supportLevel = Math.min(...recentLows);

    // 在支撑位下方设置止损
    return supportLevel * 0.98;
  }

  private hasTechnicalSupport(priceData: PriceData[], price: number): boolean {
    // 检查是否有技术支撑位
    const recentLows = priceData.slice(-30).map((d) => d.low);
    const supportCount = recentLows.filter((low) => Math.abs(low - price) / price < 0.02).length;
    return supportCount >= 2;
  }

  private calculateMarketTimingScore(riskMetrics: RiskMetrics, marketConditions: any): number {
    let score = 50; // 基础分数

    // 基于夏普比率
    if (riskMetrics.ratios.sharpe_ratio > 1) score += 20;
    else if (riskMetrics.ratios.sharpe_ratio < 0) score -= 20;

    // 基于波动率
    if (riskMetrics.volatility.annualized_volatility < 0.15) score += 15;
    else if (riskMetrics.volatility.annualized_volatility > 0.3) score -= 15;

    // 基于市场趋势
    if (marketConditions.trend === 'bullish') score += 15;
    else if (marketConditions.trend === 'bearish') score -= 15;

    return Math.max(0, Math.min(100, score));
  }

  private calculateOverallRiskScore(
    riskMetrics: RiskMetrics,
    stressTestResult?: StressTestResult
  ): number {
    let score = 30; // 降低基础分数

    // 波动率影响 - 更敏感
    const volatility = riskMetrics.volatility.annualized_volatility;
    if (volatility > 0.5) score += 40;
    else if (volatility > 0.3) score += 25;
    else if (volatility > 0.2) score += 15;
    else if (volatility < 0.15) score -= 15;

    // VaR影响 - 更严格
    const var95 = riskMetrics.var.var_95_1d;
    if (var95 > 0.08) score += 30;
    else if (var95 > 0.05) score += 20;
    else if (var95 > 0.03) score += 10;

    // 最大回撤影响 - 更严格
    const maxDrawdown = riskMetrics.drawdown.max_drawdown;
    if (maxDrawdown > 0.3) score += 30;
    else if (maxDrawdown > 0.2) score += 20;
    else if (maxDrawdown > 0.1) score += 10;

    // 夏普比率影响
    const sharpeRatio = riskMetrics.ratios.sharpe_ratio;
    if (sharpeRatio < 0) score += 15;
    else if (sharpeRatio > 1) score -= 15;

    // 压力测试结果影响
    if (stressTestResult) {
      if (stressTestResult.summary.stress_test_score < 30) score += 20;
      else if (stressTestResult.summary.stress_test_score < 60) score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  private determineRiskLevel(riskScore: number): 'low' | 'medium' | 'high' | 'extreme' {
    if (riskScore >= 80) return 'extreme';
    if (riskScore >= 60) return 'high';
    if (riskScore >= 30) return 'medium';
    return 'low';
  }

  private determineInvestmentRecommendation(
    riskScore: number,
    positionRecommendation: PositionRecommendation,
    riskAdjustedReturn: RiskAdjustedReturn
  ): 'buy' | 'hold' | 'reduce' | 'sell' | 'avoid' {
    if (riskScore >= 80) return 'avoid';
    if (riskScore >= 60) return 'sell';
    if (riskScore >= 40) return 'reduce';

    if (
      riskAdjustedReturn.sharpe_ratio > 1 &&
      positionRecommendation.position_adjustment === 'increase'
    ) {
      return 'buy';
    }

    return 'hold';
  }

  private generateDiversificationRecommendation(
    riskMetrics: RiskMetrics
  ): DiversificationRecommendation {
    const correlationScore = (1 - riskMetrics.correlation.market_correlation) * 100;
    const diversificationScore = riskMetrics.correlation.diversification_ratio * 100;

    let concentrationRiskLevel: 'low' | 'medium' | 'high' | 'extreme';
    if (riskMetrics.correlation.market_correlation > 0.8) concentrationRiskLevel = 'extreme';
    else if (riskMetrics.correlation.market_correlation > 0.6) concentrationRiskLevel = 'high';
    else if (riskMetrics.correlation.market_correlation > 0.4) concentrationRiskLevel = 'medium';
    else concentrationRiskLevel = 'low';

    const suggestions = [];
    if (riskMetrics.correlation.market_correlation > 0.7) {
      suggestions.push({
        suggestion_type: 'reduce_concentration' as const,
        description: '降低与市场的相关性，增加非相关资产',
        priority: 'high' as const,
        expected_benefit: 0.15,
      });
    }

    return {
      current_diversification_score: diversificationScore,
      recommended_diversification_score: Math.max(diversificationScore, 70),
      concentration_risk_level: concentrationRiskLevel,
      diversification_suggestions: suggestions,
      correlation_warnings:
        riskMetrics.correlation.market_correlation > 0.8 ? ['市场相关性过高，存在系统性风险'] : [],
    };
  }

  private generateMonitoringIndicators(riskMetrics: RiskMetrics): RiskMonitoringIndicators {
    const keyIndicators = [
      {
        indicator_name: '日波动率',
        current_value: riskMetrics.volatility.daily_volatility,
        warning_threshold: 0.025,
        critical_threshold: 0.04,
        monitoring_frequency: 'daily' as const,
        alert_condition: '超过警告阈值时发出预警',
      },
      {
        indicator_name: 'VaR 95%',
        current_value: riskMetrics.var.var_95_1d,
        warning_threshold: 0.03,
        critical_threshold: 0.05,
        monitoring_frequency: 'daily' as const,
        alert_condition: '超过风险预算时立即警报',
      },
    ];

    const automatedAlerts = [
      {
        alert_type: 'volatility' as const,
        trigger_condition: '波动率超过历史90%分位数',
        action_required: '考虑降低仓位或增加对冲',
      },
    ];

    return {
      key_indicators: keyIndicators,
      risk_dashboard_metrics: ['波动率', 'VaR', '最大回撤', '夏普比率'],
      automated_alerts: automatedAlerts,
    };
  }

  private generateSummaryRecommendations(
    riskLevel: 'low' | 'medium' | 'high' | 'extreme',
    positionRecommendation: PositionRecommendation,
    stopLossRecommendation: StopLossRecommendation,
    hedgingRecommendation: HedgingRecommendation
  ): string[] {
    const recommendations = [];

    // 基于风险等级的建议
    switch (riskLevel) {
      case 'extreme':
        recommendations.push('风险极高，建议立即清仓或大幅减仓');
        recommendations.push('暂停新增投资，等待市场稳定');
        break;
      case 'high':
        recommendations.push('风险较高，建议降低仓位至30%以下');
        recommendations.push('设置严格的止损位，密切监控市场变化');
        break;
      case 'medium':
        recommendations.push('风险适中，可维持当前仓位但需谨慎');
        recommendations.push('建议设置动态止损位，定期重新评估');
        break;
      case 'low':
        recommendations.push('风险较低，可以正常投资');
        recommendations.push('保持适度分散化，定期监控风险指标');
        break;
    }

    // 基于仓位建议
    if (positionRecommendation.position_adjustment === 'close') {
      recommendations.push('建议清仓，风险超出可承受范围');
    } else if (positionRecommendation.position_adjustment === 'decrease') {
      recommendations.push(
        `建议减仓至${(positionRecommendation.recommended_position_size * 100).toFixed(0)}%`
      );
    }

    // 基于对冲建议
    if (hedgingRecommendation.hedging_required) {
      recommendations.push('建议实施对冲策略以降低风险敞口');
    }

    return recommendations;
  }

  private generateRiskWarnings(
    riskMetrics: RiskMetrics,
    stressTestResult?: StressTestResult
  ): string[] {
    const warnings = [];

    if (riskMetrics.volatility.annualized_volatility > 0.3) {
      warnings.push('波动率过高，可能面临大幅价格波动');
    }

    if (riskMetrics.var.var_95_1d > 0.05) {
      warnings.push('VaR超过5%，单日损失风险较大');
    }

    if (riskMetrics.drawdown.max_drawdown > 0.2) {
      warnings.push('历史最大回撤超过20%，需要关注回撤风险');
    }

    if (riskMetrics.correlation.market_correlation > 0.8) {
      warnings.push('与市场相关性过高，系统性风险较大');
    }

    if (stressTestResult && stressTestResult.summary.stress_test_score < 30) {
      warnings.push('压力测试评分过低，极端情况下损失可能很大');
    }

    return warnings;
  }

  private generateImplementationPriority(
    riskLevel: 'low' | 'medium' | 'high' | 'extreme',
    positionRecommendation: PositionRecommendation,
    hedgingRecommendation: HedgingRecommendation
  ): Array<{
    action: string;
    priority: 'immediate' | 'high' | 'medium' | 'low';
    timeline: string;
  }> {
    const priorities = [];

    if (riskLevel === 'extreme') {
      priorities.push({
        action: '立即减仓或清仓',
        priority: 'immediate' as const,
        timeline: '立即执行',
      });
    }

    if (positionRecommendation.position_adjustment === 'decrease') {
      priorities.push({
        action: '调整仓位大小',
        priority: 'high' as const,
        timeline: '1个交易日内',
      });
    }

    if (hedgingRecommendation.hedging_required) {
      priorities.push({
        action: '实施对冲策略',
        priority: 'high' as const,
        timeline: '3个交易日内',
      });
    }

    priorities.push({
      action: '设置风险监控指标',
      priority: 'medium' as const,
      timeline: '1周内',
    });

    return priorities;
  }

  private normalCDF(x: number): number {
    // 标准正态分布累积分布函数的近似计算
    return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
  }

  private erf(x: number): number {
    // 误差函数的近似计算
    const a1 = 0.254829592;
    const a2 = -0.284496736;
    const a3 = 1.421413741;
    const a4 = -1.453152027;
    const a5 = 1.061405429;
    const p = 0.3275911;

    const sign = x >= 0 ? 1 : -1;
    x = Math.abs(x);

    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - ((((a5 * t + a4) * t + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

    return sign * y;
  }
}

// 导出单例实例
export const riskControlRecommender = RiskControlRecommender.getInstance();
