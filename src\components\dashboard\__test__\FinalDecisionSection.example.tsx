'use client';

import { FinalDecision } from '@/types/langgraph-database';
import { FinalDecisionSection } from '../FinalDecisionSection';

// 示例数据
const mockFinalDecisions: FinalDecision[] = [
  {
    id: 1,
    decision_id: 'decision_001',
    workflow_id: 'workflow_001',
    decision_type: 'buy',
    confidence_level: 85,
    decision_rationale: `基于综合分析，我们认为该股票具有良好的投资价值：

1. **基本面分析**：公司财务状况良好，营收增长稳定，市盈率合理
2. **技术面分析**：股价突破关键阻力位，技术指标显示上涨趋势
3. **情绪面分析**：市场情绪积极，投资者信心增强
4. **新闻面分析**：近期利好消息频出，行业前景看好

综合多个维度的分析结果，建议在当前价位买入该股票，预期未来3-6个月有较好的上涨空间。`,
    entry_price_range: {
      min: 45.5,
      max: 47.2,
      optimal: 46.3,
    },
    stop_loss_price: 42.8,
    take_profit_price: 52.5,
    position_size_percentage: 15,
    created_at: new Date('2024-01-15T10:30:00Z'),
  },
  {
    id: 2,
    decision_id: 'decision_002',
    workflow_id: 'workflow_002',
    decision_type: 'sell',
    confidence_level: 78,
    decision_rationale: `经过深入分析，我们建议卖出该股票：

1. **估值过高**：当前股价已超出合理估值区间
2. **技术面转弱**：技术指标显示下跌信号
3. **行业风险**：所处行业面临政策调整风险
4. **资金流出**：机构资金持续流出

建议及时止盈或减仓，规避潜在下跌风险。`,
    entry_price_range: {
      min: 28.5,
      max: 29.8,
    },
    stop_loss_price: 31.2,
    take_profit_price: 25.6,
    position_size_percentage: 8,
    created_at: new Date('2024-01-15T14:45:00Z'),
  },
  {
    id: 3,
    decision_id: 'decision_003',
    workflow_id: 'workflow_003',
    decision_type: 'hold',
    confidence_level: 72,
    decision_rationale: `当前建议继续持有该股票：

1. **基本面稳定**：公司经营状况良好，无重大风险
2. **估值合理**：股价处于合理估值区间
3. **趋势不明**：短期内缺乏明确的方向性信号
4. **分红预期**：预期将有稳定的分红收益

建议保持现有仓位，等待更明确的投资机会。`,
    position_size_percentage: 12,
    created_at: new Date('2024-01-15T16:20:00Z'),
  },
  {
    id: 4,
    decision_id: 'decision_004',
    workflow_id: 'workflow_004',
    decision_type: 'avoid',
    confidence_level: 90,
    decision_rationale: `强烈建议避免投资该股票：

1. **财务风险**：公司负债率过高，现金流紧张
2. **行业衰退**：所处行业面临结构性衰退
3. **监管风险**：面临严格的监管审查
4. **技术面恶化**：股价持续下跌，技术面极度恶化

该股票存在重大投资风险，建议完全避免。`,
    position_size_percentage: 0,
    created_at: new Date('2024-01-15T18:10:00Z'),
  },
];

export function FinalDecisionSectionExample() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            最终决策展示组件示例
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            展示不同类型的投资决策：买入、卖出、持有、避免
          </p>
        </div>

        <div className="space-y-12">
          {mockFinalDecisions.map((decision, index) => (
            <div key={decision.decision_id} className="bg-white dark:bg-gray-800 rounded-xl p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  示例 {index + 1}: {decision.decision_type.toUpperCase()} 决策
                </h2>
                <p className="text-sm text-gray-500">
                  工作流ID: {decision.workflow_id} | 决策ID: {decision.decision_id}
                </p>
              </div>

              <FinalDecisionSection decision={decision} />
            </div>
          ))}
        </div>

        {/* 功能说明 */}
        <div className="mt-12 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
            组件功能说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-200">
            <div>
              <h4 className="font-medium mb-2">✨ 主要功能</h4>
              <ul className="space-y-1 text-blue-700 dark:text-blue-300">
                <li>• 显示投资建议和决策类型</li>
                <li>• 可视化决策置信度</li>
                <li>• 展示价格目标和止损止盈</li>
                <li>• 仓位管理和风险评估</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🎨 设计特点</h4>
              <ul className="space-y-1 text-blue-700 dark:text-blue-300">
                <li>• 响应式设计，支持移动端</li>
                <li>• 动画效果增强用户体验</li>
                <li>• 颜色编码区分决策类型</li>
                <li>• 详细的决策理由展示</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FinalDecisionSectionExample;
