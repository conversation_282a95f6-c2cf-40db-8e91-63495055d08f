/**
 * 风险管理师状态管理模块
 * 实现风险管理师的状态跟踪、进度报告、错误处理和性能监控
 */

import { EventEmitter } from 'events';

// 风险管理师状态类型定义
export type RiskManagerStatus =
  | 'pending'
  | 'running'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface RiskManagerState {
  id: string;
  workflowId: string;
  status: RiskManagerStatus;
  progress: number;
  currentTask: string;
  startTime?: number;
  endTime?: number;
  executionTimeMs?: number;
  pausedTime?: number; // 暂停时的累计时间
  pausedAt?: number; // 暂停时间戳
  resumedAt?: number; // 恢复时间戳
  error?: {
    message: string;
    code: string;
    timestamp: number;
    stack?: string;
  };
  performance: {
    dataCollectionTime?: number;
    analysisTime?: number;
    reportGenerationTime?: number;
    totalTime?: number;
    pausedDuration?: number; // 总暂停时长
  };
  tasks: RiskManagerTask[];
  metadata?: Record<string, any>;
  logs: RiskManagerLog[]; // 日志记录
}

export interface RiskManagerLog {
  id: string;
  timestamp: number;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  context?: Record<string, any>;
  taskId?: string;
}

export interface RiskManagerTask {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: number;
  endTime?: number;
  executionTimeMs?: number;
  pausedTime?: number; // 任务暂停时的累计时间
  pausedAt?: number; // 任务暂停时间戳
  error?: string;
  result?: any;
}

export interface RiskManagerProgressUpdate {
  workflowId: string;
  status: RiskManagerStatus;
  progress: number;
  currentTask: string;
  executionTimeMs?: number;
  error?: string;
  performance?: Partial<RiskManagerState['performance']>;
}

export interface RiskManagerConfig {
  maxExecutionTime: number; // 最大执行时间（毫秒）
  retryAttempts: number; // 重试次数
  progressUpdateInterval: number; // 进度更新间隔（毫秒）
  enablePerformanceMonitoring: boolean; // 是否启用性能监控
  enableDetailedLogging: boolean; // 是否启用详细日志
}

/**
 * 风险管理师状态管理器
 */
export class RiskManagerStateManager extends EventEmitter {
  private states: Map<string, RiskManagerState> = new Map();
  private config: RiskManagerConfig;
  private progressTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: Partial<RiskManagerConfig> = {}) {
    super();
    this.config = {
      maxExecutionTime: 300000, // 5分钟
      retryAttempts: 3,
      progressUpdateInterval: 1000, // 1秒
      enablePerformanceMonitoring: true,
      enableDetailedLogging: true,
      ...config,
    };
  }

  /**
   * 初始化风险管理师状态
   */
  initializeState(workflowId: string, metadata?: Record<string, any>): RiskManagerState {
    const state: RiskManagerState = {
      id: `risk_manager_${workflowId}_${Date.now()}`,
      workflowId,
      status: 'pending',
      progress: 0,
      currentTask: '准备开始风险分析',
      performance: {},
      tasks: this.createDefaultTasks(),
      metadata,
      logs: [],
    };

    this.states.set(workflowId, state);
    this.addLog(workflowId, 'info', '风险管理师状态已初始化', { metadata });
    this.emit('stateInitialized', state);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Initialized state for workflow ${workflowId}`);
    }

    return state;
  }

  /**
   * 开始风险分析
   */
  startAnalysis(workflowId: string): void {
    const state = this.getState(workflowId);
    if (!state) {
      throw new Error(`Risk manager state not found for workflow ${workflowId}`);
    }

    state.status = 'running';
    state.startTime = Date.now();
    state.currentTask = '开始风险数据收集';
    state.progress = 5;

    this.addLog(workflowId, 'info', '风险分析已开始', {
      startTime: state.startTime,
      initialProgress: state.progress,
    });

    this.updateState(workflowId, state);
    this.startProgressMonitoring(workflowId);
    this.startExecutionTimeMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Started analysis for workflow ${workflowId}`);
    }
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(
    workflowId: string,
    taskId: string,
    status: RiskManagerTask['status'],
    progress?: number,
    result?: any,
    error?: string
  ): void {
    const state = this.getState(workflowId);
    if (!state) return;

    const task = state.tasks.find((t) => t.id === taskId);
    if (!task) {
      this.addLog(workflowId, 'warn', `任务 ${taskId} 不存在`, { taskId, status });
      return;
    }

    const now = Date.now();
    const previousStatus = task.status;

    // 更新任务状态
    if (status === 'running' && (task.status === 'pending' || (task as any).status === 'paused')) {
      if (task.status === 'pending') {
        task.startTime = now;
      }
      // 如果从暂停恢复，计算暂停时长
      if ((task as any).status === 'paused' && (task as any).pausedAt) {
        const pausedDuration = now - (task as any).pausedAt;
        task.pausedTime = (task.pausedTime || 0) + pausedDuration;
      }
    } else if ((status === 'completed' || status === 'failed') && task.startTime) {
      task.endTime = now;
      // 计算执行时间（排除暂停时间）
      const totalPausedTime = task.pausedTime || 0;
      task.executionTimeMs = now - task.startTime - totalPausedTime;
    }

    task.status = status;
    if (progress !== undefined) task.progress = progress;
    if (result !== undefined) task.result = result;
    if (error) task.error = error;

    // 记录任务状态变化
    this.addLog(
      workflowId,
      error ? 'error' : status === 'completed' ? 'info' : 'debug',
      `任务 ${task.name} 状态从 ${previousStatus} 更新为 ${status}`,
      {
        taskId,
        previousStatus,
        newStatus: status,
        progress,
        error,
        executionTime: task.executionTimeMs,
      },
      taskId
    );

    // 更新整体进度
    this.updateOverallProgress(state);

    // 更新当前任务描述
    if (status === 'running') {
      state.currentTask = task.description;
    } else if (status === 'completed') {
      const nextTask = this.getNextPendingTask(state);
      state.currentTask = nextTask ? nextTask.description : '风险分析即将完成';
    }

    this.updateState(workflowId, state);

    if (this.config.enableDetailedLogging) {
      console.log(
        `[Risk Manager State] Task ${taskId} updated to ${status} for workflow ${workflowId}`
      );
    }
  }

  /**
   * 完成风险分析
   */
  completeAnalysis(workflowId: string, result?: any): void {
    const state = this.getState(workflowId);
    if (!state) return;

    const now = Date.now();
    state.status = 'completed';
    state.endTime = now;
    state.progress = 100;
    state.currentTask = '风险分析已完成';

    if (state.startTime) {
      // 计算总执行时间（排除暂停时间）
      const totalPausedTime = state.pausedTime || 0;
      state.executionTimeMs = now - state.startTime - totalPausedTime;
      state.performance.totalTime = state.executionTimeMs;
    }

    // 标记所有未完成的任务为已完成
    state.tasks.forEach((task) => {
      if (
        task.status === 'pending' ||
        task.status === 'running' ||
        (task as any).status === 'paused'
      ) {
        task.status = 'completed';
        task.progress = 100;
        if (!task.endTime) task.endTime = now;
        if (task.startTime && !task.executionTimeMs) {
          const taskPausedTime = task.pausedTime || 0;
          task.executionTimeMs = now - task.startTime - taskPausedTime;
        }
      }
    });

    this.addLog(workflowId, 'info', '风险分析已完成', {
      totalExecutionTime: state.executionTimeMs,
      totalPausedTime: state.pausedTime,
      result,
    });

    this.updateState(workflowId, state);
    this.stopProgressMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(
        `[Risk Manager State] Analysis completed for workflow ${workflowId} in ${state.executionTimeMs}ms`
      );
    }
  }

  /**
   * 处理分析失败
   */
  failAnalysis(workflowId: string, error: Error | string): void {
    const state = this.getState(workflowId);
    if (!state) return;

    const now = Date.now();
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    state.status = 'failed';
    state.endTime = now;
    state.currentTask = '风险分析失败';
    state.error = {
      message: errorObj.message,
      code: 'RISK_ANALYSIS_FAILED',
      timestamp: now,
      stack: errorObj.stack,
    };

    if (state.startTime) {
      // 计算总执行时间（排除暂停时间）
      let totalPausedTime = (state as any).pausedTime || 0;
      if ((state as any).status === 'paused' && (state as any).pausedAt) {
        totalPausedTime += now - (state as any).pausedAt;
      }
      state.executionTimeMs = now - state.startTime - totalPausedTime;
      state.performance.totalTime = state.executionTimeMs;
    }

    // 标记当前运行或暂停的任务为失败
    const activeTask = state.tasks.find(
      (task) => task.status === 'running' || (task as any).status === 'paused'
    );
    if (activeTask) {
      activeTask.status = 'failed';
      activeTask.error = errorObj.message;
      activeTask.endTime = now;
      if (activeTask.startTime) {
        let taskPausedTime = activeTask.pausedTime || 0;
        if ((activeTask as any).status === 'paused' && (activeTask as any).pausedAt) {
          taskPausedTime += now - (activeTask as any).pausedAt;
        }
        activeTask.executionTimeMs = now - activeTask.startTime - taskPausedTime;
      }
    }

    this.addLog(workflowId, 'error', '风险分析失败', {
      error: errorObj.message,
      stack: errorObj.stack,
      executionTime: state.executionTimeMs,
      failedTask: activeTask?.id,
    });

    this.updateState(workflowId, state);
    this.stopProgressMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.error(`[Risk Manager State] Analysis failed for workflow ${workflowId}:`, error);
    }
  }

  /**
   * 暂停风险分析
   */
  pauseAnalysis(workflowId: string): void {
    const state = this.getState(workflowId);
    if (!state || state.status !== 'running') return;

    const now = Date.now();
    state.status = 'paused';
    state.pausedAt = now;
    state.currentTask = '风险分析已暂停';

    // 计算当前执行时间（不包括之前的暂停时间）
    if (state.startTime) {
      const currentExecutionTime = now - state.startTime - (state.pausedTime || 0);
      state.executionTimeMs = currentExecutionTime;
    }

    // 暂停所有运行中的任务
    state.tasks.forEach((task) => {
      if (task.status === 'running') {
        task.status = 'paused';
        task.pausedAt = now;
        if (task.startTime) {
          const taskExecutionTime = now - task.startTime - (task.pausedTime || 0);
          task.executionTimeMs = taskExecutionTime;
        }
      }
    });

    this.addLog(workflowId, 'info', '风险分析已暂停');
    this.updateState(workflowId, state);
    this.stopProgressMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Analysis paused for workflow ${workflowId}`);
    }
  }

  /**
   * 恢复风险分析
   */
  resumeAnalysis(workflowId: string): void {
    const state = this.getState(workflowId);
    if (!state || state.status !== 'paused') return;

    const now = Date.now();
    state.status = 'running';
    state.resumedAt = now;

    // 计算暂停时长
    if (state.pausedAt) {
      const pausedDuration = now - state.pausedAt;
      state.pausedTime = (state.pausedTime || 0) + pausedDuration;
      state.performance.pausedDuration = state.pausedTime;
    }

    // 恢复暂停的任务
    const pausedTask = state.tasks.find((task) => (task as any).status === 'paused');
    if (pausedTask) {
      pausedTask.status = 'running';
      if (pausedTask.pausedAt) {
        const taskPausedDuration = now - pausedTask.pausedAt;
        pausedTask.pausedTime = (pausedTask.pausedTime || 0) + taskPausedDuration;
      }
      state.currentTask = pausedTask.description;
    } else {
      // 如果没有暂停的任务，找到下一个待执行的任务
      const nextTask = this.getNextPendingTask(state);
      state.currentTask = nextTask ? nextTask.description : '继续风险分析';
    }

    this.addLog(workflowId, 'info', '风险分析已恢复');
    this.updateState(workflowId, state);
    this.startProgressMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Analysis resumed for workflow ${workflowId}`);
    }
  }

  /**
   * 取消风险分析
   */
  cancelAnalysis(workflowId: string): void {
    const state = this.getState(workflowId);
    if (!state) return;

    const now = Date.now();
    state.status = 'cancelled';
    state.endTime = now;
    state.currentTask = '风险分析已取消';

    // 计算总执行时间（排除暂停时间）
    if (state.startTime) {
      let totalPausedTime = state.pausedTime || 0;
      if ((state as any).status === 'paused' && (state as any).pausedAt) {
        totalPausedTime += now - (state as any).pausedAt;
      }
      state.executionTimeMs = now - state.startTime - totalPausedTime;
      state.performance.totalTime = state.executionTimeMs;
    }

    // 标记所有运行中或暂停的任务为已取消
    state.tasks.forEach((task) => {
      if (task.status === 'running' || (task as any).status === 'paused') {
        task.status = 'failed';
        task.error = '分析被取消';
        task.endTime = now;
        if (task.startTime) {
          let taskPausedTime = task.pausedTime || 0;
          if ((task as any).status === 'paused' && (task as any).pausedAt) {
            taskPausedTime += now - (task as any).pausedAt;
          }
          task.executionTimeMs = now - task.startTime - taskPausedTime;
        }
      }
    });

    this.addLog(workflowId, 'info', '风险分析已取消');
    this.updateState(workflowId, state);
    this.stopProgressMonitoring(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Analysis cancelled for workflow ${workflowId}`);
    }
  }

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics(
    workflowId: string,
    metrics: Partial<RiskManagerState['performance']>
  ): void {
    const state = this.getState(workflowId);
    if (!state) return;

    state.performance = { ...state.performance, ...metrics };
    this.updateState(workflowId, state);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Performance metrics updated for workflow ${workflowId}`);
    }
  }

  /**
   * 获取状态
   */
  getState(workflowId: string): RiskManagerState | undefined {
    return this.states.get(workflowId);
  }

  /**
   * 获取所有状态
   */
  getAllStates(): RiskManagerState[] {
    return Array.from(this.states.values());
  }

  /**
   * 清理状态
   */
  cleanupState(workflowId: string): void {
    this.stopProgressMonitoring(workflowId);
    this.states.delete(workflowId);

    if (this.config.enableDetailedLogging) {
      console.log(`[Risk Manager State] Cleaned up state for workflow ${workflowId}`);
    }
  }

  /**
   * 添加日志记录
   */
  addLog(
    workflowId: string,
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    context?: Record<string, any>,
    taskId?: string
  ): void {
    const state = this.getState(workflowId);
    if (!state) return;

    const log: RiskManagerLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      level,
      message,
      context,
      taskId,
    };

    state.logs.push(log);

    // 限制日志数量，保留最近的1000条
    if (state.logs.length > 1000) {
      state.logs = state.logs.slice(-1000);
    }

    this.updateState(workflowId, state);

    if (this.config.enableDetailedLogging) {
      const logPrefix = `[Risk Manager ${level.toUpperCase()}]`;
      const logMessage = `${logPrefix} [${workflowId}${taskId ? `:${taskId}` : ''}] ${message}`;

      switch (level) {
        case 'error':
          console.error(logMessage, context);
          break;
        case 'warn':
          console.warn(logMessage, context);
          break;
        case 'debug':
          console.debug(logMessage, context);
          break;
        default:
          console.log(logMessage, context);
      }
    }
  }

  /**
   * 获取日志记录
   */
  getLogs(
    workflowId: string,
    options?: {
      level?: 'debug' | 'info' | 'warn' | 'error';
      taskId?: string;
      limit?: number;
      since?: number;
    }
  ): RiskManagerLog[] {
    const state = this.getState(workflowId);
    if (!state) return [];

    let logs = state.logs;

    // 按级别过滤
    if (options?.level) {
      logs = logs.filter((log) => log.level === options.level);
    }

    // 按任务ID过滤
    if (options?.taskId) {
      logs = logs.filter((log) => log.taskId === options.taskId);
    }

    // 按时间过滤
    if (options?.since) {
      logs = logs.filter((log) => log.timestamp >= options.since!);
    }

    // 限制数量
    if (options?.limit) {
      logs = logs.slice(-options.limit);
    }

    return logs.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 清除日志记录
   */
  clearLogs(workflowId: string, olderThan?: number): void {
    const state = this.getState(workflowId);
    if (!state) return;

    if (olderThan) {
      state.logs = state.logs.filter((log) => log.timestamp >= olderThan);
    } else {
      state.logs = [];
    }

    this.addLog(workflowId, 'info', '日志已清除', { olderThan });
    this.updateState(workflowId, state);
  }

  /**
   * 获取状态统计信息
   */
  getStatistics(): {
    total: number;
    pending: number;
    running: number;
    paused: number;
    completed: number;
    failed: number;
    cancelled: number;
    averageExecutionTime: number;
  } {
    const states = this.getAllStates();
    const stats = {
      total: states.length,
      pending: 0,
      running: 0,
      paused: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      averageExecutionTime: 0,
    };

    let totalExecutionTime = 0;
    let completedCount = 0;

    states.forEach((state) => {
      stats[state.status]++;
      if (state.executionTimeMs && (state.status === 'completed' || state.status === 'failed')) {
        totalExecutionTime += state.executionTimeMs;
        completedCount++;
      }
    });

    if (completedCount > 0) {
      stats.averageExecutionTime = Math.round(totalExecutionTime / completedCount);
    }

    return stats;
  }

  // 私有方法

  private createDefaultTasks(): RiskManagerTask[] {
    return [
      {
        id: 'data_collection',
        name: '风险数据收集',
        description: '收集市场数据、财务数据和流动性数据',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'market_risk_analysis',
        name: '市场风险分析',
        description: '分析波动率、Beta系数和市场相关性',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'liquidity_risk_analysis',
        name: '流动性风险分析',
        description: '评估交易量、价差和市场深度',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'credit_risk_analysis',
        name: '信用风险分析',
        description: '评估财务健康度和偿债能力',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'operational_risk_analysis',
        name: '操作风险分析',
        description: '分析公司治理和管理层风险',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'scenario_analysis',
        name: '情景分析',
        description: '进行压力测试和蒙特卡洛模拟',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'risk_metrics_calculation',
        name: '风险指标计算',
        description: '计算VaR、最大回撤等风险指标',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'control_recommendations',
        name: '风控建议生成',
        description: '生成仓位控制和止损建议',
        status: 'pending',
        progress: 0,
      },
      {
        id: 'report_generation',
        name: '风险报告生成',
        description: '生成结构化的风险评估报告',
        status: 'pending',
        progress: 0,
      },
    ];
  }

  private updateState(workflowId: string, state: RiskManagerState): void {
    this.states.set(workflowId, state);
    this.emit('stateUpdated', state);

    // 发送进度更新事件
    const progressUpdate: RiskManagerProgressUpdate = {
      workflowId: state.workflowId,
      status: state.status,
      progress: state.progress,
      currentTask: state.currentTask,
      executionTimeMs: state.executionTimeMs,
      error: state.error?.message,
      performance: state.performance,
    };

    this.emit('progressUpdate', progressUpdate);
  }

  private updateOverallProgress(state: RiskManagerState): void {
    const totalTasks = state.tasks.length;
    const completedTasks = state.tasks.filter((task) => task.status === 'completed').length;
    const runningTasks = state.tasks.filter((task) => task.status === 'running').length;

    // 计算整体进度：已完成任务 + 运行中任务的平均进度
    let totalProgress = completedTasks * 100;
    state.tasks
      .filter((task) => task.status === 'running')
      .forEach((task) => {
        totalProgress += task.progress;
      });

    state.progress = Math.min(Math.round(totalProgress / totalTasks), 100);
  }

  private getNextPendingTask(state: RiskManagerState): RiskManagerTask | undefined {
    return state.tasks.find((task) => task.status === 'pending');
  }

  private startProgressMonitoring(workflowId: string): void {
    if (!this.config.enablePerformanceMonitoring) return;

    const timer = setInterval(() => {
      const state = this.getState(workflowId);
      if (!state || state.status !== 'running') {
        this.stopProgressMonitoring(workflowId);
        return;
      }

      // 更新执行时间
      if (state.startTime) {
        state.executionTimeMs = Date.now() - state.startTime;
        this.updateState(workflowId, state);
      }
    }, this.config.progressUpdateInterval);

    this.progressTimers.set(workflowId, timer);
  }

  private stopProgressMonitoring(workflowId: string): void {
    const timer = this.progressTimers.get(workflowId);
    if (timer) {
      clearInterval(timer);
      this.progressTimers.delete(workflowId);
    }
  }

  private startExecutionTimeMonitoring(workflowId: string): void {
    if (!this.config.enablePerformanceMonitoring) return;

    // 设置最大执行时间监控
    setTimeout(() => {
      const state = this.getState(workflowId);
      if (state && state.status === 'running') {
        this.failAnalysis(
          workflowId,
          `分析超时：超过最大执行时间 ${this.config.maxExecutionTime}ms`
        );
      }
    }, this.config.maxExecutionTime);
  }
}

// 全局状态管理器实例
export const riskManagerStateManager = new RiskManagerStateManager();

// 导出便捷函数
export function initializeRiskManagerState(
  workflowId: string,
  metadata?: Record<string, any>
): RiskManagerState {
  return riskManagerStateManager.initializeState(workflowId, metadata);
}

export function startRiskAnalysis(workflowId: string): void {
  riskManagerStateManager.startAnalysis(workflowId);
}

export function updateRiskTaskStatus(
  workflowId: string,
  taskId: string,
  status: RiskManagerTask['status'],
  progress?: number,
  result?: any,
  error?: string
): void {
  riskManagerStateManager.updateTaskStatus(workflowId, taskId, status, progress, result, error);
}

export function completeRiskAnalysis(workflowId: string, result?: any): void {
  riskManagerStateManager.completeAnalysis(workflowId, result);
}

export function failRiskAnalysis(workflowId: string, error: Error | string): void {
  riskManagerStateManager.failAnalysis(workflowId, error);
}

export function getRiskManagerState(workflowId: string): RiskManagerState | undefined {
  return riskManagerStateManager.getState(workflowId);
}

export function updateRiskPerformanceMetrics(
  workflowId: string,
  metrics: Partial<RiskManagerState['performance']>
): void {
  riskManagerStateManager.updatePerformanceMetrics(workflowId, metrics);
}

export function pauseRiskAnalysis(workflowId: string): void {
  riskManagerStateManager.pauseAnalysis(workflowId);
}

export function resumeRiskAnalysis(workflowId: string): void {
  riskManagerStateManager.resumeAnalysis(workflowId);
}

export function addRiskManagerLog(
  workflowId: string,
  level: 'debug' | 'info' | 'warn' | 'error',
  message: string,
  context?: Record<string, any>,
  taskId?: string
): void {
  riskManagerStateManager.addLog(workflowId, level, message, context, taskId);
}

export function getRiskManagerLogs(
  workflowId: string,
  options?: {
    level?: 'debug' | 'info' | 'warn' | 'error';
    taskId?: string;
    limit?: number;
    since?: number;
  }
): RiskManagerLog[] {
  return riskManagerStateManager.getLogs(workflowId, options);
}

export function clearRiskManagerLogs(workflowId: string, olderThan?: number): void {
  riskManagerStateManager.clearLogs(workflowId, olderThan);
}
