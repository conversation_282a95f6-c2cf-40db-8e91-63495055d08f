/**
 * Sitemap API 路由
 * 使用 SitemapGenerator 动态生成 XML sitemap
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSitemapGenerator } from '@/lib/seo/sitemap-generator';

/**
 * GET 请求处理器
 * 生成动态 sitemap
 */
export async function GET(request: NextRequest) {
  try {
    const generator = createSitemapGenerator();
    const sitemap = await generator.generateSitemap();

    return new NextResponse(sitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
        'X-Robots-Tag': 'noindex',
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);

    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
}
