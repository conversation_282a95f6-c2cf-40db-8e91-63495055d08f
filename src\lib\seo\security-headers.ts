/**
 * SEO 安全头和 HTTP 头设置
 * 实现 SEO 相关的安全头和优化头设置
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export interface SecurityHeadersConfig {
  enableCSP: boolean;
  enableHSTS: boolean;
  enableXFrameOptions: boolean;
  enableReferrerPolicy: boolean;
  enablePermissionsPolicy: boolean;
  customHeaders?: Record<string, string>;
}

/**
 * SEO 安全头管理器
 */
export class SEOSecurityHeaders {
  private config: SecurityHeadersConfig;

  constructor(config?: Partial<SecurityHeadersConfig>) {
    this.config = {
      enableCSP: true,
      enableHSTS: true,
      enableXFrameOptions: true,
      enableReferrerPolicy: true,
      enablePermissionsPolicy: true,
      ...config,
    };
  }

  /**
   * 应用 SEO 安全头到响应
   */
  applyHeaders(response: NextResponse, request: NextRequest): NextResponse {
    // 基础安全头
    this.applyBasicSecurityHeaders(response);

    // SEO 优化头
    this.applySEOHeaders(response, request);

    // 性能优化头
    this.applyPerformanceHeaders(response);

    // 自定义头
    this.applyCustomHeaders(response);

    return response;
  }

  /**
   * 应用基础安全头
   */
  private applyBasicSecurityHeaders(response: NextResponse): void {
    // Content Security Policy
    if (this.config.enableCSP) {
      const csp = this.generateCSP();
      response.headers.set('Content-Security-Policy', csp);
    }

    // HTTP Strict Transport Security
    if (this.config.enableHSTS) {
      response.headers.set(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains; preload'
      );
    }

    // X-Frame-Options
    if (this.config.enableXFrameOptions) {
      response.headers.set('X-Frame-Options', 'DENY');
    }

    // X-Content-Type-Options
    response.headers.set('X-Content-Type-Options', 'nosniff');

    // Referrer Policy
    if (this.config.enableReferrerPolicy) {
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    }

    // Permissions Policy
    if (this.config.enablePermissionsPolicy) {
      const permissionsPolicy = this.generatePermissionsPolicy();
      response.headers.set('Permissions-Policy', permissionsPolicy);
    }
  }

  /**
   * 应用 SEO 优化头
   */
  private applySEOHeaders(response: NextResponse, request: NextRequest): void {
    // X-Robots-Tag (根据路径动态设置)
    const robotsTag = this.getRobotsTagForPath(request.nextUrl.pathname);
    if (robotsTag) {
      response.headers.set('X-Robots-Tag', robotsTag);
    }

    // 语言相关头
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      response.headers.set('Content-Language', this.detectContentLanguage(acceptLanguage));
    }

    // 缓存控制（SEO 友好）
    if (!response.headers.get('Cache-Control')) {
      response.headers.set('Cache-Control', this.getDefaultCacheControl(request.nextUrl.pathname));
    }
  }

  /**
   * 应用性能优化头
   */
  private applyPerformanceHeaders(response: NextResponse): void {
    // DNS 预取
    response.headers.set('X-DNS-Prefetch-Control', 'on');

    // 预加载关键资源
    const preloadLinks = this.generatePreloadLinks();
    if (preloadLinks.length > 0) {
      response.headers.set('Link', preloadLinks.join(', '));
    }
  }

  /**
   * 应用自定义头
   */
  private applyCustomHeaders(response: NextResponse): void {
    if (this.config.customHeaders) {
      Object.entries(this.config.customHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }
  }

  /**
   * 生成 Content Security Policy
   */
  private generateCSP(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.openai.com https://www.google-analytics.com",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];

    return directives.join('; ');
  }

  /**
   * 生成 Permissions Policy
   */
  private generatePermissionsPolicy(): string {
    const policies = [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'interest-cohort=()',
      'payment=()',
      'usb=()',
    ];

    return policies.join(', ');
  }

  /**
   * 根据路径获取 Robots Tag
   */
  private getRobotsTagForPath(pathname: string): string | null {
    // API 路由不被索引
    if (pathname.startsWith('/api/')) {
      return 'noindex, nofollow';
    }

    // 管理页面不被索引
    if (pathname.startsWith('/admin/') || pathname.startsWith('/debug/')) {
      return 'noindex, nofollow';
    }

    // 登录注册页面
    if (pathname === '/login' || pathname === '/register') {
      return 'noindex, follow';
    }

    // 默认允许索引
    return 'index, follow';
  }

  /**
   * 检测内容语言
   */
  private detectContentLanguage(acceptLanguage: string): string {
    // 简单的语言检测逻辑
    if (acceptLanguage.includes('zh')) {
      return 'zh-CN';
    }
    if (acceptLanguage.includes('en')) {
      return 'en-US';
    }
    return 'zh-CN'; // 默认中文
  }

  /**
   * 获取默认缓存控制
   */
  private getDefaultCacheControl(pathname: string): string {
    // 静态资源长期缓存
    if (pathname.includes('/_next/static/')) {
      return 'public, max-age=31536000, immutable';
    }

    // API 路由短期缓存
    if (pathname.startsWith('/api/')) {
      return 'public, max-age=300, s-maxage=300';
    }

    // 页面中等缓存
    return 'public, max-age=3600, s-maxage=3600';
  }

  /**
   * 生成预加载链接
   */
  private generatePreloadLinks(): string[] {
    const links = [];

    // 预加载关键字体
    links.push('</fonts/inter-var.woff2>; rel=preload; as=font; type=font/woff2; crossorigin');

    // 预加载关键图片
    links.push('</tradingAgent.png>; rel=preload; as=image');

    return links;
  }
}

/**
 * 创建 SEO 安全头管理器
 */
export function createSEOSecurityHeaders(
  config?: Partial<SecurityHeadersConfig>
): SEOSecurityHeaders {
  return new SEOSecurityHeaders(config);
}

/**
 * 中间件辅助函数：应用 SEO 安全头
 */
export function applySEOSecurityHeaders(
  response: NextResponse,
  request: NextRequest,
  config?: Partial<SecurityHeadersConfig>
): NextResponse {
  const securityHeaders = createSEOSecurityHeaders(config);
  return securityHeaders.applyHeaders(response, request);
}
