/**
 * 风险控制建议生成器测试
 * 测试各种风险控制建议功能
 */

import { RiskControlRecommender } from '../risk-control-recommender';
import { PriceData, RiskMetrics } from '../risk-metrics-calculator';
import { StressTestResult } from '../risk-scenario-analyzer';

describe('RiskControlRecommender', () => {
  let recommender: RiskControlRecommender;
  let mockRiskMetrics: RiskMetrics;
  let mockPriceData: PriceData[];

  beforeEach(() => {
    recommender = RiskControlRecommender.getInstance();

    // 模拟风险指标数据
    mockRiskMetrics = {
      volatility: {
        daily_volatility: 0.02,
        weekly_volatility: 0.045,
        monthly_volatility: 0.09,
        annualized_volatility: 0.32,
        volatility_trend: 0.05,
        volatility_percentile: 75,
      },
      var: {
        var_95_1d: 0.035,
        var_99_1d: 0.055,
        var_95_10d: 0.11,
        var_99_10d: 0.17,
        expected_shortfall_95: 0.045,
        expected_shortfall_99: 0.065,
      },
      drawdown: {
        max_drawdown: 0.15,
        max_drawdown_duration: 45,
        current_drawdown: 0.05,
        recovery_time_estimate: 30,
        drawdown_frequency: 2.5,
      },
      ratios: {
        sharpe_ratio: 0.8,
        sortino_ratio: 1.1,
        calmar_ratio: 0.6,
        information_ratio: 0.4,
        treynor_ratio: 0.12,
        beta: 1.2,
      },
      liquidity: {
        amihud_ratio: 0.0001,
        turnover_rate: 0.15,
        price_impact: 0.1,
        bid_ask_spread: 0.002,
        market_depth_score: 75,
      },
      correlation: {
        market_correlation: 0.75,
        sector_correlation: 0.85,
        correlation_stability: 0.8,
        diversification_ratio: 0.9,
      },
    };

    // 模拟价格数据
    mockPriceData = [];
    let price = 100;
    for (let i = 0; i < 100; i++) {
      const change = (Math.random() - 0.5) * 0.04; // ±2%随机变化
      price = price * (1 + change);
      mockPriceData.push({
        date: new Date(Date.now() - (100 - i) * 24 * 60 * 60 * 1000).toISOString(),
        open: price * 0.999,
        high: price * 1.01,
        low: price * 0.99,
        close: price,
        volume: 1000000 + Math.random() * 500000,
      });
    }
  });

  describe('generatePositionRecommendation', () => {
    it('应该基于风险指标生成仓位建议', () => {
      const recommendation = recommender.generatePositionRecommendation(
        mockRiskMetrics,
        0.5, // 当前仓位50%
        0.02, // 2% VaR限制
        1000000 // 100万组合价值
      );

      expect(recommendation).toBeDefined();
      expect(recommendation.recommended_position_size).toBeGreaterThanOrEqual(0);
      expect(recommendation.recommended_position_size).toBeLessThanOrEqual(1);
      expect(['increase', 'decrease', 'maintain', 'close']).toContain(
        recommendation.position_adjustment
      );
      expect(recommendation.adjustment_reason).toBeTruthy();
      expect(recommendation.risk_budget_utilization).toBeGreaterThanOrEqual(0);
      expect(recommendation.confidence_level).toBeGreaterThan(0);
      expect(recommendation.confidence_level).toBeLessThanOrEqual(1);
    });

    it('应该在高风险情况下建议减仓', () => {
      const highRiskMetrics = {
        ...mockRiskMetrics,
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0.08, // 8% VaR，非常高
        },
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.6, // 60%年化波动率
        },
      };

      const recommendation = recommender.generatePositionRecommendation(
        highRiskMetrics,
        0.8, // 当前仓位80%
        0.02
      );

      expect(recommendation.position_adjustment).toBe('decrease');
      expect(recommendation.recommended_position_size).toBeLessThan(0.5);
    });

    it('应该在极高风险情况下建议清仓', () => {
      const extremeRiskMetrics = {
        ...mockRiskMetrics,
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0.15, // 15% VaR，极高
        },
        drawdown: {
          ...mockRiskMetrics.drawdown,
          max_drawdown: 0.4, // 40%最大回撤
        },
      };

      const recommendation = recommender.generatePositionRecommendation(
        extremeRiskMetrics,
        0.5,
        0.02
      );

      expect(recommendation.position_adjustment).toBe('close');
      expect(recommendation.recommended_position_size).toBeLessThan(0.05);
    });
  });

  describe('generateStopLossRecommendation', () => {
    it('应该生成合理的止损建议', () => {
      const currentPrice = mockPriceData[mockPriceData.length - 1].close;
      const recommendation = recommender.generateStopLossRecommendation(
        mockPriceData,
        mockRiskMetrics,
        currentPrice,
        0.5, // 50%仓位
        0.05 // 5%最大损失
      );

      expect(recommendation).toBeDefined();
      expect(recommendation.stop_loss_price).toBeLessThan(currentPrice);
      expect(recommendation.stop_loss_percentage).toBeGreaterThan(0);
      expect(recommendation.stop_loss_percentage).toBeLessThanOrEqual(0.05);
      expect(['fixed', 'trailing', 'volatility_based', 'technical']).toContain(
        recommendation.stop_loss_type
      );
      expect(recommendation.risk_reward_ratio).toBeGreaterThanOrEqual(0);
      expect(['daily', 'weekly', 'monthly']).toContain(recommendation.adjustment_frequency);
    });

    it('应该在高波动率情况下使用基于波动率的止损', () => {
      const highVolatilityMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          daily_volatility: 0.04, // 4%日波动率
          volatility_trend: 0.2,
        },
      };

      const currentPrice = mockPriceData[mockPriceData.length - 1].close;
      const recommendation = recommender.generateStopLossRecommendation(
        mockPriceData,
        highVolatilityMetrics,
        currentPrice,
        0.5
      );

      expect(recommendation.stop_loss_type).toBe('volatility_based');
      expect(recommendation.adjustment_frequency).toBe('daily');
    });
  });

  describe('generateTimeHorizonRecommendation', () => {
    it('应该基于风险指标生成时间窗口建议', () => {
      const marketConditions = {
        trend: 'bullish' as const,
        volatility_regime: 'medium' as const,
        market_cycle: 'mid' as const,
      };

      const recommendation = recommender.generateTimeHorizonRecommendation(
        mockRiskMetrics,
        marketConditions
      );

      expect(recommendation).toBeDefined();
      expect(recommendation.recommended_holding_period).toBeGreaterThan(0);
      expect(recommendation.min_holding_period).toBeLessThanOrEqual(
        recommendation.recommended_holding_period
      );
      expect(recommendation.max_holding_period).toBeGreaterThanOrEqual(
        recommendation.recommended_holding_period
      );
      expect(['immediate', 'wait', 'gradual']).toContain(recommendation.optimal_entry_timing);
      expect(['profit_target', 'time_based', 'risk_based', 'technical']).toContain(
        recommendation.exit_strategy
      );
      expect(recommendation.market_timing_score).toBeGreaterThanOrEqual(0);
      expect(recommendation.market_timing_score).toBeLessThanOrEqual(100);
    });

    it('应该在高波动率情况下建议较短持有期', () => {
      const highVolatilityMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.5, // 50%年化波动率
        },
      };

      const marketConditions = {
        trend: 'sideways' as const,
        volatility_regime: 'high' as const,
        market_cycle: 'late' as const,
      };

      const recommendation = recommender.generateTimeHorizonRecommendation(
        highVolatilityMetrics,
        marketConditions
      );

      expect(recommendation.recommended_holding_period).toBeLessThan(90); // 少于3个月
      expect(recommendation.optimal_entry_timing).toBe('wait');
    });
  });

  describe('generateHedgingRecommendation', () => {
    it('应该在高风险情况下建议对冲', () => {
      const highRiskMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.4, // 40%波动率
        },
        ratios: {
          ...mockRiskMetrics.ratios,
          beta: 1.5, // 高Beta
        },
      };

      const recommendation = recommender.generateHedgingRecommendation(
        highRiskMetrics,
        1000000, // 100万组合
        0.8 // 80%市场相关性
      );

      expect(recommendation.hedging_required).toBe(true);
      expect(recommendation.hedging_strategies.length).toBeGreaterThan(0);
      expect(recommendation.portfolio_hedge_ratio).toBeGreaterThan(0);

      // 检查对冲策略类型
      const strategyTypes = recommendation.hedging_strategies.map((s) => s.strategy_type);
      expect(strategyTypes).toContain('options');
    });

    it('应该在低风险情况下不建议对冲', () => {
      const lowRiskMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.15, // 15%波动率
        },
        ratios: {
          ...mockRiskMetrics.ratios,
          beta: 0.8, // 低Beta
        },
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0.02, // 2% VaR
        },
      };

      const recommendation = recommender.generateHedgingRecommendation(
        lowRiskMetrics,
        1000000,
        0.5
      );

      expect(recommendation.hedging_required).toBe(false);
      expect(recommendation.hedging_strategies.length).toBe(0);
    });
  });

  describe('calculateRiskAdjustedReturn', () => {
    it('应该计算风险调整后收益', () => {
      const expectedReturn = 0.12; // 12%预期收益
      const riskFreeRate = 0.03; // 3%无风险利率

      const result = recommender.calculateRiskAdjustedReturn(
        mockRiskMetrics,
        expectedReturn,
        riskFreeRate
      );

      expect(result).toBeDefined();
      expect(result.expected_return).toBe(expectedReturn);
      expect(result.risk_adjusted_return).toBeLessThan(expectedReturn);
      expect(result.risk_premium).toBe(expectedReturn - riskFreeRate);
      expect(result.probability_of_profit).toBeGreaterThan(0);
      expect(result.probability_of_profit).toBeLessThanOrEqual(1);
      expect(result.expected_maximum_loss).toBeGreaterThan(0);
    });

    it('应该在高风险情况下大幅调整收益', () => {
      const highRiskMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.6, // 60%波动率
        },
        drawdown: {
          ...mockRiskMetrics.drawdown,
          max_drawdown: 0.3, // 30%最大回撤
        },
      };

      const result = recommender.calculateRiskAdjustedReturn(
        highRiskMetrics,
        0.15, // 15%预期收益
        0.03
      );

      expect(result.risk_adjusted_return).toBeLessThan(0.1); // 大幅调整
      expect(result.expected_maximum_loss).toBeGreaterThan(0.2);
    });
  });

  describe('generateComprehensiveRecommendation', () => {
    it('应该生成完整的风险控制建议', () => {
      const mockStressTest: StressTestResult = {
        test_name: '综合压力测试',
        scenarios: [],
        summary: {
          worst_case_loss: 0.25,
          average_loss: 0.12,
          probability_of_loss: 0.3,
          stress_test_score: 45,
        },
        recommendations: ['建议降低仓位'],
      };

      const recommendation = recommender.generateComprehensiveRecommendation(
        mockRiskMetrics,
        mockPriceData,
        mockStressTest,
        0.6, // 当前仓位60%
        1000000, // 100万组合
        0.02 // 2%风险容忍度
      );

      expect(recommendation).toBeDefined();
      expect(['low', 'medium', 'high', 'extreme']).toContain(recommendation.overall_risk_level);
      expect(recommendation.risk_score).toBeGreaterThanOrEqual(0);
      expect(recommendation.risk_score).toBeLessThanOrEqual(100);
      expect(['buy', 'hold', 'reduce', 'sell', 'avoid']).toContain(
        recommendation.investment_recommendation
      );

      // 检查各项建议是否存在
      expect(recommendation.position_recommendation).toBeDefined();
      expect(recommendation.stop_loss_recommendation).toBeDefined();
      expect(recommendation.time_horizon_recommendation).toBeDefined();
      expect(recommendation.hedging_recommendation).toBeDefined();
      expect(recommendation.risk_adjusted_return).toBeDefined();
      expect(recommendation.diversification_recommendation).toBeDefined();
      expect(recommendation.monitoring_indicators).toBeDefined();

      expect(recommendation.summary_recommendations.length).toBeGreaterThan(0);
      expect(recommendation.implementation_priority.length).toBeGreaterThan(0);
    });

    it('应该在极高风险情况下建议避免投资', () => {
      const extremeRiskMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.8, // 80%波动率
        },
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0.12, // 12% VaR
        },
        drawdown: {
          ...mockRiskMetrics.drawdown,
          max_drawdown: 0.5, // 50%最大回撤
        },
      };

      const recommendation = recommender.generateComprehensiveRecommendation(
        extremeRiskMetrics,
        mockPriceData,
        undefined,
        0.5
      );

      expect(recommendation.overall_risk_level).toBe('extreme');
      expect(recommendation.investment_recommendation).toBe('avoid');
      expect(recommendation.position_recommendation.position_adjustment).toBe('close');
    });

    it('应该在低风险情况下建议正常投资', () => {
      const lowRiskMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0.12, // 12%波动率
        },
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0.015, // 1.5% VaR
        },
        drawdown: {
          ...mockRiskMetrics.drawdown,
          max_drawdown: 0.08, // 8%最大回撤
        },
        ratios: {
          ...mockRiskMetrics.ratios,
          sharpe_ratio: 1.5, // 高夏普比率
        },
      };

      const recommendation = recommender.generateComprehensiveRecommendation(
        lowRiskMetrics,
        mockPriceData,
        undefined,
        0.3
      );

      expect(recommendation.overall_risk_level).toBe('low');
      expect(['buy', 'hold']).toContain(recommendation.investment_recommendation);
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空价格数据', () => {
      expect(() => {
        recommender.generateStopLossRecommendation([], mockRiskMetrics, 100, 0.5);
      }).not.toThrow();
    });

    it('应该处理极端风险指标', () => {
      const extremeMetrics = {
        ...mockRiskMetrics,
        volatility: {
          ...mockRiskMetrics.volatility,
          annualized_volatility: 0, // 零波动率
        },
        var: {
          ...mockRiskMetrics.var,
          var_95_1d: 0, // 零VaR
        },
      };

      const recommendation = recommender.generatePositionRecommendation(extremeMetrics, 0.5, 0.02);

      expect(recommendation).toBeDefined();
      expect(recommendation.recommended_position_size).toBeGreaterThanOrEqual(0);
    });

    it('应该处理负的夏普比率', () => {
      const negativeSharpMetrics = {
        ...mockRiskMetrics,
        ratios: {
          ...mockRiskMetrics.ratios,
          sharpe_ratio: -0.5, // 负夏普比率
        },
      };

      const result = recommender.calculateRiskAdjustedReturn(negativeSharpMetrics, 0.08, 0.03);

      expect(result).toBeDefined();
      expect(result.risk_adjusted_return).toBeLessThan(0.08);
    });
  });

  describe('单例模式测试', () => {
    it('应该返回同一个实例', () => {
      const instance1 = RiskControlRecommender.getInstance();
      const instance2 = RiskControlRecommender.getInstance();

      expect(instance1).toBe(instance2);
    });
  });
});
