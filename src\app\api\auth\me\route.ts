/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-19 16:30:27
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 20:47:43
 * @FilePath: \trading-agents-frontend\src\app\api\auth\me\route.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { error, success } from '@/lib/api-helpers';
import { getCurrentUser } from '@/lib/auth';
import { getUserById } from '@/lib/user-db';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    if (!currentUser) {
      return error('未登录', 401);
    }

    // 获取完整用户信息
    const user = await getUserById(currentUser.userId);

    if (!user) {
      return error('用户不存在', 404);
    }

    // 返回用户信息（不包含敏感信息）
    return success({
      id: user.id,
      email: user.email,
      username: user.username,
      email_verified: user.email_verified,
      avatar_url: user.avatar_url,
      role: user.role,
      created_at: user.created_at,
      last_login_at: user.last_login_at,
    });
  } catch (err) {
    console.error('Get user info error:', err);
    return error('服务器内部错误', 500);
  }
}
