/**
 * React Hook for Risk Manager State Management
 * 提供风险管理师状态的实时监控和管理功能
 */

import {
  RiskManagerLog,
  RiskManagerProgressUpdate,
  RiskManagerState,
  RiskManagerTask,
  riskManagerStateManager,
} from '@/lib/risk-manager-state';
import { useCallback, useEffect, useMemo, useState } from 'react';

export interface UseRiskManagerStateOptions {
  workflowId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onProgressUpdate?: (update: RiskManagerProgressUpdate) => void;
  onStateChange?: (state: RiskManagerState) => void;
  onError?: (error: Error) => void;
}

export interface UseRiskManagerStateReturn {
  // 状态数据
  state: RiskManagerState | null;
  isLoading: boolean;
  error: string | null;

  // 状态检查
  isPending: boolean;
  isRunning: boolean;
  isPaused: boolean;
  isCompleted: boolean;
  isFailed: boolean;
  isCancelled: boolean;

  // 进度信息
  progress: number;
  currentTask: string;
  executionTimeMs: number;
  estimatedTimeRemaining: number;

  // 任务信息
  tasks: RiskManagerTask[];
  completedTasks: number;
  totalTasks: number;
  currentRunningTask: RiskManagerTask | null;

  // 性能指标
  performance: RiskManagerState['performance'];

  // 操作方法
  refresh: () => void;
  startAnalysis: () => Promise<void>;
  pauseAnalysis: () => Promise<void>;
  resumeAnalysis: () => Promise<void>;
  cancelAnalysis: () => Promise<void>;
  retryAnalysis: () => Promise<void>;
  clearError: () => void;

  // 日志管理
  logs: RiskManagerLog[];
  addLog: (
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    context?: Record<string, any>,
    taskId?: string
  ) => void;
  getLogs: (options?: {
    level?: 'debug' | 'info' | 'warn' | 'error';
    taskId?: string;
    limit?: number;
    since?: number;
  }) => RiskManagerLog[];
  clearLogs: (olderThan?: number) => void;

  // 任务操作
  updateTaskStatus: (
    taskId: string,
    status: RiskManagerTask['status'],
    progress?: number,
    result?: any,
    error?: string
  ) => void;
}

export function useRiskManagerState(
  options: UseRiskManagerStateOptions
): UseRiskManagerStateReturn {
  const {
    workflowId,
    autoRefresh = true,
    refreshInterval = 1000,
    onProgressUpdate,
    onStateChange,
    onError,
  } = options;

  const [state, setState] = useState<RiskManagerState | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 刷新状态
  const refresh = useCallback(() => {
    try {
      const currentState = riskManagerStateManager.getState(workflowId);
      setState(currentState || null);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取风险管理师状态失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [workflowId, onError]);

  // 开始分析
  const startAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 如果状态不存在，先初始化
      if (!state) {
        riskManagerStateManager.initializeState(workflowId);
      }

      riskManagerStateManager.startAnalysis(workflowId);
      refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动风险分析失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    } finally {
      setIsLoading(false);
    }
  }, [workflowId, state, refresh, onError]);

  // 暂停分析
  const pauseAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      riskManagerStateManager.pauseAnalysis(workflowId);
      refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '暂停风险分析失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    } finally {
      setIsLoading(false);
    }
  }, [workflowId, refresh, onError]);

  // 恢复分析
  const resumeAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      riskManagerStateManager.resumeAnalysis(workflowId);
      refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '恢复风险分析失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    } finally {
      setIsLoading(false);
    }
  }, [workflowId, refresh, onError]);

  // 取消分析
  const cancelAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      riskManagerStateManager.cancelAnalysis(workflowId);
      refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '取消风险分析失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    } finally {
      setIsLoading(false);
    }
  }, [workflowId, refresh, onError]);

  // 重试分析
  const retryAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // 重新初始化状态
      riskManagerStateManager.initializeState(workflowId);
      riskManagerStateManager.startAnalysis(workflowId);
      refresh();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '重试风险分析失败';
      setError(errorMessage);
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    } finally {
      setIsLoading(false);
    }
  }, [workflowId, refresh, onError]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 更新任务状态
  const updateTaskStatus = useCallback(
    (
      taskId: string,
      status: RiskManagerTask['status'],
      progress?: number,
      result?: any,
      error?: string
    ) => {
      try {
        riskManagerStateManager.updateTaskStatus(
          workflowId,
          taskId,
          status,
          progress,
          result,
          error
        );
        refresh();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '更新任务状态失败';
        setError(errorMessage);
        if (onError) {
          onError(err instanceof Error ? err : new Error(errorMessage));
        }
      }
    },
    [workflowId, refresh, onError]
  );

  // 日志管理
  const addLog = useCallback(
    (
      level: 'debug' | 'info' | 'warn' | 'error',
      message: string,
      context?: Record<string, any>,
      taskId?: string
    ) => {
      try {
        riskManagerStateManager.addLog(workflowId, level, message, context, taskId);
        refresh();
      } catch (err) {
        console.error('Failed to add log:', err);
      }
    },
    [workflowId, refresh]
  );

  const getLogs = useCallback(
    (options?: {
      level?: 'debug' | 'info' | 'warn' | 'error';
      taskId?: string;
      limit?: number;
      since?: number;
    }) => {
      return riskManagerStateManager.getLogs(workflowId, options);
    },
    [workflowId]
  );

  const clearLogs = useCallback(
    (olderThan?: number) => {
      try {
        riskManagerStateManager.clearLogs(workflowId, olderThan);
        refresh();
      } catch (err) {
        console.error('Failed to clear logs:', err);
      }
    },
    [workflowId, refresh]
  );

  // 设置事件监听器
  useEffect(() => {
    const handleStateUpdated = (updatedState: RiskManagerState) => {
      if (updatedState.workflowId === workflowId) {
        setState(updatedState);
        if (onStateChange) {
          onStateChange(updatedState);
        }
      }
    };

    const handleProgressUpdate = (update: RiskManagerProgressUpdate) => {
      if (update.workflowId === workflowId) {
        if (onProgressUpdate) {
          onProgressUpdate(update);
        }
      }
    };

    riskManagerStateManager.on('stateUpdated', handleStateUpdated);
    riskManagerStateManager.on('progressUpdate', handleProgressUpdate);

    return () => {
      riskManagerStateManager.off('stateUpdated', handleStateUpdated);
      riskManagerStateManager.off('progressUpdate', handleProgressUpdate);
    };
  }, [workflowId, onStateChange, onProgressUpdate]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    // 立即刷新一次
    refresh();

    // 设置定时刷新
    const interval = setInterval(() => {
      // 只有在运行状态时才继续刷新
      if (state?.status === 'running') {
        refresh();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refresh, state?.status]);

  // 计算派生状态
  const isPending = state?.status === 'pending';
  const isRunning = state?.status === 'running';
  const isPaused = state?.status === 'paused';
  const isCompleted = state?.status === 'completed';
  const isFailed = state?.status === 'failed';
  const isCancelled = state?.status === 'cancelled';

  const progress = state?.progress || 0;
  const currentTask = state?.currentTask || '';
  const executionTimeMs = state?.executionTimeMs || 0;

  const tasks = state?.tasks || [];
  const completedTasks = tasks.filter((task) => task.status === 'completed').length;
  const totalTasks = tasks.length;
  const currentRunningTask = tasks.find((task) => task.status === 'running') || null;

  const performance = state?.performance || {};
  const logs = state?.logs || [];

  // 估算剩余时间
  const estimatedTimeRemaining = useMemo(() => {
    if (!state || (!isRunning && !isPaused) || progress === 0) return 0;

    const elapsedTime = executionTimeMs;
    const remainingProgress = 100 - progress;
    const timePerPercent = elapsedTime / progress;

    return Math.round(remainingProgress * timePerPercent);
  }, [state, isRunning, isPaused, progress, executionTimeMs]);

  return {
    // 状态数据
    state,
    isLoading,
    error,

    // 状态检查
    isPending,
    isRunning,
    isPaused,
    isCompleted,
    isFailed,
    isCancelled,

    // 进度信息
    progress,
    currentTask,
    executionTimeMs,
    estimatedTimeRemaining,

    // 任务信息
    tasks,
    completedTasks,
    totalTasks,
    currentRunningTask,

    // 性能指标
    performance,

    // 操作方法
    refresh,
    startAnalysis,
    pauseAnalysis,
    resumeAnalysis,
    cancelAnalysis,
    retryAnalysis,
    clearError,

    // 任务操作
    updateTaskStatus,

    // 日志管理
    logs,
    addLog,
    getLogs,
    clearLogs,
  };
}

// 便捷的 Hook 变体
export function useRiskManagerProgress(workflowId: string) {
  const { progress, currentTask, executionTimeMs, estimatedTimeRemaining, isRunning } =
    useRiskManagerState({
      workflowId,
      autoRefresh: true,
      refreshInterval: 500, // 更频繁的更新
    });

  return {
    progress,
    currentTask,
    executionTimeMs,
    estimatedTimeRemaining,
    isRunning,
  };
}

export function useRiskManagerTasks(workflowId: string) {
  const { tasks, completedTasks, totalTasks, currentRunningTask, updateTaskStatus } =
    useRiskManagerState({
      workflowId,
      autoRefresh: true,
    });

  return {
    tasks,
    completedTasks,
    totalTasks,
    currentRunningTask,
    updateTaskStatus,
  };
}

export function useRiskManagerPerformance(workflowId: string) {
  const { performance, executionTimeMs, isCompleted, isFailed } = useRiskManagerState({
    workflowId,
    autoRefresh: false, // 性能数据不需要频繁更新
  });

  return {
    performance,
    executionTimeMs,
    isCompleted,
    isFailed,
  };
}
