/**
 * 内容 SEO 优化工具
 * 提供关键词优化、内容分析和 SEO 友好性检查功能
 */

export interface ContentSEOConfig {
  // 目标关键词
  primaryKeywords: string[];
  // 次要关键词
  secondaryKeywords: string[];
  // 长尾关键词
  longTailKeywords: string[];
  // 目标语言
  language: 'zh' | 'en';
  // 内容类型
  contentType: 'homepage' | 'product' | 'blog' | 'landing' | 'analysis';
  // 目标受众
  targetAudience: 'investors' | 'traders' | 'analysts' | 'general';
}

export interface ContentAnalysisResult {
  // 内容分数 (0-100)
  score: number;
  // 关键词密度
  keywordDensity: Record<string, number>;
  // 内容长度
  contentLength: number;
  // 可读性分数
  readabilityScore: number;
  // SEO 问题
  issues: ContentIssue[];
  // 改进建议
  suggestions: ContentSuggestion[];
  // 关键词分析
  keywordAnalysis: KeywordAnalysis;
}

export interface ContentIssue {
  type: 'keyword_density' | 'content_length' | 'readability' | 'structure' | 'duplication';
  severity: 'high' | 'medium' | 'low';
  message: string;
  element?: string;
}

export interface ContentSuggestion {
  type:
    | 'keyword_optimization'
    | 'content_expansion'
    | 'structure_improvement'
    | 'readability_enhancement';
  message: string;
  improvement: string;
  priority: 'high' | 'medium' | 'low';
}

export interface KeywordAnalysis {
  // 关键词出现次数
  occurrences: Record<string, number>;
  // 关键词密度
  density: Record<string, number>;
  // 关键词位置分析
  positions: Record<string, KeywordPosition[]>;
  // 缺失的关键词
  missingKeywords: string[];
  // 过度使用的关键词
  overusedKeywords: string[];
}

export interface KeywordPosition {
  element: 'title' | 'heading' | 'paragraph' | 'meta' | 'alt';
  position: number;
  context: string;
}

export class ContentOptimizer {
  private config: ContentSEOConfig;

  constructor(config: ContentSEOConfig) {
    this.config = config;
  }

  /**
   * 分析内容 SEO
   */
  analyzeContent(
    content: string,
    metadata?: {
      title?: string;
      description?: string;
      headings?: string[];
      altTexts?: string[];
    }
  ): ContentAnalysisResult {
    const issues: ContentIssue[] = [];
    const suggestions: ContentSuggestion[] = [];

    // 分析关键词
    const keywordAnalysis = this.analyzeKeywords(content, metadata);

    // 分析内容长度
    const contentLength = this.getContentLength(content);
    const lengthAnalysis = this.analyzeContentLength(contentLength);
    issues.push(...lengthAnalysis.issues);
    suggestions.push(...lengthAnalysis.suggestions);

    // 分析可读性
    const readabilityScore = this.calculateReadabilityScore(content);
    const readabilityAnalysis = this.analyzeReadability(readabilityScore);
    issues.push(...readabilityAnalysis.issues);
    suggestions.push(...readabilityAnalysis.suggestions);

    // 分析关键词密度
    const densityAnalysis = this.analyzeKeywordDensity(keywordAnalysis);
    issues.push(...densityAnalysis.issues);
    suggestions.push(...densityAnalysis.suggestions);

    // 分析内容结构
    const structureAnalysis = this.analyzeContentStructure(content, metadata);
    issues.push(...structureAnalysis.issues);
    suggestions.push(...structureAnalysis.suggestions);

    // 计算总分
    const score = this.calculateContentScore(issues, keywordAnalysis, readabilityScore);

    return {
      score,
      keywordDensity: keywordAnalysis.density,
      contentLength,
      readabilityScore,
      issues,
      suggestions,
      keywordAnalysis,
    };
  }

  /**
   * 优化内容
   */
  optimizeContent(
    content: string,
    targetKeywords?: string[]
  ): {
    optimizedContent: string;
    changes: string[];
    improvements: string[];
  } {
    const changes: string[] = [];
    const improvements: string[] = [];
    let optimizedContent = content;

    const keywords = targetKeywords || this.config.primaryKeywords;

    // 优化关键词分布
    const keywordOptimization = this.optimizeKeywordDistribution(optimizedContent, keywords);
    optimizedContent = keywordOptimization.content;
    changes.push(...keywordOptimization.changes);
    improvements.push(...keywordOptimization.improvements);

    // 优化句子结构
    const structureOptimization = this.optimizeSentenceStructure(optimizedContent);
    optimizedContent = structureOptimization.content;
    changes.push(...structureOptimization.changes);
    improvements.push(...structureOptimization.improvements);

    // 添加语义相关词汇
    const semanticOptimization = this.addSemanticKeywords(optimizedContent, keywords);
    optimizedContent = semanticOptimization.content;
    changes.push(...semanticOptimization.changes);
    improvements.push(...semanticOptimization.improvements);

    return {
      optimizedContent,
      changes,
      improvements,
    };
  }

  /**
   * 分析关键词
   */
  private analyzeKeywords(
    content: string,
    metadata?: {
      title?: string;
      description?: string;
      headings?: string[];
      altTexts?: string[];
    }
  ): KeywordAnalysis {
    const allKeywords = [
      ...this.config.primaryKeywords,
      ...this.config.secondaryKeywords,
      ...this.config.longTailKeywords,
    ];

    const occurrences: Record<string, number> = {};
    const density: Record<string, number> = {};
    const positions: Record<string, KeywordPosition[]> = {};
    const missingKeywords: string[] = [];
    const overusedKeywords: string[] = [];

    const totalWords = this.getWordCount(content);
    const fullText = [
      content,
      metadata?.title || '',
      metadata?.description || '',
      ...(metadata?.headings || []),
      ...(metadata?.altTexts || []),
    ].join(' ');

    allKeywords.forEach((keyword) => {
      const regex = new RegExp(keyword, 'gi');
      const matches = fullText.match(regex) || [];
      const count = matches.length;

      occurrences[keyword] = count;
      density[keyword] = totalWords > 0 ? (count / totalWords) * 100 : 0;
      positions[keyword] = this.findKeywordPositions(keyword, content, metadata);

      // 检查关键词使用情况
      if (count === 0) {
        missingKeywords.push(keyword);
      } else if (density[keyword] > 3) {
        // 密度超过 3% 认为过度使用
        overusedKeywords.push(keyword);
      }
    });

    return {
      occurrences,
      density,
      positions,
      missingKeywords,
      overusedKeywords,
    };
  }

  /**
   * 查找关键词位置
   */
  private findKeywordPositions(
    keyword: string,
    content: string,
    metadata?: {
      title?: string;
      description?: string;
      headings?: string[];
      altTexts?: string[];
    }
  ): KeywordPosition[] {
    const positions: KeywordPosition[] = [];
    const regex = new RegExp(keyword, 'gi');

    // 在标题中查找
    if (metadata?.title) {
      const matches = metadata.title.matchAll(regex);
      for (const match of Array.from(matches)) {
        positions.push({
          element: 'title',
          position: match.index || 0,
          context: metadata.title,
        });
      }
    }

    // 在描述中查找
    if (metadata?.description) {
      const matches = metadata.description.matchAll(regex);
      for (const match of Array.from(matches)) {
        positions.push({
          element: 'meta',
          position: match.index || 0,
          context: metadata.description,
        });
      }
    }

    // 在标题中查找
    metadata?.headings?.forEach((heading) => {
      const matches = heading.matchAll(regex);
      for (const match of Array.from(matches)) {
        positions.push({
          element: 'heading',
          position: match.index || 0,
          context: heading,
        });
      }
    });

    // 在内容中查找
    const contentMatches = content.matchAll(regex);
    for (const match of Array.from(contentMatches)) {
      const start = Math.max(0, (match.index || 0) - 50);
      const end = Math.min(content.length, (match.index || 0) + 50);
      positions.push({
        element: 'paragraph',
        position: match.index || 0,
        context: content.substring(start, end),
      });
    }

    return positions;
  }

  /**
   * 分析内容长度
   */
  private analyzeContentLength(length: number): {
    issues: ContentIssue[];
    suggestions: ContentSuggestion[];
  } {
    const issues: ContentIssue[] = [];
    const suggestions: ContentSuggestion[] = [];

    const minLength = this.getMinContentLength();
    const maxLength = this.getMaxContentLength();

    if (length < minLength) {
      issues.push({
        type: 'content_length',
        severity: 'high',
        message: `内容长度过短 (${length} 字符)，建议至少 ${minLength} 字符`,
      });
      suggestions.push({
        type: 'content_expansion',
        message: '扩展内容以提供更多价值',
        improvement: `添加更多详细信息、示例或解释，目标长度 ${minLength}-${maxLength} 字符`,
        priority: 'high',
      });
    } else if (length > maxLength) {
      issues.push({
        type: 'content_length',
        severity: 'medium',
        message: `内容长度过长 (${length} 字符)，可能影响用户体验`,
      });
      suggestions.push({
        type: 'content_expansion',
        message: '考虑分割内容或精简表达',
        improvement: '将长内容分成多个部分或删除冗余信息',
        priority: 'medium',
      });
    }

    return { issues, suggestions };
  }

  /**
   * 分析可读性
   */
  private analyzeReadability(score: number): {
    issues: ContentIssue[];
    suggestions: ContentSuggestion[];
  } {
    const issues: ContentIssue[] = [];
    const suggestions: ContentSuggestion[] = [];

    if (score < 60) {
      issues.push({
        type: 'readability',
        severity: 'high',
        message: `可读性分数过低 (${score})，内容可能难以理解`,
      });
      suggestions.push({
        type: 'readability_enhancement',
        message: '提高内容可读性',
        improvement: '使用更简单的句子结构、常用词汇，添加段落分隔',
        priority: 'high',
      });
    } else if (score < 80) {
      suggestions.push({
        type: 'readability_enhancement',
        message: '可读性有提升空间',
        improvement: '优化句子长度和词汇选择',
        priority: 'medium',
      });
    }

    return { issues, suggestions };
  }

  /**
   * 分析关键词密度
   */
  private analyzeKeywordDensity(keywordAnalysis: KeywordAnalysis): {
    issues: ContentIssue[];
    suggestions: ContentSuggestion[];
  } {
    const issues: ContentIssue[] = [];
    const suggestions: ContentSuggestion[] = [];

    // 检查缺失的主要关键词
    const missingPrimary = this.config.primaryKeywords.filter((keyword) =>
      keywordAnalysis.missingKeywords.includes(keyword)
    );

    if (missingPrimary.length > 0) {
      issues.push({
        type: 'keyword_density',
        severity: 'high',
        message: `缺少主要关键词: ${missingPrimary.join(', ')}`,
      });
      suggestions.push({
        type: 'keyword_optimization',
        message: '添加主要关键词',
        improvement: `在内容中自然地融入关键词: ${missingPrimary.join(', ')}`,
        priority: 'high',
      });
    }

    // 检查过度使用的关键词
    if (keywordAnalysis.overusedKeywords.length > 0) {
      issues.push({
        type: 'keyword_density',
        severity: 'medium',
        message: `关键词使用过度: ${keywordAnalysis.overusedKeywords.join(', ')}`,
      });
      suggestions.push({
        type: 'keyword_optimization',
        message: '减少关键词密度',
        improvement: '使用同义词或相关词汇替换部分重复的关键词',
        priority: 'medium',
      });
    }

    return { issues, suggestions };
  }

  /**
   * 分析内容结构
   */
  private analyzeContentStructure(
    content: string,
    metadata?: {
      title?: string;
      description?: string;
      headings?: string[];
    }
  ): {
    issues: ContentIssue[];
    suggestions: ContentSuggestion[];
  } {
    const issues: ContentIssue[] = [];
    const suggestions: ContentSuggestion[] = [];

    // 检查段落结构
    const paragraphs = content.split('\n\n').filter((p) => p.trim().length > 0);
    const avgParagraphLength = paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length;

    if (avgParagraphLength > 500) {
      issues.push({
        type: 'structure',
        severity: 'medium',
        message: '段落过长，可能影响可读性',
      });
      suggestions.push({
        type: 'structure_improvement',
        message: '优化段落结构',
        improvement: '将长段落分解为更短的段落，每段专注一个主题',
        priority: 'medium',
      });
    }

    // 检查标题中的关键词
    if (metadata?.headings) {
      const headingsWithKeywords = metadata.headings.filter((heading) =>
        this.config.primaryKeywords.some((keyword) =>
          heading.toLowerCase().includes(keyword.toLowerCase())
        )
      );

      if (headingsWithKeywords.length === 0) {
        suggestions.push({
          type: 'structure_improvement',
          message: '在标题中使用关键词',
          improvement: '在部分标题中自然地融入主要关键词',
          priority: 'medium',
        });
      }
    }

    return { issues, suggestions };
  }

  /**
   * 优化关键词分布
   */
  private optimizeKeywordDistribution(
    content: string,
    keywords: string[]
  ): {
    content: string;
    changes: string[];
    improvements: string[];
  } {
    const changes: string[] = [];
    const improvements: string[] = [];
    let optimizedContent = content;

    // 这里可以实现更复杂的关键词分布优化逻辑
    // 目前返回原内容
    return {
      content: optimizedContent,
      changes,
      improvements,
    };
  }

  /**
   * 优化句子结构
   */
  private optimizeSentenceStructure(content: string): {
    content: string;
    changes: string[];
    improvements: string[];
  } {
    const changes: string[] = [];
    const improvements: string[] = [];
    let optimizedContent = content;

    // 这里可以实现句子结构优化逻辑
    // 目前返回原内容
    return {
      content: optimizedContent,
      changes,
      improvements,
    };
  }

  /**
   * 添加语义相关词汇
   */
  private addSemanticKeywords(
    content: string,
    keywords: string[]
  ): {
    content: string;
    changes: string[];
    improvements: string[];
  } {
    const changes: string[] = [];
    const improvements: string[] = [];
    let optimizedContent = content;

    // 这里可以实现语义词汇添加逻辑
    // 目前返回原内容
    return {
      content: optimizedContent,
      changes,
      improvements,
    };
  }

  /**
   * 计算内容分数
   */
  private calculateContentScore(
    issues: ContentIssue[],
    keywordAnalysis: KeywordAnalysis,
    readabilityScore: number
  ): number {
    let score = 100;

    // 根据问题扣分
    issues.forEach((issue) => {
      switch (issue.severity) {
        case 'high':
          score -= 15;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    // 关键词覆盖率加分
    const keywordCoverage =
      (this.config.primaryKeywords.length - keywordAnalysis.missingKeywords.length) /
      this.config.primaryKeywords.length;
    score += keywordCoverage * 20;

    // 可读性分数影响
    score = score * (readabilityScore / 100);

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算可读性分数
   */
  private calculateReadabilityScore(content: string): number {
    const sentences = content.split(/[.!?]+/).filter((s) => s.trim().length > 0);
    const words = content.split(/\s+/).filter((w) => w.length > 0);
    const syllables = words.reduce((sum, word) => sum + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // 简化的可读性公式 (基于 Flesch Reading Ease)
    const score = 206.835 - 1.015 * avgWordsPerSentence - 84.6 * avgSyllablesPerWord;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算音节数 (简化版)
   */
  private countSyllables(word: string): number {
    // 对于中文，每个字符大致对应一个音节
    if (this.config.language === 'zh') {
      return word.replace(/[^\u4e00-\u9fff]/g, '').length || 1;
    }

    // 对于英文，使用简化的音节计算
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    return Math.max(1, count);
  }

  /**
   * 获取内容长度
   */
  private getContentLength(content: string): number {
    return content.length;
  }

  /**
   * 获取词数
   */
  private getWordCount(content: string): number {
    if (this.config.language === 'zh') {
      // 中文按字符计算
      return content.replace(/[^\u4e00-\u9fff]/g, '').length;
    } else {
      // 英文按单词计算
      return content.split(/\s+/).filter((w) => w.length > 0).length;
    }
  }

  /**
   * 获取最小内容长度
   */
  private getMinContentLength(): number {
    switch (this.config.contentType) {
      case 'homepage':
        return 300;
      case 'product':
        return 500;
      case 'blog':
        return 800;
      case 'landing':
        return 400;
      case 'analysis':
        return 600;
      default:
        return 300;
    }
  }

  /**
   * 获取最大内容长度
   */
  private getMaxContentLength(): number {
    switch (this.config.contentType) {
      case 'homepage':
        return 1500;
      case 'product':
        return 2000;
      case 'blog':
        return 5000;
      case 'landing':
        return 2000;
      case 'analysis':
        return 3000;
      default:
        return 2000;
    }
  }
}

/**
 * 创建内容优化器实例
 */
export function createContentOptimizer(config: ContentSEOConfig): ContentOptimizer {
  return new ContentOptimizer(config);
}

/**
 * 预定义的内容配置
 */
export const contentConfigs = {
  tradingAgentsHomepage: {
    primaryKeywords: [
      '多智能体金融分析',
      'AI交易系统',
      '量化投资工具',
      '股票分析平台',
      'TradingAgents',
    ],
    secondaryKeywords: ['智能投资', '金融科技', '交易决策', '市场分析', '风险管理', '投资策略'],
    longTailKeywords: [
      '基于大语言模型的金融分析',
      '多智能体协作交易系统',
      '实时股票分析工具',
      '专业投资决策支持',
      'AI驱动的量化投资',
    ],
    language: 'zh' as const,
    contentType: 'homepage' as const,
    targetAudience: 'investors' as const,
  },

  analysisPage: {
    primaryKeywords: ['股票分析', '投资分析', '市场研究', '交易信号'],
    secondaryKeywords: ['技术分析', '基本面分析', '情绪分析', '风险评估'],
    longTailKeywords: ['专业股票分析报告', '多维度投资研究', '实时市场分析'],
    language: 'zh' as const,
    contentType: 'analysis' as const,
    targetAudience: 'analysts' as const,
  },
};
