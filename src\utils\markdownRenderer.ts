/**
 * 增强的 Markdown 渲染工具
 * 专门用于渲染分析报告中的 markdown 内容
 */

export interface MarkdownRenderOptions {
  enableTables?: boolean;
  enableCodeBlocks?: boolean;
  enableHighlight?: boolean;
  maxLength?: number;
}

export class MarkdownRenderer {
  private options: MarkdownRenderOptions;

  constructor(options: MarkdownRenderOptions = {}) {
    this.options = {
      enableTables: true,
      enableCodeBlocks: true,
      enableHighlight: true,
      maxLength: 10000,
      ...options,
    };
  }

  /**
   * 渲染 markdown 内容为 HTML
   */
  render(content: string): string {
    if (!content) return '';

    // 截断过长的内容
    if (this.options.maxLength && content.length > this.options.maxLength) {
      content = content.substring(0, this.options.maxLength) + '...';
    }

    let html = content;

    // 处理代码块
    if (this.options.enableCodeBlocks) {
      html = this.renderCodeBlocks(html);
    }

    // 处理表格
    if (this.options.enableTables) {
      html = this.renderTables(html);
    }

    // 处理标题
    html = this.renderHeadings(html);

    // 处理列表
    html = this.renderLists(html);

    // 处理文本格式
    html = this.renderTextFormatting(html);

    // 处理链接
    html = this.renderLinks(html);

    // 处理段落
    html = this.renderParagraphs(html);

    // 处理高亮
    if (this.options.enableHighlight) {
      html = this.renderHighlights(html);
    }

    return html;
  }

  /**
   * 渲染代码块
   */
  private renderCodeBlocks(content: string): string {
    // 多行代码块
    content = content.replace(
      /```(\w+)?\n([\s\S]*?)```/g,
      '<pre class="bg-slate-100 dark:bg-slate-800 p-4 rounded-lg overflow-x-auto my-4"><code class="text-sm font-mono text-slate-800 dark:text-slate-200">$2</code></pre>'
    );

    // 行内代码
    content = content.replace(
      /`([^`]+)`/g,
      '<code class="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded text-sm font-mono text-blue-600 dark:text-blue-400">$1</code>'
    );

    return content;
  }

  /**
   * 渲染表格
   */
  private renderTables(content: string): string {
    const tableRegex = /\|(.+)\|\n\|[-\s|:]+\|\n((?:\|.+\|\n?)*)/g;

    return content.replace(tableRegex, (match, header, rows) => {
      const headerCells = header
        .split('|')
        .map((cell: string) => cell.trim())
        .filter((cell: string) => cell);
      const rowsArray = rows
        .trim()
        .split('\n')
        .map((row: string) =>
          row
            .split('|')
            .map((cell: string) => cell.trim())
            .filter((cell: string) => cell)
        );

      let tableHtml =
        '<div class="overflow-x-auto my-4"><table class="min-w-full border border-slate-200 dark:border-slate-700 rounded-lg">';

      // 表头
      tableHtml += '<thead class="bg-slate-50 dark:bg-slate-800"><tr>';
      headerCells.forEach((cell: any) => {
        tableHtml += `<th class="px-4 py-2 text-left text-sm font-medium text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700">${cell}</th>`;
      });
      tableHtml += '</tr></thead>';

      // 表体
      tableHtml += '<tbody>';
      rowsArray.forEach((row: any, index: number) => {
        const bgClass =
          index % 2 === 0 ? 'bg-white dark:bg-slate-900' : 'bg-slate-50 dark:bg-slate-800';
        tableHtml += `<tr class="${bgClass}">`;
        row.forEach((cell: any) => {
          tableHtml += `<td class="px-4 py-2 text-sm text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700">${cell}</td>`;
        });
        tableHtml += '</tr>';
      });
      tableHtml += '</tbody></table></div>';

      return tableHtml;
    });
  }

  /**
   * 渲染标题
   */
  private renderHeadings(content: string): string {
    return content
      .replace(
        /^### (.*?)$/gm,
        '<h3 class="text-lg font-semibold mt-6 mb-3 text-slate-900 dark:text-white border-b border-slate-200 dark:border-slate-700 pb-2">$1</h3>'
      )
      .replace(
        /^## (.*?)$/gm,
        '<h2 class="text-xl font-bold mt-8 mb-4 text-slate-900 dark:text-white border-b-2 border-blue-500 pb-2">$1</h2>'
      )
      .replace(
        /^# (.*?)$/gm,
        '<h1 class="text-2xl font-bold mt-10 mb-6 text-slate-900 dark:text-white">$1</h1>'
      );
  }

  /**
   * 渲染列表
   */
  private renderLists(content: string): string {
    // 有序列表
    content = content.replace(
      /^\d+\.\s(.+)$/gm,
      '<li class="ml-6 mb-2 text-slate-700 dark:text-slate-300 list-decimal">$1</li>'
    );

    // 无序列表
    content = content.replace(
      /^[-*+]\s(.+)$/gm,
      '<li class="ml-6 mb-2 text-slate-700 dark:text-slate-300 list-disc">$1</li>'
    );

    // 包装列表项
    content = content.replace(/(<li[^>]*>.*<\/li>)/g, '<ul class="my-4 space-y-1">$1</ul>');

    return content;
  }

  /**
   * 渲染文本格式
   */
  private renderTextFormatting(content: string): string {
    return (
      content
        // 粗体
        .replace(
          /\*\*(.*?)\*\*/g,
          '<strong class="font-semibold text-slate-900 dark:text-white">$1</strong>'
        )
        // 斜体
        .replace(/\*(.*?)\*/g, '<em class="italic text-slate-700 dark:text-slate-300">$1</em>')
        // 删除线
        .replace(/~~(.*?)~~/g, '<del class="line-through text-slate-500">$1</del>')
    );
  }

  /**
   * 渲染链接
   */
  private renderLinks(content: string): string {
    // Markdown 链接
    content = content.replace(
      /\[([^\]]+)\]\(([^)]+)\)/g,
      '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>'
    );

    // 自动链接
    content = content.replace(
      /(https?:\/\/[^\s]+)/g,
      '<a href="$1" class="text-blue-600 dark:text-blue-400 hover:underline break-all" target="_blank" rel="noopener noreferrer">$1</a>'
    );

    return content;
  }

  /**
   * 渲染段落
   */
  private renderParagraphs(content: string): string {
    // 分割段落
    const paragraphs = content.split(/\n\s*\n/);

    return paragraphs
      .map((paragraph) => {
        paragraph = paragraph.trim();
        if (!paragraph) return '';

        // 如果已经是 HTML 标签，不要包装
        if (paragraph.startsWith('<')) return paragraph;

        return `<p class="mb-4 text-slate-700 dark:text-slate-300 leading-relaxed">${paragraph}</p>`;
      })
      .join('');
  }

  /**
   * 渲染高亮内容
   */
  private renderHighlights(content: string): string {
    return (
      content
        // 警告
        .replace(
          /⚠️\s*(.*?)(?=\n|$)/g,
          '<div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-3 my-4"><div class="flex items-center"><span class="text-yellow-600 mr-2">⚠️</span><span class="text-yellow-800 dark:text-yellow-200">$1</span></div></div>'
        )
        // 成功
        .replace(
          /✅\s*(.*?)(?=\n|$)/g,
          '<div class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400 p-3 my-4"><div class="flex items-center"><span class="text-green-600 mr-2">✅</span><span class="text-green-800 dark:text-green-200">$1</span></div></div>'
        )
        // 错误
        .replace(
          /❌\s*(.*?)(?=\n|$)/g,
          '<div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-3 my-4"><div class="flex items-center"><span class="text-red-600 mr-2">❌</span><span class="text-red-800 dark:text-red-200">$1</span></div></div>'
        )
        // 信息
        .replace(
          /ℹ️\s*(.*?)(?=\n|$)/g,
          '<div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-3 my-4"><div class="flex items-center"><span class="text-blue-600 mr-2">ℹ️</span><span class="text-blue-800 dark:text-blue-200">$1</span></div></div>'
        )
    );
  }

  /**
   * 提取摘要
   */
  static extractSummary(content: string, maxLength: number = 200): string {
    if (!content) return '';

    // 移除 markdown 标记
    const plainText = content
      .replace(/[#*`_~\[\]()]/g, '')
      .replace(/\n+/g, ' ')
      .trim();

    if (plainText.length <= maxLength) return plainText;

    // 在单词边界截断
    const truncated = plainText.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');

    return lastSpace > maxLength * 0.8
      ? truncated.substring(0, lastSpace) + '...'
      : truncated + '...';
  }

  /**
   * 检测内容类型
   */
  static detectContentType(
    content: string
  ): 'technical' | 'fundamental' | 'news' | 'research' | 'general' {
    const lowerContent = content.toLowerCase();

    if (
      lowerContent.includes('技术分析') ||
      lowerContent.includes('macd') ||
      lowerContent.includes('rsi')
    ) {
      return 'technical';
    }
    if (
      lowerContent.includes('基本面') ||
      lowerContent.includes('财务') ||
      lowerContent.includes('营收')
    ) {
      return 'fundamental';
    }
    if (
      lowerContent.includes('新闻') ||
      lowerContent.includes('消息') ||
      lowerContent.includes('公告')
    ) {
      return 'news';
    }
    if (
      lowerContent.includes('研究') ||
      lowerContent.includes('多头') ||
      lowerContent.includes('空头')
    ) {
      return 'research';
    }

    return 'general';
  }
}

// 默认渲染器实例
export const defaultMarkdownRenderer = new MarkdownRenderer();

// 便捷函数
export const renderMarkdown = (content: string, options?: MarkdownRenderOptions): string => {
  const renderer = new MarkdownRenderer(options);
  return renderer.render(content);
};

export const extractSummary = (content: string, maxLength?: number): string => {
  return MarkdownRenderer.extractSummary(content, maxLength);
};

export const detectContentType = (content: string) => {
  return MarkdownRenderer.detectContentType(content);
};
