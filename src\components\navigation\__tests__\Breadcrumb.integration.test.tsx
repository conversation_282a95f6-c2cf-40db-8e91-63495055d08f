import { render, screen } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { Breadcrumb } from '../Breadcrumb';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('Breadcrumb Integration Tests', () => {
  beforeEach(() => {
    mockUsePathname.mockClear();
  });

  describe('Analysis Pages Integration', () => {
    it('integrates correctly with analysis history page', () => {
      mockUsePathname.mockReturnValue('/analysis/history');

      render(<Breadcrumb />);

      // Verify complete breadcrumb path for analysis history
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByLabelText('面包屑导航')).toBeInTheDocument();

      // Check breadcrumb items
      const homeLink = screen.getByRole('link', { name: /首页/ });
      const analysisLink = screen.getByRole('link', { name: /分析中心/ });
      const currentPage = screen.getByText('分析历史');

      expect(homeLink).toHaveAttribute('href', '/');
      expect(analysisLink).toHaveAttribute('href', '/analysis');
      expect(currentPage).toHaveAttribute('aria-current', 'page');

      // Verify navigation structure
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems).toHaveLength(3);
    });

    it('integrates correctly with analysis compare page', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');

      render(<Breadcrumb />);

      // Verify complete breadcrumb path for analysis compare
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Check breadcrumb items
      const homeLink = screen.getByRole('link', { name: /首页/ });
      const analysisLink = screen.getByRole('link', { name: /分析中心/ });
      const currentPage = screen.getByText('分析对比');

      expect(homeLink).toHaveAttribute('href', '/');
      expect(analysisLink).toHaveAttribute('href', '/analysis');
      expect(currentPage).toHaveAttribute('aria-current', 'page');

      // Verify navigation structure
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems).toHaveLength(3);
    });

    it('integrates correctly with analysis detail page', () => {
      const analysisId = 'wf_12345_abc';
      mockUsePathname.mockReturnValue(`/analysis/${analysisId}`);

      render(<Breadcrumb />);

      // Verify complete breadcrumb path for analysis detail
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Check breadcrumb items
      const homeLink = screen.getByRole('link', { name: /首页/ });
      const analysisLink = screen.getByRole('link', { name: /分析中心/ });
      const currentPage = screen.getByText(`分析详情 (${analysisId})`);

      expect(homeLink).toHaveAttribute('href', '/');
      expect(analysisLink).toHaveAttribute('href', '/analysis');
      expect(currentPage).toHaveAttribute('aria-current', 'page');

      // Verify navigation structure
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems).toHaveLength(3);
    });

    it('handles nested analysis paths correctly', () => {
      mockUsePathname.mockReturnValue('/analysis/history/filter');

      render(<Breadcrumb />);

      // Verify complete breadcrumb path for nested analysis page
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Check breadcrumb items
      const homeLink = screen.getByRole('link', { name: /首页/ });
      const analysisLink = screen.getByRole('link', { name: /分析中心/ });
      const historyLink = screen.getByRole('link', { name: /分析历史/ });
      const currentPage = screen.getByText('filter');

      expect(homeLink).toHaveAttribute('href', '/');
      expect(analysisLink).toHaveAttribute('href', '/analysis');
      expect(historyLink).toHaveAttribute('href', '/analysis/history');
      expect(currentPage).toHaveAttribute('aria-current', 'page');

      // Verify navigation structure
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems).toHaveLength(4);
    });
  });

  describe('Page Layout Integration', () => {
    it('accommodates breadcrumb navigation in page layouts', () => {
      mockUsePathname.mockReturnValue('/analysis/history');

      const { container } = render(
        <div className="page-layout">
          <div className="breadcrumb-container mb-4">
            <Breadcrumb />
          </div>
          <div className="page-content">
            <h1>Analysis History</h1>
          </div>
        </div>
      );

      // Verify breadcrumb is properly contained within layout
      const breadcrumbContainer = container.querySelector('.breadcrumb-container');
      const breadcrumbNav = screen.getByRole('navigation');

      expect(breadcrumbContainer).toContainElement(breadcrumbNav);
      expect(breadcrumbContainer).toHaveClass('mb-4'); // Proper spacing

      // Verify breadcrumb doesn't interfere with page content
      const pageContent = container.querySelector('.page-content');
      expect(pageContent).toBeInTheDocument();
      expect(pageContent).not.toContainElement(breadcrumbNav);
    });

    it('maintains proper spacing and layout structure', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');

      render(<Breadcrumb className="mb-6" />);

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('mb-6');
      expect(nav).toHaveClass('flex'); // Default layout class
    });
  });

  describe('Cross-Page Navigation Context', () => {
    it('provides consistent navigation context across analysis pages', () => {
      const testPaths = ['/analysis/history', '/analysis/compare', '/analysis/wf_123_test'];

      testPaths.forEach((path) => {
        mockUsePathname.mockReturnValue(path);
        const { unmount } = render(<Breadcrumb />);

        // All analysis pages should have consistent home and analysis center links
        const homeLink = screen.getByRole('link', { name: /首页/ });
        const analysisLink = screen.getByRole('link', { name: /分析中心/ });

        expect(homeLink).toHaveAttribute('href', '/');
        expect(analysisLink).toHaveAttribute('href', '/analysis');

        // All should have proper ARIA navigation
        expect(screen.getByRole('navigation')).toHaveAttribute('aria-label', '面包屑导航');

        unmount();
      });
    });

    it('preserves navigation state across page transitions', () => {
      // Simulate navigation from history to compare page
      mockUsePathname.mockReturnValue('/analysis/history');
      const { rerender } = render(<Breadcrumb />);

      // Verify initial state
      expect(screen.getByText('分析历史')).toHaveAttribute('aria-current', 'page');

      // Simulate navigation to compare page
      mockUsePathname.mockReturnValue('/analysis/compare');
      rerender(<Breadcrumb />);

      // Verify new state
      expect(screen.getByText('分析对比')).toHaveAttribute('aria-current', 'page');
      expect(screen.queryByText('分析历史')).not.toBeInTheDocument();

      // Verify consistent parent navigation
      expect(screen.getByRole('link', { name: /首页/ })).toHaveAttribute('href', '/');
      expect(screen.getByRole('link', { name: /分析中心/ })).toHaveAttribute('href', '/analysis');
    });
  });

  describe('Responsive Layout Integration', () => {
    it('integrates properly with mobile layouts', () => {
      mockUsePathname.mockReturnValue('/analysis/history');

      render(
        <div className="mobile-layout">
          <Breadcrumb className="px-4 py-2" />
        </div>
      );

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveClass('px-4', 'py-2');

      // Verify breadcrumb items are still accessible on mobile
      expect(screen.getByRole('link', { name: /首页/ })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /分析中心/ })).toBeInTheDocument();
      expect(screen.getByText('分析历史')).toBeInTheDocument();
    });

    it('maintains accessibility in different layout contexts', () => {
      mockUsePathname.mockReturnValue('/analysis/compare');

      render(
        <div className="container">
          <header>
            <Breadcrumb />
          </header>
          <main>Content</main>
        </div>
      );

      // Verify ARIA navigation is maintained in header context
      const nav = screen.getByRole('navigation');
      expect(nav).toHaveAttribute('aria-label', '面包屑导航');
      expect(nav).toHaveAttribute('role', 'navigation');

      // Verify it doesn't interfere with main content
      const main = screen.getByRole('main');
      expect(main).not.toContainElement(nav);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('handles invalid analysis paths gracefully', () => {
      mockUsePathname.mockReturnValue('/analysis/invalid-path-123');

      render(<Breadcrumb />);

      // Should still render basic navigation structure
      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /首页/ })).toHaveAttribute('href', '/');
      expect(screen.getByRole('link', { name: /分析中心/ })).toHaveAttribute('href', '/analysis');

      // Should handle the invalid segment gracefully
      expect(screen.getByText('分析详情 (invalid-path-123)')).toBeInTheDocument();
    });

    it('handles deeply nested paths without breaking', () => {
      mockUsePathname.mockReturnValue('/analysis/history/filter/advanced/settings');

      render(<Breadcrumb />);

      // Should render all path segments
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems.length).toBeGreaterThan(3);

      // Should maintain proper navigation structure
      expect(screen.getByRole('link', { name: /首页/ })).toHaveAttribute('href', '/');
      expect(screen.getByRole('link', { name: /分析中心/ })).toHaveAttribute('href', '/analysis');
      expect(screen.getByRole('link', { name: /分析历史/ })).toHaveAttribute(
        'href',
        '/analysis/history'
      );
    });

    it('handles special characters in analysis IDs', () => {
      const specialId = 'wf_2024-01-01_test@123';
      mockUsePathname.mockReturnValue(`/analysis/${specialId}`);

      render(<Breadcrumb />);

      // Should handle special characters in analysis ID
      expect(screen.getByText(`分析详情 (${specialId})`)).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });
});
