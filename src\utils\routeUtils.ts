/**
 * 路由权限管理工具
 */

// 不需要登录就能访问的路由
export const publicRoutes = [
  '/', // 首页 - 产品介绍和功能展示
  '/login', // 登录页
  '/register', // 注册页
  '/debug', // 调试页面
  '/debug/env', // 环境调试页面
];

// API 路由不需要路由守卫处理（由后端处理认证）
export const apiRoutes = ['/api'];

// 需要认证的路由
export const protectedRoutes = [
  '/tasks', // 任务管理
  '/create-task', // 创建任务
  '/analysis', // 分析页面
  '/messages', // 消息查询
  '/database', // 数据库管理
];

/**
 * 检查路径是否匹配路由列表
 */
export function matchesRoutes(pathname: string, routes: string[]): boolean {
  return routes.some((route) => pathname === route || pathname.startsWith(route + '/'));
}

/**
 * 检查是否为公开路由
 */
export function isPublicRoute(pathname: string): boolean {
  return matchesRoutes(pathname, publicRoutes);
}

/**
 * 检查是否为 API 路由
 */
export function isApiRoute(pathname: string): boolean {
  return matchesRoutes(pathname, apiRoutes);
}

/**
 * 检查是否为受保护的路由
 */
export function isProtectedRoute(pathname: string): boolean {
  return matchesRoutes(pathname, protectedRoutes);
}

/**
 * 检查是否为认证相关路由（登录/注册）
 */
export function isAuthRoute(pathname: string): boolean {
  return pathname === '/login' || pathname === '/register';
}

/**
 * 获取路由类型
 */
export function getRouteType(
  pathname: string
): 'public' | 'protected' | 'api' | 'auth' | 'unknown' {
  if (isApiRoute(pathname)) return 'api';
  if (isAuthRoute(pathname)) return 'auth';
  if (isPublicRoute(pathname)) return 'public';
  if (isProtectedRoute(pathname)) return 'protected';
  return 'unknown';
}
