/**
 * SEO 模块入口文件
 * 导出所有 SEO 相关的类、函数和配置
 */

// 类型定义
export type {
  SEOConfig,
  PageSEOProps,
  SupportedLocale,
  PageType,
  OpenGraphConfig,
  TwitterCardConfig,
  SEOPageConfig,
  SEOAnalytics,
  StructuredDataConfig,
  OrganizationSchema,
  WebsiteSchema,
  SoftwareApplicationSchema,
  BreadcrumbSchema,
  CoreWebVitals,
  SEOMonitorConfig,
  LanguageDetectionResult,
  HreflangLink,
  LanguageSwitchLink,
} from '@/types/seo';

// 配置和常量
export {
  SEO_CONSTANTS,
  DEFAULT_SEO_CONFIG,
  SEO_CONTENT,
  STRUCTURED_DATA_TEMPLATES,
  SEO_BEST_PRACTICES,
} from './config';

// 工具类和函数
export { SEOUtils, WebVitalsUtils } from './utils';

// 元数据生成器
export { MetadataGenerator } from './metadata-generator';

// 错误处理器
export { SEOErrorHandler } from './error-handler';

// 中间件
export {
  seoMiddleware,
  shouldApplySEO,
  getPageLocale,
  cleanPathname,
  generateLanguageSwitchUrl,
  isMultilingualPath,
  getUserPreferredLocale,
} from './middleware';

// 多语言 SEO 支持
export {
  I18N_SEO_CONTENT,
  LanguageDetector,
  HreflangGenerator,
  I18nContentSwitcher,
  LANGUAGE_DETECTION_CONFIG,
  HREFLANG_CONFIG,
} from './i18n-seo';

// 运行时配置
export {
  getRuntimeSEOConfig,
  isProduction,
  isAnalyticsEnabled,
  isSearchConsoleEnabled,
  getFullUrl,
  getCDNUrl,
  getEnvironmentConfig,
} from './runtime-config';

// Sitemap 生成器
export { SitemapGenerator, createSitemapGenerator, generateSitemap } from './sitemap-generator';
export type { SitemapPage, SitemapConfig } from './sitemap-generator';

// 便捷函数
import type { SupportedLocale, PageSEOProps } from '@/types/seo';
import { MetadataGenerator as MetadataGeneratorClass } from './metadata-generator';

export const createSEOGenerator = (locale?: SupportedLocale) => {
  return new MetadataGeneratorClass(locale);
};

export const generatePageMetadata = (props: PageSEOProps) => {
  const generator = new MetadataGeneratorClass(props.locale);
  return generator.generatePageMetadata(props);
};

export const generateStructuredData = (
  page: string,
  dynamicData?: any,
  locale?: SupportedLocale
) => {
  const generator = new MetadataGeneratorClass(locale);
  return generator.generateStructuredData(page, dynamicData);
};

export const generateJsonLdScripts = (
  page: string,
  dynamicData?: any,
  locale?: SupportedLocale
) => {
  const generator = new MetadataGeneratorClass(locale);
  return generator.generateJsonLdScripts(page, dynamicData);
};

export const generateEnhancedJsonLdScripts = (
  page: string,
  dynamicData?: any,
  locale?: SupportedLocale
) => {
  const generator = new MetadataGeneratorClass(locale);
  return generator.generateEnhancedJsonLdScripts(page, dynamicData);
};

// 导出增强的结构化数据生成器
export {
  StructuredDataGenerator,
  generatePageStructuredData,
  createStructuredDataGenerator,
} from './structured-data-generator';

// 导出结构化数据验证器
export {
  StructuredDataValidator,
  validateStructuredData,
  validateMultipleStructuredData,
  generateValidationReport,
} from './structured-data-validator';
export type { ValidationResult, ValidationOptions } from './structured-data-validator';

// 导出 SEO 测试工具
export { SEOTester, testPageSEO, testAllPagesSEO } from './seo-testing';
export type { SEOTestResult, SEOTestOptions } from './seo-testing';

// Robots.txt 生成器
export { RobotsGenerator, createRobotsGenerator, generateRobotsTxt } from './robots-generator';
export type { RobotsRule, RobotsConfig } from './robots-generator';

// 安全头管理
export {
  SEOSecurityHeaders,
  createSEOSecurityHeaders,
  applySEOSecurityHeaders,
} from './security-headers';
export type { SecurityHeadersConfig } from './security-headers';

// 验证文件管理
export {
  VerificationFileManager,
  createVerificationFileManager,
  handleVerificationFile,
} from './verification-files';
export type { VerificationConfig } from './verification-files';

// SSR 优化
export {
  SSROptimizer,
  createSSROptimizer,
  generateOptimizedMetadata,
  getStaticGenerationConfig,
} from './ssr-optimization';
export type { SSROptimizationConfig, PreloadResource } from './ssr-optimization';

// 性能优化
export {
  PerformanceOptimizer,
  createPerformanceOptimizer,
  applyPerformanceHeaders,
} from './performance-optimization';
export type { PerformanceConfig, CoreWebVitalsThresholds } from './performance-optimization';

// 静态生成
export {
  StaticGenerationManager,
  createStaticGenerationManager,
  generateStaticParams,
  getRevalidateTime,
  generateStaticMetadata,
} from './static-generation';
export type { StaticPageConfig, StaticGenerationResult } from './static-generation';
