/**
 * 多语言 SEO 系统测试
 */

import { describe, it, expect, beforeEach } from '@jest/globals';

// Mock Next.js server components for testing
jest.mock('next/server', () => ({
  NextRequest: jest.fn(),
  NextResponse: jest.fn(),
}));
import {
  LanguageDetector,
  HreflangGenerator,
  I18nContentSwitcher,
  I18N_SEO_CONTENT,
  LANGUAGE_DETECTION_CONFIG,
  HREFLANG_CONFIG,
} from '../i18n-seo';
import { SupportedLocale, PageType } from '@/types/seo';

describe('LanguageDetector', () => {
  describe('detectFromHeaders', () => {
    it('should detect Chinese from Accept-Language header', () => {
      const result = LanguageDetector.detectFromHeaders('zh-CN,zh;q=0.9,en;q=0.8');
      expect(result).toBe('zh');
    });

    it('should detect English from Accept-Language header', () => {
      const result = LanguageDetector.detectFromHeaders('en-US,en;q=0.9');
      expect(result).toBe('en');
    });

    it('should return default locale for unsupported language', () => {
      const result = LanguageDetector.detectFromHeaders('fr-FR,fr;q=0.9');
      expect(result).toBe('zh');
    });

    it('should return default locale for empty header', () => {
      const result = LanguageDetector.detectFromHeaders('');
      expect(result).toBe('zh');
    });
  });

  describe('detectFromPath', () => {
    it('should detect Chinese from root path', () => {
      const result = LanguageDetector.detectFromPath('/');
      expect(result).toEqual({ locale: 'zh', cleanPath: '/' });
    });

    it('should detect English from /en path', () => {
      const result = LanguageDetector.detectFromPath('/en');
      expect(result).toEqual({ locale: 'en', cleanPath: '/' });
    });

    it('should detect English from /en/analysis path', () => {
      const result = LanguageDetector.detectFromPath('/en/analysis');
      expect(result).toEqual({ locale: 'en', cleanPath: '/analysis' });
    });

    it('should detect Chinese from /analysis path', () => {
      const result = LanguageDetector.detectFromPath('/analysis');
      expect(result).toEqual({ locale: 'zh', cleanPath: '/analysis' });
    });
  });

  describe('detectFromCookie', () => {
    it('should detect valid locale from cookie', () => {
      const result = LanguageDetector.detectFromCookie('en');
      expect(result).toBe('en');
    });

    it('should return null for invalid locale', () => {
      const result = LanguageDetector.detectFromCookie('fr');
      expect(result).toBeNull();
    });

    it('should return null for empty cookie', () => {
      const result = LanguageDetector.detectFromCookie('');
      expect(result).toBeNull();
    });
  });

  describe('detect', () => {
    it('should prioritize path locale over other sources', () => {
      const result = LanguageDetector.detect({
        pathname: '/en/analysis',
        acceptLanguage: 'zh-CN,zh;q=0.9',
        cookieValue: 'zh',
      });
      expect(result).toEqual({ locale: 'en', cleanPath: '/analysis' });
    });

    it('should use cookie when path is default', () => {
      const result = LanguageDetector.detect({
        pathname: '/analysis',
        acceptLanguage: 'zh-CN,zh;q=0.9',
        cookieValue: 'en',
      });
      expect(result).toEqual({ locale: 'en', cleanPath: '/analysis' });
    });

    it('should use Accept-Language when no cookie', () => {
      const result = LanguageDetector.detect({
        pathname: '/analysis',
        acceptLanguage: 'en-US,en;q=0.9',
      });
      expect(result).toEqual({ locale: 'en', cleanPath: '/analysis' });
    });
  });
});

describe('HreflangGenerator', () => {
  describe('generateHreflangLinks', () => {
    it('should generate correct hreflang links for root path', () => {
      const links = HreflangGenerator.generateHreflangLinks('/', 'zh');

      expect(links).toContainEqual({
        hreflang: 'zh-CN',
        href: 'https://tradingagents.com',
      });

      expect(links).toContainEqual({
        hreflang: 'en-US',
        href: 'https://tradingagents.com/en',
      });

      expect(links).toContainEqual({
        hreflang: 'x-default',
        href: 'https://tradingagents.com',
      });
    });

    it('should generate correct hreflang links for analysis path', () => {
      const links = HreflangGenerator.generateHreflangLinks('/analysis', 'zh');

      expect(links).toContainEqual({
        hreflang: 'zh-CN',
        href: 'https://tradingagents.com/analysis',
      });

      expect(links).toContainEqual({
        hreflang: 'en-US',
        href: 'https://tradingagents.com/en/analysis',
      });
    });
  });

  describe('generateAlternates', () => {
    it('should generate Next.js alternates config', () => {
      const alternates = HreflangGenerator.generateAlternates('/analysis', 'zh');

      expect(alternates.canonical).toBe('https://tradingagents.com/analysis');
      expect(alternates.languages.zh).toBe('https://tradingagents.com/analysis');
      expect(alternates.languages.en).toBe('https://tradingagents.com/en/analysis');
      expect(alternates.languages['x-default']).toBe('https://tradingagents.com/analysis');
    });
  });
});

describe('I18nContentSwitcher', () => {
  describe('getPageContent', () => {
    it('should return Chinese content for home page', () => {
      const content = I18nContentSwitcher.getPageContent('home', 'zh');

      expect(content.title).toContain('TradingAgents');
      expect(content.title).toContain('多智能体');
      expect(content.hreflang).toBe('zh-CN');
    });

    it('should return English content for home page', () => {
      const content = I18nContentSwitcher.getPageContent('home', 'en');

      expect(content.title).toContain('TradingAgents');
      expect(content.title).toContain('Multi-Agent');
      expect(content.hreflang).toBe('en-US');
    });

    it('should return fallback content for invalid page', () => {
      const content = I18nContentSwitcher.getPageContent('invalid' as PageType, 'zh');

      // Should fallback to home page content
      expect(content.title).toContain('TradingAgents');
    });
  });

  describe('getDynamicContent', () => {
    it('should replace stock symbol in content', () => {
      const content = I18nContentSwitcher.getDynamicContent('analysis', 'zh', {
        stockSymbol: 'AAPL',
      });

      expect(content.title).toContain('AAPL');
      expect(content.description).toContain('AAPL分析');
    });

    it('should replace task title in content', () => {
      const content = I18nContentSwitcher.getDynamicContent('tasks', 'zh', {
        taskTitle: '苹果股票分析',
      });

      expect(content.title).toContain('苹果股票分析');
    });
  });

  describe('generateLanguageSwitchLinks', () => {
    it('should generate language switch links for current path', () => {
      const links = I18nContentSwitcher.generateLanguageSwitchLinks('/analysis', 'zh');

      expect(links).toHaveLength(2);

      const zhLink = links.find((link) => link.locale === 'zh');
      const enLink = links.find((link) => link.locale === 'en');

      expect(zhLink).toEqual({
        locale: 'zh',
        label: '中文',
        href: '/analysis',
        active: true,
      });

      expect(enLink).toEqual({
        locale: 'en',
        label: 'English',
        href: '/en/analysis',
        active: false,
      });
    });

    it('should generate language switch links for English path', () => {
      const links = I18nContentSwitcher.generateLanguageSwitchLinks('/en/tasks', 'en');

      const zhLink = links.find((link) => link.locale === 'zh');
      const enLink = links.find((link) => link.locale === 'en');

      expect(zhLink).toEqual({
        locale: 'zh',
        label: '中文',
        href: '/tasks',
        active: false,
      });

      expect(enLink).toEqual({
        locale: 'en',
        label: 'English',
        href: '/en/tasks',
        active: true,
      });
    });
  });
});

describe('I18N_SEO_CONTENT', () => {
  it('should have content for all supported locales', () => {
    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      expect(I18N_SEO_CONTENT[locale]).toBeDefined();
    });
  });

  it('should have content for all page types', () => {
    const pageTypes: PageType[] = ['home', 'analysis', 'tasks', 'messages', 'create-task'];

    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      pageTypes.forEach((pageType) => {
        const content = I18N_SEO_CONTENT[locale][pageType];
        expect(content).toBeDefined();
        expect(content.title).toBeTruthy();
        expect(content.description).toBeTruthy();
        expect(content.keywords).toBeInstanceOf(Array);
        expect(content.keywords.length).toBeGreaterThan(0);
        expect(content.hreflang).toBeTruthy();
      });
    });
  });

  it('should have proper hreflang values', () => {
    expect(I18N_SEO_CONTENT.zh.home.hreflang).toBe('zh-CN');
    expect(I18N_SEO_CONTENT.en.home.hreflang).toBe('en-US');
  });

  it('should have social media optimized content', () => {
    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      const homeContent = I18N_SEO_CONTENT[locale].home;
      expect(homeContent.socialTitle).toBeTruthy();
      expect(homeContent.socialDescription).toBeTruthy();
    });
  });
});

describe('Configuration Validation', () => {
  it('should have consistent locale mappings', () => {
    Object.keys(HREFLANG_CONFIG.localeToHreflang).forEach((locale) => {
      expect(LANGUAGE_DETECTION_CONFIG.supportedLocales).toContain(locale as SupportedLocale);
    });
  });

  it('should have URL patterns for all supported locales', () => {
    LANGUAGE_DETECTION_CONFIG.supportedLocales.forEach((locale) => {
      expect(HREFLANG_CONFIG.urlPatterns[locale]).toBeDefined();
    });
  });

  it('should have valid cookie configuration', () => {
    const cookieConfig = LANGUAGE_DETECTION_CONFIG.cookieConfig;
    expect(cookieConfig.name).toBeTruthy();
    expect(cookieConfig.maxAge).toBeGreaterThan(0);
    expect(cookieConfig.path).toBe('/');
    expect(['strict', 'lax', 'none']).toContain(cookieConfig.sameSite);
  });
});
