/**
 * 图片 SEO 管理系统
 * 提供图片 SEO 优化的统一管理和自动化功能
 */

export interface ImageSEOConfig {
  // 自动生成 alt 属性
  autoGenerateAlt: boolean;
  // 启用 WebP 格式
  enableWebP: boolean;
  // 启用懒加载
  enableLazyLoading: boolean;
  // 图片质量 (1-100)
  defaultQuality: number;
  // 最大文件大小 (bytes)
  maxFileSize: number;
  // 最大宽度
  maxWidth: number;
  // 最大高度
  maxHeight: number;
  // 支持的格式
  supportedFormats: string[];
  // CDN 基础 URL
  cdnBaseUrl?: string;
}

export interface ImageMetadata {
  src: string;
  alt: string;
  title?: string;
  caption?: string;
  width?: number;
  height?: number;
  fileSize?: number;
  format?: string;
  lastModified?: Date;
  seoScore?: number;
  issues?: string[];
}

export interface ImageOptimizationResult {
  originalSrc: string;
  optimizedSrc: string;
  alt: string;
  improvements: string[];
  seoScore: number;
  metadata: ImageMetadata;
}

export class ImageSEOManager {
  private config: ImageSEOConfig;
  private imageCache: Map<string, ImageMetadata> = new Map();

  constructor(config: Partial<ImageSEOConfig> = {}) {
    this.config = {
      autoGenerateAlt: true,
      enableWebP: true,
      enableLazyLoading: true,
      defaultQuality: 85,
      maxFileSize: 1024 * 1024, // 1MB
      maxWidth: 1920,
      maxHeight: 1080,
      supportedFormats: ['jpg', 'jpeg', 'png', 'webp', 'avif'],
      ...config,
    };
  }

  /**
   * 优化图片 SEO
   */
  async optimizeImage(
    src: string,
    options: {
      alt?: string;
      imageType?: 'logo' | 'chart' | 'screenshot' | 'avatar' | 'icon' | 'decorative' | 'content';
      context?: Record<string, any>;
      forceRegenerate?: boolean;
    } = {}
  ): Promise<ImageOptimizationResult> {
    const { alt, imageType = 'content', context, forceRegenerate = false } = options;

    // 检查缓存
    if (!forceRegenerate && this.imageCache.has(src)) {
      const cached = this.imageCache.get(src)!;
      return this.buildOptimizationResult(src, cached);
    }

    // 获取图片元数据
    const metadata = await this.getImageMetadata(src);

    // 生成或验证 alt 属性
    const optimizedAlt =
      alt || (this.config.autoGenerateAlt ? this.generateAltText(src, imageType, context) : '');

    // 优化图片源
    const optimizedSrc = this.optimizeImageSrc(src, metadata);

    // 计算 SEO 分数
    const seoScore = this.calculateImageSEOScore(metadata, optimizedAlt);

    // 生成改进建议
    const improvements = this.generateImprovements(metadata, optimizedAlt);

    // 更新元数据
    const updatedMetadata: ImageMetadata = {
      ...metadata,
      alt: optimizedAlt,
      seoScore,
      issues: improvements,
    };

    // 缓存结果
    this.imageCache.set(src, updatedMetadata);

    return {
      originalSrc: src,
      optimizedSrc,
      alt: optimizedAlt,
      improvements,
      seoScore,
      metadata: updatedMetadata,
    };
  }

  /**
   * 批量优化图片
   */
  async optimizeImages(
    images: Array<{
      src: string;
      alt?: string;
      imageType?: string;
      context?: Record<string, any>;
    }>
  ): Promise<ImageOptimizationResult[]> {
    const results = await Promise.all(
      images.map((image) =>
        this.optimizeImage(image.src, {
          alt: image.alt,
          imageType: image.imageType as
            | 'logo'
            | 'chart'
            | 'screenshot'
            | 'avatar'
            | 'icon'
            | 'decorative'
            | 'content'
            | undefined,
          context: image.context,
        })
      )
    );

    return results;
  }

  /**
   * 获取图片元数据
   */
  private async getImageMetadata(src: string): Promise<ImageMetadata> {
    try {
      // 对于外部图片，只能获取基本信息
      if (src.startsWith('http://') || src.startsWith('https://')) {
        return {
          src,
          alt: '',
          format: this.getImageFormat(src),
        };
      }

      // 对于本地图片，可以获取更多信息
      const metadata: ImageMetadata = {
        src,
        alt: '',
        format: this.getImageFormat(src),
      };

      // 在浏览器环境中获取图片尺寸
      if (typeof window !== 'undefined') {
        try {
          const dimensions = await this.getImageDimensions(src);
          metadata.width = dimensions.width;
          metadata.height = dimensions.height;
        } catch (error) {
          console.warn('Failed to get image dimensions:', error);
        }
      }

      return metadata;
    } catch (error) {
      console.error('Failed to get image metadata:', error);
      return {
        src,
        alt: '',
      };
    }
  }

  /**
   * 获取图片尺寸
   */
  private getImageDimensions(src: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
        });
      };
      img.onerror = reject;
      img.src = src;
    });
  }

  /**
   * 获取图片格式
   */
  private getImageFormat(src: string): string {
    const extension = src.split('.').pop()?.toLowerCase();
    return extension || 'unknown';
  }

  /**
   * 生成 alt 文本
   */
  private generateAltText(src: string, imageType: string, context?: Record<string, any>): string {
    const filename = src.split('/').pop()?.split('.')[0] || '';

    switch (imageType) {
      case 'logo':
        return context?.company ? `${context.company} 标志` : 'TradingAgents 标志';

      case 'chart':
        if (context?.stockSymbol && context?.chartType) {
          return `${context.stockSymbol} ${context.chartType}图表`;
        }
        if (context?.stockSymbol) {
          return `${context.stockSymbol} 股票图表`;
        }
        return '股票分析图表';

      case 'screenshot':
        if (context?.feature) {
          return `${context.feature} 功能截图`;
        }
        return '应用功能截图';

      case 'avatar':
        if (context?.userName) {
          return `${context.userName} 的头像`;
        }
        return '用户头像';

      case 'icon':
        if (context?.feature) {
          return `${context.feature} 图标`;
        }
        return filename ? `${filename} 图标` : '功能图标';

      case 'decorative':
        return ''; // 装饰性图片不需要 alt

      case 'content':
      default:
        // 尝试从文件名生成描述
        if (filename) {
          return filename.replace(/[-_]/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
        }
        return '内容图片';
    }
  }

  /**
   * 优化图片源
   */
  private optimizeImageSrc(src: string, metadata: ImageMetadata): string {
    // 如果配置了 CDN，使用 CDN 地址
    if (this.config.cdnBaseUrl && !src.startsWith('http')) {
      return `${this.config.cdnBaseUrl}${src}`;
    }

    // 对于本地图片，Next.js 会自动处理优化
    return src;
  }

  /**
   * 计算图片 SEO 分数
   */
  private calculateImageSEOScore(metadata: ImageMetadata, alt: string): number {
    let score = 100;

    // Alt 属性检查
    if (!alt || alt.trim() === '') {
      score -= 30;
    } else {
      if (alt.length > 125) score -= 10;
      if (alt.length < 3) score -= 15;

      // 检查是否包含无用词汇
      const uselessWords = ['图片', '图像', 'image', 'picture', 'photo'];
      const hasUselessWords = uselessWords.some((word) =>
        alt.toLowerCase().includes(word.toLowerCase())
      );
      if (hasUselessWords) score -= 5;
    }

    // 文件大小检查
    if (metadata.fileSize && metadata.fileSize > this.config.maxFileSize) {
      score -= 20;
    }

    // 尺寸检查
    if (metadata.width && metadata.width > this.config.maxWidth) {
      score -= 10;
    }
    if (metadata.height && metadata.height > this.config.maxHeight) {
      score -= 10;
    }

    // 格式检查
    if (metadata.format && !this.config.supportedFormats.includes(metadata.format)) {
      score -= 15;
    }

    return Math.max(0, score);
  }

  /**
   * 生成改进建议
   */
  private generateImprovements(metadata: ImageMetadata, alt: string): string[] {
    const improvements: string[] = [];

    // Alt 属性建议
    if (!alt || alt.trim() === '') {
      improvements.push('添加描述性的 alt 属性');
    } else {
      if (alt.length > 125) {
        improvements.push('缩短 alt 文本到 125 字符以内');
      }
      if (alt.length < 3) {
        improvements.push('使用更具描述性的 alt 文本');
      }

      const uselessWords = ['图片', '图像', 'image', 'picture', 'photo'];
      const hasUselessWords = uselessWords.some((word) =>
        alt.toLowerCase().includes(word.toLowerCase())
      );
      if (hasUselessWords) {
        improvements.push('避免在 alt 文本中使用"图片"、"图像"等冗余词汇');
      }
    }

    // 文件大小建议
    if (metadata.fileSize && metadata.fileSize > this.config.maxFileSize) {
      improvements.push('压缩图片文件大小');
    }

    // 尺寸建议
    if (metadata.width && metadata.width > this.config.maxWidth) {
      improvements.push(`调整图片宽度到 ${this.config.maxWidth}px 以内`);
    }
    if (metadata.height && metadata.height > this.config.maxHeight) {
      improvements.push(`调整图片高度到 ${this.config.maxHeight}px 以内`);
    }

    // 格式建议
    if (metadata.format && !this.config.supportedFormats.includes(metadata.format)) {
      improvements.push('使用支持的图片格式 (JPG, PNG, WebP, AVIF)');
    }

    // WebP 建议
    if (
      this.config.enableWebP &&
      metadata.format &&
      ['jpg', 'jpeg', 'png'].includes(metadata.format)
    ) {
      improvements.push('考虑使用 WebP 格式以获得更好的压缩率');
    }

    return improvements;
  }

  /**
   * 构建优化结果
   */
  private buildOptimizationResult(src: string, metadata: ImageMetadata): ImageOptimizationResult {
    return {
      originalSrc: src,
      optimizedSrc: this.optimizeImageSrc(src, metadata),
      alt: metadata.alt,
      improvements: metadata.issues || [],
      seoScore: metadata.seoScore || 0,
      metadata,
    };
  }

  /**
   * 验证页面中的所有图片
   */
  validatePageImages(document: Document = window.document): {
    totalImages: number;
    validImages: number;
    issues: Array<{
      src: string;
      issues: string[];
      suggestions: string[];
    }>;
    averageScore: number;
  } {
    const images = Array.from(document.querySelectorAll('img'));
    const results = images.map((img) => {
      const src = img.getAttribute('src') || '';
      const alt = img.getAttribute('alt') || '';

      const issues: string[] = [];
      const suggestions: string[] = [];

      // 检查 alt 属性
      if (!alt) {
        issues.push('缺少 alt 属性');
        suggestions.push('添加描述性的 alt 属性');
      } else if (alt.length > 125) {
        issues.push('alt 文本过长');
        suggestions.push('缩短 alt 文本到 125 字符以内');
      }

      // 检查图片尺寸
      if (img.naturalWidth > this.config.maxWidth) {
        issues.push('图片宽度过大');
        suggestions.push(`调整图片宽度到 ${this.config.maxWidth}px 以内`);
      }

      return {
        src,
        issues,
        suggestions,
        score: this.calculateImageSEOScore({ src, alt }, alt),
      };
    });

    const validImages = results.filter((r) => r.issues.length === 0).length;
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / results.length || 0;

    return {
      totalImages: images.length,
      validImages,
      issues: results.filter((r) => r.issues.length > 0),
      averageScore: Math.round(averageScore),
    };
  }

  /**
   * 生成图片 SEO 报告
   */
  generateImageSEOReport(document: Document = window.document): string {
    const validation = this.validatePageImages(document);

    let report = `# 图片 SEO 优化报告\n\n`;
    report += `**总图片数**: ${validation.totalImages}\n`;
    report += `**优化良好**: ${validation.validImages}\n`;
    report += `**需要改进**: ${validation.totalImages - validation.validImages}\n`;
    report += `**平均分数**: ${validation.averageScore}/100\n\n`;

    if (validation.issues.length > 0) {
      report += `## 需要改进的图片\n\n`;
      validation.issues.forEach((issue, index) => {
        report += `### ${index + 1}. ${issue.src}\n`;
        report += `**问题**:\n`;
        issue.issues.forEach((i) => (report += `- ${i}\n`));
        report += `**建议**:\n`;
        issue.suggestions.forEach((s) => (report += `- ${s}\n`));
        report += '\n';
      });
    }

    return report;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.imageCache.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number;
    images: string[];
  } {
    return {
      size: this.imageCache.size,
      images: Array.from(this.imageCache.keys()),
    };
  }
}

/**
 * 创建图片 SEO 管理器实例
 */
export function createImageSEOManager(config?: Partial<ImageSEOConfig>): ImageSEOManager {
  return new ImageSEOManager(config);
}

/**
 * 全局图片 SEO 管理器实例
 */
export const globalImageSEOManager = createImageSEOManager();
