'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  ChartBarIcon,
  NewspaperIcon,
  HeartIcon,
  UserIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AnalysisReport } from '@/lib/api';

interface ReportViewerProps {
  reports: AnalysisReport[];
  selectedAnalysts: string[];
}

export function ReportViewer({ reports, selectedAnalysts }: ReportViewerProps) {
  const [selectedReport, setSelectedReport] = useState<AnalysisReport | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortBy, setSortBy] = useState<'timestamp' | 'type' | 'agent'>('timestamp');
  const [showFilters, setShowFilters] = useState(false);

  const reportTypes = {
    market: { name: '市场分析', icon: ChartBarIcon, color: 'blue' },
    social: { name: '社交媒体', icon: HeartIcon, color: 'pink' },
    news: { name: '新闻分析', icon: NewspaperIcon, color: 'green' },
    fundamentals: { name: '基本面', icon: UserIcon, color: 'purple' },
    research: { name: '研究报告', icon: DocumentTextIcon, color: 'indigo' },
    trading: { name: '交易决策', icon: ChartBarIcon, color: 'orange' },
    risk: { name: '风险评估', icon: DocumentTextIcon, color: 'red' },
    bull_analysis: { name: '多头分析', icon: DocumentTextIcon, color: 'green' },
    bear_analysis: { name: '空头分析', icon: DocumentTextIcon, color: 'red' },
    debate_summary: { name: '辩论总结', icon: DocumentTextIcon, color: 'indigo' },
    consensus: { name: '共识评估', icon: DocumentTextIcon, color: 'purple' },
  };

  const filterOptions = [
    { id: 'all', name: '全部报告' },
    ...Object.entries(reportTypes).map(([key, value]) => ({
      id: key,
      name: value.name,
    })),
  ];

  const sortOptions = [
    { id: 'timestamp', name: '时间排序' },
    { id: 'type', name: '类型排序' },
    { id: 'agent', name: '代理排序' },
  ];

  const filteredReports = reports
    .filter((report) => {
      if (filterType === 'all') return true;
      return report.type === filterType;
    })
    .filter((report) => {
      if (!searchTerm) return true;
      return (
        report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.agent.toLowerCase().includes(searchTerm.toLowerCase())
      );
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'timestamp':
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        case 'type':
          return a.type.localeCompare(b.type);
        case 'agent':
          return a.agent.localeCompare(b.agent);
        default:
          return 0;
      }
    });

  const getReportIcon = (type: string) => {
    const reportType = reportTypes[type as keyof typeof reportTypes];
    if (!reportType) return DocumentTextIcon;
    return reportType.icon;
  };

  const getReportColor = (type: string) => {
    const reportType = reportTypes[type as keyof typeof reportTypes];
    if (!reportType) return 'slate';
    return reportType.color;
  };

  const formatContent = (content: string) => {
    // 简单的 Markdown 格式化
    return content
      .replace(
        /### (.*)/g,
        '<h3 class="text-lg font-semibold mt-4 mb-2 text-slate-900 dark:text-white">$1</h3>'
      )
      .replace(
        /## (.*)/g,
        '<h2 class="text-xl font-bold mt-6 mb-3 text-slate-900 dark:text-white">$1</h2>'
      )
      .replace(
        /# (.*)/g,
        '<h1 class="text-2xl font-bold mt-8 mb-4 text-slate-900 dark:text-white">$1</h1>'
      )
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-semibold text-slate-900 dark:text-white">$1</strong>'
      )
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(
        /`(.*?)`/g,
        '<code class="bg-slate-100 dark:bg-slate-800 px-1 py-0.5 rounded text-sm">$1</code>'
      )
      .replace(/\n\n/g, '</p><p class="mb-4 text-slate-700 dark:text-slate-300">')
      .replace(/\n/g, '<br>');
  };

  const getReportStats = () => {
    const stats = {
      total: reports.length,
      byType: {} as Record<string, number>,
      byAgent: {} as Record<string, number>,
      recent: reports.filter((r) => new Date(r.timestamp).getTime() > Date.now() - 60 * 60 * 1000)
        .length,
    };

    reports.forEach((report) => {
      stats.byType[report.type] = (stats.byType[report.type] || 0) + 1;
      stats.byAgent[report.agent] = (stats.byAgent[report.agent] || 0) + 1;
    });

    return stats;
  };

  const stats = getReportStats();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 报告列表和筛选 */}
      <div className="lg:col-span-1 space-y-4">
        {/* 统计信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>报告统计</span>
              <span className="text-sm font-normal text-slate-500">共 {stats.total} 份</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">总报告数</div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.recent}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">最近1小时</div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                按类型分布:
              </h4>
              {Object.entries(stats.byType)
                .slice(0, 3)
                .map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between text-sm">
                    <span className="text-slate-600 dark:text-slate-400">
                      {reportTypes[type as keyof typeof reportTypes]?.name || type}
                    </span>
                    <span className="font-medium">{count}</span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* 搜索和筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>筛选和搜索</span>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400"
              >
                <FunnelIcon className="h-4 w-4" />
              </button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* 搜索框 */}
            <div className="relative mb-4">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="搜索报告..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-sm"
              />
            </div>

            {/* 筛选选项 */}
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-4"
              >
                <div>
                  <label className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 block">
                    报告类型:
                  </label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="w-full p-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-sm"
                  >
                    {filterOptions.map((option) => (
                      <option key={option.id} value={option.id}>
                        {option.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 block">
                    排序方式:
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'type' | 'agent')}
                    className="w-full p-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-sm"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.id} value={option.id}>
                        {option.name}
                      </option>
                    ))}
                  </select>
                </div>
              </motion.div>
            )}
          </CardContent>
        </Card>

        {/* 报告列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>分析报告</span>
              <span className="text-sm font-normal text-slate-500">({filteredReports.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredReports.length === 0 ? (
                <div className="text-center text-slate-500 py-8">
                  {searchTerm || filterType !== 'all' ? '未找到匹配的报告' : '暂无报告'}
                </div>
              ) : (
                filteredReports.map((report, index) => {
                  const Icon = getReportIcon(report.type);
                  const color = getReportColor(report.type);

                  return (
                    <motion.div
                      key={`${report.type}-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => setSelectedReport(report)}
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                        selectedReport === report
                          ? `border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20 shadow-md`
                          : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div
                          className={`p-2 rounded-lg bg-${color}-100 dark:bg-${color}-900/20 flex-shrink-0`}
                        >
                          <Icon className={`h-4 w-4 text-${color}-600`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 dark:text-white truncate">
                            {report.title}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="text-xs text-slate-500 truncate">{report.agent}</span>
                            <span className="text-xs text-slate-400">•</span>
                            <div className="flex items-center space-x-1 text-xs text-slate-500">
                              <ClockIcon className="h-3 w-3" />
                              <span>{new Date(report.timestamp).toLocaleTimeString()}</span>
                            </div>
                          </div>
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-1 line-clamp-2">
                            {report.content.substring(0, 100)}...
                          </p>
                        </div>
                        {selectedReport === report && (
                          <div className="text-blue-500">
                            <ArrowsUpDownIcon className="h-4 w-4" />
                          </div>
                        )}
                      </div>
                    </motion.div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 报告详情 */}
      <div className="lg:col-span-2">
        <Card className="h-full">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{selectedReport ? selectedReport.title : '选择报告查看详情'}</span>
              {selectedReport && (
                <div className="flex items-center space-x-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs bg-${getReportColor(
                      selectedReport.type
                    )}-100 text-${getReportColor(selectedReport.type)}-800 dark:bg-${getReportColor(
                      selectedReport.type
                    )}-900/20 dark:text-${getReportColor(selectedReport.type)}-400`}
                  >
                    {reportTypes[selectedReport.type as keyof typeof reportTypes]?.name ||
                      selectedReport.type}
                  </span>
                </div>
              )}
            </CardTitle>
            {selectedReport && (
              <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400">
                <span>代理: {selectedReport.agent}</span>
                <span>•</span>
                <span>时间: {new Date(selectedReport.timestamp).toLocaleString()}</span>
                <span>•</span>
                <span>字数: {selectedReport.content.length}</span>
              </div>
            )}
          </CardHeader>
          <CardContent>
            {selectedReport ? (
              <motion.div
                key={selectedReport.timestamp}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                {/* 报告摘要 */}
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    报告摘要
                  </h3>
                  <p className="text-slate-700 dark:text-slate-300">
                    {selectedReport.content.substring(0, 200)}...
                  </p>
                </div>

                {/* 报告内容 */}
                <div className="prose prose-slate dark:prose-invert max-w-none">
                  <div
                    className="text-slate-700 dark:text-slate-300 leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: `<p class="mb-4 text-slate-700 dark:text-slate-300">${formatContent(
                        selectedReport.content
                      )}</p>`,
                    }}
                  />
                </div>

                {/* 报告元数据 */}
                <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">
                        生成时间:
                      </span>
                      <div className="text-slate-600 dark:text-slate-400">
                        {new Date(selectedReport.timestamp).toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">
                        报告类型:
                      </span>
                      <div className="text-slate-600 dark:text-slate-400">
                        {reportTypes[selectedReport.type as keyof typeof reportTypes]?.name ||
                          selectedReport.type}
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-slate-700 dark:text-slate-300">
                        生成代理:
                      </span>
                      <div className="text-slate-600 dark:text-slate-400">
                        {selectedReport.agent}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    导出报告
                  </button>
                  <button className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                    分享报告
                  </button>
                  <button className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                    添加备注
                  </button>
                </div>
              </motion.div>
            ) : (
              <div className="flex items-center justify-center h-64 text-slate-500">
                <div className="text-center">
                  <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
                  <p>请从左侧列表中选择一个报告查看详细内容</p>
                  <p className="text-sm mt-2">
                    {reports.length > 0 ? `共有 ${reports.length} 份报告可查看` : '暂无报告'}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
