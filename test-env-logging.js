/**
 * 测试环境变量日志功能
 * 这个脚本用于验证环境变量打印功能是否正常工作
 */

// 模拟 Next.js 环境
process.env.NODE_ENV = 'development';
process.env.NEXT_PUBLIC_API_BASE_URL = 'http://localhost:3000';
process.env.NEXT_PUBLIC_OPENAI_API_KEY = 'sk-test1234567890abcdef';

// 导入环境变量日志工具
const { logEnvironmentVariables, getEnvironmentSummary } = require('./src/utils/env-logger.ts');

console.log('🧪 测试环境变量日志功能...\n');

try {
    // 测试服务端日志功能
    console.log('1. 测试服务端环境变量打印:');
    logEnvironmentVariables();

    // 测试环境摘要功能
    console.log('\n2. 测试环境摘要功能:');
    const summary = getEnvironmentSummary();
    console.log('环境摘要:', JSON.stringify(summary, null, 2));

    console.log('\n✅ 环境变量日志功能测试通过!');

} catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
}