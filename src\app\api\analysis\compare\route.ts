import LangGraphDatabase from '@/lib/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { workflowIds } = await request.json();

    if (!workflowIds || !Array.isArray(workflowIds) || workflowIds.length < 2) {
      return NextResponse.json(
        {
          success: false,
          error: '请至少选择两个分析进行对比',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    if (workflowIds.length > 5) {
      return NextResponse.json(
        {
          success: false,
          error: '最多只能同时对比5个分析',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // 获取每个工作流的完整状态
    const comparisons = await Promise.all(
      workflowIds.map(async (workflowId: string) => {
        try {
          // 获取工作流基本信息
          const workflow = await LangGraphDatabase.getWorkflow(workflowId);
          if (!workflow) {
            throw new Error(`工作流 ${workflowId} 不存在`);
          }

          // 获取分析报告
          const analystReports = await LangGraphDatabase.getAnalystReports(workflowId);

          // 获取研究报告
          const researchReports = await LangGraphDatabase.getResearchReports(workflowId);

          // 获取最终决策
          const finalDecision = await LangGraphDatabase.getFinalDecision(workflowId);

          // 获取工作流事件（用于计算性能指标）
          const events = await LangGraphDatabase.getWorkflowEvents(workflowId, { limit: 100 });

          return {
            workflow,
            analystReports,
            researchReports,
            finalDecision,
            events,
            // 计算一些对比指标
            metrics: {
              totalReports: analystReports.length + researchReports.length,
              completedAnalysts: analystReports.filter((r) => r.status === 'completed').length,
              totalAnalysts: analystReports.length,
              hasDecision: !!finalDecision,
              errorCount: events.filter((e) => e.event_type === 'error').length,
              duration:
                workflow.completed_at && workflow.started_at
                  ? Math.floor(
                      (new Date(workflow.completed_at).getTime() -
                        new Date(workflow.started_at).getTime()) /
                        1000
                    )
                  : null,
            },
          };
        } catch (error) {
          console.error(`获取工作流 ${workflowId} 数据失败:`, error);
          return {
            workflowId,
            error: error instanceof Error ? error.message : '获取数据失败',
          };
        }
      })
    );

    // 检查是否有错误
    const errors = comparisons.filter((c) => 'error' in c);
    if (errors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `获取数据失败: ${errors.map((e) => e.error).join(', ')}`,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // 生成对比摘要
    const validComparisons = comparisons.filter((c) => !('error' in c)) as any[];
    const summary = {
      totalAnalyses: validComparisons.length,
      tickers: Array.from(new Set(validComparisons.map((c) => c.workflow.ticker))),
      statuses: validComparisons.reduce((acc, c) => {
        acc[c.workflow.status] = (acc[c.workflow.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      avgDuration: validComparisons
        .filter((c) => c.metrics.duration)
        .reduce((sum, c, _, arr) => sum + c.metrics.duration / arr.length, 0),
      successRate:
        validComparisons.filter((c) => c.workflow.status === 'completed').length /
        validComparisons.length,
    };

    return NextResponse.json({
      success: true,
      data: {
        comparisons: validComparisons,
        summary,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('分析对比失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '分析对比失败',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
