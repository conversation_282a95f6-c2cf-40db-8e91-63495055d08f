/**
 * HTML 语义结构优化工具
 * 提供语义化 HTML 结构分析和优化功能
 */

export interface SemanticStructureConfig {
  // 页面类型
  pageType: 'home' | 'analysis' | 'tasks' | 'messages' | 'create-task' | 'article';
  // 主标题
  mainTitle: string;
  // 页面描述
  description?: string;
  // 是否有导航
  hasNavigation?: boolean;
  // 是否有侧边栏
  hasSidebar?: boolean;
  // 是否有页脚
  hasFooter?: boolean;
  // 内容区域数量
  contentSections?: number;
}

export interface HeadingStructure {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  text: string;
  id?: string;
  ariaLabel?: string;
}

export interface AriaAttributes {
  role?: string;
  ariaLabel?: string;
  ariaLabelledBy?: string;
  ariaDescribedBy?: string;
  ariaExpanded?: boolean;
  ariaHidden?: boolean;
  ariaLive?: 'polite' | 'assertive' | 'off';
  ariaAtomic?: boolean;
  ariaRelevant?: 'additions' | 'removals' | 'text' | 'all';
}

export class SemanticStructureOptimizer {
  private config: SemanticStructureConfig;

  constructor(config: SemanticStructureConfig) {
    this.config = config;
  }

  /**
   * 生成页面的语义化结构
   */
  generatePageStructure(): {
    landmarks: Record<string, AriaAttributes>;
    headingStructure: HeadingStructure[];
    skipLinks: Array<{ href: string; text: string }>;
  } {
    const landmarks = this.generateLandmarks();
    const headingStructure = this.generateHeadingStructure();
    const skipLinks = this.generateSkipLinks();

    return {
      landmarks,
      headingStructure,
      skipLinks,
    };
  }

  /**
   * 生成页面地标 (Landmarks)
   */
  private generateLandmarks(): Record<string, AriaAttributes> {
    const landmarks: Record<string, AriaAttributes> = {};

    // 主要导航
    if (this.config.hasNavigation) {
      landmarks.navigation = {
        role: 'navigation',
        ariaLabel: '主导航',
      };
    }

    // 主要内容区域
    landmarks.main = {
      role: 'main',
      ariaLabel: '主要内容',
    };

    // 侧边栏
    if (this.config.hasSidebar) {
      landmarks.aside = {
        role: 'complementary',
        ariaLabel: '相关信息',
      };
    }

    // 页脚
    if (this.config.hasFooter) {
      landmarks.footer = {
        role: 'contentinfo',
        ariaLabel: '页脚信息',
      };
    }

    // 根据页面类型添加特定地标
    switch (this.config.pageType) {
      case 'analysis':
        landmarks.analysisOverview = {
          role: 'region',
          ariaLabel: '分析概览',
        };
        landmarks.analysisResults = {
          role: 'region',
          ariaLabel: '分析结果',
        };
        break;
      case 'tasks':
        landmarks.taskList = {
          role: 'region',
          ariaLabel: '任务列表',
        };
        landmarks.taskFilters = {
          role: 'search',
          ariaLabel: '任务筛选',
        };
        break;
      case 'create-task':
        landmarks.taskForm = {
          role: 'form',
          ariaLabel: '创建任务表单',
        };
        break;
    }

    return landmarks;
  }

  /**
   * 生成标题层级结构
   */
  private generateHeadingStructure(): HeadingStructure[] {
    const headings: HeadingStructure[] = [];

    // 主标题 (H1)
    headings.push({
      level: 1,
      text: this.config.mainTitle,
      id: 'main-title',
      ariaLabel: `页面主标题: ${this.config.mainTitle}`,
    });

    // 根据页面类型生成子标题结构
    switch (this.config.pageType) {
      case 'home':
        headings.push(
          { level: 2, text: '核心功能特性', id: 'features-section' },
          { level: 3, text: '智能分析定制', id: 'custom-analysis' },
          { level: 3, text: '全维度专业分析', id: 'comprehensive-analysis' },
          { level: 3, text: '实时进度跟踪', id: 'real-time-tracking' },
          { level: 2, text: '开始使用', id: 'get-started' }
        );
        break;

      case 'analysis':
        headings.push(
          { level: 2, text: '分析概览', id: 'analysis-overview' },
          { level: 2, text: '智能体团队状态', id: 'agent-status' },
          { level: 3, text: '基本面分析师', id: 'fundamental-analyst' },
          { level: 3, text: '技术分析师', id: 'technical-analyst' },
          { level: 3, text: '情绪分析师', id: 'sentiment-analyst' },
          { level: 3, text: '新闻分析师', id: 'news-analyst' },
          { level: 2, text: '分析报告', id: 'analysis-reports' },
          { level: 2, text: '最终决策', id: 'final-decision' }
        );
        break;

      case 'tasks':
        headings.push(
          { level: 2, text: '任务管理', id: 'task-management' },
          { level: 3, text: '筛选和搜索', id: 'task-filters' },
          { level: 3, text: '任务列表', id: 'task-list' },
          { level: 2, text: '任务统计', id: 'task-statistics' }
        );
        break;

      case 'create-task':
        headings.push(
          { level: 2, text: '基本配置', id: 'basic-config' },
          { level: 3, text: '股票选择', id: 'stock-selection' },
          { level: 3, text: '分析周期', id: 'analysis-period' },
          { level: 2, text: '高级设置', id: 'advanced-settings' },
          { level: 3, text: '分析师选择', id: 'analyst-selection' },
          { level: 3, text: '研究深度', id: 'research-depth' }
        );
        break;

      case 'messages':
        headings.push(
          { level: 2, text: '消息列表', id: 'message-list' },
          { level: 3, text: '筛选选项', id: 'message-filters' },
          { level: 2, text: '消息详情', id: 'message-details' }
        );
        break;
    }

    return headings;
  }

  /**
   * 生成跳转链接
   */
  private generateSkipLinks(): Array<{ href: string; text: string }> {
    const skipLinks = [{ href: '#main-content', text: '跳转到主要内容' }];

    if (this.config.hasNavigation) {
      skipLinks.push({ href: '#main-navigation', text: '跳转到导航菜单' });
    }

    // 根据页面类型添加特定跳转链接
    switch (this.config.pageType) {
      case 'analysis':
        skipLinks.push(
          { href: '#analysis-overview', text: '跳转到分析概览' },
          { href: '#analysis-results', text: '跳转到分析结果' }
        );
        break;
      case 'tasks':
        skipLinks.push(
          { href: '#task-filters', text: '跳转到任务筛选' },
          { href: '#task-list', text: '跳转到任务列表' }
        );
        break;
    }

    return skipLinks;
  }

  /**
   * 验证标题层级结构
   */
  validateHeadingStructure(headings: HeadingStructure[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查是否有 H1
    const h1Count = headings.filter((h) => h.level === 1).length;
    if (h1Count === 0) {
      errors.push('页面缺少 H1 标题');
    } else if (h1Count > 1) {
      errors.push('页面有多个 H1 标题，应该只有一个');
    }

    // 检查标题层级跳跃
    for (let i = 1; i < headings.length; i++) {
      const current = headings[i];
      const previous = headings[i - 1];

      if (current.level > previous.level + 1) {
        warnings.push(
          `标题层级跳跃: H${previous.level} 后直接使用 H${current.level}，建议使用 H${
            previous.level + 1
          }`
        );
      }
    }

    // 检查标题文本长度
    headings.forEach((heading, index) => {
      if (heading.text.length > 60) {
        warnings.push(
          `标题 ${index + 1} 过长 (${heading.text.length} 字符)，建议控制在 60 字符以内`
        );
      }
      if (heading.text.length < 3) {
        warnings.push(`标题 ${index + 1} 过短，建议使用更具描述性的标题`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 生成语义化的 HTML 属性
   */
  generateSemanticAttributes(elementType: string, context?: any): Record<string, any> {
    const attributes: Record<string, any> = {};

    switch (elementType) {
      case 'article':
        attributes.role = 'article';
        if (context?.title) {
          attributes['aria-labelledby'] = `${context.id}-title`;
        }
        break;

      case 'section':
        attributes.role = 'region';
        if (context?.title) {
          attributes['aria-labelledby'] = `${context.id}-title`;
        }
        break;

      case 'nav':
        attributes.role = 'navigation';
        attributes['aria-label'] = context?.label || '导航';
        break;

      case 'button':
        if (context?.expanded !== undefined) {
          attributes['aria-expanded'] = context.expanded;
        }
        if (context?.controls) {
          attributes['aria-controls'] = context.controls;
        }
        if (context?.describedBy) {
          attributes['aria-describedby'] = context.describedBy;
        }
        break;

      case 'form':
        attributes.role = 'form';
        if (context?.label) {
          attributes['aria-label'] = context.label;
        }
        break;

      case 'list':
        attributes.role = 'list';
        if (context?.label) {
          attributes['aria-label'] = context.label;
        }
        break;

      case 'listitem':
        attributes.role = 'listitem';
        break;

      case 'status':
        attributes.role = 'status';
        attributes['aria-live'] = 'polite';
        attributes['aria-atomic'] = 'true';
        break;

      case 'alert':
        attributes.role = 'alert';
        attributes['aria-live'] = 'assertive';
        attributes['aria-atomic'] = 'true';
        break;
    }

    return attributes;
  }

  /**
   * 生成面包屑导航的语义化属性
   */
  generateBreadcrumbAttributes(): Record<string, any> {
    return {
      role: 'navigation',
      'aria-label': '面包屑导航',
    };
  }

  /**
   * 生成表格的语义化属性
   */
  generateTableAttributes(context: {
    caption?: string;
    headers?: string[];
    sortable?: boolean;
  }): Record<string, any> {
    const attributes: Record<string, any> = {
      role: 'table',
    };

    if (context.caption) {
      attributes['aria-label'] = context.caption;
    }

    if (context.sortable) {
      attributes['aria-sort'] = 'none';
    }

    return attributes;
  }
}

/**
 * 创建语义结构优化器实例
 */
export function createSemanticOptimizer(
  config: SemanticStructureConfig
): SemanticStructureOptimizer {
  return new SemanticStructureOptimizer(config);
}

/**
 * 常用的 ARIA 属性生成器
 */
export const ariaHelpers = {
  /**
   * 生成可访问的按钮属性
   */
  button: (options: {
    label?: string;
    expanded?: boolean;
    controls?: string;
    describedBy?: string;
    pressed?: boolean;
  }) => {
    const attrs: Record<string, any> = {};

    if (options.label) attrs['aria-label'] = options.label;
    if (options.expanded !== undefined) attrs['aria-expanded'] = options.expanded;
    if (options.controls) attrs['aria-controls'] = options.controls;
    if (options.describedBy) attrs['aria-describedby'] = options.describedBy;
    if (options.pressed !== undefined) attrs['aria-pressed'] = options.pressed;

    return attrs;
  },

  /**
   * 生成可访问的输入框属性
   */
  input: (options: {
    label?: string;
    describedBy?: string;
    required?: boolean;
    invalid?: boolean;
    errorMessage?: string;
  }) => {
    const attrs: Record<string, any> = {};

    if (options.label) attrs['aria-label'] = options.label;
    if (options.describedBy) attrs['aria-describedby'] = options.describedBy;
    if (options.required) attrs['aria-required'] = 'true';
    if (options.invalid) {
      attrs['aria-invalid'] = 'true';
      if (options.errorMessage) {
        attrs['aria-describedby'] = `${attrs['aria-describedby'] || ''} error-message`.trim();
      }
    }

    return attrs;
  },

  /**
   * 生成可访问的对话框属性
   */
  dialog: (options: {
    label?: string;
    labelledBy?: string;
    describedBy?: string;
    modal?: boolean;
  }) => {
    const attrs: Record<string, any> = {
      role: 'dialog',
    };

    if (options.label) attrs['aria-label'] = options.label;
    if (options.labelledBy) attrs['aria-labelledby'] = options.labelledBy;
    if (options.describedBy) attrs['aria-describedby'] = options.describedBy;
    if (options.modal) attrs['aria-modal'] = 'true';

    return attrs;
  },

  /**
   * 生成可访问的状态指示器属性
   */
  status: (options: { label?: string; live?: 'polite' | 'assertive'; atomic?: boolean }) => {
    const attrs: Record<string, any> = {
      role: 'status',
      'aria-live': options.live || 'polite',
      'aria-atomic': options.atomic !== false ? 'true' : 'false',
    };

    if (options.label) attrs['aria-label'] = options.label;

    return attrs;
  },
};
