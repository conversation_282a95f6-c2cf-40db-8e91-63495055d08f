/**
 * SEO 基础功能测试
 * 验证 SEO 类型定义、配置和工具函数的基本功能
 */

import { describe, it, expect } from '@jest/globals';
import { SEOUtils } from '../utils';
import { MetadataGenerator } from '../metadata-generator';
import { SEO_CONSTANTS, DEFAULT_SEO_CONFIG } from '../config';

describe('SEO 基础架构测试', () => {
  describe('SEO 常量配置', () => {
    it('应该包含所有必需的常量', () => {
      expect(SEO_CONSTANTS.SITE_NAME).toBe('TradingAgents');
      expect(SEO_CONSTANTS.TITLE_MAX_LENGTH).toBe(60);
      expect(SEO_CONSTANTS.DESCRIPTION_MAX_LENGTH).toBe(160);
      expect(SEO_CONSTANTS.DEFAULT_OG_IMAGE).toBe('/tradingAgent.png');
    });

    it('应该有有效的默认 SEO 配置', () => {
      expect(DEFAULT_SEO_CONFIG.title).toBeTruthy();
      expect(DEFAULT_SEO_CONFIG.description).toBeTruthy();
      expect(Array.isArray(DEFAULT_SEO_CONFIG.keywords)).toBe(true);
      expect(DEFAULT_SEO_CONFIG.openGraph).toBeTruthy();
      expect(DEFAULT_SEO_CONFIG.twitter).toBeTruthy();
    });
  });

  describe('SEO 工具函数', () => {
    it('应该正确验证标题', () => {
      expect(SEOUtils.validateTitle('有效标题')).toBe(true);
      expect(SEOUtils.validateTitle('')).toBe(false);
      expect(SEOUtils.validateTitle('a'.repeat(61))).toBe(false);
    });

    it('应该正确验证描述', () => {
      expect(SEOUtils.validateDescription('有效描述')).toBe(true);
      expect(SEOUtils.validateDescription('')).toBe(false);
      expect(SEOUtils.validateDescription('a'.repeat(161))).toBe(false);
    });

    it('应该正确验证关键词', () => {
      expect(SEOUtils.validateKeywords(['关键词1', '关键词2'])).toBe(true);
      expect(SEOUtils.validateKeywords([])).toBe(false);
      expect(SEOUtils.validateKeywords(new Array(11).fill('关键词'))).toBe(false);
    });

    it('应该正确优化标题', () => {
      const title = '测试标题';
      const optimized = SEOUtils.optimizeTitle(title);
      expect(optimized).toContain('TradingAgents');
    });

    it('应该正确生成规范化 URL', () => {
      const url = SEOUtils.generateCanonicalUrl('/test', 'zh');
      expect(url).toMatch(/^https?:\/\/.+\/test$/);
    });

    it('应该正确检测语言', () => {
      expect(SEOUtils.detectLocale('en-US,en;q=0.9')).toBe('en');
      expect(SEOUtils.detectLocale('zh-CN,zh;q=0.9')).toBe('zh');
      expect(SEOUtils.detectLocale('')).toBe('zh');
    });
  });

  describe('元数据生成器', () => {
    let generator: MetadataGenerator;

    beforeEach(() => {
      generator = new MetadataGenerator('zh');
    });

    it('应该能够创建元数据生成器实例', () => {
      expect(generator).toBeInstanceOf(MetadataGenerator);
    });

    it('应该生成有效的页面元数据', () => {
      const metadata = generator.generatePageMetadata({
        page: 'home',
        locale: 'zh',
      });

      expect(metadata.title).toBeTruthy();
      expect(metadata.description).toBeTruthy();
      expect(metadata.openGraph).toBeTruthy();
      expect(metadata.twitter).toBeTruthy();
    });

    it('应该生成有效的结构化数据', () => {
      const structuredData = generator.generateStructuredData('home');

      expect(structuredData.organization).toBeTruthy();
      expect(structuredData.website).toBeTruthy();
      expect(structuredData.softwareApplication).toBeTruthy();
    });

    it('应该生成有效的 JSON-LD 脚本', () => {
      const scripts = generator.generateJsonLdScripts('home');

      expect(Array.isArray(scripts)).toBe(true);
      expect(scripts.length).toBeGreaterThan(0);

      scripts.forEach((script) => {
        expect(() => JSON.parse(script)).not.toThrow();
      });
    });

    it('应该处理动态数据', () => {
      const metadata = generator.generatePageMetadata({
        page: 'analysis',
        dynamicData: {
          stockSymbol: 'AAPL',
          analysisId: '123',
        },
        locale: 'zh',
      });

      expect(metadata.title).toContain('AAPL');
    });

    it('应该验证生成的元数据', () => {
      const metadata = generator.generatePageMetadata({
        page: 'home',
        locale: 'zh',
      });

      const isValid = generator.validateMetadata(metadata);
      expect(isValid).toBe(true);
    });
  });
});
