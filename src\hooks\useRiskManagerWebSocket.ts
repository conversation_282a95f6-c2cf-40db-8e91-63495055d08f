/**
 * 风险管理师 WebSocket Hook
 * 提供风险管理师状态的实时 WebSocket 通信功能
 */

import { RiskManagerProgressUpdate, RiskManagerState } from '@/lib/risk-manager-state';
import { useCallback, useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export interface UseRiskManagerWebSocketOptions {
  workflowId: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  onStateUpdate?: (state: RiskManagerState) => void;
  onProgressUpdate?: (update: RiskManagerProgressUpdate) => void;
  onError?: (error: string) => void;
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
}

export interface UseRiskManagerWebSocketReturn {
  // 连接状态
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;

  // 最新数据
  latestState: RiskManagerState | null;
  latestProgress: RiskManagerProgressUpdate | null;

  // 连接控制
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;

  // 订阅控制
  subscribe: () => void;
  unsubscribe: () => void;
  isSubscribed: boolean;

  // 数据获取
  getAllStates: () => void;

  // 连接统计
  connectionStats: {
    connectTime?: number;
    reconnectCount: number;
    lastError?: string;
    lastErrorTime?: number;
  };
}

export function useRiskManagerWebSocket(
  options: UseRiskManagerWebSocketOptions
): UseRiskManagerWebSocketReturn {
  const {
    workflowId,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 1000,
    onStateUpdate,
    onProgressUpdate,
    onError,
    onConnect,
    onDisconnect,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [latestState, setLatestState] = useState<RiskManagerState | null>(null);
  const [latestProgress, setLatestProgress] = useState<RiskManagerProgressUpdate | null>(null);
  const [connectionStats, setConnectionStats] = useState({
    reconnectCount: 0,
    lastError: undefined as string | undefined,
    lastErrorTime: undefined as number | undefined,
    connectTime: undefined as number | undefined,
  });

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);

  // 连接到 WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      const socket = io({
        path: '/api/risk-manager/ws',
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
      });

      socketRef.current = socket;

      // 连接成功
      socket.on('connect', () => {
        console.log('[Risk Manager WebSocket] Connected to server');
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        setConnectionStats((prev) => ({
          ...prev,
          connectTime: Date.now(),
          reconnectCount: reconnectCountRef.current,
        }));
        reconnectCountRef.current = 0;

        if (onConnect) {
          onConnect();
        }

        // 自动订阅
        if (workflowId) {
          subscribe();
        }
      });

      // 连接失败
      socket.on('connect_error', (err) => {
        console.error('[Risk Manager WebSocket] Connection error:', err);
        setIsConnecting(false);
        setError(`连接失败: ${err.message}`);
        setConnectionStats((prev) => ({
          ...prev,
          lastError: err.message,
          lastErrorTime: Date.now(),
        }));

        if (onError) {
          onError(`连接失败: ${err.message}`);
        }

        // 尝试重连
        if (reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          setConnectionStats((prev) => ({
            ...prev,
            reconnectCount: reconnectCountRef.current,
          }));

          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(
              `[Risk Manager WebSocket] Attempting reconnect ${reconnectCountRef.current}/${reconnectAttempts}`
            );
            connect();
          }, reconnectDelay * reconnectCountRef.current);
        }
      });

      // 断开连接
      socket.on('disconnect', (reason) => {
        console.log('[Risk Manager WebSocket] Disconnected:', reason);
        setIsConnected(false);
        setIsSubscribed(false);

        if (onDisconnect) {
          onDisconnect(reason);
        }

        // 如果是意外断开，尝试重连
        if (reason === 'io server disconnect' || reason === 'transport close') {
          if (reconnectCountRef.current < reconnectAttempts) {
            reconnectCountRef.current++;
            setConnectionStats((prev) => ({
              ...prev,
              reconnectCount: reconnectCountRef.current,
            }));

            reconnectTimeoutRef.current = setTimeout(() => {
              console.log(
                `[Risk Manager WebSocket] Attempting reconnect ${reconnectCountRef.current}/${reconnectAttempts}`
              );
              connect();
            }, reconnectDelay * reconnectCountRef.current);
          }
        }
      });

      // 订阅确认
      socket.on('subscription_confirmed', (data: { workflowId: string; room: string }) => {
        console.log('[Risk Manager WebSocket] Subscription confirmed:', data);
        setIsSubscribed(true);
      });

      // 取消订阅确认
      socket.on('unsubscription_confirmed', (data: { workflowId: string; room: string }) => {
        console.log('[Risk Manager WebSocket] Unsubscription confirmed:', data);
        setIsSubscribed(false);
      });

      // 状态初始化
      socket.on(
        'risk_manager_state_initialized',
        (data: { workflowId: string; state: RiskManagerState }) => {
          if (data.workflowId === workflowId) {
            console.log('[Risk Manager WebSocket] State initialized:', data.state);
            setLatestState(data.state);
            if (onStateUpdate) {
              onStateUpdate(data.state);
            }
          }
        }
      );

      // 状态更新
      socket.on(
        'risk_manager_state_update',
        (data: { workflowId: string; state: RiskManagerState }) => {
          if (data.workflowId === workflowId) {
            console.log('[Risk Manager WebSocket] State updated:', data.state);
            setLatestState(data.state);
            if (onStateUpdate) {
              onStateUpdate(data.state);
            }
          }
        }
      );

      // 进度更新
      socket.on('risk_manager_progress_update', (update: RiskManagerProgressUpdate) => {
        if (update.workflowId === workflowId) {
          console.log('[Risk Manager WebSocket] Progress updated:', update);
          setLatestProgress(update);
          if (onProgressUpdate) {
            onProgressUpdate(update);
          }
        }
      });

      // 错误事件
      socket.on(
        'risk_manager_error',
        (data: { workflowId: string; error: string; timestamp: number }) => {
          if (data.workflowId === workflowId) {
            console.error('[Risk Manager WebSocket] Risk manager error:', data);
            setError(data.error);
            setConnectionStats((prev) => ({
              ...prev,
              lastError: data.error,
              lastErrorTime: data.timestamp,
            }));
            if (onError) {
              onError(data.error);
            }
          }
        }
      );

      // 所有状态响应
      socket.on(
        'all_risk_manager_states',
        (data: { states: RiskManagerState[]; statistics: any }) => {
          console.log('[Risk Manager WebSocket] All states received:', data);
          // 可以在这里处理所有状态数据
        }
      );

      // 通用错误处理
      socket.on('error', (err) => {
        console.error('[Risk Manager WebSocket] Socket error:', err);
        setError(err.message || '未知错误');
        if (onError) {
          onError(err.message || '未知错误');
        }
      });
    } catch (err) {
      console.error('[Risk Manager WebSocket] Failed to create socket:', err);
      setIsConnecting(false);
      setError(err instanceof Error ? err.message : '创建连接失败');
    }
  }, [
    workflowId,
    reconnectAttempts,
    reconnectDelay,
    onConnect,
    onDisconnect,
    onError,
    onStateUpdate,
    onProgressUpdate,
  ]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setIsSubscribed(false);
    reconnectCountRef.current = 0;
  }, []);

  // 重连
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 100);
  }, [disconnect, connect]);

  // 订阅工作流状态
  const subscribe = useCallback(() => {
    if (!socketRef.current?.connected || !workflowId) {
      return;
    }

    socketRef.current.emit('subscribe_risk_manager', { workflowId });
  }, [workflowId]);

  // 取消订阅
  const unsubscribe = useCallback(() => {
    if (!socketRef.current?.connected || !workflowId) {
      return;
    }

    socketRef.current.emit('unsubscribe_risk_manager', { workflowId });
  }, [workflowId]);

  // 获取所有状态
  const getAllStates = useCallback(() => {
    if (!socketRef.current?.connected) {
      return;
    }

    socketRef.current.emit('get_all_risk_manager_states');
  }, []);

  // 自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // 工作流ID变化时重新订阅
  useEffect(() => {
    if (isConnected && workflowId) {
      subscribe();
    }
  }, [isConnected, workflowId, subscribe]);

  return {
    // 连接状态
    isConnected,
    isConnecting,
    error,

    // 最新数据
    latestState,
    latestProgress,

    // 连接控制
    connect,
    disconnect,
    reconnect,

    // 订阅控制
    subscribe,
    unsubscribe,
    isSubscribed,

    // 数据获取
    getAllStates,

    // 连接统计
    connectionStats,
  };
}
