/**
 * 风险指标计算工具函数
 * 提供便捷的风险指标计算接口
 */

import { PriceData, RiskMetrics, riskMetricsCalculator } from './risk-metrics-calculator';
import {
  MonteCarloSimulation,
  riskScenarioAnalyzer,
  StressTestResult,
} from './risk-scenario-analyzer';

/**
 * 从股票数据计算完整风险指标
 */
export async function calculateStockRiskMetrics(
  stockData: PriceData[],
  marketData?: PriceData[],
  riskFreeRate: number = 0.03
): Promise<RiskMetrics> {
  try {
    console.log('[RiskMetricsUtils] Calculating risk metrics for stock data');

    if (!stockData || stockData.length < 30) {
      throw new Error('需要至少30个交易日的数据来计算风险指标');
    }

    const metrics = riskMetricsCalculator.calculateRiskMetrics(stockData, marketData, riskFreeRate);

    console.log('[RiskMetricsUtils] Risk metrics calculation completed');
    return metrics;
  } catch (error) {
    console.error('[RiskMetricsUtils] Error calculating risk metrics:', error);
    throw error;
  }
}

/**
 * 计算投资组合风险指标
 */
export async function calculatePortfolioRiskMetrics(
  positions: Array<{
    symbol: string;
    weight: number;
    priceData: PriceData[];
  }>,
  marketData?: PriceData[]
): Promise<{
  individual_metrics: { [symbol: string]: RiskMetrics };
  portfolio_metrics: {
    weighted_volatility: number;
    portfolio_beta: number;
    portfolio_var: number;
    concentration_risk: any;
    diversification_ratio: number;
  };
}> {
  console.log('[RiskMetricsUtils] Calculating portfolio risk metrics');

  const individualMetrics: { [symbol: string]: RiskMetrics } = {};
  let weightedVolatility = 0;
  let weightedBeta = 0;
  let weightedVar = 0;

  // 计算每个持仓的风险指标
  for (const position of positions) {
    const metrics = await calculateStockRiskMetrics(position.priceData, marketData);

    individualMetrics[position.symbol] = metrics;

    // 计算加权指标
    weightedVolatility += position.weight * metrics.volatility.annualized_volatility;
    weightedBeta += position.weight * metrics.ratios.beta;
    weightedVar += position.weight * metrics.var.var_95_1d;
  }

  // 计算集中度风险
  const concentrationRisk = riskMetricsCalculator.calculateConcentrationRisk(
    positions.map((pos) => ({
      symbol: pos.symbol,
      weight: pos.weight,
      returns: pos.priceData
        .slice(1)
        .map((data, i) => (data.close - pos.priceData[i].close) / pos.priceData[i].close),
    }))
  );

  // 计算分散化比率
  const portfolioVolatility = Math.sqrt(
    positions.reduce((sum, pos1, i) => {
      return (
        sum +
        positions.reduce((innerSum, pos2, j) => {
          const corr = i === j ? 1 : 0.5; // 简化相关性假设
          const vol1 = individualMetrics[pos1.symbol].volatility.annualized_volatility;
          const vol2 = individualMetrics[pos2.symbol].volatility.annualized_volatility;
          return innerSum + pos1.weight * pos2.weight * vol1 * vol2 * corr;
        }, 0)
      );
    }, 0)
  );

  const diversificationRatio = weightedVolatility / portfolioVolatility;

  return {
    individual_metrics: individualMetrics,
    portfolio_metrics: {
      weighted_volatility: weightedVolatility,
      portfolio_beta: weightedBeta,
      portfolio_var: weightedVar,
      concentration_risk: concentrationRisk,
      diversification_ratio: diversificationRatio,
    },
  };
}

/**
 * 生成风险报告摘要
 */
export function generateRiskSummary(metrics: RiskMetrics): {
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
  risk_score: number;
  key_risks: string[];
  recommendations: string[];
} {
  console.log('[RiskMetricsUtils] Generating risk summary');

  let riskScore = 0;
  const keyRisks: string[] = [];
  const recommendations: string[] = [];

  // 波动率风险评估
  if (metrics.volatility.annualized_volatility > 0.3) {
    riskScore += 30;
    keyRisks.push('高波动率风险');
    recommendations.push('考虑降低仓位或使用期权对冲');
  } else if (metrics.volatility.annualized_volatility > 0.2) {
    riskScore += 15;
    keyRisks.push('中等波动率风险');
  }

  // VaR风险评估
  if (metrics.var.var_95_1d > 0.05) {
    riskScore += 25;
    keyRisks.push('高VaR风险');
    recommendations.push('设置严格的止损位');
  } else if (metrics.var.var_95_1d > 0.03) {
    riskScore += 10;
    keyRisks.push('中等VaR风险');
  }

  // 回撤风险评估
  if (metrics.drawdown.max_drawdown > 0.2) {
    riskScore += 20;
    keyRisks.push('高回撤风险');
    recommendations.push('分散投资降低回撤风险');
  } else if (metrics.drawdown.max_drawdown > 0.1) {
    riskScore += 10;
    keyRisks.push('中等回撤风险');
  }

  // 流动性风险评估
  if (metrics.liquidity.market_depth_score < 50) {
    riskScore += 15;
    keyRisks.push('流动性风险');
    recommendations.push('注意交易时机，避免大额交易');
  }

  // Beta风险评估
  if (Math.abs(metrics.ratios.beta) > 1.5) {
    riskScore += 10;
    keyRisks.push('高Beta风险');
    recommendations.push('关注市场整体走势');
  }

  // 确定风险等级
  let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  if (riskScore >= 60) {
    riskLevel = 'HIGH';
  } else if (riskScore >= 30) {
    riskLevel = 'MEDIUM';
  } else {
    riskLevel = 'LOW';
  }

  // 添加通用建议
  if (riskLevel === 'HIGH') {
    recommendations.push('建议暂缓投资或大幅降低仓位');
  } else if (riskLevel === 'MEDIUM') {
    recommendations.push('适度投资，密切监控风险指标');
  } else {
    recommendations.push('风险可控，可以正常投资');
  }

  return {
    risk_level: riskLevel,
    risk_score: riskScore,
    key_risks: keyRisks,
    recommendations: recommendations,
  };
}

/**
 * 格式化风险指标用于显示
 */
export function formatRiskMetrics(metrics: RiskMetrics): {
  [category: string]: { [metric: string]: string };
} {
  return {
    volatility: {
      日波动率: `${(metrics.volatility.daily_volatility * 100).toFixed(2)}%`,
      年化波动率: `${(metrics.volatility.annualized_volatility * 100).toFixed(2)}%`,
      波动率趋势: `${(metrics.volatility.volatility_trend * 100).toFixed(2)}%`,
      波动率百分位: `${metrics.volatility.volatility_percentile.toFixed(1)}%`,
    },
    var: {
      '95% VaR (1日)': `${(metrics.var.var_95_1d * 100).toFixed(2)}%`,
      '99% VaR (1日)': `${(metrics.var.var_99_1d * 100).toFixed(2)}%`,
      '95% VaR (10日)': `${(metrics.var.var_95_10d * 100).toFixed(2)}%`,
      '预期损失 (95%)': `${(metrics.var.expected_shortfall_95 * 100).toFixed(2)}%`,
    },
    drawdown: {
      最大回撤: `${(metrics.drawdown.max_drawdown * 100).toFixed(2)}%`,
      回撤持续时间: `${metrics.drawdown.max_drawdown_duration}天`,
      当前回撤: `${(metrics.drawdown.current_drawdown * 100).toFixed(2)}%`,
      回撤频率: `${metrics.drawdown.drawdown_frequency.toFixed(1)}次/年`,
    },
    ratios: {
      夏普比率: metrics.ratios.sharpe_ratio.toFixed(3),
      Sortino比率: metrics.ratios.sortino_ratio.toFixed(3),
      Calmar比率: metrics.ratios.calmar_ratio.toFixed(3),
      Beta系数: metrics.ratios.beta.toFixed(3),
      Treynor比率: metrics.ratios.treynor_ratio.toFixed(3),
    },
    liquidity: {
      Amihud比率: metrics.liquidity.amihud_ratio.toExponential(2),
      换手率: `${(metrics.liquidity.turnover_rate * 100).toFixed(2)}%`,
      价格冲击: `${(metrics.liquidity.price_impact * 100).toFixed(4)}%`,
      市场深度评分: `${metrics.liquidity.market_depth_score.toFixed(1)}/100`,
    },
    correlation: {
      市场相关性: metrics.correlation.market_correlation.toFixed(3),
      行业相关性: metrics.correlation.sector_correlation.toFixed(3),
      相关性稳定性: `${(metrics.correlation.correlation_stability * 100).toFixed(1)}%`,
      分散化比率: metrics.correlation.diversification_ratio.toFixed(3),
    },
  };
}

/**
 * 验证风险指标数据质量
 */
export function validateRiskMetrics(metrics: RiskMetrics): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // 检查波动率
  if (metrics.volatility.annualized_volatility <= 0) {
    errors.push('年化波动率必须大于0');
  }
  if (metrics.volatility.annualized_volatility > 2) {
    warnings.push('年化波动率异常高，可能存在数据质量问题');
  }

  // 检查VaR
  if (metrics.var.var_95_1d <= 0) {
    errors.push('VaR值必须大于0');
  }
  if (metrics.var.var_99_1d <= metrics.var.var_95_1d) {
    warnings.push('99% VaR应该大于95% VaR');
  }

  // 检查回撤
  if (metrics.drawdown.max_drawdown < 0) {
    errors.push('最大回撤不能为负数');
  }
  if (metrics.drawdown.max_drawdown > 0.8) {
    warnings.push('最大回撤超过80%，风险极高');
  }

  // 检查Beta
  if (Math.abs(metrics.ratios.beta) > 5) {
    warnings.push('Beta系数异常，可能存在计算错误');
  }

  // 检查流动性指标
  if (metrics.liquidity.market_depth_score < 0 || metrics.liquidity.market_depth_score > 100) {
    errors.push('市场深度评分必须在0-100之间');
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
}

/**
 * 需求 5.1-5.5: 运行综合风险场景分析和压力测试
 */
export async function runComprehensiveRiskScenarioAnalysis(
  priceData: PriceData[],
  marketData?: PriceData[]
): Promise<{
  basic_metrics: RiskMetrics;
  stress_test: StressTestResult;
  monte_carlo: MonteCarloSimulation;
  scenario_summary: {
    overall_risk_score: number;
    key_vulnerabilities: string[];
    stress_test_recommendations: string[];
  };
}> {
  console.log('[RiskMetricsUtils] Running comprehensive risk scenario analysis');

  // 计算基础风险指标
  const basicMetrics = await calculateStockRiskMetrics(priceData, marketData);

  // 运行综合压力测试
  const stressTest = riskScenarioAnalyzer.runComprehensiveStressTest(priceData, marketData);

  // 运行蒙特卡洛模拟
  const monteCarlo = riskScenarioAnalyzer.runMonteCarloSimulation(priceData, 10000, 252);

  // 生成综合评估
  const scenarioSummary = generateScenarioSummary(basicMetrics, stressTest, monteCarlo);

  console.log('[RiskMetricsUtils] Comprehensive risk scenario analysis completed');

  return {
    basic_metrics: basicMetrics,
    stress_test: stressTest,
    monte_carlo: monteCarlo,
    scenario_summary: scenarioSummary,
  };
}

/**
 * 需求 5.1: 市场下跌场景分析
 */
export async function analyzeMarketDeclineScenarios(
  priceData: PriceData[],
  marketData?: PriceData[]
): Promise<{
  scenarios: any[];
  worst_case_impact: number;
  probability_weighted_loss: number;
  recommendations: string[];
}> {
  console.log('[RiskMetricsUtils] Analyzing market decline scenarios');

  const scenarios = riskScenarioAnalyzer.simulateMarketDeclineScenarios(priceData, marketData);
  const expectedLoss = riskScenarioAnalyzer.calculateScenarioExpectedLoss(scenarios);

  const recommendations: string[] = [];
  if (expectedLoss.worst_case_loss > 0.25) {
    recommendations.push('市场下跌风险极高，建议大幅降低仓位');
    recommendations.push('考虑购买保护性看跌期权');
  } else if (expectedLoss.worst_case_loss > 0.15) {
    recommendations.push('市场下跌风险较高，建议适度降低仓位');
    recommendations.push('设置严格的止损位');
  } else {
    recommendations.push('市场下跌风险可控，保持正常投资策略');
  }

  return {
    scenarios: scenarios,
    worst_case_impact: expectedLoss.worst_case_loss,
    probability_weighted_loss: expectedLoss.weighted_expected_loss,
    recommendations: recommendations,
  };
}

/**
 * 需求 5.4: 蒙特卡洛风险模拟
 */
export async function runMonteCarloRiskSimulation(
  priceData: PriceData[],
  numSimulations: number = 10000,
  timeHorizon: number = 252
): Promise<{
  simulation_results: MonteCarloSimulation;
  risk_assessment: {
    probability_of_loss: number;
    expected_return: number;
    downside_risk: number;
    upside_potential: number;
  };
  recommendations: string[];
}> {
  console.log('[RiskMetricsUtils] Running Monte Carlo risk simulation');

  const simulationResults = riskScenarioAnalyzer.runMonteCarloSimulation(
    priceData,
    numSimulations,
    timeHorizon
  );

  // 计算风险评估指标
  const currentPrice = priceData[priceData.length - 1].close;
  const expectedReturn = (simulationResults.results.percentiles.p50 - currentPrice) / currentPrice;
  const downsideRisk = (simulationResults.results.percentiles.p5 - currentPrice) / currentPrice;
  const upsidePotential = (simulationResults.results.percentiles.p95 - currentPrice) / currentPrice;

  // 生成建议
  const recommendations: string[] = [];
  if (simulationResults.results.risk_metrics.probability_of_loss > 0.6) {
    recommendations.push('模拟显示亏损概率较高，建议谨慎投资');
  }
  if (Math.abs(downsideRisk) > 0.3) {
    recommendations.push('下行风险较大，建议设置保护性止损');
  }
  if (expectedReturn > 0.1) {
    recommendations.push('预期收益较好，但需注意风险控制');
  }

  return {
    simulation_results: simulationResults,
    risk_assessment: {
      probability_of_loss: simulationResults.results.risk_metrics.probability_of_loss,
      expected_return: expectedReturn,
      downside_risk: downsideRisk,
      upside_potential: upsidePotential,
    },
    recommendations: recommendations,
  };
}

/**
 * 生成场景分析综合评估
 */
function generateScenarioSummary(
  basicMetrics: RiskMetrics,
  stressTest: StressTestResult,
  monteCarlo: MonteCarloSimulation
): {
  overall_risk_score: number;
  key_vulnerabilities: string[];
  stress_test_recommendations: string[];
} {
  // 计算综合风险评分 (0-100, 分数越低风险越高)
  let riskScore = 50; // 基础分

  // 基于基础指标调整
  if (basicMetrics.volatility.annualized_volatility > 0.3) riskScore -= 15;
  if (basicMetrics.var.var_95_1d > 0.05) riskScore -= 10;
  if (basicMetrics.drawdown.max_drawdown > 0.2) riskScore -= 10;

  // 基于压力测试调整
  riskScore = Math.min(riskScore, stressTest.summary.stress_test_score);

  // 基于蒙特卡洛模拟调整
  if (monteCarlo.results.risk_metrics.probability_of_loss > 0.6) riskScore -= 10;

  // 识别关键脆弱性
  const keyVulnerabilities: string[] = [];
  if (basicMetrics.volatility.annualized_volatility > 0.4) {
    keyVulnerabilities.push('极高波动率风险');
  }
  if (stressTest.summary.worst_case_loss > 0.3) {
    keyVulnerabilities.push('压力测试显示极端损失风险');
  }
  if (monteCarlo.results.risk_metrics.probability_of_loss > 0.7) {
    keyVulnerabilities.push('蒙特卡洛模拟显示高亏损概率');
  }
  if (basicMetrics.correlation.market_correlation > 0.8) {
    keyVulnerabilities.push('与市场高度相关，系统性风险高');
  }

  return {
    overall_risk_score: Math.max(0, Math.min(100, riskScore)),
    key_vulnerabilities: keyVulnerabilities,
    stress_test_recommendations: stressTest.recommendations,
  };
}
