# TradingAgents Frontend 数据备份

## 备份文件说明

### `full-database-backup.sql` - 完整数据备份

- **备份时间**: 2025-07-29T04:15:00.000Z
- **适用场景**: **全新数据库环境**
- **特点**: 使用 `INSERT IGNORE` 避免重复插入

### `safe-data-restore.sql` - 安全数据恢复

- **备份时间**: 2025-07-29T04:20:00.000Z
- **适用场景**: **现有数据库环境**
- **特点**: 使用 `ON DUPLICATE KEY UPDATE` 处理重复记录

#### 包含数据

- **用户数据** (3 条记录)

  - 测试用户 (<EMAIL>)
  - 管理员 (<EMAIL>)
  - 真实用户 (<EMAIL>)

- **任务数据** (6 条记录)

  - AAPL - Apple 股票分析
  - TSLA - Tesla 技术分析
  - NVDA - NVIDIA 基本面分析
  - MSFT - Microsoft 综合分析
  - GOOGL - Google 快速分析
  - 688111 - 688111 股票市场简要分析报告 (已完成)

- **分析结果** (1 条记录)
  - 688111 股票的综合分析结果
  - 包含新闻分析、情绪分析、技术分析和基本面分析

## 使用方法

### 场景 1: 全新数据库环境

```bash
# 1. 首先创建数据库结构
mysql -u username -p database_name < database/init.sql

# 2. 然后恢复数据
mysql -u username -p database_name < database/backups/full-database-backup.sql
```

### 场景 2: 现有数据库环境 (推荐)

```bash
# 直接执行安全恢复脚本
mysql -u username -p database_name < database/backups/safe-data-restore.sql
```

### 验证恢复结果

```sql
-- 检查用户数据
SELECT COUNT(*) FROM users;

-- 检查任务数据
SELECT COUNT(*) FROM tasks;

-- 检查分析结果
SELECT COUNT(*) FROM analysis_results;
```

## 文件选择指南

| 场景     | 推荐文件                   | 说明                     |
| -------- | -------------------------- | ------------------------ |
| 全新部署 | `full-database-backup.sql` | 空数据库，直接插入数据   |
| 现有环境 | `safe-data-restore.sql`    | 有数据的数据库，安全更新 |
| 数据同步 | `safe-data-restore.sql`    | 同步最新数据到现有环境   |

## 注意事项

1. **数据完整性**: 备份包含完整的用户认证信息和密码哈希
2. **外键约束**: 备份文件会自动处理外键约束
3. **字符编码**: 使用 UTF8MB4 编码，支持中文和特殊字符
4. **重复处理**: 安全恢复脚本会智能处理重复数据

## 备份历史

- 2025-07-29: 创建完整数据备份和安全恢复脚本
