/**
 * 页面 SEO 组件
 * 提供完整的页面 SEO 支持，包括元数据和结构化数据
 */

'use client';

import { useEffect } from 'react';
import { MetadataGenerator } from '@/lib/seo/metadata-generator';
import { generatePageStructuredData } from '@/lib/seo/structured-data-generator';
import { validateStructuredData } from '@/lib/seo/structured-data-validator';
import { SEOUtils } from '@/lib/seo/utils';
import { PageType, SupportedLocale } from '@/types/seo';

interface PageSEOProps {
  page: PageType;
  title?: string;
  description?: string;
  keywords?: string[];
  dynamicData?: {
    stockSymbol?: string;
    analysisId?: string;
    taskId?: string;
    taskTitle?: string;
    analysisType?: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk';
    recommendation?: 'Buy' | 'Sell' | 'Hold';
    targetPrice?: number;
  };
  locale?: SupportedLocale;
  enableStructuredData?: boolean;
  enableValidation?: boolean;
}

/**
 * 页面 SEO 组件
 * 在客户端动态更新页面的 SEO 信息
 */
export function PageSEO({
  page,
  title,
  description,
  keywords,
  dynamicData,
  locale = 'zh',
  enableStructuredData = true,
  enableValidation = true,
}: PageSEOProps) {
  useEffect(() => {
    try {
      // 更新页面标题
      if (title) {
        document.title = title;
      } else {
        const generator = new MetadataGenerator(locale);
        const metadata = generator.generatePageMetadata({ page, dynamicData, locale });
        if (metadata.title && typeof metadata.title === 'string') {
          document.title = metadata.title;
        }
      }

      // 更新页面描述
      const descriptionMeta = document.querySelector('meta[name="description"]');
      if (description && descriptionMeta) {
        descriptionMeta.setAttribute('content', description);
      }

      // 更新关键词
      const keywordsMeta = document.querySelector('meta[name="keywords"]');
      if (keywords && keywordsMeta) {
        keywordsMeta.setAttribute('content', keywords.join(', '));
      }

      // 添加结构化数据
      if (enableStructuredData) {
        addStructuredDataToPage(page, dynamicData, locale, enableValidation);
      }
    } catch (error) {
      console.error('Error updating page SEO:', error);
    }
  }, [
    page,
    title,
    description,
    keywords,
    dynamicData,
    locale,
    enableStructuredData,
    enableValidation,
  ]);

  return null; // 这是一个副作用组件，不渲染任何内容
}

/**
 * 添加结构化数据到页面
 */
function addStructuredDataToPage(
  page: PageType,
  dynamicData?: any,
  locale: SupportedLocale = 'zh',
  enableValidation: boolean = true
) {
  try {
    // 移除现有的动态结构化数据
    const existingScripts = document.querySelectorAll('script[data-seo-dynamic="true"]');
    existingScripts.forEach((script) => script.remove());

    // 生成新的结构化数据
    const structuredData = generatePageStructuredData(page, locale, dynamicData);

    // 为每个结构化数据对象创建脚本标签
    Object.entries(structuredData).forEach(([key, data]) => {
      if (data) {
        // 验证结构化数据（如果启用）
        if (enableValidation) {
          const validation = validateStructuredData(data);
          if (!validation.isValid) {
            console.warn(`Invalid structured data for ${key}:`, validation.errors);
            return;
          }
        }

        // 创建脚本标签
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.setAttribute('data-seo-dynamic', 'true');
        script.setAttribute('data-seo-type', key);
        script.textContent = JSON.stringify(data, null, 2);

        // 添加到页面头部
        document.head.appendChild(script);
      }
    });
  } catch (error) {
    console.error('Error adding structured data to page:', error);
  }
}

/**
 * 股票分析页面 SEO 组件
 */
export function StockAnalysisSEO({
  stockSymbol,
  analysisId,
  analysisType = 'Technical',
  recommendation,
  targetPrice,
  locale = 'zh',
}: {
  stockSymbol: string;
  analysisId?: string;
  analysisType?: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk';
  recommendation?: 'Buy' | 'Sell' | 'Hold';
  targetPrice?: number;
  locale?: SupportedLocale;
}) {
  const analysisTypeLabels = {
    zh: {
      Technical: '技术分析',
      Fundamental: '基本面分析',
      Sentiment: '情绪分析',
      Risk: '风险分析',
    },
    en: {
      Technical: 'Technical Analysis',
      Fundamental: 'Fundamental Analysis',
      Sentiment: 'Sentiment Analysis',
      Risk: 'Risk Analysis',
    },
  };

  const title =
    locale === 'zh'
      ? `${stockSymbol} ${analysisTypeLabels.zh[analysisType]} - TradingAgents`
      : `${stockSymbol} ${analysisTypeLabels.en[analysisType]} - TradingAgents`;

  const description =
    locale === 'zh'
      ? `${stockSymbol} 的专业${analysisTypeLabels.zh[analysisType]}报告，基于多智能体AI协作分析`
      : `Professional ${analysisTypeLabels.en[analysisType]} report for ${stockSymbol}, based on multi-agent AI collaborative analysis`;

  const keywords =
    locale === 'zh'
      ? [
          stockSymbol,
          '股票分析',
          analysisTypeLabels.zh[analysisType],
          'AI分析',
          '多智能体',
          '交易决策',
        ]
      : [
          stockSymbol,
          'stock analysis',
          analysisTypeLabels.en[analysisType],
          'AI analysis',
          'multi-agent',
          'trading decision',
        ];

  return (
    <PageSEO
      page="analysis"
      title={title}
      description={description}
      keywords={keywords}
      dynamicData={{
        stockSymbol,
        analysisId,
        analysisType,
        recommendation,
        targetPrice,
      }}
      locale={locale}
    />
  );
}

/**
 * 任务页面 SEO 组件
 */
export function TaskPageSEO({
  taskId,
  taskTitle,
  locale = 'zh',
}: {
  taskId?: string;
  taskTitle?: string;
  locale?: SupportedLocale;
}) {
  const title = taskTitle
    ? `${taskTitle} - 任务管理 - TradingAgents`
    : locale === 'zh'
    ? '任务管理 - TradingAgents'
    : 'Task Management - TradingAgents';

  const description =
    locale === 'zh'
      ? '管理和监控多智能体金融分析任务，实时跟踪分析进度和结果'
      : 'Manage and monitor multi-agent financial analysis tasks, track analysis progress and results in real-time';

  const keywords =
    locale === 'zh'
      ? ['任务管理', '金融分析', '多智能体', 'AI任务', '分析监控']
      : ['task management', 'financial analysis', 'multi-agent', 'AI tasks', 'analysis monitoring'];

  return (
    <PageSEO
      page="tasks"
      title={title}
      description={description}
      keywords={keywords}
      dynamicData={{
        taskId,
        taskTitle,
      }}
      locale={locale}
    />
  );
}

/**
 * 消息页面 SEO 组件
 */
export function MessagePageSEO({ locale = 'zh' }: { locale?: SupportedLocale }) {
  const title = locale === 'zh' ? '消息查询 - TradingAgents' : 'Messages - TradingAgents';

  const description =
    locale === 'zh'
      ? '查看和管理多智能体分析过程中的消息记录和通信日志'
      : 'View and manage message records and communication logs during multi-agent analysis processes';

  const keywords =
    locale === 'zh'
      ? ['消息查询', '通信日志', '分析记录', '多智能体通信']
      : ['message query', 'communication logs', 'analysis records', 'multi-agent communication'];

  return (
    <PageSEO
      page="messages"
      title={title}
      description={description}
      keywords={keywords}
      locale={locale}
    />
  );
}

/**
 * 创建任务页面 SEO 组件
 */
export function CreateTaskPageSEO({ locale = 'zh' }: { locale?: SupportedLocale }) {
  const title =
    locale === 'zh' ? '创建分析任务 - TradingAgents' : 'Create Analysis Task - TradingAgents';

  const description =
    locale === 'zh'
      ? '创建新的多智能体金融分析任务，配置分析参数和目标股票'
      : 'Create new multi-agent financial analysis tasks, configure analysis parameters and target stocks';

  const keywords =
    locale === 'zh'
      ? ['创建任务', '分析配置', '股票分析', '多智能体设置']
      : ['create task', 'analysis configuration', 'stock analysis', 'multi-agent setup'];

  return (
    <PageSEO
      page="create-task"
      title={title}
      description={description}
      keywords={keywords}
      locale={locale}
    />
  );
}
