// State Synchronization Service
// Handles real-time state updates, caching synchronization, and notification services
// Requirements: 需求 1.6, 需求 2.1, 需求 2.2, 需求 2.3, 需求 9.1

import { EventEmitter } from 'events';
import { analysisService } from './analysis-service';
import { EnhancedLangGraphDatabase } from './enhanced-langgraph-database';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface AgentStateChangeEvent {
  workflowId: string;
  agentType: string;
  previousStatus: string;
  newStatus: string;
  executionTime?: number;
  result?: any;
  error?: string;
  timestamp: Date;
}

export interface WorkflowStatusUpdate {
  workflowId: string;
  status: string;
  progress: number;
  currentStage: string;
  error?: string;
  timestamp: Date;
}

export interface NotificationEvent {
  type: 'analysis_complete' | 'analysis_error' | 'agent_complete' | 'status_update';
  workflowId: string;
  taskId?: string;
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface CacheEntry {
  key: string;
  data: any;
  timestamp: Date;
  ttl: number; // Time to live in milliseconds
}

// ============================================================================
// State Synchronization Service
// ============================================================================

export class StateSyncService extends EventEmitter {
  private cache: Map<string, CacheEntry> = new Map();
  private subscribers: Map<string, Set<string>> = new Map(); // workflowId -> Set of subscriber IDs
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    // Increase max listeners to avoid warnings in tests
    this.setMaxListeners(20);
    this.setupEventHandlers();
    this.startCacheCleanup();
  }

  // ============================================================================
  // Agent State Change Listeners
  // ============================================================================

  /**
   * Listen for agent state changes and update database
   * Requirements: 需求 1.6, 需求 2.1, 需求 2.2
   */
  async handleAgentStateChange(event: AgentStateChangeEvent): Promise<void> {
    try {
      // Update database with new agent state
      await EnhancedLangGraphDatabase.batchUpdateAgentStates([
        {
          workflow_id: event.workflowId,
          analyst_type: event.agentType,
          status: event.newStatus as 'completed' | 'failed',
          execution_time_ms: event.executionTime,
        },
      ]);

      // Update cache
      await this.updateWorkflowCache(event.workflowId);

      // Log the state change event
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: event.workflowId,
        stage_name: `agent_${event.agentType}`,
        event_type: 'state_change',
        content: `智能体 ${event.agentType} 状态变更: ${event.previousStatus} -> ${event.newStatus}`,
        metadata: {
          agentType: event.agentType,
          previousStatus: event.previousStatus,
          newStatus: event.newStatus,
          executionTime: event.executionTime,
          result: event.result,
          error: event.error,
        },
      });

      // Broadcast state update to subscribers
      await this.broadcastStateUpdate(event.workflowId, {
        type: 'agent_state_change',
        agentType: event.agentType,
        status: event.newStatus,
        executionTime: event.executionTime,
        result: event.result,
        error: event.error,
      });

      // Send notification if agent completed or failed
      if (event.newStatus === 'completed' || event.newStatus === 'failed') {
        await this.sendAgentCompletionNotification(event);
      }

      this.emit('agentStateChanged', event);
    } catch (error) {
      console.error('处理智能体状态变化失败:', error);
      this.emit('error', { type: 'agent_state_change_error', error, event });
    }
  }

  /**
   * Handle workflow status updates with enhanced caching and error handling
   * Requirements: 需求 2.1, 需求 2.2, 需求 2.3
   */
  async handleWorkflowStatusUpdate(update: WorkflowStatusUpdate): Promise<void> {
    try {
      // Update database
      await EnhancedLangGraphDatabase.updateWorkflowStatus({
        workflow_id: update.workflowId,
        current_stage: update.currentStage,
        progress: update.progress,
        status: update.status as any,
        error_message: update.error || undefined,
      });

      // Invalidate old cache entries first
      this.invalidateWorkflowCaches(update.workflowId);

      // Update cache with new data
      await this.updateWorkflowCache(update.workflowId);

      // Broadcast to subscribers with enhanced data
      await this.broadcastStateUpdate(update.workflowId, {
        type: 'workflow_status_update',
        status: update.status,
        progress: update.progress,
        currentStage: update.currentStage,
        error: update.error,
        previousStage: await this.getPreviousStage(update.workflowId),
        updateSource: 'workflow_status_handler',
      });

      // Send notification for significant status changes
      if (update.status === 'completed' || update.status === 'failed') {
        await this.sendWorkflowCompletionNotification(update);
      }

      // Log the status update
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: update.workflowId,
        stage_name: update.currentStage,
        event_type: 'log',
        content: `工作流状态更新: ${update.status} (${update.progress}%)`,
        metadata: {
          status: update.status,
          progress: update.progress,
          currentStage: update.currentStage,
          error: update.error,
          timestamp: update.timestamp,
        },
      });

      this.emit('workflowStatusUpdated', update);
    } catch (error) {
      console.error('处理工作流状态更新失败:', error);

      // Try to recover by invalidating cache and retrying once
      try {
        this.invalidateWorkflowCaches(update.workflowId);
        await this.updateWorkflowCache(update.workflowId);
        console.log(`工作流 ${update.workflowId} 状态更新恢复成功`);
      } catch (recoveryError) {
        console.error('状态更新恢复失败:', recoveryError);
      }

      this.emit('error', { type: 'workflow_status_update_error', error, update });
    }
  }

  /**
   * Get previous stage for comparison
   */
  private async getPreviousStage(workflowId: string): Promise<string | undefined> {
    try {
      const cached = this.getFromCache(`workflow_${workflowId}_basic`);
      return cached?.currentStage;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Handle batch state updates for multiple workflows
   * Requirements: 需求 6.4, 需求 6.5
   */
  async handleBatchStateUpdates(updates: WorkflowStatusUpdate[]): Promise<void> {
    const batchSize = 10; // Process in batches to avoid overwhelming the system

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);

      // Process batch in parallel
      const promises = batch.map((update) =>
        this.handleWorkflowStatusUpdate(update).catch((error) => {
          console.error(`批量更新失败 (工作流 ${update.workflowId}):`, error);
          return null; // Continue with other updates
        })
      );

      await Promise.all(promises);

      // Small delay between batches to prevent overwhelming the database
      if (i + batchSize < updates.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    console.log(`批量处理了 ${updates.length} 个状态更新`);
  }

  /**
   * Sync workflow state from database (for recovery scenarios)
   * Requirements: 需求 5.4, 需求 5.5
   */
  async syncWorkflowStateFromDatabase(workflowId: string): Promise<void> {
    try {
      // Clear existing cache
      this.invalidateWorkflowCaches(workflowId);

      // Get fresh data from database
      const completeStatus = await EnhancedLangGraphDatabase.getCompleteWorkflowStatus(workflowId);

      // Update cache with fresh data
      const ttl = completeStatus.workflow.status === 'running' ? 10000 : 300000;
      this.setCache(`workflow_${workflowId}`, completeStatus, ttl);

      // Broadcast sync update
      await this.broadcastStateUpdate(workflowId, {
        type: 'sync_update',
        status: completeStatus.workflow.status,
        progress: completeStatus.workflow.progress,
        currentStage: completeStatus.workflow.current_stage,
        syncedAt: new Date().toISOString(),
      });

      console.log(`工作流 ${workflowId} 状态同步完成`);
    } catch (error) {
      console.error(`同步工作流状态失败 (${workflowId}):`, error);
      throw error;
    }
  }

  // ============================================================================
  // Real-time State Broadcasting
  // ============================================================================

  /**
   * Subscribe to workflow updates
   * Requirements: 需求 2.2, 需求 2.3
   */
  subscribeToWorkflow(workflowId: string, subscriberId: string): void {
    if (!this.subscribers.has(workflowId)) {
      this.subscribers.set(workflowId, new Set());
    }
    this.subscribers.get(workflowId)!.add(subscriberId);

    console.log(`订阅者 ${subscriberId} 订阅工作流 ${workflowId}`);
  }

  /**
   * Unsubscribe from workflow updates
   */
  unsubscribeFromWorkflow(workflowId: string, subscriberId: string): void {
    const subscribers = this.subscribers.get(workflowId);
    if (subscribers) {
      subscribers.delete(subscriberId);
      if (subscribers.size === 0) {
        this.subscribers.delete(workflowId);
      }
    }

    console.log(`订阅者 ${subscriberId} 取消订阅工作流 ${workflowId}`);
  }

  /**
   * Broadcast state update to all subscribers
   * Requirements: 需求 2.2, 需求 2.3
   */
  private async broadcastStateUpdate(workflowId: string, data: any): Promise<void> {
    const subscribers = this.subscribers.get(workflowId);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const updateData = {
      workflowId,
      timestamp: new Date().toISOString(),
      ...data,
    };

    // Emit to all subscribers
    this.emit('stateUpdate', {
      workflowId,
      subscribers: Array.from(subscribers),
      data: updateData,
    });

    console.log(`广播状态更新到 ${subscribers.size} 个订阅者:`, workflowId);
  }

  /**
   * Get current workflow state for new subscribers
   * Requirements: 需求 2.1, 需求 2.2
   */
  async getCurrentWorkflowState(workflowId: string): Promise<any> {
    try {
      // Try to get from cache first
      const cached = this.getFromCache(`workflow_${workflowId}`);
      if (cached) {
        return cached;
      }

      // Get from database and cache it
      const status = await analysisService.getAnalysisStatus(workflowId);
      this.setCache(`workflow_${workflowId}`, status, 30000); // Cache for 30 seconds

      return status;
    } catch (error) {
      console.error('获取当前工作流状态失败:', error);
      throw error;
    }
  }

  // ============================================================================
  // Caching System
  // ============================================================================

  /**
   * Set cache entry
   * Requirements: 需求 6.1, 需求 6.2, 需求 6.3
   */
  setCache(key: string, data: any, ttl: number = 60000): void {
    this.cache.set(key, {
      key,
      data,
      timestamp: new Date(),
      ttl,
    });
  }

  /**
   * Get cache entry
   */
  getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    // Check if expired
    const now = new Date().getTime();
    const entryTime = entry.timestamp.getTime();
    if (now - entryTime > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Update workflow cache with intelligent TTL
   * Requirements: 需求 6.1, 需求 6.2, 需求 6.3
   */
  private async updateWorkflowCache(workflowId: string): Promise<void> {
    try {
      const status = await analysisService.getAnalysisStatus(workflowId);

      // Determine TTL based on workflow status
      let ttl = 30000; // Default 30 seconds

      if (status.status === 'completed' || status.status === 'failed') {
        ttl = 300000; // 5 minutes for completed/failed workflows
      } else if (status.status === 'running') {
        ttl = 10000; // 10 seconds for active workflows
      }

      this.setCache(`workflow_${workflowId}`, status, ttl);

      // Also cache individual components for faster partial updates
      this.setCache(
        `workflow_${workflowId}_basic`,
        {
          workflowId: status.workflowId,
          status: status.status,
          progress: status.progress,
          currentStage: status.currentStage,
        },
        ttl
      );

      if (status.analystReports && status.analystReports.length > 0) {
        this.setCache(`workflow_${workflowId}_analysts`, status.analystReports, ttl);
      }

      if (status.researchReports && status.researchReports.length > 0) {
        this.setCache(`workflow_${workflowId}_research`, status.researchReports, ttl);
      }

      if (status.finalDecision) {
        this.setCache(`workflow_${workflowId}_decision`, status.finalDecision, ttl);
      }
    } catch (error) {
      console.error('更新工作流缓存失败:', error);
    }
  }

  /**
   * Invalidate related caches when workflow state changes
   * Requirements: 需求 6.3, 需求 6.4
   */
  private invalidateWorkflowCaches(workflowId: string): void {
    const keysToInvalidate = [
      `workflow_${workflowId}`,
      `workflow_${workflowId}_basic`,
      `workflow_${workflowId}_analysts`,
      `workflow_${workflowId}_research`,
      `workflow_${workflowId}_decision`,
    ];

    for (const key of keysToInvalidate) {
      this.cache.delete(key);
    }

    console.log(`已清除工作流 ${workflowId} 的相关缓存`);
  }

  /**
   * Get cached workflow data with fallback
   * Requirements: 需求 6.1, 需求 6.2
   */
  async getCachedWorkflowData(workflowId: string, component?: string): Promise<any> {
    const cacheKey = component ? `workflow_${workflowId}_${component}` : `workflow_${workflowId}`;

    // Try to get from cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    // If not in cache and no specific component requested, get full status
    if (!component) {
      const status = await analysisService.getAnalysisStatus(workflowId);
      this.setCache(cacheKey, status, 30000);
      return status;
    }

    return null;
  }

  /**
   * Clear cache for workflow
   */
  clearWorkflowCache(workflowId: string): void {
    this.cache.delete(`workflow_${workflowId}`);
  }

  /**
   * Start cache cleanup process
   */
  private startCacheCleanup(): void {
    // Skip cleanup interval in test environment
    if (process.env.NODE_ENV === 'test') {
      return;
    }

    this.cleanupInterval = setInterval(() => {
      const now = new Date().getTime();
      let cleanedCount = 0;

      for (const [key, entry] of Array.from(this.cache.entries())) {
        const entryTime = entry.timestamp.getTime();
        if (now - entryTime > entry.ttl) {
          this.cache.delete(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`清理了 ${cleanedCount} 个过期缓存条目`);
      }
    }, 60000); // Clean up every minute
  }

  // ============================================================================
  // Notification System
  // ============================================================================

  /**
   * Send agent completion notification
   * Requirements: 需求 9.1
   */
  private async sendAgentCompletionNotification(event: AgentStateChangeEvent): Promise<void> {
    const notification: NotificationEvent = {
      type: 'agent_complete',
      workflowId: event.workflowId,
      title: `智能体 ${event.agentType} ${event.newStatus === 'completed' ? '完成' : '失败'}`,
      message:
        event.newStatus === 'completed'
          ? `${event.agentType} 分析师已完成分析`
          : `${event.agentType} 分析师分析失败: ${event.error || '未知错误'}`,
      data: {
        agentType: event.agentType,
        status: event.newStatus,
        executionTime: event.executionTime,
        result: event.result,
        error: event.error,
      },
      timestamp: new Date(),
      priority: event.newStatus === 'failed' ? 'high' : 'medium',
    };

    await this.sendNotification(notification);
  }

  /**
   * Send workflow completion notification
   * Requirements: 需求 9.1
   */
  private async sendWorkflowCompletionNotification(update: WorkflowStatusUpdate): Promise<void> {
    const notification: NotificationEvent = {
      type: update.status === 'completed' ? 'analysis_complete' : 'analysis_error',
      workflowId: update.workflowId,
      title: update.status === 'completed' ? '分析完成' : '分析失败',
      message:
        update.status === 'completed'
          ? '股票分析已成功完成'
          : `分析失败: ${update.error || '未知错误'}`,
      data: {
        status: update.status,
        progress: update.progress,
        currentStage: update.currentStage,
        error: update.error,
      },
      timestamp: new Date(),
      priority: update.status === 'failed' ? 'urgent' : 'high',
    };

    await this.sendNotification(notification);
  }

  /**
   * Send notification with enhanced delivery options
   * Requirements: 需求 9.1, 需求 9.2, 需求 9.3
   */
  private async sendNotification(notification: NotificationEvent): Promise<void> {
    try {
      // Emit notification event for external handlers (WebSocket, email, etc.)
      this.emit('notification', notification);

      // Store notification in cache for retrieval
      this.setCache(
        `notification_${notification.workflowId}_${Date.now()}`,
        notification,
        86400000
      ); // 24 hours

      // Log notification to database
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: notification.workflowId,
        stage_name: 'notification',
        event_type: 'log',
        content: `发送通知: ${notification.title}`,
        metadata: {
          notificationType: notification.type,
          priority: notification.priority,
          message: notification.message,
          data: notification.data,
        },
      });

      // Send to specific notification channels based on priority
      await this.routeNotificationByPriority(notification);

      console.log(
        `发送通知: ${notification.title} (${notification.type}, 优先级: ${notification.priority})`
      );
    } catch (error) {
      console.error('发送通知失败:', error);
      this.emit('notificationError', { notification, error });
    }
  }

  /**
   * Route notifications based on priority level
   * Requirements: 需求 9.1, 需求 9.2
   */
  private async routeNotificationByPriority(notification: NotificationEvent): Promise<void> {
    // Emit different events based on priority for external handlers
    switch (notification.priority) {
      case 'urgent':
        this.emit('urgentNotification', notification);
        break;
      case 'high':
        this.emit('highPriorityNotification', notification);
        break;
      case 'medium':
        this.emit('mediumPriorityNotification', notification);
        break;
      case 'low':
        this.emit('lowPriorityNotification', notification);
        break;
    }
  }

  /**
   * Get notifications for a workflow
   * Requirements: 需求 9.4, 需求 9.5
   */
  getWorkflowNotifications(workflowId: string): NotificationEvent[] {
    const notifications: NotificationEvent[] = [];

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (key.startsWith(`notification_${workflowId}_`)) {
        notifications.push(entry.data as NotificationEvent);
      }
    }

    return notifications.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Mark notification as read
   * Requirements: 需求 9.6
   */
  markNotificationAsRead(workflowId: string, notificationId: string): void {
    const key = `notification_${workflowId}_${notificationId}`;
    const entry = this.cache.get(key);

    if (entry) {
      entry.data.read = true;
      entry.data.readAt = new Date();
      this.cache.set(key, entry);
    }
  }

  // ============================================================================
  // Event Handlers Setup
  // ============================================================================

  /**
   * Setup event handlers for integration with other services
   * Requirements: 需求 1.6, 需求 2.1, 需求 2.2, 需求 2.3
   */
  private setupEventHandlers(): void {
    // Listen to analysis service events
    analysisService.on('statusUpdate', (data) => {
      this.handleWorkflowStatusUpdate({
        workflowId: data.workflowId,
        status: data.status,
        progress: data.progress,
        currentStage: data.currentStage,
        timestamp: new Date(),
      });
    });

    analysisService.on('analysisCompleted', (data) => {
      this.handleWorkflowStatusUpdate({
        workflowId: data.workflowId,
        status: 'completed',
        progress: 100,
        currentStage: 'completed',
        timestamp: new Date(),
      });
    });

    analysisService.on('analysisError', (data) => {
      this.handleWorkflowStatusUpdate({
        workflowId: data.workflowId,
        status: 'failed',
        progress: 0,
        currentStage: 'failed',
        error: data.error,
        timestamp: new Date(),
      });
    });

    // Listen to LangGraph updates for more granular state changes
    analysisService.on('langGraphUpdate', (data) => {
      this.handleLangGraphStateChange(data);
    });

    // Listen to analysis service for agent-specific events
    analysisService.on('agentCompleted', (data) => {
      this.handleAgentStateChange({
        workflowId: data.workflowId,
        agentType: data.agentType,
        previousStatus: 'running',
        newStatus: 'completed',
        executionTime: data.executionTime,
        result: data.result,
        timestamp: new Date(),
      });
    });

    analysisService.on('agentFailed', (data) => {
      this.handleAgentStateChange({
        workflowId: data.workflowId,
        agentType: data.agentType,
        previousStatus: 'running',
        newStatus: 'failed',
        error: data.error,
        timestamp: new Date(),
      });
    });

    // Setup cleanup on process exit (only in non-test environment)
    if (process.env.NODE_ENV !== 'test') {
      process.on('SIGINT', () => this.destroy());
      process.on('SIGTERM', () => this.destroy());
    }
  }

  /**
   * Handle LangGraph state changes for more granular updates
   * Requirements: 需求 1.6, 需求 2.1, 需求 2.2
   */
  private async handleLangGraphStateChange(data: any): Promise<void> {
    try {
      const { workflowId, currentStep, isProcessing, error, progress } = data;

      // Update cache with the latest state
      await this.updateWorkflowCache(workflowId);

      // Broadcast granular update to subscribers
      await this.broadcastStateUpdate(workflowId, {
        type: 'langgraph_update',
        currentStep,
        isProcessing,
        error,
        progress,
      });

      // Log the state change
      await EnhancedLangGraphDatabase.logWorkflowEvent({
        workflow_id: workflowId,
        stage_name: currentStep,
        event_type: 'state_change',
        content: `LangGraph 状态更新: ${currentStep}`,
        metadata: {
          currentStep,
          isProcessing,
          error,
          progress,
        },
      });

      this.emit('langGraphStateChanged', data);
    } catch (error) {
      console.error('处理 LangGraph 状态变化失败:', error);
      this.emit('error', { type: 'langgraph_state_change_error', error, data });
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys()),
    };
  }

  /**
   * Get subscriber statistics
   */
  getSubscriberStats(): { workflows: number; totalSubscribers: number } {
    let totalSubscribers = 0;
    for (const subscribers of Array.from(this.subscribers.values())) {
      totalSubscribers += subscribers.size;
    }

    return {
      workflows: this.subscribers.size,
      totalSubscribers,
    };
  }

  /**
   * Manually trigger cache cleanup (for testing)
   */
  triggerCacheCleanup(): number {
    const now = new Date().getTime();
    let cleanedCount = 0;

    for (const [key, entry] of Array.from(this.cache.entries())) {
      const entryTime = entry.timestamp.getTime();
      if (now - entryTime > entry.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    console.log('清理 StateSyncService 资源...');

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.cache.clear();
    this.subscribers.clear();
    this.removeAllListeners();
  }

  /**
   * Cleanup resources (private method for internal use)
   */
  private cleanup(): void {
    this.destroy();
  }
}

// ============================================================================
// Export Service Instance
// ============================================================================

export const stateSyncService = new StateSyncService();
export default StateSyncService;
