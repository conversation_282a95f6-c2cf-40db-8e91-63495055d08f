/**
 * Core Web Vitals 性能监控系统
 * 监控 LCP、FID、CLS 等关键性能指标
 */

export interface WebVitalsMetric {
  name: 'CLS' | 'INP' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

export interface PerformanceBudget {
  LCP: number; // 最大内容绘制 < 2.5s
  INP: number; // 交互到下次绘制 < 200ms
  CLS: number; // 累积布局偏移 < 0.1
  FCP: number; // 首次内容绘制 < 1.8s
  TTFB: number; // 首字节时间 < 800ms
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, WebVitalsMetric> = new Map();
  private budget: PerformanceBudget;
  private alertCallbacks: Array<(metric: WebVitalsMetric) => void> = [];

  constructor() {
    this.budget = {
      LCP: 2500, // 2.5 seconds
      INP: 200, // 200 milliseconds
      CLS: 0.1, // 0.1 score
      FCP: 1800, // 1.8 seconds
      TTFB: 800, // 800 milliseconds
    };
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 初始化性能监控
   */
  async initialize(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      // 动态导入 web-vitals 库
      const { onCLS, onINP, onFCP, onLCP, onTTFB } = await import('web-vitals');

      // 监控各项指标
      onCLS(this.handleMetric.bind(this));
      onINP(this.handleMetric.bind(this));
      onFCP(this.handleMetric.bind(this));
      onLCP(this.handleMetric.bind(this));
      onTTFB(this.handleMetric.bind(this));

      console.log('Performance monitoring initialized');
    } catch (error) {
      console.error('Failed to initialize performance monitoring:', error);
    }
  }

  /**
   * 处理性能指标
   */
  private handleMetric(metric: WebVitalsMetric): void {
    // 存储指标
    this.metrics.set(metric.name, metric);

    // 评估性能等级
    const rating = this.evaluateMetric(metric);
    metric.rating = rating;

    // 检查是否超出预算
    if (this.isOverBudget(metric)) {
      this.triggerAlert(metric);
    }

    // 发送到分析服务
    this.sendToAnalytics(metric);

    console.log(`${metric.name}: ${metric.value} (${rating})`);
  }

  /**
   * 评估性能指标等级
   */
  private evaluateMetric(metric: WebVitalsMetric): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      INP: { good: 200, poor: 500 },
      CLS: { good: 0.1, poor: 0.25 },
      FCP: { good: 1800, poor: 3000 },
      TTFB: { good: 800, poor: 1800 },
    };

    const threshold = thresholds[metric.name];
    if (metric.value <= threshold.good) return 'good';
    if (metric.value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  /**
   * 检查是否超出性能预算
   */
  private isOverBudget(metric: WebVitalsMetric): boolean {
    return metric.value > this.budget[metric.name];
  }

  /**
   * 触发性能警报
   */
  private triggerAlert(metric: WebVitalsMetric): void {
    console.warn(
      `Performance budget exceeded for ${metric.name}: ${metric.value} > ${
        this.budget[metric.name]
      }`
    );

    this.alertCallbacks.forEach((callback) => {
      try {
        callback(metric);
      } catch (error) {
        console.error('Error in performance alert callback:', error);
      }
    });
  }

  /**
   * 发送指标到分析服务
   */
  private sendToAnalytics(metric: WebVitalsMetric): void {
    // 发送到 Google Analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        event_label: metric.id,
        non_interaction: true,
      });
    }

    // 发送到自定义分析端点
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric: metric.name,
          value: metric.value,
          rating: metric.rating,
          url: window.location.href,
          timestamp: Date.now(),
        }),
      }).catch((error) => {
        console.error('Failed to send performance metric:', error);
      });
    }
  }

  /**
   * 添加性能警报回调
   */
  onAlert(callback: (metric: WebVitalsMetric) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * 获取当前性能指标
   */
  getMetrics(): Map<string, WebVitalsMetric> {
    return new Map(this.metrics);
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: Record<string, WebVitalsMetric>;
    budget: PerformanceBudget;
    overBudget: string[];
    score: number;
  } {
    const metrics: Record<string, WebVitalsMetric> = {};
    const overBudget: string[] = [];
    let totalScore = 0;
    let metricCount = 0;

    this.metrics.forEach((metric, name) => {
      metrics[name] = metric;

      if (this.isOverBudget(metric)) {
        overBudget.push(name);
      }

      // 计算分数 (good: 100, needs-improvement: 50, poor: 0)
      const score = metric.rating === 'good' ? 100 : metric.rating === 'needs-improvement' ? 50 : 0;
      totalScore += score;
      metricCount++;
    });

    const averageScore = metricCount > 0 ? Math.round(totalScore / metricCount) : 0;

    return {
      metrics,
      budget: this.budget,
      overBudget,
      score: averageScore,
    };
  }

  /**
   * 更新性能预算
   */
  updateBudget(newBudget: Partial<PerformanceBudget>): void {
    this.budget = { ...this.budget, ...newBudget };
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();
