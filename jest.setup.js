// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Used for __tests__/testing-library.js
// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';

// Add custom Jest matchers
expect.extend({
    toBeOneOf(received, validOptions) {
        const pass = validOptions.includes(received);
        if (pass) {
            return {
                message: () => `expected ${received} not to be one of ${validOptions.join(', ')}`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be one of ${validOptions.join(', ')}`,
                pass: false,
            };
        }
    },
});
