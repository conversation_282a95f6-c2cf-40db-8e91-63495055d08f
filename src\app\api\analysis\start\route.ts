import { langGraphService } from '@/lib/langgraph-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { taskId, config } = body;

    if (!taskId) {
      return NextResponse.json(
        { success: false, message: '缺少必需的参数: taskId' },
        { status: 400 }
      );
    }

    // 确保服务已初始化
    await langGraphService.initialize();

    console.log(`[API] Received analysis request for task: ${taskId} with config:`, config);

    const ticker = config?.ticker;
    if (!ticker) {
      return NextResponse.json(
        { success: false, message: '缺少必需的参数: config.ticker' },
        { status: 400 }
      );
    }

    // 启动后台分析任务，不阻塞响应
    langGraphService.analyzeStock(taskId, ticker, config || {}).catch(async (err) => {
      console.error(`[API] Background analysis for task ${taskId} failed:`, err);

      // 更新任务状态为失败
      try {
        const { LangGraphDatabase } = await import('@/lib/langgraph-database');
        await LangGraphDatabase.updateWorkflowStatus({
          workflow_id: taskId,
          status: 'failed',
          error_message: err instanceof Error ? err.message : 'Unknown error',
          current_stage: 'error',
          progress: 0,
        });
      } catch (dbError) {
        console.error(`[API] Failed to update task status for ${taskId}:`, dbError);
      }
    });

    return NextResponse.json({
      success: true,
      message: `分析任务已启动，任务ID: ${taskId}`,
      taskId: taskId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('启动分析失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '启动分析失败',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
