/**
 * 风险指标计算引擎
 * 提供各种风险指标的计算功能
 */

export interface PriceData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface RiskMetrics {
  volatility: VolatilityMetrics;
  var: VaRMetrics;
  drawdown: DrawdownMetrics;
  ratios: RiskRatios;
  liquidity: LiquidityMetrics;
  correlation: CorrelationMetrics;
}

export interface VolatilityMetrics {
  daily_volatility: number;
  weekly_volatility: number;
  monthly_volatility: number;
  annualized_volatility: number;
  volatility_trend: number;
  volatility_percentile: number;
}

export interface VaRMetrics {
  var_95_1d: number;
  var_99_1d: number;
  var_95_10d: number;
  var_99_10d: number;
  expected_shortfall_95: number;
  expected_shortfall_99: number;
}

export interface DrawdownMetrics {
  max_drawdown: number;
  max_drawdown_duration: number;
  current_drawdown: number;
  recovery_time_estimate: number;
  drawdown_frequency: number;
}

export interface RiskRatios {
  sharpe_ratio: number;
  sortino_ratio: number;
  calmar_ratio: number;
  information_ratio: number;
  treynor_ratio: number;
  beta: number;
}

export interface LiquidityMetrics {
  amihud_ratio: number;
  turnover_rate: number;
  price_impact: number;
  bid_ask_spread: number;
  market_depth_score: number;
}

export interface CorrelationMetrics {
  market_correlation: number;
  sector_correlation: number;
  correlation_stability: number;
  diversification_ratio: number;
}

/**
 * 风险指标计算器类
 */
export class RiskMetricsCalculator {
  private static instance: RiskMetricsCalculator;

  public static getInstance(): RiskMetricsCalculator {
    if (!RiskMetricsCalculator.instance) {
      RiskMetricsCalculator.instance = new RiskMetricsCalculator();
    }
    return RiskMetricsCalculator.instance;
  }

  /**
   * 计算全套风险指标
   */
  public calculateRiskMetrics(
    priceData: PriceData[],
    marketData?: PriceData[],
    riskFreeRate: number = 0.03
  ): RiskMetrics {
    console.log('[RiskMetricsCalculator] Starting comprehensive risk metrics calculation');

    if (!priceData || priceData.length < 30) {
      throw new Error('需要至少30个交易日的数据来计算风险指标');
    }

    const returns = this.calculateReturns(priceData);
    const marketReturns = marketData ? this.calculateReturns(marketData) : null;

    return {
      volatility: this.calculateVolatilityMetrics(returns),
      var: this.calculateVaRMetrics(returns),
      drawdown: this.calculateDrawdownMetrics(priceData),
      ratios: this.calculateRiskRatios(returns, marketReturns, riskFreeRate),
      liquidity: this.calculateLiquidityMetrics(priceData),
      correlation: this.calculateCorrelationMetrics(returns, marketReturns),
    };
  }

  /**
   * 计算收益率序列
   */
  private calculateReturns(priceData: PriceData[]): number[] {
    const returns: number[] = [];
    for (let i = 1; i < priceData.length; i++) {
      const currentPrice = priceData[i].close;
      const previousPrice = priceData[i - 1].close;
      returns.push((currentPrice - previousPrice) / previousPrice);
    }
    return returns;
  }

  /**
   * 计算波动率指标
   */
  private calculateVolatilityMetrics(returns: number[]): VolatilityMetrics {
    const dailyVol = this.standardDeviation(returns);
    const weeklyVol = dailyVol * Math.sqrt(5);
    const monthlyVol = dailyVol * Math.sqrt(21);
    const annualizedVol = dailyVol * Math.sqrt(252);

    // 计算波动率趋势（最近30天 vs 之前30天）
    const recentReturns = returns.slice(-30);
    const previousReturns = returns.slice(-60, -30);
    const recentVol = this.standardDeviation(recentReturns);
    const previousVol = this.standardDeviation(previousReturns);
    const volatilityTrend = previousVol > 0 ? (recentVol - previousVol) / previousVol : 0;

    // 计算波动率百分位数（当前波动率在历史中的位置）
    const rollingVols = this.calculateRollingVolatility(returns, 30);
    const volatilityPercentile = this.calculatePercentile(rollingVols, recentVol);

    return {
      daily_volatility: dailyVol,
      weekly_volatility: weeklyVol,
      monthly_volatility: monthlyVol,
      annualized_volatility: annualizedVol,
      volatility_trend: volatilityTrend,
      volatility_percentile: volatilityPercentile,
    };
  }

  /**
   * 计算VaR指标
   */
  private calculateVaRMetrics(returns: number[]): VaRMetrics {
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const n = sortedReturns.length;

    // 计算不同置信度的VaR
    const var95Index = Math.floor(n * 0.05);
    const var99Index = Math.floor(n * 0.01);

    const var95_1d = -sortedReturns[var95Index];
    const var99_1d = -sortedReturns[var99Index];

    // 10日VaR（假设独立性）
    const var95_10d = var95_1d * Math.sqrt(10);
    const var99_10d = var99_1d * Math.sqrt(10);

    // 计算Expected Shortfall (CVaR)
    const es95 = -this.mean(sortedReturns.slice(0, var95Index + 1));
    const es99 = -this.mean(sortedReturns.slice(0, var99Index + 1));

    return {
      var_95_1d: var95_1d,
      var_99_1d: var99_1d,
      var_95_10d: var95_10d,
      var_99_10d: var99_10d,
      expected_shortfall_95: es95,
      expected_shortfall_99: es99,
    };
  }

  /**
   * 计算回撤指标
   */
  private calculateDrawdownMetrics(priceData: PriceData[]): DrawdownMetrics {
    const prices = priceData.map((d) => d.close);
    const cumulativeReturns = this.calculateCumulativeReturns(prices);

    let maxDrawdown = 0;
    let maxDrawdownDuration = 0;
    let currentDrawdown = 0;
    let currentDrawdownDuration = 0;
    let peak = cumulativeReturns[0];
    let peakIndex = 0;
    let drawdownCount = 0;

    for (let i = 1; i < cumulativeReturns.length; i++) {
      if (cumulativeReturns[i] > peak) {
        peak = cumulativeReturns[i];
        peakIndex = i;
        if (currentDrawdownDuration > 0) {
          drawdownCount++;
        }
        currentDrawdownDuration = 0;
      } else {
        currentDrawdownDuration = i - peakIndex;
        currentDrawdown = (cumulativeReturns[i] - peak) / peak;

        if (Math.abs(currentDrawdown) > maxDrawdown) {
          maxDrawdown = Math.abs(currentDrawdown);
          maxDrawdownDuration = currentDrawdownDuration;
        }
      }
    }

    // 估算恢复时间（基于历史平均恢复时间）
    const recoveryTimeEstimate = maxDrawdownDuration * 1.5; // 简化估算

    // 回撤频率（每年发生的回撤次数）
    const drawdownFrequency = (drawdownCount / priceData.length) * 252;

    return {
      max_drawdown: maxDrawdown,
      max_drawdown_duration: maxDrawdownDuration,
      current_drawdown: Math.abs(currentDrawdown),
      recovery_time_estimate: recoveryTimeEstimate,
      drawdown_frequency: drawdownFrequency,
    };
  }

  /**
   * 计算风险比率
   */
  private calculateRiskRatios(
    returns: number[],
    marketReturns: number[] | null,
    riskFreeRate: number
  ): RiskRatios {
    const meanReturn = this.mean(returns);
    const volatility = this.standardDeviation(returns);
    const annualizedReturn = meanReturn * 252;
    const annualizedVol = volatility * Math.sqrt(252);

    // Sharpe比率
    const sharpeRatio = annualizedVol > 0 ? (annualizedReturn - riskFreeRate) / annualizedVol : 0;

    // Sortino比率（只考虑下行波动）
    const downwardReturns = returns.filter((r) => r < 0);
    const downwardVol = this.standardDeviation(downwardReturns) * Math.sqrt(252);
    const sortinoRatio = downwardVol > 0 ? (annualizedReturn - riskFreeRate) / downwardVol : 0;

    // Beta系数
    let beta = 1.0;
    let treynorRatio = 0;
    let informationRatio = 0;

    if (marketReturns && marketReturns.length === returns.length) {
      const marketMeanReturn = this.mean(marketReturns);
      const marketVolatility = this.standardDeviation(marketReturns);
      const correlation = this.correlation(returns, marketReturns);

      beta = correlation * (volatility / marketVolatility);

      // Treynor比率
      treynorRatio = beta !== 0 ? (annualizedReturn - riskFreeRate) / beta : 0;

      // Information比率（超额收益的一致性）
      const excessReturns = returns.map((r, i) => r - marketReturns[i]);
      const trackingError = this.standardDeviation(excessReturns) * Math.sqrt(252);
      informationRatio = trackingError > 0 ? (this.mean(excessReturns) * 252) / trackingError : 0;
    }

    // Calmar比率（年化收益/最大回撤）
    const maxDrawdown = this.calculateMaxDrawdown(returns);
    const calmarRatio = maxDrawdown > 0 ? annualizedReturn / maxDrawdown : 0;

    return {
      sharpe_ratio: sharpeRatio,
      sortino_ratio: sortinoRatio,
      calmar_ratio: calmarRatio,
      information_ratio: informationRatio,
      treynor_ratio: treynorRatio,
      beta: beta,
    };
  }

  /**
   * 计算流动性指标
   */
  private calculateLiquidityMetrics(priceData: PriceData[]): LiquidityMetrics {
    // Amihud非流动性比率
    let amihudSum = 0;
    let validDays = 0;

    for (const data of priceData) {
      if (data.volume > 0) {
        const dailyReturn = Math.abs((data.close - data.open) / data.open);
        const dollarVolume = data.volume * data.close;
        amihudSum += dailyReturn / dollarVolume;
        validDays++;
      }
    }

    const amihudRatio = validDays > 0 ? amihudSum / validDays : 0;

    // 换手率
    const avgVolume = this.mean(priceData.map((d) => d.volume));
    const avgPrice = this.mean(priceData.map((d) => d.close));
    const marketCap = avgPrice * avgVolume * 100; // 假设流通股本
    const turnoverRate = avgVolume / (marketCap / avgPrice);

    // 价格冲击（简化计算）
    const priceImpact = amihudRatio * 1000000; // 百万美元交易的价格冲击

    // 买卖价差（估算）
    const bidAskSpread = this.estimateBidAskSpread(priceData);

    // 市场深度评分（基于成交量稳定性）
    const volumeVolatility = this.standardDeviation(priceData.map((d) => d.volume));
    const avgVolumeNormalized = avgVolume > 0 ? avgVolume : 1;
    const marketDepthScore = Math.max(0, 100 - (volumeVolatility / avgVolumeNormalized) * 100);

    return {
      amihud_ratio: amihudRatio,
      turnover_rate: turnoverRate,
      price_impact: priceImpact,
      bid_ask_spread: bidAskSpread,
      market_depth_score: marketDepthScore,
    };
  }

  /**
   * 计算相关性指标
   */
  private calculateCorrelationMetrics(
    returns: number[],
    marketReturns: number[] | null
  ): CorrelationMetrics {
    let marketCorrelation = 0;
    let correlationStability = 0;
    let diversificationRatio = 1;

    if (marketReturns && marketReturns.length === returns.length) {
      marketCorrelation = this.correlation(returns, marketReturns);

      // 相关性稳定性（滚动相关性的标准差）
      const rollingCorrelations = this.calculateRollingCorrelation(returns, marketReturns, 30);
      correlationStability = 1 - this.standardDeviation(rollingCorrelations);

      // 分散化比率
      const portfolioVol = this.standardDeviation(returns);
      const marketVol = this.standardDeviation(marketReturns);
      const weightedVol = 0.5 * portfolioVol + 0.5 * marketVol;
      diversificationRatio = weightedVol > 0 ? portfolioVol / weightedVol : 1;
    }

    // 行业相关性（假设值，实际需要行业数据）
    const sectorCorrelation = marketCorrelation * 1.2; // 通常高于市场相关性

    return {
      market_correlation: marketCorrelation,
      sector_correlation: Math.min(1, sectorCorrelation),
      correlation_stability: Math.max(0, correlationStability),
      diversification_ratio: diversificationRatio,
    };
  }

  // 辅助计算方法

  private mean(values: number[]): number {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private standardDeviation(values: number[]): number {
    const mean = this.mean(values);
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return Math.sqrt(this.mean(squaredDiffs));
  }

  private correlation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    const sumX = x.slice(0, n).reduce((a, b) => a + b, 0);
    const sumY = y.slice(0, n).reduce((a, b) => a + b, 0);
    const sumXY = x.slice(0, n).reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.slice(0, n).reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.slice(0, n).reduce((sum, yi) => sum + yi * yi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  private calculateCumulativeReturns(prices: number[]): number[] {
    const cumulativeReturns = [1];
    for (let i = 1; i < prices.length; i++) {
      const return_ = (prices[i] - prices[i - 1]) / prices[i - 1];
      cumulativeReturns.push(cumulativeReturns[i - 1] * (1 + return_));
    }
    return cumulativeReturns;
  }

  private calculateMaxDrawdown(returns: number[]): number {
    const cumulativeReturns = [1];
    for (const return_ of returns) {
      cumulativeReturns.push(cumulativeReturns[cumulativeReturns.length - 1] * (1 + return_));
    }

    let maxDrawdown = 0;
    let peak = cumulativeReturns[0];

    for (let i = 1; i < cumulativeReturns.length; i++) {
      if (cumulativeReturns[i] > peak) {
        peak = cumulativeReturns[i];
      } else {
        const drawdown = (peak - cumulativeReturns[i]) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  private calculateRollingVolatility(returns: number[], window: number): number[] {
    const rollingVols: number[] = [];
    for (let i = window - 1; i < returns.length; i++) {
      const windowReturns = returns.slice(i - window + 1, i + 1);
      rollingVols.push(this.standardDeviation(windowReturns));
    }
    return rollingVols;
  }

  private calculateRollingCorrelation(x: number[], y: number[], window: number): number[] {
    const rollingCorrs: number[] = [];
    for (let i = window - 1; i < x.length; i++) {
      const windowX = x.slice(i - window + 1, i + 1);
      const windowY = y.slice(i - window + 1, i + 1);
      rollingCorrs.push(this.correlation(windowX, windowY));
    }
    return rollingCorrs;
  }

  private calculatePercentile(values: number[], target: number): number {
    const sorted = [...values].sort((a, b) => a - b);
    let count = 0;
    for (const value of sorted) {
      if (value <= target) count++;
      else break;
    }
    return (count / sorted.length) * 100;
  }

  private estimateBidAskSpread(priceData: PriceData[]): number {
    // 简化的买卖价差估算（基于高低价差）
    const spreads = priceData.map((d) => (d.high - d.low) / d.close);
    return this.mean(spreads);
  }

  /**
   * 计算风险调整收益指标
   */
  public calculateRiskAdjustedReturns(
    returns: number[],
    benchmarkReturns?: number[],
    riskFreeRate: number = 0.03
  ): any {
    const metrics = this.calculateRiskMetrics(
      returns.map((r, i) => ({
        date: new Date(Date.now() - (returns.length - i) * 24 * 60 * 60 * 1000).toISOString(),
        open: 100 * (1 + r),
        high: 100 * (1 + r * 1.02),
        low: 100 * (1 + r * 0.98),
        close: 100 * (1 + r),
        volume: 1000000,
      })),
      benchmarkReturns?.map((r, i) => ({
        date: new Date(
          Date.now() - (benchmarkReturns.length - i) * 24 * 60 * 60 * 1000
        ).toISOString(),
        open: 100 * (1 + r),
        high: 100 * (1 + r * 1.02),
        low: 100 * (1 + r * 0.98),
        close: 100 * (1 + r),
        volume: 1000000,
      })),
      riskFreeRate
    );

    return {
      sharpe_ratio: metrics.ratios.sharpe_ratio,
      sortino_ratio: metrics.ratios.sortino_ratio,
      calmar_ratio: metrics.ratios.calmar_ratio,
      max_drawdown: metrics.drawdown.max_drawdown,
      volatility: metrics.volatility.annualized_volatility,
      var_95: metrics.var.var_95_1d,
      expected_shortfall: metrics.var.expected_shortfall_95,
    };
  }

  /**
   * 计算历史趋势分析
   * 需求 4.7: 提供风险指标的历史趋势分析
   */
  public calculateHistoricalTrends(
    priceData: PriceData[],
    windowSize: number = 30
  ): {
    volatility_trend: number[];
    var_trend: number[];
    drawdown_trend: number[];
    beta_trend: number[];
    sharpe_trend: number[];
    dates: string[];
  } {
    console.log('[RiskMetricsCalculator] Calculating historical trends');

    if (priceData.length < windowSize * 2) {
      throw new Error(`需要至少${windowSize * 2}个交易日的数据来计算历史趋势`);
    }

    const volatilityTrend: number[] = [];
    const varTrend: number[] = [];
    const drawdownTrend: number[] = [];
    const betaTrend: number[] = [];
    const sharpeTrend: number[] = [];
    const dates: string[] = [];

    // 滚动计算风险指标
    for (let i = windowSize; i <= priceData.length; i++) {
      const windowData = priceData.slice(i - windowSize, i);
      const returns = this.calculateReturns(windowData);

      // 波动率趋势
      const volatility = this.standardDeviation(returns) * Math.sqrt(252);
      volatilityTrend.push(volatility);

      // VaR趋势
      const sortedReturns = [...returns].sort((a, b) => a - b);
      const var95Index = Math.floor(returns.length * 0.05);
      const var95 = -sortedReturns[var95Index];
      varTrend.push(var95);

      // 回撤趋势
      const drawdownMetrics = this.calculateDrawdownMetrics(windowData);
      drawdownTrend.push(drawdownMetrics.max_drawdown);

      // Beta趋势（假设市场收益率为正态分布）
      const marketReturns = this.generateMarketReturns(returns.length);
      const correlation = this.correlation(returns, marketReturns);
      const marketVol = this.standardDeviation(marketReturns);
      const beta = correlation * (this.standardDeviation(returns) / marketVol);
      betaTrend.push(beta);

      // 夏普比率趋势
      const meanReturn = this.mean(returns) * 252;
      const sharpe = volatility > 0 ? (meanReturn - 0.03) / volatility : 0;
      sharpeTrend.push(sharpe);

      dates.push(windowData[windowData.length - 1].date);
    }

    return {
      volatility_trend: volatilityTrend,
      var_trend: varTrend,
      drawdown_trend: drawdownTrend,
      beta_trend: betaTrend,
      sharpe_trend: sharpeTrend,
      dates: dates,
    };
  }

  /**
   * 计算集中度风险
   * 需求 4.6: 计算相关性和集中度风险
   */
  public calculateConcentrationRisk(
    positions: { symbol: string; weight: number; returns: number[] }[]
  ): {
    herfindahl_index: number;
    effective_number_of_positions: number;
    max_weight: number;
    top_5_concentration: number;
    diversification_score: number;
  } {
    console.log('[RiskMetricsCalculator] Calculating concentration risk');

    // Herfindahl指数（权重平方和）
    const herfindalIndex = positions.reduce((sum, pos) => sum + pos.weight * pos.weight, 0);

    // 有效持仓数量
    const effectiveNumberOfPositions = 1 / herfindalIndex;

    // 最大权重
    const maxWeight = Math.max(...positions.map((pos) => pos.weight));

    // 前5大持仓集中度
    const sortedWeights = positions.map((pos) => pos.weight).sort((a, b) => b - a);
    const top5Concentration = sortedWeights.slice(0, 5).reduce((sum, weight) => sum + weight, 0);

    // 分散化评分（基于相关性矩阵）
    let avgCorrelation = 0;
    let correlationCount = 0;

    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const corr = this.correlation(positions[i].returns, positions[j].returns);
        avgCorrelation += Math.abs(corr);
        correlationCount++;
      }
    }

    avgCorrelation = correlationCount > 0 ? avgCorrelation / correlationCount : 0;
    const diversificationScore = Math.max(0, 100 - avgCorrelation * 100);

    return {
      herfindahl_index: herfindalIndex,
      effective_number_of_positions: effectiveNumberOfPositions,
      max_weight: maxWeight,
      top_5_concentration: top5Concentration,
      diversification_score: diversificationScore,
    };
  }

  /**
   * 增强的Beta系数计算
   * 需求 4.1: 计算历史波动率和 Beta 系数
   */
  public calculateEnhancedBeta(
    returns: number[],
    marketReturns: number[],
    windowSize: number = 252
  ): {
    beta: number;
    alpha: number;
    r_squared: number;
    tracking_error: number;
    up_beta: number;
    down_beta: number;
    beta_stability: number;
  } {
    console.log('[RiskMetricsCalculator] Calculating enhanced beta metrics');

    if (returns.length !== marketReturns.length) {
      throw new Error('收益率序列长度必须相同');
    }

    const n = Math.min(returns.length, marketReturns.length);
    const assetReturns = returns.slice(0, n);
    const benchmarkReturns = marketReturns.slice(0, n);

    // 基础Beta计算
    const correlation = this.correlation(assetReturns, benchmarkReturns);
    const assetVol = this.standardDeviation(assetReturns);
    const marketVol = this.standardDeviation(benchmarkReturns);
    const beta = correlation * (assetVol / marketVol);

    // Alpha计算
    const assetMeanReturn = this.mean(assetReturns);
    const marketMeanReturn = this.mean(benchmarkReturns);
    const alpha = assetMeanReturn - beta * marketMeanReturn;

    // R平方
    const rSquared = correlation * correlation;

    // 跟踪误差
    const excessReturns = assetReturns.map((r, i) => r - beta * benchmarkReturns[i] - alpha);
    const trackingError = this.standardDeviation(excessReturns);

    // 上行Beta和下行Beta
    const upMarketReturns: number[] = [];
    const upAssetReturns: number[] = [];
    const downMarketReturns: number[] = [];
    const downAssetReturns: number[] = [];

    for (let i = 0; i < n; i++) {
      if (benchmarkReturns[i] > 0) {
        upMarketReturns.push(benchmarkReturns[i]);
        upAssetReturns.push(assetReturns[i]);
      } else {
        downMarketReturns.push(benchmarkReturns[i]);
        downAssetReturns.push(assetReturns[i]);
      }
    }

    const upBeta =
      upMarketReturns.length > 10
        ? this.correlation(upAssetReturns, upMarketReturns) *
          (this.standardDeviation(upAssetReturns) / this.standardDeviation(upMarketReturns))
        : beta;

    const downBeta =
      downMarketReturns.length > 10
        ? this.correlation(downAssetReturns, downMarketReturns) *
          (this.standardDeviation(downAssetReturns) / this.standardDeviation(downMarketReturns))
        : beta;

    // Beta稳定性（滚动Beta的标准差）
    const rollingBetas = this.calculateRollingBeta(
      assetReturns,
      benchmarkReturns,
      Math.min(60, n / 4)
    );
    const betaStability = 1 - this.standardDeviation(rollingBetas) / Math.abs(beta);

    return {
      beta: beta,
      alpha: alpha,
      r_squared: rSquared,
      tracking_error: trackingError,
      up_beta: upBeta,
      down_beta: downBeta,
      beta_stability: Math.max(0, betaStability),
    };
  }

  /**
   * 增强的VaR计算
   * 需求 4.2: 计算 VaR（风险价值）在不同置信水平下的值
   */
  public calculateEnhancedVaR(
    returns: number[],
    confidenceLevels: number[] = [0.9, 0.95, 0.99],
    methods: ('historical' | 'parametric' | 'monte_carlo')[] = ['historical', 'parametric']
  ): {
    historical_var: { [key: string]: number };
    parametric_var: { [key: string]: number };
    monte_carlo_var?: { [key: string]: number };
    expected_shortfall: { [key: string]: number };
    var_backtesting: {
      violations: number;
      violation_rate: number;
      kupiec_test_pvalue: number;
    };
  } {
    console.log('[RiskMetricsCalculator] Calculating enhanced VaR metrics');

    const result: any = {
      historical_var: {},
      parametric_var: {},
      expected_shortfall: {},
    };

    const sortedReturns = [...returns].sort((a, b) => a - b);
    const mean = this.mean(returns);
    const std = this.standardDeviation(returns);

    // 历史模拟法VaR
    if (methods.includes('historical')) {
      for (const confidence of confidenceLevels) {
        const index = Math.floor(returns.length * (1 - confidence));
        const var_value = -sortedReturns[index];
        result.historical_var[`var_${Math.round(confidence * 100)}`] = var_value;

        // Expected Shortfall (CVaR)
        const es = -this.mean(sortedReturns.slice(0, index + 1));
        result.expected_shortfall[`es_${Math.round(confidence * 100)}`] = es;
      }
    }

    // 参数法VaR（假设正态分布）
    if (methods.includes('parametric')) {
      for (const confidence of confidenceLevels) {
        const z_score = this.getZScore(confidence);
        const var_value = -(mean + z_score * std);
        result.parametric_var[`var_${Math.round(confidence * 100)}`] = var_value;
      }
    }

    // 蒙特卡洛模拟VaR
    if (methods.includes('monte_carlo')) {
      result.monte_carlo_var = {};
      const simulations = this.monteCarloSimulation(mean, std, 10000);
      const sortedSimulations = simulations.sort((a, b) => a - b);

      for (const confidence of confidenceLevels) {
        const index = Math.floor(simulations.length * (1 - confidence));
        const var_value = -sortedSimulations[index];
        result.monte_carlo_var[`var_${Math.round(confidence * 100)}`] = var_value;
      }
    }

    // VaR回测
    const var95 = result.historical_var.var_95 || result.parametric_var.var_95;
    if (var95) {
      const violations = returns.filter((r) => -r > var95).length;
      const violationRate = violations / returns.length;
      const expectedViolationRate = 0.05;

      // Kupiec似然比检验
      const kupiecStat =
        -2 *
        Math.log(
          (Math.pow(expectedViolationRate, violations) *
            Math.pow(1 - expectedViolationRate, returns.length - violations)) /
            (Math.pow(violationRate, violations) *
              Math.pow(1 - violationRate, returns.length - violations))
        );

      result.var_backtesting = {
        violations: violations,
        violation_rate: violationRate,
        kupiec_test_pvalue: 1 - this.chiSquareCDF(kupiecStat, 1),
      };
    }

    return result;
  }

  // 新增辅助方法

  private generateMarketReturns(length: number): number[] {
    // 生成模拟市场收益率（正态分布）
    const returns: number[] = [];
    for (let i = 0; i < length; i++) {
      returns.push(this.normalRandom() * 0.02); // 2%日波动率
    }
    return returns;
  }

  private normalRandom(): number {
    // Box-Muller变换生成正态分布随机数
    let u = 0,
      v = 0;
    while (u === 0) u = Math.random();
    while (v === 0) v = Math.random();
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  }

  private calculateRollingBeta(
    returns: number[],
    marketReturns: number[],
    window: number
  ): number[] {
    const rollingBetas: number[] = [];
    for (let i = window - 1; i < returns.length; i++) {
      const windowReturns = returns.slice(i - window + 1, i + 1);
      const windowMarketReturns = marketReturns.slice(i - window + 1, i + 1);
      const correlation = this.correlation(windowReturns, windowMarketReturns);
      const vol = this.standardDeviation(windowReturns);
      const marketVol = this.standardDeviation(windowMarketReturns);
      const beta = correlation * (vol / marketVol);
      rollingBetas.push(beta);
    }
    return rollingBetas;
  }

  private getZScore(confidence: number): number {
    // 获取正态分布的Z分数
    const zScores: { [key: number]: number } = {
      0.9: -1.282,
      0.95: -1.645,
      0.99: -2.326,
    };
    return zScores[confidence] || -1.645;
  }

  private monteCarloSimulation(mean: number, std: number, simulations: number): number[] {
    const results: number[] = [];
    for (let i = 0; i < simulations; i++) {
      results.push(mean + std * this.normalRandom());
    }
    return results;
  }

  private chiSquareCDF(x: number, df: number): number {
    // 简化的卡方分布CDF计算
    if (x <= 0) return 0;
    if (df === 1) {
      return 2 * (1 - Math.exp(-x / 2)) - 1;
    }
    // 对于df=1的近似
    return 1 - Math.exp(-x / 2);
  }
}

// 导出单例实例
export const riskMetricsCalculator = RiskMetricsCalculator.getInstance();
