const mysql = require('mysql2/promise');

async function checkUserTables() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 13306,
      user: 'root',
      password: 'trading123',
      database: 'trading_analysis',
    });
    
    console.log('🔍 检查用户相关表...\n');
    const userTables = ['users', 'user_sessions', 'user_activity_logs'];
    
    for (const table of userTables) {
      try {
        const [tables] = await connection.execute(`SHOW TABLES LIKE '${table}'`);
        if (tables.length > 0) {
          console.log(`✅ ${table} 表存在`);
          const [columns] = await connection.execute(`DESCRIBE ${table}`);
          console.log(`   字段: ${columns.map(c => c.Field).join(', ')}`);
          
          // 检查数据行数
          const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
          console.log(`   数据行数: ${rows[0].count}\n`);
        } else {
          console.log(`❌ ${table} 表不存在\n`);
        }
      } catch (error) {
        console.log(`❌ 检查 ${table} 表失败: ${error.message}\n`);
      }
    }
    
    await connection.end();
    console.log('🎉 用户表检查完成！');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
  }
}

checkUserTables();