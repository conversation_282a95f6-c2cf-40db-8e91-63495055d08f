/**
 * 风险管理师状态管理测试
 */

import { RiskManagerStateManager } from '../risk-manager-state';

describe('RiskManagerStateManager', () => {
  let stateManager: RiskManagerStateManager;
  const testWorkflowId = 'test-workflow-123';

  beforeEach(() => {
    stateManager = new RiskManagerStateManager({
      maxExecutionTime: 10000, // 10秒用于测试
      retryAttempts: 2,
      progressUpdateInterval: 100, // 100ms用于测试
      enablePerformanceMonitoring: true,
      enableDetailedLogging: false, // 关闭日志以保持测试输出清洁
    });
  });

  afterEach(() => {
    // 清理状态
    stateManager.cleanupState(testWorkflowId);
  });

  describe('状态初始化', () => {
    it('应该正确初始化风险管理师状态', () => {
      const metadata = { ticker: 'AAPL', startedAt: new Date().toISOString() };
      const state = stateManager.initializeState(testWorkflowId, metadata);

      expect(state).toBeDefined();
      expect(state.workflowId).toBe(testWorkflowId);
      expect(state.status).toBe('pending');
      expect(state.progress).toBe(0);
      expect(state.metadata).toEqual(metadata);
      expect(state.tasks).toHaveLength(9); // 默认9个任务
      expect(state.performance).toEqual({});
    });

    it('应该触发状态初始化事件', (done) => {
      stateManager.on('stateInitialized', (state) => {
        expect(state.workflowId).toBe(testWorkflowId);
        expect(state.status).toBe('pending');
        done();
      });

      stateManager.initializeState(testWorkflowId);
    });
  });

  describe('分析启动', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
    });

    it('应该正确启动风险分析', () => {
      stateManager.startAnalysis(testWorkflowId);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.status).toBe('running');
      expect(state?.progress).toBe(5);
      expect(state?.startTime).toBeDefined();
      expect(state?.currentTask).toBe('开始风险数据收集');
    });

    it('应该触发状态更新事件', (done) => {
      stateManager.on('stateUpdated', (state) => {
        if (state.status === 'running') {
          expect(state.workflowId).toBe(testWorkflowId);
          expect(state.status).toBe('running');
          done();
        }
      });

      stateManager.startAnalysis(testWorkflowId);
    });

    it('应该触发进度更新事件', (done) => {
      stateManager.on('progressUpdate', (update) => {
        expect(update.workflowId).toBe(testWorkflowId);
        expect(update.status).toBe('running');
        expect(update.progress).toBe(5);
        done();
      });

      stateManager.startAnalysis(testWorkflowId);
    });
  });

  describe('任务状态更新', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
      stateManager.startAnalysis(testWorkflowId);
    });

    it('应该正确更新任务状态为运行中', () => {
      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'running', 50);
      const state = stateManager.getState(testWorkflowId);
      const task = state?.tasks.find((t) => t.id === 'data_collection');

      expect(task?.status).toBe('running');
      expect(task?.progress).toBe(50);
      expect(task?.startTime).toBeDefined();
    });

    it('应该正确更新任务状态为已完成', () => {
      const result = { data: 'test data' };

      // 先设置为运行中
      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'running', 0);

      // 然后完成任务
      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'completed', 100, result);

      const state = stateManager.getState(testWorkflowId);
      const task = state?.tasks.find((t) => t.id === 'data_collection');

      expect(task?.status).toBe('completed');
      expect(task?.progress).toBe(100);
      expect(task?.result).toEqual(result);
      expect(task?.endTime).toBeDefined();
      expect(task?.executionTimeMs).toBeDefined();
    });

    it('应该正确更新任务状态为失败', () => {
      const errorMessage = 'Test error';

      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'running', 0);
      stateManager.updateTaskStatus(
        testWorkflowId,
        'data_collection',
        'failed',
        0,
        null,
        errorMessage
      );

      const state = stateManager.getState(testWorkflowId);
      const task = state?.tasks.find((t) => t.id === 'data_collection');

      expect(task?.status).toBe('failed');
      expect(task?.error).toBe(errorMessage);
      expect(task?.endTime).toBeDefined();
    });

    it('应该正确更新整体进度', () => {
      // 完成第一个任务
      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'completed', 100);

      let state = stateManager.getState(testWorkflowId);
      expect(state?.progress).toBeGreaterThan(5); // 应该比初始进度高

      // 完成更多任务
      stateManager.updateTaskStatus(testWorkflowId, 'market_risk_analysis', 'completed', 100);
      stateManager.updateTaskStatus(testWorkflowId, 'liquidity_risk_analysis', 'completed', 100);

      state = stateManager.getState(testWorkflowId);
      expect(state?.progress).toBeGreaterThan(30); // 进度应该继续增加
    });
  });

  describe('分析完成', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
      stateManager.startAnalysis(testWorkflowId);
    });

    it('应该正确完成风险分析', () => {
      const result = { riskLevel: 'medium', score: 5 };

      stateManager.completeAnalysis(testWorkflowId, result);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.status).toBe('completed');
      expect(state?.progress).toBe(100);
      expect(state?.endTime).toBeDefined();
      expect(state?.executionTimeMs).toBeDefined();
      expect(state?.performance.totalTime).toBeDefined();
      expect(state?.currentTask).toBe('风险分析已完成');
    });

    it('应该将所有未完成任务标记为已完成', () => {
      stateManager.completeAnalysis(testWorkflowId);
      const state = stateManager.getState(testWorkflowId);

      state?.tasks.forEach((task) => {
        expect(task.status).toBe('completed');
        expect(task.progress).toBe(100);
      });
    });
  });

  describe('分析失败', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
      stateManager.startAnalysis(testWorkflowId);
    });

    it('应该正确处理分析失败', () => {
      const error = new Error('Test error');

      stateManager.failAnalysis(testWorkflowId, error);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.status).toBe('failed');
      expect(state?.error?.message).toBe('Test error');
      expect(state?.error?.code).toBe('RISK_ANALYSIS_FAILED');
      expect(state?.endTime).toBeDefined();
      expect(state?.executionTimeMs).toBeDefined();
      expect(state?.currentTask).toBe('风险分析失败');
    });

    it('应该处理字符串错误', () => {
      const errorMessage = 'String error message';

      stateManager.failAnalysis(testWorkflowId, errorMessage);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.status).toBe('failed');
      expect(state?.error?.message).toBe(errorMessage);
    });
  });

  describe('分析取消', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
      stateManager.startAnalysis(testWorkflowId);
    });

    it('应该正确取消风险分析', () => {
      stateManager.cancelAnalysis(testWorkflowId);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.status).toBe('cancelled');
      expect(state?.endTime).toBeDefined();
      expect(state?.executionTimeMs).toBeDefined();
      expect(state?.currentTask).toBe('风险分析已取消');
    });

    it('应该将运行中的任务标记为失败', () => {
      // 先启动一个任务
      stateManager.updateTaskStatus(testWorkflowId, 'data_collection', 'running', 50);

      stateManager.cancelAnalysis(testWorkflowId);
      const state = stateManager.getState(testWorkflowId);
      const task = state?.tasks.find((t) => t.id === 'data_collection');

      expect(task?.status).toBe('failed');
      expect(task?.error).toBe('分析被取消');
    });
  });

  describe('性能指标', () => {
    beforeEach(() => {
      stateManager.initializeState(testWorkflowId);
    });

    it('应该正确更新性能指标', () => {
      const metrics = {
        dataCollectionTime: 1000,
        analysisTime: 2000,
        reportGenerationTime: 500,
      };

      stateManager.updatePerformanceMetrics(testWorkflowId, metrics);
      const state = stateManager.getState(testWorkflowId);

      expect(state?.performance).toEqual(metrics);
    });

    it('应该合并性能指标', () => {
      stateManager.updatePerformanceMetrics(testWorkflowId, { dataCollectionTime: 1000 });
      stateManager.updatePerformanceMetrics(testWorkflowId, { analysisTime: 2000 });

      const state = stateManager.getState(testWorkflowId);

      expect(state?.performance.dataCollectionTime).toBe(1000);
      expect(state?.performance.analysisTime).toBe(2000);
    });
  });

  describe('统计信息', () => {
    it('应该返回正确的统计信息', () => {
      // 创建多个状态
      stateManager.initializeState('workflow1');
      stateManager.initializeState('workflow2');
      stateManager.initializeState('workflow3');

      // 启动一些分析
      stateManager.startAnalysis('workflow1');
      stateManager.startAnalysis('workflow2');

      // 完成一个分析
      stateManager.completeAnalysis('workflow1');

      // 失败一个分析
      stateManager.failAnalysis('workflow2', 'Test error');

      const stats = stateManager.getStatistics();

      expect(stats.total).toBe(3);
      expect(stats.pending).toBe(1);
      expect(stats.running).toBe(0);
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
      expect(stats.cancelled).toBe(0);

      // 清理
      stateManager.cleanupState('workflow1');
      stateManager.cleanupState('workflow2');
      stateManager.cleanupState('workflow3');
    });
  });

  describe('状态清理', () => {
    it('应该正确清理状态', () => {
      stateManager.initializeState(testWorkflowId);
      expect(stateManager.getState(testWorkflowId)).toBeDefined();

      stateManager.cleanupState(testWorkflowId);
      expect(stateManager.getState(testWorkflowId)).toBeUndefined();
    });
  });

  describe('错误处理', () => {
    it('应该处理不存在的工作流ID', () => {
      expect(() => {
        stateManager.startAnalysis('non-existent-workflow');
      }).toThrow('Risk manager state not found for workflow non-existent-workflow');
    });

    it('应该处理不存在的任务ID', () => {
      stateManager.initializeState(testWorkflowId);

      // 更新不存在的任务不应该抛出错误
      expect(() => {
        stateManager.updateTaskStatus(testWorkflowId, 'non-existent-task', 'running');
      }).not.toThrow();
    });
  });
});
