/**
 * Robots.txt API 路由
 * 使用 RobotsGenerator 动态生成 robots.txt 文件
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRobotsGenerator } from '@/lib/seo/robots-generator';

/**
 * GET 请求处理器
 * 生成动态 robots.txt
 */
export async function GET(_request: NextRequest) {
  try {
    const generator = createRobotsGenerator();
    const robotsTxt = generator.generateRobotsTxt();

    // 验证生成的内容
    if (!generator.validateRobotsTxt(robotsTxt)) {
      console.warn('Generated robots.txt failed validation');
    }

    return new NextResponse(robotsTxt, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // 24小时缓存
        'X-Robots-Tag': 'noindex',
      },
    });
  } catch (error) {
    console.error('Error generating robots.txt:', error);

    // 返回基本的 robots.txt 作为后备
    const fallbackRobots = `User-agent: *\nDisallow: /api/\nDisallow: /admin/`;

    return new NextResponse(fallbackRobots, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
      },
    });
  }
}
