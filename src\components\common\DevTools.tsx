'use client';

import { useEffect, useState } from 'react';

/**
 * 开发工具组件
 * 在开发环境下显示环境变量和系统状态
 */
export function DevTools() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'env' | 'system' | 'api'>('env');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 只在开发环境下显示，并且避免水合不匹配
  if (process.env.NODE_ENV !== 'development' || !mounted) {
    return null;
  }

  // 动态获取环境摘要，避免导入问题
  const [summary, setSummary] = useState({
    environment: 'development',
    configuredCount: 0,
    totalConfigs: 0,
  });

  useEffect(() => {
    // 动态导入环境工具
    const loadEnvSummary = async () => {
      try {
        const { getEnvironmentSummary } = await import('@/utils/env-logger');
        setSummary(getEnvironmentSummary());
      } catch (error) {
        console.warn('Failed to load environment summary:', error);
      }
    };

    if (mounted) {
      loadEnvSummary();
    }
  }, [mounted]);

  const envVars = [
    {
      name: 'NODE_ENV',
      value: process.env.NODE_ENV,
      status: process.env.NODE_ENV ? 'success' : 'error',
    },
    {
      name: 'NEXT_PUBLIC_API_BASE_URL',
      value: process.env.NEXT_PUBLIC_API_BASE_URL,
      status: process.env.NEXT_PUBLIC_API_BASE_URL ? 'success' : 'error',
    },
    {
      name: 'NEXT_PUBLIC_API_BACKEND_BASE_URL',
      value: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL,
      status: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL ? 'success' : 'warning',
    },
    {
      name: 'NEXT_PUBLIC_WS_URL',
      value: process.env.NEXT_PUBLIC_WS_URL,
      status: process.env.NEXT_PUBLIC_WS_URL ? 'success' : 'warning',
    },
    {
      name: 'NEXT_PUBLIC_OPENAI_API_KEY',
      value: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
      status: process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 'success' : 'error',
    },
    {
      name: 'NEXT_PUBLIC_FINNHUB_API_KEY',
      value: process.env.NEXT_PUBLIC_FINNHUB_API_KEY,
      status: process.env.NEXT_PUBLIC_FINNHUB_API_KEY ? 'success' : 'warning',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <>
      {/* 开发工具触发按钮 */}
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
          title="开发工具"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        </button>
      </div>

      {/* 开发工具面板 */}
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={() => setIsOpen(false)}>
          <div
            className="fixed right-4 bottom-20 w-96 max-h-[80vh] bg-white dark:bg-gray-800 rounded-lg shadow-2xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="bg-blue-600 text-white p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">🛠️ 开发工具</h3>
                <button onClick={() => setIsOpen(false)} className="text-white hover:text-gray-200">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* 环境摘要 */}
              <div className="mt-2 text-sm">
                <div className="flex items-center space-x-4">
                  <span>环境: {summary.environment}</span>
                  <span>
                    配置: {summary.configuredCount}/{summary.totalConfigs}
                  </span>
                </div>
              </div>
            </div>

            {/* 标签页 */}
            <div className="flex border-b border-gray-200 dark:border-gray-600">
              {[
                { key: 'env', label: '环境变量', icon: '🔧' },
                { key: 'system', label: '系统信息', icon: '💻' },
                { key: 'api', label: 'API状态', icon: '🌐' },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`flex-1 px-4 py-2 text-sm font-medium ${
                    activeTab === tab.key
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                  }`}
                >
                  {tab.icon} {tab.label}
                </button>
              ))}
            </div>

            {/* 内容区域 */}
            <div className="p-4 max-h-96 overflow-y-auto">
              {activeTab === 'env' && (
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 dark:text-white">客户端环境变量</h4>
                  {envVars.map((env) => (
                    <div
                      key={env.name}
                      className="border border-gray-200 dark:border-gray-600 rounded-lg p-3"
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-mono text-sm text-gray-900 dark:text-white">
                          {env.name}
                        </span>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                            env.status
                          )}`}
                        >
                          {getStatusIcon(env.status)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-300 font-mono break-all">
                        {env.value
                          ? env.name.includes('KEY') && env.value.length > 8
                            ? `${env.value.substring(0, 6)}****${env.value.substring(
                                env.value.length - 4
                              )}`
                            : env.value
                          : '未设置'}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'system' && (
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 dark:text-white">系统信息</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">用户代理:</span>
                      <span className="text-gray-900 dark:text-white text-xs">
                        {navigator.userAgent.split(' ')[0]}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">屏幕分辨率:</span>
                      <span className="text-gray-900 dark:text-white">
                        {window.screen.width}x{window.screen.height}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">视口大小:</span>
                      <span className="text-gray-900 dark:text-white">
                        {window.innerWidth}x{window.innerHeight}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">时区:</span>
                      <span className="text-gray-900 dark:text-white">
                        {Intl.DateTimeFormat().resolvedOptions().timeZone}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">语言:</span>
                      <span className="text-gray-900 dark:text-white">{navigator.language}</span>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'api' && (
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 dark:text-white">API连接状态</h4>
                  <div className="space-y-2">
                    {[
                      { name: '前端API', url: process.env.NEXT_PUBLIC_API_BASE_URL },
                      { name: '后端API', url: process.env.NEXT_PUBLIC_API_BACKEND_BASE_URL },
                      { name: 'WebSocket', url: process.env.NEXT_PUBLIC_WS_URL },
                    ].map((api) => (
                      <div
                        key={api.name}
                        className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded"
                      >
                        <span className="text-sm text-gray-900 dark:text-white">{api.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-600 dark:text-gray-300">
                            {api.url || '未配置'}
                          </span>
                          <span
                            className={`w-2 h-2 rounded-full ${
                              api.url ? 'bg-green-500' : 'bg-red-500'
                            }`}
                          ></span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 底部操作 */}
            <div className="border-t border-gray-200 dark:border-gray-600 p-4">
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    console.clear();
                    console.log('🧹 控制台已清空');
                  }}
                  className="flex-1 px-3 py-2 text-xs bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded"
                >
                  清空控制台
                </button>
                <button
                  onClick={() => {
                    const envInfo = envVars
                      .map((env) => `${env.name}: ${env.value || '未设置'}`)
                      .join('\n');
                    navigator.clipboard.writeText(envInfo);
                    console.log('📋 环境变量信息已复制到剪贴板');
                  }}
                  className="flex-1 px-3 py-2 text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded"
                >
                  复制环境变量
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
