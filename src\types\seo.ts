/**
 * SEO 相关的 TypeScript 类型定义
 * 支持多语言、动态元数据生成和结构化数据
 */

import { Metadata } from 'next';

// 支持的语言类型
export type SupportedLocale = 'zh' | 'en';

// 页面类型枚举
export type PageType = 'home' | 'analysis' | 'tasks' | 'messages' | 'create-task';

// 语言检测结果
export interface LanguageDetectionResult {
  locale: SupportedLocale;
  cleanPath: string;
  shouldRedirect: boolean;
  redirectUrl?: string;
}

// hreflang 链接配置
export interface HreflangLink {
  hreflang: string;
  href: string;
}

// 语言切换链接
export interface LanguageSwitchLink {
  locale: SupportedLocale;
  label: string;
  href: string;
  active: boolean;
}

// SEO 配置接口
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  canonical?: string;
  openGraph: OpenGraphConfig;
  twitter: TwitterCardConfig;
  structuredData?: Record<string, any>;
}

// Open Graph 配置
export interface OpenGraphConfig {
  title: string;
  description: string;
  image: string;
  type: 'website' | 'article';
  locale: 'zh_CN' | 'en_US';
  siteName?: string;
  url?: string;
}

// Twitter Card 配置
export interface TwitterCardConfig {
  card: 'summary_large_image' | 'summary' | 'app' | 'player';
  title: string;
  description: string;
  image: string;
  site?: string;
  creator?: string;
}

// 页面 SEO 属性
export interface PageSEOProps {
  page: PageType;
  dynamicData?: {
    analysisId?: string;
    stockSymbol?: string;
    taskTitle?: string;
    taskId?: string;
  };
  locale?: SupportedLocale;
}

// SEO 页面配置数据模型
export interface SEOPageConfig {
  id: string;
  path: string;
  title: Record<SupportedLocale, string>;
  description: Record<SupportedLocale, string>;
  keywords: Record<SupportedLocale, string[]>;
  structuredData?: Record<string, any>;
  lastModified: Date;
  priority: number;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
}

// SEO 分析数据
export interface SEOAnalytics {
  pageUrl: string;
  impressions: number;
  clicks: number;
  ctr: number;
  position: number;
  date: Date;
}

// 结构化数据类型
export interface StructuredDataConfig {
  organization?: OrganizationSchema;
  website?: WebsiteSchema;
  softwareApplication?: SoftwareApplicationSchema;
  breadcrumb?: BreadcrumbSchema;
  financialService?: FinancialServiceSchema;
  investmentService?: InvestmentServiceSchema;
  tradingPlatform?: TradingPlatformSchema;
  analysisReport?: AnalysisReportSchema;
  stockAnalysis?: StockAnalysisSchema;
  aiAgent?: AIAgentSchema;
}

// 金融服务相关的结构化数据类型
export interface FinancialServiceSchema {
  '@context': 'https://schema.org';
  '@type': 'FinancialProduct';
  name: string;
  description: string;
  provider: OrganizationSchema;
  category: string;
  feesAndCommissionsSpecification?: string;
  interestRate?: string;
  annualPercentageRate?: string;
}

export interface InvestmentServiceSchema {
  '@context': 'https://schema.org';
  '@type': 'Service';
  name: string;
  description: string;
  provider: OrganizationSchema;
  serviceType: 'Investment Advisory' | 'Portfolio Management' | 'Trading Platform';
  areaServed: string[];
  availableChannel: string[];
}

export interface TradingPlatformSchema {
  '@context': 'https://schema.org';
  '@type': 'SoftwareApplication';
  name: string;
  description: string;
  applicationCategory: 'FinanceApplication';
  operatingSystem: string[];
  softwareVersion?: string;
  releaseNotes?: string;
  screenshot?: string[];
  featureList?: string[];
  requirements?: string;
  downloadUrl?: string;
  installUrl?: string;
}

export interface AnalysisReportSchema {
  '@context': 'https://schema.org';
  '@type': 'Report';
  name: string;
  description: string;
  author: OrganizationSchema;
  datePublished: string;
  dateModified?: string;
  about: string;
  abstract: string;
  keywords: string[];
  inLanguage: string;
}

export interface StockAnalysisSchema {
  '@context': 'https://schema.org';
  '@type': 'AnalysisNewsArticle';
  headline: string;
  description: string;
  author: OrganizationSchema;
  datePublished: string;
  dateModified?: string;
  about: {
    '@type': 'Corporation';
    name: string;
    tickerSymbol: string;
  };
  analysisType: 'Technical' | 'Fundamental' | 'Sentiment' | 'Risk';
  recommendation?: 'Buy' | 'Sell' | 'Hold';
  targetPrice?: number;
  currency?: string;
}

export interface AIAgentSchema {
  '@context': 'https://schema.org';
  '@type': 'SoftwareAgent';
  name: string;
  description: string;
  creator: OrganizationSchema;
  applicationCategory: 'AI Assistant' | 'Financial Analyst' | 'Trading Bot';
  capabilities: string[];
  programmingLanguage?: string[];
  operatingSystem?: string[];
}

// Schema.org 组织信息
export interface OrganizationSchema {
  '@context': 'https://schema.org';
  '@type': 'Organization';
  name: string;
  description: string;
  url: string;
  logo: string;
  contactPoint?: {
    '@type': 'ContactPoint';
    contactType: string;
    availableLanguage: string[];
  };
  sameAs?: string[];
}

// Schema.org 网站信息
export interface WebsiteSchema {
  '@context': 'https://schema.org';
  '@type': 'WebSite';
  name: string;
  url: string;
  description: string;
  potentialAction?: {
    '@type': 'SearchAction';
    target: string;
    'query-input': string;
  };
}

// Schema.org 软件应用信息
export interface SoftwareApplicationSchema {
  '@context': 'https://schema.org';
  '@type': 'SoftwareApplication';
  name: string;
  applicationCategory: string;
  operatingSystem: string;
  description: string;
  offers?: {
    '@type': 'Offer';
    price: string;
    priceCurrency: string;
  };
  aggregateRating?: {
    '@type': 'AggregateRating';
    ratingValue: string;
    ratingCount: string;
  };
}

// Schema.org 面包屑导航
export interface BreadcrumbSchema {
  '@context': 'https://schema.org';
  '@type': 'BreadcrumbList';
  itemListElement: Array<{
    '@type': 'ListItem';
    position: number;
    name: string;
    item: string;
  }>;
}

// SEO 测试结果
export interface SEOTestResult {
  title: boolean;
  description: boolean;
  structuredData: boolean;
  performance: boolean;
  accessibility: boolean;
  score: number;
}

// Core Web Vitals 指标
export interface CoreWebVitals {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
}

// SEO 监控配置
export interface SEOMonitorConfig {
  enableAnalytics: boolean;
  enableWebVitals: boolean;
  enableStructuredDataValidation: boolean;
  reportingInterval: number;
}
