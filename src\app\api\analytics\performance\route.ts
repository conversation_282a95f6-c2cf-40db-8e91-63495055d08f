import { NextRequest, NextResponse } from 'next/server';

interface PerformanceMetric {
  metric: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  url: string;
  timestamp: number;
  userAgent?: string;
  connection?: string;
}

// 存储性能数据的简单内存存储（生产环境应使用数据库）
const performanceData: PerformanceMetric[] = [];

export async function POST(request: NextRequest) {
  try {
    const data: PerformanceMetric = await request.json();

    // 验证数据
    if (!data.metric || typeof data.value !== 'number' || !data.url) {
      return NextResponse.json({ error: 'Invalid performance data' }, { status: 400 });
    }

    // 添加额外信息
    const enrichedData: PerformanceMetric = {
      ...data,
      userAgent: request.headers.get('user-agent') || undefined,
      connection: request.headers.get('connection') || undefined,
    };

    // 存储数据
    performanceData.push(enrichedData);

    // 保持数据量在合理范围内（最多保留1000条记录）
    if (performanceData.length > 1000) {
      performanceData.splice(0, performanceData.length - 1000);
    }

    // 检查是否需要发送警报
    if (data.rating === 'poor') {
      console.warn(`Poor performance detected: ${data.metric} = ${data.value} on ${data.url}`);

      // 这里可以集成警报系统，如发送邮件或推送通知
      await sendPerformanceAlert(enrichedData);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing performance data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const metric = searchParams.get('metric');
    const limit = parseInt(searchParams.get('limit') || '100');
    const since = searchParams.get('since');

    let filteredData = performanceData;

    // 按指标过滤
    if (metric) {
      filteredData = filteredData.filter((d) => d.metric === metric);
    }

    // 按时间过滤
    if (since) {
      const sinceTimestamp = parseInt(since);
      filteredData = filteredData.filter((d) => d.timestamp >= sinceTimestamp);
    }

    // 限制返回数量
    const limitedData = filteredData.slice(-limit);

    // 计算统计信息
    const stats = calculatePerformanceStats(limitedData);

    return NextResponse.json({
      data: limitedData,
      stats,
      total: filteredData.length,
    });
  } catch (error) {
    console.error('Error retrieving performance data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * 发送性能警报
 */
async function sendPerformanceAlert(data: PerformanceMetric): Promise<void> {
  // 这里可以实现具体的警报逻辑
  // 例如：发送邮件、Slack 通知、推送到监控系统等

  console.log('Performance alert:', {
    metric: data.metric,
    value: data.value,
    url: data.url,
    timestamp: new Date(data.timestamp).toISOString(),
  });

  // 示例：发送到外部监控服务
  if (process.env.PERFORMANCE_WEBHOOK_URL) {
    try {
      await fetch(process.env.PERFORMANCE_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: `🚨 性能警报: ${data.metric} 指标为 ${data.value}，评级为 ${data.rating}`,
          url: data.url,
          timestamp: data.timestamp,
        }),
      });
    } catch (error) {
      console.error('Failed to send performance alert:', error);
    }
  }
}

/**
 * 计算性能统计信息
 */
function calculatePerformanceStats(data: PerformanceMetric[]) {
  if (data.length === 0) {
    return {
      averages: {},
      ratings: {},
      trends: {},
    };
  }

  const metricGroups = data.reduce((groups, item) => {
    if (!groups[item.metric]) {
      groups[item.metric] = [];
    }
    groups[item.metric].push(item);
    return groups;
  }, {} as Record<string, PerformanceMetric[]>);

  const averages: Record<string, number> = {};
  const ratings: Record<string, Record<string, number>> = {};
  const trends: Record<string, 'improving' | 'stable' | 'degrading'> = {};

  Object.entries(metricGroups).forEach(([metric, values]) => {
    // 计算平均值
    averages[metric] = values.reduce((sum, v) => sum + v.value, 0) / values.length;

    // 计算评级分布
    ratings[metric] = values.reduce((counts, v) => {
      counts[v.rating] = (counts[v.rating] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    // 计算趋势（比较最近25%和之前25%的数据）
    if (values.length >= 8) {
      const sortedValues = values.sort((a, b) => a.timestamp - b.timestamp);
      const quarterSize = Math.floor(sortedValues.length / 4);
      const oldValues = sortedValues.slice(0, quarterSize);
      const newValues = sortedValues.slice(-quarterSize);

      const oldAvg = oldValues.reduce((sum, v) => sum + v.value, 0) / oldValues.length;
      const newAvg = newValues.reduce((sum, v) => sum + v.value, 0) / newValues.length;

      const changePercent = ((newAvg - oldAvg) / oldAvg) * 100;

      if (changePercent < -5) {
        trends[metric] = 'improving'; // 值降低是好事（除了某些指标）
      } else if (changePercent > 5) {
        trends[metric] = 'degrading';
      } else {
        trends[metric] = 'stable';
      }
    }
  });

  return {
    averages,
    ratings,
    trends,
  };
}
