// State Synchronization Service Tests
// Tests for real-time state updates, caching, and notification functionality

import { analysisService } from '../analysis-service';
import { EnhancedLangGraphDatabase } from '../enhanced-langgraph-database';
import {
  AgentStateChangeEvent,
  StateSyncService,
  WorkflowStatusUpdate,
} from '../state-sync-service';

// Mock dependencies
jest.mock('../enhanced-langgraph-database');
jest.mock('../analysis-service');
jest.mock('../akshare/adapter');
jest.mock('../langgraph-server');

const mockEnhancedLangGraphDatabase = EnhancedLangGraphDatabase as jest.Mocked<
  typeof EnhancedLangGraphDatabase
>;
const mockAnalysisService = analysisService as jest.Mocked<typeof analysisService>;

describe('StateSyncService', () => {
  let stateSyncService: StateSyncService;

  beforeEach(() => {
    // Setup mocks
    mockEnhancedLangGraphDatabase.batchUpdateAgentStates = jest.fn().mockResolvedValue(undefined);
    mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue(undefined);
    mockEnhancedLangGraphDatabase.updateWorkflowStatus = jest.fn().mockResolvedValue(undefined);
    mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({
      status: 'running',
      progress: 50,
      currentStage: 'analysis',
    });
    mockAnalysisService.on = jest.fn();
    mockAnalysisService.emit = jest.fn();

    stateSyncService = new StateSyncService();
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    // Clean up the service instance to clear timers
    if (stateSyncService) {
      stateSyncService.destroy();
    }
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe('handleAgentStateChange', () => {
    it('should handle agent state change successfully', async () => {
      const event: AgentStateChangeEvent = {
        workflowId: 'workflow_123',
        agentType: 'fundamental',
        previousStatus: 'running',
        newStatus: 'completed',
        executionTime: 5000,
        result: { analysis: 'positive' },
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates = jest.fn().mockResolvedValue(undefined);
      mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue('event_123');
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({
        workflowId: 'workflow_123',
        status: 'running',
        progress: 50,
      });

      const stateUpdateSpy = jest.fn();
      stateSyncService.on('stateUpdate', stateUpdateSpy);

      const agentStateChangedSpy = jest.fn();
      stateSyncService.on('agentStateChanged', agentStateChangedSpy);

      await stateSyncService.handleAgentStateChange(event);

      expect(mockEnhancedLangGraphDatabase.batchUpdateAgentStates).toHaveBeenCalledWith([
        {
          workflow_id: 'workflow_123',
          analyst_type: 'fundamental',
          status: 'completed',
          execution_time_ms: 5000,
        },
      ]);

      expect(mockEnhancedLangGraphDatabase.logWorkflowEvent).toHaveBeenCalledWith({
        workflow_id: 'workflow_123',
        stage_name: 'agent_fundamental',
        event_type: 'state_change',
        content: '智能体 fundamental 状态变更: running -> completed',
        metadata: {
          agentType: 'fundamental',
          previousStatus: 'running',
          newStatus: 'completed',
          executionTime: 5000,
          result: { analysis: 'positive' },
          error: undefined,
        },
      });

      expect(agentStateChangedSpy).toHaveBeenCalledWith(event);
    });

    it('should handle agent state change errors gracefully', async () => {
      const event: AgentStateChangeEvent = {
        workflowId: 'workflow_123',
        agentType: 'technical',
        previousStatus: 'running',
        newStatus: 'failed',
        error: 'Analysis failed',
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates = jest
        .fn()
        .mockRejectedValue(new Error('Database error'));

      const errorSpy = jest.fn();
      stateSyncService.on('error', errorSpy);

      await stateSyncService.handleAgentStateChange(event);

      expect(errorSpy).toHaveBeenCalledWith({
        type: 'agent_state_change_error',
        error: expect.any(Error),
        event,
      });
    });
  });

  describe('handleWorkflowStatusUpdate', () => {
    it('should handle workflow status update successfully', async () => {
      const update: WorkflowStatusUpdate = {
        workflowId: 'workflow_123',
        status: 'running',
        progress: 75,
        currentStage: 'research',
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.updateWorkflowStatus = jest.fn().mockResolvedValue(undefined);
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({
        workflowId: 'workflow_123',
        status: 'running',
        progress: 75,
      });

      const workflowStatusUpdatedSpy = jest.fn();
      stateSyncService.on('workflowStatusUpdated', workflowStatusUpdatedSpy);

      await stateSyncService.handleWorkflowStatusUpdate(update);

      expect(mockEnhancedLangGraphDatabase.updateWorkflowStatus).toHaveBeenCalledWith({
        workflow_id: 'workflow_123',
        current_stage: 'research',
        progress: 75,
        status: 'running',
        error_message: undefined,
      });

      expect(workflowStatusUpdatedSpy).toHaveBeenCalledWith(update);
    });

    it('should send notification for completed workflow', async () => {
      const update: WorkflowStatusUpdate = {
        workflowId: 'workflow_123',
        status: 'completed',
        progress: 100,
        currentStage: 'completed',
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.updateWorkflowStatus = jest.fn().mockResolvedValue(undefined);
      mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue('event_123');
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({
        workflowId: 'workflow_123',
        status: 'completed',
        progress: 100,
      });

      const notificationSpy = jest.fn();
      stateSyncService.on('notification', notificationSpy);

      await stateSyncService.handleWorkflowStatusUpdate(update);

      expect(notificationSpy).toHaveBeenCalledWith({
        type: 'analysis_complete',
        workflowId: 'workflow_123',
        title: '分析完成',
        message: '股票分析已成功完成',
        data: {
          status: 'completed',
          progress: 100,
          currentStage: 'completed',
          error: undefined,
        },
        timestamp: expect.any(Date),
        priority: 'high',
      });
    });
  });

  describe('subscription management', () => {
    it('should manage workflow subscriptions correctly', () => {
      const workflowId = 'workflow_123';
      const subscriber1 = 'user_1';
      const subscriber2 = 'user_2';

      // Subscribe users
      stateSyncService.subscribeToWorkflow(workflowId, subscriber1);
      stateSyncService.subscribeToWorkflow(workflowId, subscriber2);

      // Check subscriber stats
      const stats = stateSyncService.getSubscriberStats();
      expect(stats.workflows).toBe(1);
      expect(stats.totalSubscribers).toBe(2);

      // Unsubscribe one user
      stateSyncService.unsubscribeFromWorkflow(workflowId, subscriber1);

      const updatedStats = stateSyncService.getSubscriberStats();
      expect(updatedStats.workflows).toBe(1);
      expect(updatedStats.totalSubscribers).toBe(1);

      // Unsubscribe last user
      stateSyncService.unsubscribeFromWorkflow(workflowId, subscriber2);

      const finalStats = stateSyncService.getSubscriberStats();
      expect(finalStats.workflows).toBe(0);
      expect(finalStats.totalSubscribers).toBe(0);
    });

    it('should broadcast state updates to subscribers', async () => {
      const workflowId = 'workflow_123';
      const subscriber1 = 'user_1';
      const subscriber2 = 'user_2';

      stateSyncService.subscribeToWorkflow(workflowId, subscriber1);
      stateSyncService.subscribeToWorkflow(workflowId, subscriber2);

      const stateUpdateSpy = jest.fn();
      stateSyncService.on('stateUpdate', stateUpdateSpy);

      // Trigger a state update
      const update: WorkflowStatusUpdate = {
        workflowId,
        status: 'running',
        progress: 50,
        currentStage: 'analysis',
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.updateWorkflowStatus = jest.fn().mockResolvedValue(undefined);
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({
        workflowId,
        status: 'running',
        progress: 50,
      });

      await stateSyncService.handleWorkflowStatusUpdate(update);

      expect(stateUpdateSpy).toHaveBeenCalledWith({
        workflowId,
        subscribers: expect.arrayContaining([subscriber1, subscriber2]),
        data: expect.objectContaining({
          workflowId,
          type: 'workflow_status_update',
          status: 'running',
          progress: 50,
          currentStage: 'analysis',
        }),
      });
    });
  });

  describe('caching system', () => {
    it('should set and get cache entries correctly', () => {
      const key = 'test_key';
      const data = { test: 'data' };
      const ttl = 5000;

      stateSyncService.setCache(key, data, ttl);

      const retrieved = stateSyncService.getFromCache(key);
      expect(retrieved).toEqual(data);

      const stats = stateSyncService.getCacheStats();
      expect(stats.size).toBe(1);
      expect(stats.entries).toContain(key);
    });

    it('should return null for expired cache entries', () => {
      const key = 'test_key';
      const data = { test: 'data' };
      const ttl = 100; // Very short TTL

      stateSyncService.setCache(key, data, ttl);

      // Fast forward time beyond TTL
      jest.advanceTimersByTime(200);

      const retrieved = stateSyncService.getFromCache(key);
      expect(retrieved).toBeNull();
    });

    it('should clean up expired cache entries', () => {
      const key1 = 'key1';
      const key2 = 'key2';
      const data = { test: 'data' };

      stateSyncService.setCache(key1, data, 100); // Short TTL
      stateSyncService.setCache(key2, data, 60000); // Long TTL

      expect(stateSyncService.getCacheStats().size).toBe(2);

      // Fast forward time to expire first entry
      jest.advanceTimersByTime(150);

      // Manually trigger cache cleanup
      const cleanedCount = stateSyncService.triggerCacheCleanup();
      expect(cleanedCount).toBe(1);

      // Check that expired entry is removed and valid entry remains
      const statsAfterCleanup = stateSyncService.getCacheStats();
      expect(statsAfterCleanup.size).toBe(1);
      expect(statsAfterCleanup.entries).not.toContain(key1);
      expect(statsAfterCleanup.entries).toContain(key2);

      // Verify the remaining entry is still accessible
      expect(stateSyncService.getFromCache(key2)).toEqual(data);
      expect(stateSyncService.getFromCache(key1)).toBeNull();
    });
  });

  describe('getCurrentWorkflowState', () => {
    it('should return cached state if available', async () => {
      const workflowId = 'workflow_123';
      const cachedState = { status: 'running', progress: 50 };

      stateSyncService.setCache(`workflow_${workflowId}`, cachedState);

      const result = await stateSyncService.getCurrentWorkflowState(workflowId);

      expect(result).toEqual(cachedState);
      expect(mockAnalysisService.getAnalysisStatus).not.toHaveBeenCalled();
    });

    it('should fetch from database and cache if not in cache', async () => {
      const workflowId = 'workflow_123';
      const dbState = { status: 'running', progress: 75 };

      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue(dbState);

      const result = await stateSyncService.getCurrentWorkflowState(workflowId);

      expect(result).toEqual(dbState);
      expect(mockAnalysisService.getAnalysisStatus).toHaveBeenCalledWith(workflowId);

      // Should now be cached
      const cachedResult = stateSyncService.getFromCache(`workflow_${workflowId}`);
      expect(cachedResult).toEqual(dbState);
    });
  });

  describe('notification system', () => {
    it('should send agent completion notification', async () => {
      const event: AgentStateChangeEvent = {
        workflowId: 'workflow_123',
        agentType: 'sentiment',
        previousStatus: 'running',
        newStatus: 'completed',
        executionTime: 3000,
        result: { sentiment: 'positive' },
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates = jest.fn().mockResolvedValue(undefined);
      mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue('event_123');
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({});

      const notificationSpy = jest.fn();
      stateSyncService.on('notification', notificationSpy);

      await stateSyncService.handleAgentStateChange(event);

      expect(notificationSpy).toHaveBeenCalledWith({
        type: 'agent_complete',
        workflowId: 'workflow_123',
        title: '智能体 sentiment 完成',
        message: 'sentiment 分析师已完成分析',
        data: {
          agentType: 'sentiment',
          status: 'completed',
          executionTime: 3000,
          result: { sentiment: 'positive' },
          error: undefined,
        },
        timestamp: expect.any(Date),
        priority: 'medium',
      });
    });

    it('should send high priority notification for failed agent', async () => {
      const event: AgentStateChangeEvent = {
        workflowId: 'workflow_123',
        agentType: 'technical',
        previousStatus: 'running',
        newStatus: 'failed',
        error: 'Connection timeout',
        timestamp: new Date(),
      };

      mockEnhancedLangGraphDatabase.batchUpdateAgentStates = jest.fn().mockResolvedValue(undefined);
      mockEnhancedLangGraphDatabase.logWorkflowEvent = jest.fn().mockResolvedValue('event_123');
      mockAnalysisService.getAnalysisStatus = jest.fn().mockResolvedValue({});

      const notificationSpy = jest.fn();
      stateSyncService.on('notification', notificationSpy);

      await stateSyncService.handleAgentStateChange(event);

      expect(notificationSpy).toHaveBeenCalledWith({
        type: 'agent_complete',
        workflowId: 'workflow_123',
        title: '智能体 technical 失败',
        message: 'technical 分析师分析失败: Connection timeout',
        data: {
          agentType: 'technical',
          status: 'failed',
          executionTime: undefined,
          result: undefined,
          error: 'Connection timeout',
        },
        timestamp: expect.any(Date),
        priority: 'high',
      });
    });
  });
});
