/**
 * 风险场景分析与压力测试模块
 * 实现市场下跌场景模拟、蒙特卡洛模拟、压力测试计算逻辑和相关性分析
 */

import { PriceData } from './risk-metrics-calculator';

export interface ScenarioAnalysisResult {
  scenario_name: string;
  scenario_type: 'market_decline' | 'interest_rate' | 'sector_shock' | 'monte_carlo';
  scenario_parameters: any;
  expected_loss: number;
  probability: number;
  confidence_interval: {
    lower_bound: number;
    upper_bound: number;
  };
  risk_metrics: {
    var_95: number;
    var_99: number;
    expected_shortfall: number;
    max_drawdown: number;
  };
  correlation_changes: {
    original_correlation: number;
    stressed_correlation: number;
    correlation_change: number;
  };
}

export interface StressTestResult {
  test_name: string;
  scenarios: ScenarioAnalysisResult[];
  summary: {
    worst_case_loss: number;
    average_loss: number;
    probability_of_loss: number;
    stress_test_score: number;
  };
  recommendations: string[];
}

export interface MonteCarloSimulation {
  simulation_id: string;
  parameters: {
    num_simulations: number;
    time_horizon_days: number;
    initial_price: number;
    drift: number;
    volatility: number;
  };
  results: {
    price_paths: number[][];
    final_prices: number[];
    returns: number[];
    percentiles: {
      p5: number;
      p10: number;
      p25: number;
      p50: number;
      p75: number;
      p90: number;
      p95: number;
    };
    risk_metrics: {
      var_95: number;
      var_99: number;
      expected_shortfall: number;
      probability_of_loss: number;
    };
  };
}

/**
 * 风险场景分析器类
 */
export class RiskScenarioAnalyzer {
  private static instance: RiskScenarioAnalyzer;

  public static getInstance(): RiskScenarioAnalyzer {
    if (!RiskScenarioAnalyzer.instance) {
      RiskScenarioAnalyzer.instance = new RiskScenarioAnalyzer();
    }
    return RiskScenarioAnalyzer.instance;
  }

  /**
   * 需求 5.1: 模拟市场下跌场景（-10%、-20%、-30%）
   */
  public simulateMarketDeclineScenarios(
    priceData: PriceData[],
    marketData?: PriceData[]
  ): ScenarioAnalysisResult[] {
    console.log('[RiskScenarioAnalyzer] Simulating market decline scenarios');

    // 验证输入数据
    if (!priceData || priceData.length === 0) {
      console.warn('[RiskScenarioAnalyzer] Empty price data provided, returning empty results');
      return [];
    }

    const declineScenarios = [-0.1, -0.2, -0.3]; // -10%, -20%, -30%
    const results: ScenarioAnalysisResult[] = [];

    const currentPrice = priceData[priceData.length - 1].close;
    const returns = this.calculateReturns(priceData);
    const volatility = this.standardDeviation(returns);

    // 计算原始相关性（如果有市场数据）
    let originalCorrelation = 0;
    if (marketData && marketData.length === priceData.length) {
      const marketReturns = this.calculateReturns(marketData);
      originalCorrelation = this.correlation(returns, marketReturns);
    }

    for (const decline of declineScenarios) {
      const scenarioPrice = currentPrice * (1 + decline);
      const expectedLoss = Math.abs(decline);

      // 在压力下相关性通常会增加
      const stressedCorrelation = Math.min(1, originalCorrelation * 1.3 + 0.2);
      const correlationChange = stressedCorrelation - originalCorrelation;

      // 计算压力下的风险指标
      const stressedReturns = this.simulateStressedReturns(returns, decline, volatility);
      const riskMetrics = this.calculateStressedRiskMetrics(stressedReturns);

      // 估算场景概率（基于历史数据）
      const probability = this.estimateScenarioProbability(returns, decline);

      results.push({
        scenario_name: `市场下跌${Math.abs(decline * 100)}%`,
        scenario_type: 'market_decline',
        scenario_parameters: {
          decline_percentage: decline,
          scenario_price: scenarioPrice,
          current_price: currentPrice,
        },
        expected_loss: expectedLoss,
        probability: probability,
        confidence_interval: {
          lower_bound: expectedLoss * 0.8,
          upper_bound: expectedLoss * 1.2,
        },
        risk_metrics: riskMetrics,
        correlation_changes: {
          original_correlation: originalCorrelation,
          stressed_correlation: stressedCorrelation,
          correlation_change: correlationChange,
        },
      });
    }

    console.log('[RiskScenarioAnalyzer] Market decline scenarios completed');
    return results;
  }

  /**
   * 需求 5.2: 分析利率变动对股价的影响
   */
  public analyzeInterestRateImpact(
    priceData: PriceData[],
    rateChanges: number[] = [0.005, 0.01, 0.02] // 0.5%, 1%, 2%
  ): ScenarioAnalysisResult[] {
    console.log('[RiskScenarioAnalyzer] Analyzing interest rate impact');

    const results: ScenarioAnalysisResult[] = [];
    const returns = this.calculateReturns(priceData);
    const currentPrice = priceData[priceData.length - 1].close;

    for (const rateChange of rateChanges) {
      // 利率敏感性估算（简化模型）
      // 假设利率上升1%，股价下跌5%（可根据行业调整）
      const priceImpact = -rateChange * 5;
      const expectedLoss = Math.abs(priceImpact);

      // 利率冲击下的相关性变化
      const originalCorrelation = 0.6; // 假设值
      const stressedCorrelation = Math.min(1, originalCorrelation * 1.2);

      // 模拟利率冲击下的收益率
      const stressedReturns = returns.map((r) => r + priceImpact / returns.length);
      const riskMetrics = this.calculateStressedRiskMetrics(stressedReturns);

      // 估算利率变动概率
      const probability = this.estimateInterestRateProbability(rateChange);

      results.push({
        scenario_name: `利率上升${(rateChange * 100).toFixed(1)}%`,
        scenario_type: 'interest_rate',
        scenario_parameters: {
          rate_change: rateChange,
          price_impact: priceImpact,
          sensitivity: -5, // 利率敏感性系数
        },
        expected_loss: expectedLoss,
        probability: probability,
        confidence_interval: {
          lower_bound: expectedLoss * 0.7,
          upper_bound: expectedLoss * 1.3,
        },
        risk_metrics: riskMetrics,
        correlation_changes: {
          original_correlation: originalCorrelation,
          stressed_correlation: stressedCorrelation,
          correlation_change: stressedCorrelation - originalCorrelation,
        },
      });
    }

    return results;
  }

  /**
   * 需求 5.3: 评估行业特定风险事件的冲击
   */
  public analyzeSectorShockImpact(
    priceData: PriceData[],
    sectorShocks: Array<{
      name: string;
      impact: number;
      probability: number;
    }> = [
      { name: '监管政策变化', impact: -0.15, probability: 0.1 },
      { name: '技术革新冲击', impact: -0.25, probability: 0.05 },
      { name: '供应链中断', impact: -0.12, probability: 0.15 },
    ]
  ): ScenarioAnalysisResult[] {
    console.log('[RiskScenarioAnalyzer] Analyzing sector shock impact');

    const results: ScenarioAnalysisResult[] = [];
    const returns = this.calculateReturns(priceData);

    for (const shock of sectorShocks) {
      const expectedLoss = Math.abs(shock.impact);

      // 行业冲击下相关性显著增加
      const originalCorrelation = 0.7; // 行业内相关性通常较高
      const stressedCorrelation = Math.min(1, originalCorrelation * 1.5);

      // 模拟行业冲击下的收益率
      const stressedReturns = this.simulateStressedReturns(returns, shock.impact, 0.02);
      const riskMetrics = this.calculateStressedRiskMetrics(stressedReturns);

      results.push({
        scenario_name: shock.name,
        scenario_type: 'sector_shock',
        scenario_parameters: {
          shock_impact: shock.impact,
          shock_type: shock.name,
        },
        expected_loss: expectedLoss,
        probability: shock.probability,
        confidence_interval: {
          lower_bound: expectedLoss * 0.6,
          upper_bound: expectedLoss * 1.4,
        },
        risk_metrics: riskMetrics,
        correlation_changes: {
          original_correlation: originalCorrelation,
          stressed_correlation: stressedCorrelation,
          correlation_change: stressedCorrelation - originalCorrelation,
        },
      });
    }

    return results;
  }

  /**
   * 需求 5.4: 进行蒙特卡洛模拟，生成价格分布
   */
  public runMonteCarloSimulation(
    priceData: PriceData[],
    numSimulations: number = 10000,
    timeHorizonDays: number = 252
  ): MonteCarloSimulation {
    console.log(
      `[RiskScenarioAnalyzer] Running Monte Carlo simulation with ${numSimulations} paths`
    );

    // 验证输入参数
    if (!priceData || priceData.length === 0) {
      throw new Error('Price data cannot be empty for Monte Carlo simulation');
    }

    if (numSimulations <= 0) {
      console.warn('[RiskScenarioAnalyzer] Invalid number of simulations, using default 1000');
      numSimulations = 1000;
    }

    if (timeHorizonDays <= 0) {
      console.warn('[RiskScenarioAnalyzer] Invalid time horizon, using default 252 days');
      timeHorizonDays = 252;
    }

    const returns = this.calculateReturns(priceData);
    const currentPrice = priceData[priceData.length - 1].close;

    // 计算历史统计参数
    const drift = this.mean(returns);
    const volatility = this.standardDeviation(returns);

    console.log(
      `[RiskScenarioAnalyzer] Historical drift: ${(drift * 252 * 100).toFixed(2)}% annually`
    );
    console.log(
      `[RiskScenarioAnalyzer] Historical volatility: ${(volatility * Math.sqrt(252) * 100).toFixed(
        2
      )}% annually`
    );

    const pricePaths: number[][] = [];
    const finalPrices: number[] = [];
    const simulationReturns: number[] = [];

    // 运行蒙特卡洛模拟
    for (let sim = 0; sim < numSimulations; sim++) {
      const path = this.simulatePricePath(currentPrice, drift, volatility, timeHorizonDays);
      pricePaths.push(path);

      const finalPrice = path[path.length - 1];
      finalPrices.push(finalPrice);

      const totalReturn = (finalPrice - currentPrice) / currentPrice;
      simulationReturns.push(totalReturn);
    }

    // 计算百分位数
    const sortedFinalPrices = [...finalPrices].sort((a, b) => a - b);
    const sortedReturns = [...simulationReturns].sort((a, b) => a - b);

    const percentiles = {
      p5: sortedFinalPrices[Math.floor(numSimulations * 0.05)],
      p10: sortedFinalPrices[Math.floor(numSimulations * 0.1)],
      p25: sortedFinalPrices[Math.floor(numSimulations * 0.25)],
      p50: sortedFinalPrices[Math.floor(numSimulations * 0.5)],
      p75: sortedFinalPrices[Math.floor(numSimulations * 0.75)],
      p90: sortedFinalPrices[Math.floor(numSimulations * 0.9)],
      p95: sortedFinalPrices[Math.floor(numSimulations * 0.95)],
    };

    // 计算风险指标
    const var95 = -sortedReturns[Math.floor(numSimulations * 0.05)];
    const var99 = -sortedReturns[Math.floor(numSimulations * 0.01)];

    const lossReturns = sortedReturns.filter((r) => r < 0);
    const expectedShortfall =
      lossReturns.length > 0
        ? -this.mean(lossReturns.slice(0, Math.floor(numSimulations * 0.05)))
        : 0;

    const probabilityOfLoss = simulationReturns.filter((r) => r < 0).length / numSimulations;

    console.log(`[RiskScenarioAnalyzer] Monte Carlo simulation completed`);
    console.log(`[RiskScenarioAnalyzer] 95% VaR: ${(var95 * 100).toFixed(2)}%`);
    console.log(
      `[RiskScenarioAnalyzer] Probability of loss: ${(probabilityOfLoss * 100).toFixed(1)}%`
    );

    return {
      simulation_id: `mc_${Date.now()}_${numSimulations}`,
      parameters: {
        num_simulations: numSimulations,
        time_horizon_days: timeHorizonDays,
        initial_price: currentPrice,
        drift: drift,
        volatility: volatility,
      },
      results: {
        price_paths: pricePaths.slice(0, 100), // 只保存前100条路径用于可视化
        final_prices: finalPrices,
        returns: simulationReturns,
        percentiles: percentiles,
        risk_metrics: {
          var_95: var95,
          var_99: var99,
          expected_shortfall: expectedShortfall,
          probability_of_loss: probabilityOfLoss,
        },
      },
    };
  }

  /**
   * 需求 5.5: 计算不同情景下的预期损失
   */
  public calculateScenarioExpectedLoss(scenarios: ScenarioAnalysisResult[]): {
    weighted_expected_loss: number;
    worst_case_loss: number;
    best_case_loss: number;
    scenario_contributions: Array<{
      scenario: string;
      contribution: number;
      weight: number;
    }>;
  } {
    console.log('[RiskScenarioAnalyzer] Calculating scenario expected loss');

    let weightedExpectedLoss = 0;
    let worstCaseLoss = 0;
    let bestCaseLoss = Infinity;
    const scenarioContributions: Array<{
      scenario: string;
      contribution: number;
      weight: number;
    }> = [];

    for (const scenario of scenarios) {
      const weight = scenario.probability;
      const loss = scenario.expected_loss;
      const contribution = weight * loss;

      weightedExpectedLoss += contribution;
      worstCaseLoss = Math.max(worstCaseLoss, loss);
      bestCaseLoss = Math.min(bestCaseLoss, loss);

      scenarioContributions.push({
        scenario: scenario.scenario_name,
        contribution: contribution,
        weight: weight,
      });
    }

    return {
      weighted_expected_loss: weightedExpectedLoss,
      worst_case_loss: worstCaseLoss,
      best_case_loss: bestCaseLoss,
      scenario_contributions: scenarioContributions,
    };
  }

  /**
   * 需求 5.6: 分析相关性在压力下的变化
   */
  public analyzeStressCorrelationChanges(
    assetReturns: number[][],
    stressScenarios: ScenarioAnalysisResult[]
  ): {
    normal_correlation_matrix: number[][];
    stressed_correlation_matrix: number[][];
    correlation_changes: number[][];
    correlation_breakdown_risk: number;
  } {
    console.log('[RiskScenarioAnalyzer] Analyzing stress correlation changes');

    const numAssets = assetReturns.length;

    // 计算正常情况下的相关性矩阵
    const normalCorrelationMatrix = this.calculateCorrelationMatrix(assetReturns);

    // 计算压力情况下的相关性矩阵
    const stressedCorrelationMatrix = this.calculateStressedCorrelationMatrix(
      assetReturns,
      stressScenarios
    );

    // 计算相关性变化
    const correlationChanges: number[][] = [];
    let totalCorrelationChange = 0;
    let changeCount = 0;

    for (let i = 0; i < numAssets; i++) {
      correlationChanges[i] = [];
      for (let j = 0; j < numAssets; j++) {
        const change = stressedCorrelationMatrix[i][j] - normalCorrelationMatrix[i][j];
        correlationChanges[i][j] = change;

        if (i !== j) {
          // 排除对角线元素
          totalCorrelationChange += Math.abs(change);
          changeCount++;
        }
      }
    }

    // 相关性崩溃风险评分
    const correlationBreakdownRisk =
      changeCount > 0 ? (totalCorrelationChange / changeCount) * 100 : 0;

    return {
      normal_correlation_matrix: normalCorrelationMatrix,
      stressed_correlation_matrix: stressedCorrelationMatrix,
      correlation_changes: correlationChanges,
      correlation_breakdown_risk: correlationBreakdownRisk,
    };
  }

  /**
   * 综合压力测试
   */
  public runComprehensiveStressTest(
    priceData: PriceData[],
    marketData?: PriceData[]
  ): StressTestResult {
    console.log('[RiskScenarioAnalyzer] Running comprehensive stress test');

    const scenarios: ScenarioAnalysisResult[] = [];

    // 市场下跌场景
    scenarios.push(...this.simulateMarketDeclineScenarios(priceData, marketData));

    // 利率冲击场景
    scenarios.push(...this.analyzeInterestRateImpact(priceData));

    // 行业冲击场景
    scenarios.push(...this.analyzeSectorShockImpact(priceData));

    // 蒙特卡洛场景
    const monteCarloResult = this.runMonteCarloSimulation(priceData, 5000, 252);
    scenarios.push({
      scenario_name: '蒙特卡洛模拟',
      scenario_type: 'monte_carlo',
      scenario_parameters: monteCarloResult.parameters,
      expected_loss: monteCarloResult.results.risk_metrics.var_95,
      probability: 0.05,
      confidence_interval: {
        lower_bound:
          monteCarloResult.results.percentiles.p5 / priceData[priceData.length - 1].close - 1,
        upper_bound:
          monteCarloResult.results.percentiles.p95 / priceData[priceData.length - 1].close - 1,
      },
      risk_metrics: {
        var_95: monteCarloResult.results.risk_metrics.var_95,
        var_99: monteCarloResult.results.risk_metrics.var_99,
        expected_shortfall: monteCarloResult.results.risk_metrics.expected_shortfall,
        max_drawdown: 0, // 需要从价格路径计算
      },
      correlation_changes: {
        original_correlation: 0.6,
        stressed_correlation: 0.8,
        correlation_change: 0.2,
      },
    });

    // 计算综合指标
    const expectedLossMetrics = this.calculateScenarioExpectedLoss(scenarios);
    const worstCaseLoss = expectedLossMetrics.worst_case_loss;
    const averageLoss = expectedLossMetrics.weighted_expected_loss;
    const probabilityOfLoss =
      scenarios.reduce((sum, s) => sum + s.probability, 0) / scenarios.length;

    // 压力测试评分（0-100，分数越低风险越高）
    const stressTestScore = Math.max(0, 100 - worstCaseLoss * 200);

    // 生成建议
    const recommendations = this.generateStressTestRecommendations(scenarios, stressTestScore);

    console.log('[RiskScenarioAnalyzer] Comprehensive stress test completed');

    return {
      test_name: `综合压力测试_${new Date().toISOString().split('T')[0]}`,
      scenarios: scenarios,
      summary: {
        worst_case_loss: worstCaseLoss,
        average_loss: averageLoss,
        probability_of_loss: probabilityOfLoss,
        stress_test_score: stressTestScore,
      },
      recommendations: recommendations,
    };
  }

  // 私有辅助方法

  private calculateReturns(priceData: PriceData[]): number[] {
    if (!priceData || priceData.length < 2) {
      return [];
    }

    const returns: number[] = [];
    for (let i = 1; i < priceData.length; i++) {
      const currentPrice = priceData[i].close;
      const previousPrice = priceData[i - 1].close;
      if (previousPrice !== 0) {
        returns.push((currentPrice - previousPrice) / previousPrice);
      }
    }
    return returns;
  }

  private mean(values: number[]): number {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private standardDeviation(values: number[]): number {
    const mean = this.mean(values);
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    return Math.sqrt(this.mean(squaredDiffs));
  }

  private correlation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    const sumX = x.slice(0, n).reduce((a, b) => a + b, 0);
    const sumY = y.slice(0, n).reduce((a, b) => a + b, 0);
    const sumXY = x.slice(0, n).reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.slice(0, n).reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.slice(0, n).reduce((sum, yi) => sum + yi * yi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  private simulateStressedReturns(
    originalReturns: number[],
    shockMagnitude: number,
    additionalVolatility: number
  ): number[] {
    return originalReturns.map((r) => {
      const stressedReturn = r + shockMagnitude / originalReturns.length;
      const noise = this.normalRandom() * additionalVolatility;
      return stressedReturn + noise;
    });
  }

  private calculateStressedRiskMetrics(stressedReturns: number[]): any {
    const sortedReturns = [...stressedReturns].sort((a, b) => a - b);
    const n = sortedReturns.length;

    const var95 = -sortedReturns[Math.floor(n * 0.05)];
    const var99 = -sortedReturns[Math.floor(n * 0.01)];
    const expectedShortfall = -this.mean(sortedReturns.slice(0, Math.floor(n * 0.05)));
    const maxDrawdown = this.calculateMaxDrawdownFromReturns(stressedReturns);

    return {
      var_95: var95,
      var_99: var99,
      expected_shortfall: expectedShortfall,
      max_drawdown: maxDrawdown,
    };
  }

  private estimateScenarioProbability(returns: number[], targetDecline: number): number {
    // 基于历史数据估算场景概率
    const extremeReturns = returns.filter((r) => r <= targetDecline);
    const historicalProbability = extremeReturns.length / returns.length;

    // 调整概率（考虑尾部风险）
    return Math.max(0.001, Math.min(0.5, historicalProbability * 2));
  }

  private estimateInterestRateProbability(rateChange: number): number {
    // 基于历史利率变动估算概率
    if (rateChange <= 0.005) return 0.3;
    if (rateChange <= 0.01) return 0.15;
    if (rateChange <= 0.02) return 0.05;
    return 0.01;
  }

  private simulatePricePath(
    initialPrice: number,
    drift: number,
    volatility: number,
    days: number
  ): number[] {
    const path = [initialPrice];
    let currentPrice = initialPrice;

    for (let i = 1; i <= days; i++) {
      const randomShock = this.normalRandom();
      const dailyReturn = drift + volatility * randomShock;
      currentPrice = currentPrice * (1 + dailyReturn);
      path.push(currentPrice);
    }

    return path;
  }

  private normalRandom(): number {
    // Box-Muller变换生成正态分布随机数
    let u = 0,
      v = 0;
    while (u === 0) u = Math.random();
    while (v === 0) v = Math.random();
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  }

  private calculateMaxDrawdownFromReturns(returns: number[]): number {
    const cumulativeReturns = [1];
    for (const return_ of returns) {
      cumulativeReturns.push(cumulativeReturns[cumulativeReturns.length - 1] * (1 + return_));
    }

    let maxDrawdown = 0;
    let peak = cumulativeReturns[0];

    for (let i = 1; i < cumulativeReturns.length; i++) {
      if (cumulativeReturns[i] > peak) {
        peak = cumulativeReturns[i];
      } else {
        const drawdown = (peak - cumulativeReturns[i]) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  private calculateCorrelationMatrix(assetReturns: number[][]): number[][] {
    const numAssets = assetReturns.length;
    const correlationMatrix: number[][] = [];

    for (let i = 0; i < numAssets; i++) {
      correlationMatrix[i] = [];
      for (let j = 0; j < numAssets; j++) {
        if (i === j) {
          correlationMatrix[i][j] = 1.0;
        } else {
          correlationMatrix[i][j] = this.correlation(assetReturns[i], assetReturns[j]);
        }
      }
    }

    return correlationMatrix;
  }

  private calculateStressedCorrelationMatrix(
    assetReturns: number[][],
    stressScenarios: ScenarioAnalysisResult[]
  ): number[][] {
    const numAssets = assetReturns.length;
    const stressedMatrix: number[][] = [];

    // 在压力情况下，相关性通常会增加
    const stressMultiplier = 1.3;
    const stressAddition = 0.2;

    for (let i = 0; i < numAssets; i++) {
      stressedMatrix[i] = [];
      for (let j = 0; j < numAssets; j++) {
        if (i === j) {
          stressedMatrix[i][j] = 1.0;
        } else {
          const normalCorr = this.correlation(assetReturns[i], assetReturns[j]);
          const stressedCorr = Math.min(1, normalCorr * stressMultiplier + stressAddition);
          stressedMatrix[i][j] = stressedCorr;
        }
      }
    }

    return stressedMatrix;
  }

  private generateStressTestRecommendations(
    scenarios: ScenarioAnalysisResult[],
    stressTestScore: number
  ): string[] {
    const recommendations: string[] = [];

    if (stressTestScore < 30) {
      recommendations.push('风险极高，建议暂停投资或大幅减仓');
      recommendations.push('考虑购买保护性期权或其他对冲工具');
      recommendations.push('增加现金持有比例，提高流动性');
    } else if (stressTestScore < 60) {
      recommendations.push('风险较高，建议降低仓位至50%以下');
      recommendations.push('分散投资，避免集中持仓');
      recommendations.push('设置严格的止损位');
    } else if (stressTestScore < 80) {
      recommendations.push('风险适中，可以正常投资但需密切监控');
      recommendations.push('建议设置动态止损位');
      recommendations.push('定期重新评估风险状况');
    } else {
      recommendations.push('风险较低，可以正常投资');
      recommendations.push('保持适度分散化');
      recommendations.push('定期进行压力测试');
    }

    // 基于具体场景添加建议
    const worstScenario = scenarios.reduce((worst, current) =>
      current.expected_loss > worst.expected_loss ? current : worst
    );

    if (worstScenario.scenario_type === 'market_decline') {
      recommendations.push('关注市场整体走势，考虑市场中性策略');
    } else if (worstScenario.scenario_type === 'interest_rate') {
      recommendations.push('关注央行政策动向，考虑利率敏感性');
    } else if (worstScenario.scenario_type === 'sector_shock') {
      recommendations.push('关注行业政策变化，分散行业配置');
    }

    return recommendations;
  }
}

// 导出单例实例
export const riskScenarioAnalyzer = RiskScenarioAnalyzer.getInstance();
