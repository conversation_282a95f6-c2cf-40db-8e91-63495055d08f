/**
 * Sitemap Index API 路由
 * 生成 sitemap 索引文件，用于大型网站
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSitemapGenerator } from '@/lib/seo/sitemap-generator';

/**
 * GET 请求处理器
 * 生成 sitemap 索引
 */
export async function GET(request: NextRequest) {
  try {
    const generator = createSitemapGenerator();
    const sitemapIndex = await generator.generateSitemapIndex();

    return new NextResponse(sitemapIndex, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
        'X-Robots-Tag': 'noindex',
      },
    });
  } catch (error) {
    console.error('Error generating sitemap index:', error);

    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
}
