/**
 * Risk Comparison API Routes
 * 风险数据对比分析API接口
 */

import { compareRiskAssessments } from '@/lib/risk-data-storage';
import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/risk-data/compare - 比较多个工作流的风险评估结果
 * 需求 9.5: 系统应提供风险分析的对比功能
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workflow_ids } = body;

    // 验证输入
    if (!Array.isArray(workflow_ids) || workflow_ids.length === 0) {
      return NextResponse.json(
        { error: 'workflow_ids must be a non-empty array' },
        { status: 400 }
      );
    }

    if (workflow_ids.length > 10) {
      return NextResponse.json(
        { error: 'Cannot compare more than 10 workflows at once' },
        { status: 400 }
      );
    }

    const comparisonResult = await compareRiskAssessments(workflow_ids);

    return NextResponse.json({
      success: true,
      data: comparisonResult,
    });
  } catch (error) {
    console.error('Error comparing risk assessments:', error);
    return NextResponse.json({ error: 'Failed to compare risk assessments' }, { status: 500 });
  }
}

/**
 * GET /api/risk-data/compare - 获取风险对比数据（通过查询参数）
 * 查询参数: workflow_ids=id1,id2,id3
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowIdsParam = searchParams.get('workflow_ids');

    if (!workflowIdsParam) {
      return NextResponse.json({ error: 'Missing workflow_ids parameter' }, { status: 400 });
    }

    const workflowIds = workflowIdsParam.split(',').filter((id) => id.trim());

    if (workflowIds.length === 0) {
      return NextResponse.json({ error: 'No valid workflow IDs provided' }, { status: 400 });
    }

    if (workflowIds.length > 10) {
      return NextResponse.json(
        { error: 'Cannot compare more than 10 workflows at once' },
        { status: 400 }
      );
    }

    const comparisonResult = await compareRiskAssessments(workflowIds);

    return NextResponse.json({
      success: true,
      data: comparisonResult,
    });
  } catch (error) {
    console.error('Error comparing risk assessments:', error);
    return NextResponse.json({ error: 'Failed to compare risk assessments' }, { status: 500 });
  }
}
