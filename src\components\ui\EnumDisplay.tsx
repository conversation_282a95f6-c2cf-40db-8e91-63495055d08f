'use client';

import {
    getAgentStatusLabel,
    getAnalysisPeriodLabel,
    getResearchDepthLabel,
    getRiskLevelLabel,
    getTaskStatusLabel,
    getTradingActionLabel
} from '@/utils/enums';

interface EnumDisplayProps {
  type: 'researchDepth' | 'analysisPeriod' | 'taskStatus' | 'agentStatus' | 'tradingAction' | 'riskLevel';
  value: string;
  className?: string;
}

export function EnumDisplay({ type, value, className = '' }: EnumDisplayProps) {
  const getLabel = () => {
    switch (type) {
      case 'researchDepth':
        return getResearchDepthLabel(value);
      case 'analysisPeriod':
        return getAnalysisPeriodLabel(value);
      case 'taskStatus':
        return getTaskStatusLabel(value);
      case 'agentStatus':
        return getAgentStatusLabel(value);
      case 'tradingAction':
        return getTradingActionLabel(value);
      case 'riskLevel':
        return getRiskLevelLabel(value);
      default:
        return value;
    }
  };

  const getStatusColor = () => {
    if (type === 'taskStatus') {
      switch (value) {
        case 'pending':
          return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
        case 'running':
          return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
        case 'completed':
          return 'text-green-600 bg-green-100 dark:bg-green-900/20';
        case 'failed':
          return 'text-red-600 bg-red-100 dark:bg-red-900/20';
        case 'cancelled':
          return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
        default:
          return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
      }
    }

    if (type === 'riskLevel') {
      switch (value) {
        case 'low':
          return 'text-green-600 bg-green-100 dark:bg-green-900/20';
        case 'medium':
          return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
        case 'high':
          return 'text-red-600 bg-red-100 dark:bg-red-900/20';
        case 'very_high':
          return 'text-red-800 bg-red-200 dark:bg-red-900/30';
        default:
          return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
      }
    }

    if (type === 'tradingAction') {
      switch (value) {
        case 'buy':
          return 'text-green-600 bg-green-100 dark:bg-green-900/20';
        case 'sell':
          return 'text-red-600 bg-red-100 dark:bg-red-900/20';
        case 'hold':
          return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
        default:
          return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
      }
    }

    return '';
  };

  const statusColor = getStatusColor();
  const finalClassName = statusColor 
    ? `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor} ${className}`
    : className;

  return (
    <span className={finalClassName}>
      {getLabel()}
    </span>
  );
}