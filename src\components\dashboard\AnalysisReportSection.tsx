'use client';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  ArrowTopRightOnSquareIcon,
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  ChartBarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
  DocumentTextIcon,
  EyeIcon,
  HeartIcon,
  InformationCircleIcon,
  NewspaperIcon,
} from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';

// 类型定义
interface AnalystReport {
  id: number;
  report_id: string;
  analyst_type: 'fundamental' | 'technical' | 'sentiment' | 'news';
  summary?: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
  created_at: string;
}

interface ResearchReport {
  id: number;
  report_id: string;
  researcher_type: 'bull' | 'bear';
  summary?: string;
  confidence_level?: number;
  target_price?: number;
  time_horizon?: string;
  status: 'completed' | 'failed';
  created_at: string;
}

interface AnalysisReportSectionProps {
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
}

// 分析师配置
const analystConfig = {
  fundamental: {
    name: '基本面分析师',
    icon: ChartBarIcon,
    description: '财务数据、估值分析',
    iconBg: 'bg-blue-100 dark:bg-blue-900/20',
    iconColor: 'text-blue-600',
    cardBg: 'bg-blue-50 dark:bg-blue-900/10',
    titleColor: 'text-blue-800 dark:text-blue-200',
    infoIconColor: 'text-blue-600',
  },
  technical: {
    name: '技术分析师',
    icon: ArrowTrendingUpIcon,
    description: '技术指标、图表分析',
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600',
    cardBg: 'bg-green-50 dark:bg-green-900/10',
    titleColor: 'text-green-800 dark:text-green-200',
    infoIconColor: 'text-green-600',
  },
  sentiment: {
    name: '情绪分析师',
    icon: HeartIcon,
    description: '市场情绪、投资者心理',
    iconBg: 'bg-pink-100 dark:bg-pink-900/20',
    iconColor: 'text-pink-600',
    cardBg: 'bg-pink-50 dark:bg-pink-900/10',
    titleColor: 'text-pink-800 dark:text-pink-200',
    infoIconColor: 'text-pink-600',
  },
  news: {
    name: '新闻分析师',
    icon: NewspaperIcon,
    description: '新闻事件、市场影响',
    iconBg: 'bg-purple-100 dark:bg-purple-900/20',
    iconColor: 'text-purple-600',
    cardBg: 'bg-purple-50 dark:bg-purple-900/10',
    titleColor: 'text-purple-800 dark:text-purple-200',
    infoIconColor: 'text-purple-600',
  },
};

// 研究员配置
const researcherConfig = {
  bull: {
    name: '多头研究员',
    icon: ArrowTrendingUpIcon,
    description: '看涨观点、积极因素',
    iconBg: 'bg-green-100 dark:bg-green-900/20',
    iconColor: 'text-green-600',
    cardBg: 'bg-green-50 dark:bg-green-900/10',
    titleColor: 'text-green-800 dark:text-green-200',
    infoIconColor: 'text-green-600',
  },
  bear: {
    name: '空头研究员',
    icon: ArrowTrendingDownIcon,
    description: '看跌观点、风险因素',
    iconBg: 'bg-red-100 dark:bg-red-900/20',
    iconColor: 'text-red-600',
    cardBg: 'bg-red-50 dark:bg-red-900/10',
    titleColor: 'text-red-800 dark:text-red-200',
    infoIconColor: 'text-red-600',
  },
};

// 简化的 markdown 渲染
const renderContent = (content: string) => {
  if (!content) return '';

  return (
    content
      // 标题
      .replace(
        /### (.*?)(?=\n|$)/gm,
        '<h3 class="text-base font-semibold mt-4 mb-2 text-slate-900 dark:text-white">$1</h3>'
      )
      .replace(
        /## (.*?)(?=\n|$)/gm,
        '<h2 class="text-lg font-bold mt-4 mb-3 text-slate-900 dark:text-white">$1</h2>'
      )
      // 粗体
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-semibold text-slate-900 dark:text-white">$1</strong>'
      )
      // 列表
      .replace(
        /^- (.*?)$/gm,
        '<li class="ml-4 mb-1 text-slate-700 dark:text-slate-300 list-disc">$1</li>'
      )
      // 段落
      .replace(/\n\n/g, '</p><p class="mb-3 text-slate-700 dark:text-slate-300 leading-relaxed">')
      .replace(/\n/g, '<br>')
  );
};

// 提取结论和详情
const parseReportContent = (content: string) => {
  if (!content) return { conclusion: '', details: '' };

  const lines = content.split('\n');
  const firstLine = lines[0] || '';

  // 尝试提取结论（通常在第一行或包含特定关键词）
  const conclusionMatch = firstLine.match(/^(买入|卖出|持有|中性|看涨|看跌|积极|消极)\s*-?\s*(.*)/);

  if (conclusionMatch) {
    return {
      conclusion: conclusionMatch[1],
      details: conclusionMatch[2] + '\n' + lines.slice(1).join('\n'),
    };
  }

  return {
    conclusion: firstLine.length > 100 ? firstLine.substring(0, 100) + '...' : firstLine,
    details: lines.slice(1).join('\n'),
  };
};

export function AnalysisReportSection({
  analystReports,
  researchReports,
}: AnalysisReportSectionProps) {
  const [expandedReports, setExpandedReports] = useState<Set<string>>(new Set());

  const toggleReport = (reportId: string) => {
    const newExpanded = new Set(expandedReports);
    if (newExpanded.has(reportId)) {
      newExpanded.delete(reportId);
    } else {
      newExpanded.add(reportId);
    }
    setExpandedReports(newExpanded);
  };

  const completedAnalystReports = analystReports.filter(
    (report) => report.status === 'completed' && report.summary
  );

  const completedResearchReports = researchReports.filter(
    (report) => report.status === 'completed' && report.summary
  );

  if (completedAnalystReports.length === 0 && completedResearchReports.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DocumentTextIcon className="h-5 w-5" />
            <span>分析报告</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-slate-500 py-8">
            <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-slate-400" />
            <p>暂无分析报告</p>
            <p className="text-sm mt-2">分析完成后报告将在此处显示</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DocumentTextIcon className="h-5 w-5" />
            <span>分析报告</span>
          </div>
          <span className="text-sm font-normal text-slate-500">
            共 {completedAnalystReports.length + completedResearchReports.length} 份报告
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 分析师报告 */}
        {completedAnalystReports.map((report, index) => {
          const config = analystConfig[report.analyst_type];
          const { conclusion, details } = parseReportContent(report.summary || '');
          const isExpanded = expandedReports.has(report.report_id);
          const Icon = config.icon;

          return (
            <motion.div
              key={report.report_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
            >
              {/* 报告头部 */}
              <div className="p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={config.iconBg}>
                      <Icon className={`h-5 w-5 ${config.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-900 dark:text-white">
                        {config.name}
                      </h3>
                      <p className="text-sm text-slate-500">{config.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right text-sm text-slate-500">
                      <div className="flex items-center space-x-1">
                        <ClockIcon className="h-3 w-3" />
                        <span>{new Date(report.created_at).toLocaleString()}</span>
                      </div>
                      {report.execution_time_ms && (
                        <div className="mt-1">
                          耗时 {Math.round(report.execution_time_ms / 1000)}秒
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => toggleReport(report.report_id)}
                      className="p-2 hover:bg-slate-200 dark:hover:bg-slate-700 rounded-lg transition-colors"
                    >
                      {isExpanded ? (
                        <ChevronDownIcon className="h-4 w-4" />
                      ) : (
                        <ChevronRightIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* 报告内容 */}
              <div className="p-4">
                {/* 结论摘要 */}
                <div className={config.cardBg}>
                  <div className="flex items-start space-x-2">
                    <InformationCircleIcon
                      className={`h-4 w-4 mt-0.5 flex-shrink-0 ${config.infoIconColor}`}
                    />
                    <div>
                      <h4 className={`text-sm font-medium ${config.titleColor} mb-1`}>分析结论</h4>
                      <p className="text-sm text-slate-700 dark:text-slate-300">{conclusion}</p>
                    </div>
                  </div>
                </div>

                {/* 详细内容 */}
                <AnimatePresence>
                  {isExpanded && details && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-slate-200 dark:border-slate-700 pt-4"
                    >
                      <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                        详细分析
                      </h4>
                      <div
                        className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed"
                        dangerouslySetInnerHTML={{
                          __html: renderContent(details),
                        }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          );
        })}

        {/* 研究员报告 */}
        {completedResearchReports.map((report, index) => {
          const config = researcherConfig[report.researcher_type];
          const { conclusion, details } = parseReportContent(report.summary || '');
          const isExpanded = expandedReports.has(report.report_id);
          const Icon = config.icon;

          return (
            <motion.div
              key={report.report_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: (completedAnalystReports.length + index) * 0.1 }}
              className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
            >
              {/* 报告头部 */}
              <div className="p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={config.iconBg}>
                      <Icon className={`h-5 w-5 ${config.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-900 dark:text-white">
                        {config.name}
                      </h3>
                      <p className="text-sm text-slate-500">{config.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {report.confidence_level && (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-slate-500">置信度</span>
                        <div className="w-16 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${report.confidence_level * 100}%` }}
                          />
                        </div>
                        <span className="text-xs text-slate-600 dark:text-slate-400">
                          {Math.round(report.confidence_level * 100)}%
                        </span>
                      </div>
                    )}
                    <button
                      onClick={() => toggleReport(report.report_id)}
                      className="p-2 hover:bg-slate-200 dark:hover:bg-slate-700 rounded-lg transition-colors"
                    >
                      {isExpanded ? (
                        <ChevronDownIcon className="h-4 w-4" />
                      ) : (
                        <ChevronRightIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* 报告内容 */}
              <div className="p-4">
                {/* 结论摘要 */}
                <div className={config.cardBg}>
                  <div className="flex items-start space-x-2">
                    <InformationCircleIcon
                      className={`h-4 w-4 mt-0.5 flex-shrink-0 ${config.infoIconColor}`}
                    />
                    <div>
                      <h4 className={`text-sm font-medium ${config.titleColor} mb-1`}>研究观点</h4>
                      <p className="text-sm text-slate-700 dark:text-slate-300">{conclusion}</p>
                    </div>
                  </div>
                </div>

                {/* 额外信息 */}
                {(report.target_price || report.time_horizon) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {report.target_price && (
                      <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                        <span className="text-sm text-slate-600 dark:text-slate-400">目标价格</span>
                        <span className="font-medium">${report.target_price.toFixed(2)}</span>
                      </div>
                    )}
                    {report.time_horizon && (
                      <div className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                        <span className="text-sm text-slate-600 dark:text-slate-400">时间范围</span>
                        <span className="font-medium">{report.time_horizon}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* 详细内容 */}
                <AnimatePresence>
                  {isExpanded && details && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-slate-200 dark:border-slate-700 pt-4"
                    >
                      <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                        详细分析
                      </h4>
                      <div
                        className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed"
                        dangerouslySetInnerHTML={{
                          __html: renderContent(details),
                        }}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          );
        })}

        {/* 操作按钮 */}
        <div className="flex items-center justify-center space-x-4 pt-4 border-t border-slate-200 dark:border-slate-700">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
            <DocumentTextIcon className="h-4 w-4" />
            <span>生成综合报告</span>
          </button>
          <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2">
            <ArrowTopRightOnSquareIcon className="h-4 w-4" />
            <span>导出报告</span>
          </button>
          <button className="px-4 py-2 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors flex items-center space-x-2">
            <EyeIcon className="h-4 w-4" />
            <span>查看原始数据</span>
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
