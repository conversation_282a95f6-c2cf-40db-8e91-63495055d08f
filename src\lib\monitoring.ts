/**
 * 错误监控和告警系统
 * 提供实时错误监控、性能追踪和告警功能
 */

import { LogContext, logger } from './logger';

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  errorsByType: Record<string, number>;
  errorsByComponent: Record<string, number>;
  lastError?: {
    message: string;
    timestamp: string;
    component?: string;
  };
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  slowRequests: number;
  totalRequests: number;
  requestsByEndpoint: Record<
    string,
    {
      count: number;
      averageTime: number;
      errorCount: number;
    }
  >;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  memory: NodeJS.MemoryUsage;
  errors: ErrorMetrics;
  performance: PerformanceMetrics;
  timestamp: string;
}

class MonitoringService {
  private errorCount = 0;
  private requestCount = 0;
  private errorsByType: Record<string, number> = {};
  private errorsByComponent: Record<string, number> = {};
  private requestTimes: number[] = [];
  private requestsByEndpoint: Record<
    string,
    {
      count: number;
      totalTime: number;
      errorCount: number;
    }
  > = {};
  private lastError?: {
    message: string;
    timestamp: string;
    component?: string;
  };

  private readonly maxRequestTimes = 1000; // 保留最近1000个请求的时间
  private readonly errorRateThreshold = parseFloat(process.env.ERROR_RATE_THRESHOLD || '0.05');
  private readonly responseTimeThreshold = parseInt(process.env.RESPONSE_TIME_THRESHOLD || '5000');

  // 记录错误
  recordError(error: Error, context?: LogContext): void {
    this.errorCount++;

    const errorType = error.constructor.name;
    this.errorsByType[errorType] = (this.errorsByType[errorType] || 0) + 1;

    if (context?.component) {
      this.errorsByComponent[context.component] =
        (this.errorsByComponent[context.component] || 0) + 1;
    }

    this.lastError = {
      message: error.message,
      timestamp: new Date().toISOString(),
      component: context?.component,
    };

    // 检查错误率是否超过阈值
    this.checkErrorRate();

    logger.debug('错误已记录到监控系统', context, {
      errorType,
      totalErrors: this.errorCount,
      errorRate: this.getErrorRate(),
    });
  }

  // 记录请求性能
  recordRequest(endpoint: string, responseTime: number, success: boolean): void {
    this.requestCount++;
    this.requestTimes.push(responseTime);

    // 保持数组大小在限制内
    if (this.requestTimes.length > this.maxRequestTimes) {
      this.requestTimes.shift();
    }

    // 记录端点统计
    if (!this.requestsByEndpoint[endpoint]) {
      this.requestsByEndpoint[endpoint] = {
        count: 0,
        totalTime: 0,
        errorCount: 0,
      };
    }

    const endpointStats = this.requestsByEndpoint[endpoint];
    endpointStats.count++;
    endpointStats.totalTime += responseTime;

    if (!success) {
      endpointStats.errorCount++;
    }

    // 检查响应时间是否超过阈值
    if (responseTime > this.responseTimeThreshold) {
      logger.warn('请求响应时间超过阈值', { component: 'Monitoring' }, undefined, {
        endpoint,
        responseTime,
        threshold: this.responseTimeThreshold,
      });
    }

    logger.debug(
      '请求性能已记录',
      { component: 'Monitoring' },
      {
        endpoint,
        responseTime,
        success,
        totalRequests: this.requestCount,
      }
    );
  }

  // 获取错误率
  private getErrorRate(): number {
    if (this.requestCount === 0) return 0;
    return this.errorCount / this.requestCount;
  }

  // 检查错误率
  private checkErrorRate(): void {
    const errorRate = this.getErrorRate();

    if (errorRate > this.errorRateThreshold && this.requestCount > 10) {
      logger.error('错误率超过阈值', { component: 'Monitoring' }, undefined, {
        errorRate: errorRate.toFixed(4),
        threshold: this.errorRateThreshold,
        totalErrors: this.errorCount,
        totalRequests: this.requestCount,
      });

      // 这里可以集成告警系统，如发送邮件、Slack通知等
      this.triggerAlert('HIGH_ERROR_RATE', {
        errorRate,
        threshold: this.errorRateThreshold,
        totalErrors: this.errorCount,
        totalRequests: this.requestCount,
      });
    }
  }

  // 获取错误指标
  getErrorMetrics(): ErrorMetrics {
    return {
      totalErrors: this.errorCount,
      errorRate: this.getErrorRate(),
      errorsByType: { ...this.errorsByType },
      errorsByComponent: { ...this.errorsByComponent },
      lastError: this.lastError ? { ...this.lastError } : undefined,
    };
  }

  // 获取性能指标
  getPerformanceMetrics(): PerformanceMetrics {
    const averageResponseTime =
      this.requestTimes.length > 0
        ? this.requestTimes.reduce((sum, time) => sum + time, 0) / this.requestTimes.length
        : 0;

    const slowRequests = this.requestTimes.filter(
      (time) => time > this.responseTimeThreshold
    ).length;

    const requestsByEndpoint: Record<
      string,
      {
        count: number;
        averageTime: number;
        errorCount: number;
      }
    > = {};

    for (const [endpoint, stats] of Object.entries(this.requestsByEndpoint)) {
      requestsByEndpoint[endpoint] = {
        count: stats.count,
        averageTime: stats.count > 0 ? stats.totalTime / stats.count : 0,
        errorCount: stats.errorCount,
      };
    }

    return {
      averageResponseTime,
      slowRequests,
      totalRequests: this.requestCount,
      requestsByEndpoint,
    };
  }

  // 获取系统健康状态
  getSystemHealth(): SystemHealth {
    const errorMetrics = this.getErrorMetrics();
    const performanceMetrics = this.getPerformanceMetrics();

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    // 判断系统健康状态
    if (errorMetrics.errorRate > this.errorRateThreshold * 2) {
      status = 'unhealthy';
    } else if (
      errorMetrics.errorRate > this.errorRateThreshold ||
      performanceMetrics.averageResponseTime > this.responseTimeThreshold
    ) {
      status = 'degraded';
    }

    return {
      status,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      errors: errorMetrics,
      performance: performanceMetrics,
      timestamp: new Date().toISOString(),
    };
  }

  // 重置统计数据
  reset(): void {
    this.errorCount = 0;
    this.requestCount = 0;
    this.errorsByType = {};
    this.errorsByComponent = {};
    this.requestTimes = [];
    this.requestsByEndpoint = {};
    this.lastError = undefined;

    logger.info('监控统计数据已重置', { component: 'Monitoring' });
  }

  // 触发告警
  private triggerAlert(alertType: string, data: any): void {
    logger.error(`系统告警: ${alertType}`, { component: 'Monitoring' }, undefined, data);

    // 这里可以集成各种告警渠道
    if (process.env.NODE_ENV === 'production') {
      // 发送到外部告警系统
      this.sendToAlertingSystem(alertType, data);
    }
  }

  // 发送到外部告警系统
  private async sendToAlertingSystem(alertType: string, data: any): Promise<void> {
    try {
      // 这里可以集成 Slack、邮件、短信等告警渠道
      // 示例：发送到 Slack
      if (process.env.SLACK_WEBHOOK_URL) {
        await this.sendSlackAlert(alertType, data);
      }

      // 示例：发送邮件
      if (process.env.ALERT_EMAIL) {
        await this.sendEmailAlert(alertType, data);
      }
    } catch (error) {
      logger.error('发送告警失败', { component: 'Monitoring' }, error as Error);
    }
  }

  // 发送 Slack 告警
  private async sendSlackAlert(alertType: string, data: any): Promise<void> {
    const webhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!webhookUrl) return;

    const message = {
      text: `🚨 系统告警: ${alertType}`,
      attachments: [
        {
          color: 'danger',
          fields: [
            {
              title: '告警类型',
              value: alertType,
              short: true,
            },
            {
              title: '时间',
              value: new Date().toISOString(),
              short: true,
            },
            {
              title: '详细信息',
              value: JSON.stringify(data, null, 2),
              short: false,
            },
          ],
        },
      ],
    };

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      throw new Error(`Slack webhook failed: ${response.statusText}`);
    }
  }

  // 发送邮件告警
  private async sendEmailAlert(alertType: string, data: any): Promise<void> {
    // 这里可以集成邮件服务，如 SendGrid、AWS SES 等
    logger.info(
      '邮件告警功能待实现',
      { component: 'Monitoring' },
      {
        alertType,
        data,
      }
    );
  }

  // 生成监控报告
  generateReport(): string {
    const health = this.getSystemHealth();

    return `
# 系统监控报告

## 系统状态
- **状态**: ${health.status}
- **运行时间**: ${Math.floor(health.uptime / 3600)}小时${Math.floor(
      (health.uptime % 3600) / 60
    )}分钟
- **内存使用**: ${Math.round(health.memory.heapUsed / 1024 / 1024)}MB / ${Math.round(
      health.memory.heapTotal / 1024 / 1024
    )}MB

## 错误统计
- **总错误数**: ${health.errors.totalErrors}
- **错误率**: ${(health.errors.errorRate * 100).toFixed(2)}%
- **错误类型分布**: ${JSON.stringify(health.errors.errorsByType, null, 2)}
- **组件错误分布**: ${JSON.stringify(health.errors.errorsByComponent, null, 2)}

## 性能统计
- **平均响应时间**: ${health.performance.averageResponseTime.toFixed(2)}ms
- **慢请求数**: ${health.performance.slowRequests}
- **总请求数**: ${health.performance.totalRequests}

## 端点统计
${Object.entries(health.performance.requestsByEndpoint)
  .map(
    ([endpoint, stats]) =>
      `- **${endpoint}**: ${stats.count}次请求, 平均${stats.averageTime.toFixed(2)}ms, ${
        stats.errorCount
      }次错误`
  )
  .join('\n')}

---
报告生成时间: ${health.timestamp}
    `.trim();
  }
}

// 导出单例实例
export const monitoring = new MonitoringService();

// 健康检查中间件
export function createHealthCheckHandler() {
  return () => {
    const health = monitoring.getSystemHealth();

    logger.info(
      '健康检查请求',
      { component: 'HealthCheck' },
      {
        status: health.status,
        errorRate: health.errors.errorRate,
        averageResponseTime: health.performance.averageResponseTime,
      }
    );

    return health;
  };
}

// 监控报告生成器
export function createMonitoringReportHandler() {
  return () => {
    const report = monitoring.generateReport();

    logger.info(
      '监控报告生成',
      { component: 'MonitoringReport' },
      {
        reportLength: report.length,
      }
    );

    return {
      report,
      timestamp: new Date().toISOString(),
    };
  };
}
