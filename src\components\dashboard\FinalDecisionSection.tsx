'use client';

import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { FinalDecision } from '@/types/langgraph-database';
import {
  ArrowTrendingDownIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  HandRaisedIcon,
  ScaleIcon,
  ShieldCheckIcon,
  TrophyIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

interface FinalDecisionSectionProps {
  decision: FinalDecision;
  className?: string;
}

export function FinalDecisionSection({ decision, className = '' }: FinalDecisionSectionProps) {
  // 获取决策类型的配置
  const getDecisionConfig = (decisionType: string) => {
    const configs = {
      buy: {
        icon: ArrowTrendingUpIcon,
        color: 'green',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        borderColor: 'border-green-200 dark:border-green-800',
        textColor: 'text-green-600',
        label: '买入建议',
        emoji: '📈',
        description: '建议买入该股票',
      },
      sell: {
        icon: ArrowTrendingDownIcon,
        color: 'red',
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
        textColor: 'text-red-600',
        label: '卖出建议',
        emoji: '📉',
        description: '建议卖出该股票',
      },
      hold: {
        icon: HandRaisedIcon,
        color: 'blue',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800',
        textColor: 'text-blue-600',
        label: '持有建议',
        emoji: '✋',
        description: '建议继续持有',
      },
      avoid: {
        icon: XCircleIcon,
        color: 'gray',
        bgColor: 'bg-gray-50 dark:bg-gray-900/20',
        borderColor: 'border-gray-200 dark:border-gray-800',
        textColor: 'text-gray-600',
        label: '避免投资',
        emoji: '🚫',
        description: '建议避免投资该股票',
      },
    };
    return configs[decisionType as keyof typeof configs] || configs.hold;
  };

  // 获取置信度等级
  const getConfidenceLevel = (confidence?: number) => {
    if (!confidence) return { level: '未知', color: 'gray', description: '置信度未知' };
    if (confidence >= 90) return { level: '极高', color: 'green', description: '非常确信' };
    if (confidence >= 80) return { level: '高', color: 'blue', description: '比较确信' };
    if (confidence >= 70) return { level: '中等', color: 'yellow', description: '一般确信' };
    if (confidence >= 60) return { level: '较低', color: 'orange', description: '不太确信' };
    return { level: '低', color: 'red', description: '缺乏信心' };
  };

  // 获取风险等级
  const getRiskLevel = (positionSize?: number) => {
    if (!positionSize) return { level: '未知', color: 'gray', description: '风险未评估' };
    if (positionSize <= 5) return { level: '低风险', color: 'green', description: '保守投资' };
    if (positionSize <= 15) return { level: '中等风险', color: 'blue', description: '平衡投资' };
    if (positionSize <= 25) return { level: '较高风险', color: 'yellow', description: '积极投资' };
    return { level: '高风险', color: 'red', description: '激进投资' };
  };

  // 解析价格区间
  const parsePriceRange = (priceRange: any) => {
    if (!priceRange) return null;
    if (typeof priceRange === 'string') {
      try {
        return JSON.parse(priceRange);
      } catch {
        return null;
      }
    }
    return priceRange;
  };

  const decisionConfig = getDecisionConfig(decision.decision_type);
  const confidenceLevel = getConfidenceLevel(decision.confidence_level);
  const riskLevel = getRiskLevel(decision.position_size_percentage);
  const priceRange = parsePriceRange(decision.entry_price_range);
  const DecisionIcon = decisionConfig.icon;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 决策标题 */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 200, damping: 15 }}
          className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-white text-4xl mb-4"
        >
          {decisionConfig.emoji}
        </motion.div>
        <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">最终投资决策</h2>
        <p className="text-slate-600 dark:text-slate-400">基于多智能体分析的综合投资建议</p>
      </div>

      {/* 主要决策卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`border-2 rounded-xl p-6 ${decisionConfig.bgColor} ${decisionConfig.borderColor}`}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-lg bg-white dark:bg-slate-800 shadow-sm`}>
              <DecisionIcon className={`h-8 w-8 ${decisionConfig.textColor}`} />
            </div>
            <div>
              <h3 className={`text-2xl font-bold ${decisionConfig.textColor}`}>
                {decisionConfig.label}
              </h3>
              <p className="text-slate-600 dark:text-slate-400">{decisionConfig.description}</p>
            </div>
          </div>

          {/* 决策时间 */}
          <div className="text-right">
            <div className="flex items-center text-sm text-slate-500 mb-1">
              <ClockIcon className="h-4 w-4 mr-1" />
              决策时间
            </div>
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              {new Date(decision.created_at).toLocaleString()}
            </div>
          </div>
        </div>

        {/* 置信度可视化 */}
        {decision.confidence_level && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                决策置信度
              </span>
              <Badge
                variant={
                  confidenceLevel.color === 'green'
                    ? 'success'
                    : confidenceLevel.color === 'blue'
                    ? 'secondary'
                    : confidenceLevel.color === 'yellow'
                    ? 'outline'
                    : 'destructive'
                }
              >
                {confidenceLevel.level} ({decision.confidence_level}%)
              </Badge>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3 overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${decision.confidence_level}%` }}
                transition={{ duration: 1, ease: 'easeOut' }}
                className={`h-full rounded-full ${
                  confidenceLevel.color === 'green'
                    ? 'bg-green-500'
                    : confidenceLevel.color === 'blue'
                    ? 'bg-blue-500'
                    : confidenceLevel.color === 'yellow'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`}
              />
            </div>
            <p className="text-xs text-slate-500 mt-1">{confidenceLevel.description}</p>
          </div>
        )}
      </motion.div>

      {/* 详细信息网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* 价格目标 */}
        {(priceRange || decision.stop_loss_price || decision.take_profit_price) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
                <span>价格目标</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {priceRange && (
                  <div>
                    <div className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">
                      建议入场价格区间
                    </div>
                    <div className="text-lg font-semibold text-slate-900 dark:text-white">
                      ¥{priceRange.min || priceRange.low || '未设定'} - ¥
                      {priceRange.max || priceRange.high || '未设定'}
                    </div>
                  </div>
                )}

                {decision.stop_loss_price && (
                  <div className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <ShieldCheckIcon className="h-4 w-4 text-red-600" />
                      <span className="text-sm font-medium text-red-700 dark:text-red-400">
                        止损价格
                      </span>
                    </div>
                    <span className="font-semibold text-red-700 dark:text-red-400">
                      ¥{decision.stop_loss_price}
                    </span>
                  </div>
                )}

                {decision.take_profit_price && (
                  <div className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <TrophyIcon className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700 dark:text-green-400">
                        止盈价格
                      </span>
                    </div>
                    <span className="font-semibold text-green-700 dark:text-green-400">
                      ¥{decision.take_profit_price}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 仓位管理 */}
        {decision.position_size_percentage && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ChartBarIcon className="h-5 w-5 text-blue-600" />
                <span>仓位管理</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-1">
                    建议仓位比例
                  </div>
                  <div className="text-3xl font-bold text-blue-600">
                    {decision.position_size_percentage}%
                  </div>
                </div>

                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.min(decision.position_size_percentage, 100)}%` }}
                    transition={{ duration: 1, ease: 'easeOut' }}
                    className={`h-full rounded-full ${
                      riskLevel.color === 'green'
                        ? 'bg-green-500'
                        : riskLevel.color === 'blue'
                        ? 'bg-blue-500'
                        : riskLevel.color === 'yellow'
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                    }`}
                  />
                </div>

                <Badge
                  variant={
                    riskLevel.color === 'green'
                      ? 'success'
                      : riskLevel.color === 'blue'
                      ? 'secondary'
                      : riskLevel.color === 'yellow'
                      ? 'outline'
                      : 'destructive'
                  }
                  className="w-full justify-center"
                >
                  {riskLevel.level}
                </Badge>

                <p className="text-xs text-slate-500">{riskLevel.description}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 风险评估 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-600" />
              <span>风险评估</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* 综合风险等级 */}
              <div className="text-center">
                <div
                  className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-2 ${
                    riskLevel.color === 'green'
                      ? 'bg-green-100 dark:bg-green-900/30'
                      : riskLevel.color === 'blue'
                      ? 'bg-blue-100 dark:bg-blue-900/30'
                      : riskLevel.color === 'yellow'
                      ? 'bg-yellow-100 dark:bg-yellow-900/30'
                      : 'bg-red-100 dark:bg-red-900/30'
                  }`}
                >
                  <ScaleIcon
                    className={`h-8 w-8 ${
                      riskLevel.color === 'green'
                        ? 'text-green-600'
                        : riskLevel.color === 'blue'
                        ? 'text-blue-600'
                        : riskLevel.color === 'yellow'
                        ? 'text-yellow-600'
                        : 'text-red-600'
                    }`}
                  />
                </div>
                <div className="font-semibold text-slate-900 dark:text-white">
                  {riskLevel.level}
                </div>
                <div className="text-sm text-slate-500">{riskLevel.description}</div>
              </div>

              {/* 风险提示 */}
              <div className="space-y-2 text-xs">
                <div className="flex items-start space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600 dark:text-slate-400">已设置止损保护机制</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600 dark:text-slate-400">基于多维度分析决策</span>
                </div>
                <div className="flex items-start space-x-2">
                  <ExclamationTriangleIcon className="h-3 w-3 text-orange-600 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-600 dark:text-slate-400">市场存在不确定性风险</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 决策理由 */}
      {decision.decision_rationale && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BanknotesIcon className="h-5 w-5 text-purple-600" />
              <span>决策理由</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-slate dark:prose-invert max-w-none">
              <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                {decision.decision_rationale}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 决策摘要 */}
      <Card className="border-2 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <CheckCircleIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">投资决策摘要</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-blue-800 dark:text-blue-200">决策类型:</span>
                  <span className="ml-2 text-blue-700 dark:text-blue-300">
                    {decisionConfig.label}
                  </span>
                </div>
                {decision.confidence_level && (
                  <div>
                    <span className="font-medium text-blue-800 dark:text-blue-200">置信度:</span>
                    <span className="ml-2 text-blue-700 dark:text-blue-300">
                      {decision.confidence_level}% ({confidenceLevel.level})
                    </span>
                  </div>
                )}
                {decision.position_size_percentage && (
                  <div>
                    <span className="font-medium text-blue-800 dark:text-blue-200">建议仓位:</span>
                    <span className="ml-2 text-blue-700 dark:text-blue-300">
                      {decision.position_size_percentage}%
                    </span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-blue-800 dark:text-blue-200">风险等级:</span>
                  <span className="ml-2 text-blue-700 dark:text-blue-300">{riskLevel.level}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 免责声明 */}
      <div className="text-center text-xs text-slate-500 bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
        <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
        <strong>风险提示:</strong>
        本投资建议仅供参考，不构成投资建议。投资有风险，入市需谨慎。请根据自身风险承受能力做出投资决策。
      </div>
    </div>
  );
}
