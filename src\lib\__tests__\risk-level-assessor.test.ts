/**
 * 风险等级评定器测试
 * Risk Level Assessor Tests
 */

import { RiskAssessment, RiskLevel, RiskType } from '@/types/risk-report';
import { RISK_LEVEL_CRITERIA, riskLevelAssessor } from '../risk-level-assessor';

describe('RiskLevelAssessor', () => {
  describe('calculateRiskScore', () => {
    it('应该正确计算市场风险评分', () => {
      const metrics = {
        volatility: 0.2,
        beta: 1.5,
        var: -0.1,
        maxDrawdown: -0.15,
      };

      const score = riskLevelAssessor.calculateRiskScore(RiskType.MARKET, metrics);

      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
      expect(typeof score).toBe('number');
    });

    it('应该正确计算流动性风险评分', () => {
      const metrics = {
        volume: 500000,
        spread: 0.01,
        amihudRatio: 0.001,
      };

      const score = riskLevelAssessor.calculateRiskScore(RiskType.LIQUIDITY, metrics);

      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('应该正确计算信用风险评分', () => {
      const metrics = {
        debtRatio: 0.6,
        currentRatio: 1.2,
        interestCoverage: 2.5,
      };

      const score = riskLevelAssessor.calculateRiskScore(RiskType.CREDIT, metrics);

      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('应该处理缺失指标的情况', () => {
      const metrics = {
        volatility: 0.3,
        // 缺少其他指标
      };

      const score = riskLevelAssessor.calculateRiskScore(RiskType.MARKET, metrics);

      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('应该在没有指标时返回默认评分', () => {
      const metrics = {};

      const score = riskLevelAssessor.calculateRiskScore(RiskType.MARKET, metrics);

      expect(score).toBe(50);
    });
  });

  describe('determineRiskLevel', () => {
    it('应该正确确定低风险等级', () => {
      const score = 20;
      const level = riskLevelAssessor.determineRiskLevel(score, RiskType.MARKET);

      expect(level).toBe(RiskLevel.LOW);
    });

    it('应该正确确定中等风险等级', () => {
      const score = 45;
      const level = riskLevelAssessor.determineRiskLevel(score, RiskType.MARKET);

      expect(level).toBe(RiskLevel.MEDIUM);
    });

    it('应该正确确定高风险等级', () => {
      const score = 70;
      const level = riskLevelAssessor.determineRiskLevel(score, RiskType.MARKET);

      expect(level).toBe(RiskLevel.HIGH);
    });

    it('应该正确确定极高风险等级', () => {
      const score = 90;
      const level = riskLevelAssessor.determineRiskLevel(score, RiskType.MARKET);

      expect(level).toBe(RiskLevel.CRITICAL);
    });

    it('应该根据不同风险类型使用不同阈值', () => {
      const score = 40;

      const marketLevel = riskLevelAssessor.determineRiskLevel(score, RiskType.MARKET);
      const liquidityLevel = riskLevelAssessor.determineRiskLevel(score, RiskType.LIQUIDITY);

      // 流动性风险的阈值更低，相同评分可能得到不同等级
      expect(marketLevel).toBeOneOf(Object.values(RiskLevel));
      expect(liquidityLevel).toBeOneOf(Object.values(RiskLevel));
    });
  });

  describe('assessRisk', () => {
    it('应该生成完整的风险评估结果', () => {
      const metrics = {
        volatility: 0.25,
        beta: 1.3,
        var: -0.08,
        maxDrawdown: -0.12,
      };

      const assessment = riskLevelAssessor.assessRisk(RiskType.MARKET, metrics);

      expect(assessment.type).toBe(RiskType.MARKET);
      expect(assessment.level).toBeOneOf(Object.values(RiskLevel));
      expect(assessment.score).toBeGreaterThanOrEqual(0);
      expect(assessment.score).toBeLessThanOrEqual(100);
      expect(assessment.description).toBeTruthy();
      expect(assessment.metrics).toEqual(metrics);
      expect(assessment.factors).toBeInstanceOf(Array);
      expect(assessment.recommendations).toBeInstanceOf(Array);
    });

    it('应该识别高风险因素', () => {
      const highRiskMetrics = {
        volatility: 0.4, // 高波动率
        beta: 2.0, // 高Beta
        var: -0.2, // 高VaR
        maxDrawdown: -0.3, // 大幅回撤
      };

      const assessment = riskLevelAssessor.assessRisk(RiskType.MARKET, highRiskMetrics);

      expect(assessment.level).toBeOneOf([RiskLevel.HIGH, RiskLevel.CRITICAL]);
      expect(assessment.factors.length).toBeGreaterThan(0);
      expect(assessment.factors).toContain('高波动率');
      expect(assessment.factors).toContain('高Beta系数');
    });

    it('应该生成相应的风险控制建议', () => {
      const highRiskMetrics = {
        volatility: 0.5,
        beta: 2.5,
      };

      const assessment = riskLevelAssessor.assessRisk(RiskType.MARKET, highRiskMetrics);

      expect(assessment.recommendations.length).toBeGreaterThan(0);

      if (assessment.level === RiskLevel.CRITICAL) {
        expect(assessment.recommendations).toContain('建议暂停投资或大幅减仓');
      }
    });

    it('应该处理额外风险因素', () => {
      const metrics = { volatility: 0.2 };
      const additionalFactors = ['市场不确定性', '政策风险'];

      const assessment = riskLevelAssessor.assessRisk(RiskType.MARKET, metrics, additionalFactors);

      expect(assessment.factors).toContain('市场不确定性');
      expect(assessment.factors).toContain('政策风险');
    });
  });

  describe('calculateOverallRiskScore', () => {
    it('应该计算综合风险评分', () => {
      const assessments: RiskAssessment[] = [
        {
          type: RiskType.MARKET,
          level: RiskLevel.MEDIUM,
          score: 50,
          description: '市场风险中等',
          metrics: { volatility: 0.2 },
          factors: [],
          recommendations: [],
        },
        {
          type: RiskType.LIQUIDITY,
          level: RiskLevel.LOW,
          score: 30,
          description: '流动性风险较低',
          metrics: { volume: 1000000 },
          factors: [],
          recommendations: [],
        },
      ];

      const result = riskLevelAssessor.calculateOverallRiskScore(assessments);

      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(100);
      expect(result.level).toBeOneOf(Object.values(RiskLevel));
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(100);
    });

    it('应该处理空评估列表', () => {
      const result = riskLevelAssessor.calculateOverallRiskScore([]);

      expect(result.score).toBe(50);
      expect(result.level).toBe(RiskLevel.MEDIUM);
      expect(result.confidence).toBe(0);
    });

    it('应该根据风险类型权重计算', () => {
      const marketAssessment: RiskAssessment = {
        type: RiskType.MARKET,
        level: RiskLevel.HIGH,
        score: 80,
        description: '市场风险高',
        metrics: { volatility: 0.4, beta: 1.8, var: -0.15, maxDrawdown: -0.2 },
        factors: [],
        recommendations: [],
      };

      const operationalAssessment: RiskAssessment = {
        type: RiskType.OPERATIONAL,
        level: RiskLevel.LOW,
        score: 20,
        description: '操作风险低',
        metrics: { governance: 0.1 },
        factors: [],
        recommendations: [],
      };

      const result = riskLevelAssessor.calculateOverallRiskScore([
        marketAssessment,
        operationalAssessment,
      ]);

      // 市场风险权重更高，应该对综合评分影响更大
      expect(result.score).toBeGreaterThan(40);
      expect(result.level).toBeOneOf([RiskLevel.MEDIUM, RiskLevel.HIGH]);
    });

    it('应该基于指标数量评估置信度', () => {
      const highDataQualityAssessment: RiskAssessment = {
        type: RiskType.MARKET,
        level: RiskLevel.MEDIUM,
        score: 50,
        description: '市场风险中等',
        metrics: { volatility: 0.2, beta: 1.1, var: -0.05, maxDrawdown: -0.1, sharpe: 1.2 },
        factors: [],
        recommendations: [],
      };

      const lowDataQualityAssessment: RiskAssessment = {
        type: RiskType.LIQUIDITY,
        level: RiskLevel.MEDIUM,
        score: 50,
        description: '流动性风险中等',
        metrics: { volume: 500000 },
        factors: [],
        recommendations: [],
      };

      const highQualityResult = riskLevelAssessor.calculateOverallRiskScore([
        highDataQualityAssessment,
      ]);

      const lowQualityResult = riskLevelAssessor.calculateOverallRiskScore([
        lowDataQualityAssessment,
      ]);

      expect(highQualityResult.confidence).toBeGreaterThan(lowQualityResult.confidence);
    });
  });

  describe('RISK_LEVEL_CRITERIA', () => {
    it('应该包含所有风险类型的标准', () => {
      const riskTypes = Object.values(RiskType);

      riskTypes.forEach((riskType) => {
        expect(RISK_LEVEL_CRITERIA[riskType]).toBeDefined();
        expect(RISK_LEVEL_CRITERIA[riskType].type).toBe(riskType);
        expect(RISK_LEVEL_CRITERIA[riskType].thresholds).toBeDefined();
        expect(RISK_LEVEL_CRITERIA[riskType].weights).toBeDefined();
        expect(RISK_LEVEL_CRITERIA[riskType].description).toBeTruthy();
      });
    });

    it('应该有合理的阈值设置', () => {
      Object.values(RISK_LEVEL_CRITERIA).forEach((criteria) => {
        expect(criteria.thresholds.low).toBeLessThan(criteria.thresholds.medium);
        expect(criteria.thresholds.medium).toBeLessThan(criteria.thresholds.high);
        expect(criteria.thresholds.low).toBeGreaterThan(0);
        expect(criteria.thresholds.high).toBeLessThan(100);
      });
    });

    it('应该有合理的权重设置', () => {
      Object.values(RISK_LEVEL_CRITERIA).forEach((criteria) => {
        const weights = Object.values(criteria.weights);
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

        expect(totalWeight).toBeCloseTo(1, 1);
        weights.forEach((weight) => {
          expect(weight).toBeGreaterThan(0);
          expect(weight).toBeLessThanOrEqual(1);
        });
      });
    });
  });
});
